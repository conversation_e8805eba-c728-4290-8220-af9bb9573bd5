#!/bin/bash

# KPE项目端口管理脚本
# 用于快速关闭前后端服务占用的端口

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 常用端口定义
FRONTEND_PORTS=(5173 5174 5175)  # 前端端口
BACKEND_PORTS=(3000)   # 后端端口

echo -e "${BLUE}=== KPE项目端口管理工具 ===${NC}"
echo -e "${YELLOW}正在检查并关闭前后端Node/Java进程...${NC}"
echo ""

# 函数：检查进程是否为允许的类型（node或java）
is_allowed_process() {
    local pid=$1
    local process_info=$(ps -p $pid -o comm= 2>/dev/null)
    local process_cmd=$(ps -p $pid -o args= 2>/dev/null)
    
    # 检查进程名是否包含node或java
    if [[ "$process_info" == *"node"* ]] || [[ "$process_info" == *"java"* ]] || \
       [[ "$process_cmd" == *"node"* ]] || [[ "$process_cmd" == *"java"* ]]; then
        return 0  # 允许的进程
    else
        return 1  # 不允许的进程
    fi
}

# 函数：杀死指定端口的进程
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port 2>/dev/null)
    if [ ! -z "$pid" ]; then
        local process_info=$(ps -p $pid -o comm= 2>/dev/null)
        local process_cmd=$(ps -p $pid -o args= 2>/dev/null)
        
        # 检查是否为允许的进程类型
        if is_allowed_process $pid; then
            echo -e "${RED}正在关闭端口 $port 的进程 (PID: $pid, 进程: $process_info)...${NC}"
            kill -9 $pid 2>/dev/null
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✓ 端口 $port 已释放${NC}"
            else
                echo -e "${RED}✗ 无法关闭端口 $port 的进程${NC}"
            fi
        else
            echo -e "${YELLOW}⚠️  端口 $port 被非Node/Java进程占用，跳过 (PID: $pid, 进程: $process_info)${NC}"
        fi
    else
        echo -e "${GREEN}端口 $port 已经空闲${NC}"
    fi
}

# 函数：批量关闭端口
kill_ports() {
    local ports=("$@")
    for port in "${ports[@]}"; do
        kill_port $port
    done
}

# 关闭前端端口
echo -e "${BLUE}=== 关闭前端端口 ===${NC}"
kill_ports "${FRONTEND_PORTS[@]}"

echo ""

# 关闭后端端口
echo -e "${BLUE}=== 关闭后端端口 ===${NC}"
kill_ports "${BACKEND_PORTS[@]}"

echo ""
echo -e "${GREEN}完成！所有前后端Node/Java进程已检查并关闭。${NC}"
