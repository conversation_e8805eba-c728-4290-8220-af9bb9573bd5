# KPE Backend .gitignore (Java/Spring Boot)

# ===== 编译输出 =====
target/
build/
out/
bin/

# ===== 编译的类文件 =====
*.class

# ===== 日志文件 =====
*.log
logs/

# ===== BlueJ 文件 =====
*.ctxt

# ===== Mobile Tools for Java (J2ME) =====
.mtj.tmp/

# ===== 包文件 =====
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# ===== JVM 崩溃日志 =====
hs_err_pid*
replay_pid*

# ===== IDE 配置 =====
.idea/
*.iws
*.iml
*.ipr
.vscode/
.settings/
.project
.classpath
.factorypath

# ===== Maven =====
.mvn/
mvnw
mvnw.cmd

# ===== Gradle =====
.gradle/
gradle/
gradlew
gradlew.bat

# ===== Spring Boot =====
*.original

# ===== 环境配置文件 =====
.env
application-local.yml
application-local.properties
application-dev.yml
application-dev.properties
application-prod.yml
application-prod.properties

# ===== 数据库文件 =====
*.db
*.sqlite
*.sqlite3
*.h2.db

# ===== 临时文件 =====
*.tmp
*.temp
*.swp
*.swo
*~

# ===== 系统文件 =====
.DS_Store
Thumbs.db

# ===== 测试覆盖率 =====
.jacoco/
jacoco.exec

# ===== STS (Spring Tool Suite) =====
.apt_generated
.apt_generated_test
.factorypath
.springBeans
.sts4-cache

# ===== IntelliJ IDEA =====
.idea
*.iws
*.iml
*.ipr
out/

# ===== NetBeans =====
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/

# ===== VS Code =====
.vscode/

# ===== 本地配置 =====
config/local/
*.local

# ===== 证书文件 =====
*.pem
*.key
*.crt
*.p12
*.pfx

# ===== 运行时文件 =====
*.pid
nohup.out

