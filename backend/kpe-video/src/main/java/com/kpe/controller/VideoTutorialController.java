package com.kpe.controller;

import com.kpe.common.BaseController;
import com.kpe.dto.ApiResponse;
import com.kpe.dto.VideoTutorialRequest;
import com.kpe.dto.VideoTutorialResponse;
import com.kpe.entity.VideoTutorial;
import com.kpe.mapper.VideoTutorialMapper;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频教程控制器（简化版）
 */
@RestController
@RequestMapping("/api/projects/{projectId}/video-tutorials")
@CrossOrigin(origins = "*")
public class VideoTutorialController extends BaseController {

    @Autowired
    private VideoTutorialMapper videoTutorialMapper;

    /**
     * 获取项目的视频教程列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<VideoTutorial>>> getVideoTutorials(@PathVariable Long projectId) {
        return executeWithResponse(() -> {
            logger.info("📺 获取项目 {} 的视频教程列表", projectId);
            List<VideoTutorial> tutorials = videoTutorialMapper.findByProjectIdOrderByCreatedAtDesc(projectId);
            logger.info("✅ 找到 {} 个视频教程", tutorials.size());
            return tutorials;
        }, "获取视频教程列表");
    }

    /**
     * 获取单个视频教程
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<VideoTutorial>> getVideoTutorial(
            @PathVariable Long projectId,
            @PathVariable Long id) {
        return executeWithResponse(() -> {
            logger.info("📺 获取视频教程详情: 项目{}, ID: {}", projectId, id);
            VideoTutorial tutorial = videoTutorialMapper.findByIdAndProjectId(id, projectId);
            return validateEntityExists(tutorial, "视频教程", id);
        }, "获取视频教程详情");
    }

    /**
     * 创建视频教程（简化版）
     */
    @PostMapping
    public ResponseEntity<ApiResponse<VideoTutorial>> createVideoTutorial(
            @PathVariable Long projectId,
            @Valid @RequestBody VideoTutorialRequest request) {
        return executeWithResponse(() -> {
            logger.info("📺 创建视频教程: 项目{}, 知识点: {}", projectId, request.getKnowledgePointId());
            
            VideoTutorial tutorial = new VideoTutorial();
            tutorial.setProjectId(projectId);
            tutorial.setKnowledgePointId(request.getKnowledgePointId());
            tutorial.setTitle(request.getTitle() != null ? request.getTitle() : "默认标题");
            tutorial.setContent("视频教程内容待生成");
            tutorial.setStatus("draft");
            tutorial.setCreatedBy("user");
            tutorial.setCreatedAt(LocalDateTime.now());
            tutorial.setUpdatedAt(LocalDateTime.now());
            
            int result = videoTutorialMapper.insert(tutorial);
            if (result > 0) {
                logger.info("✅ 视频教程创建成功: ID {}", tutorial.getId());
                return tutorial;
            } else {
                throw new RuntimeException("创建视频教程失败");
            }
        }, "创建视频教程");
    }

    /**
     * 更新视频教程
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<VideoTutorial>> updateVideoTutorial(
            @PathVariable Long projectId,
            @PathVariable Long id,
            @Valid @RequestBody VideoTutorialRequest request) {
        return executeWithResponse(() -> {
            logger.info("📺 更新视频教程: 项目{}, ID: {}", projectId, id);
            
            VideoTutorial existing = videoTutorialMapper.findByIdAndProjectId(id, projectId);
            validateEntityExists(existing, "视频教程", id);
            
            existing.setTitle(request.getTitle());
            existing.setUpdatedAt(LocalDateTime.now());
            
            int result = videoTutorialMapper.updateById(existing);
            if (result > 0) {
                logger.info("✅ 视频教程更新成功: ID {}", id);
                return existing;
            } else {
                throw new RuntimeException("更新视频教程失败");
            }
        }, "更新视频教程");
    }

    /**
     * 删除视频教程
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteVideoTutorial(
            @PathVariable Long projectId,
            @PathVariable Long id) {
        return executeWithResponse(() -> {
            logger.info("📺 删除视频教程: 项目{}, ID: {}", projectId, id);
            
            VideoTutorial existing = videoTutorialMapper.findByIdAndProjectId(id, projectId);
            validateEntityExists(existing, "视频教程", id);
            
            int result = videoTutorialMapper.deleteById(id);
            if (result > 0) {
                logger.info("✅ 视频教程删除成功: ID {}", id);
                return "删除成功";
            } else {
                throw new RuntimeException("删除视频教程失败");
            }
        }, "删除视频教程");
    }

    /**
     * 生成视频教程（占位符实现）
     */
    @PostMapping("/generate")
    public ResponseEntity<ApiResponse<VideoTutorialResponse>> generateVideoTutorial(
            @PathVariable Long projectId,
            @Valid @RequestBody VideoTutorialRequest request) {
        return executeWithResponse(() -> {
            logger.info("🎬 生成视频教程: 项目{}, 知识点: {}", projectId, request.getKnowledgePointId());
            
            // 简化实现：直接返回成功响应
            VideoTutorialResponse response = new VideoTutorialResponse();
            response.setSuccess(true);
            response.setMessage("视频教程生成功能暂未实现，请等待后续版本");
            response.setTaskId("temp-" + System.currentTimeMillis());
            
            logger.warn("⚠️ 视频教程生成功能暂未实现");
            return response;
        }, "生成视频教程");
    }
} 