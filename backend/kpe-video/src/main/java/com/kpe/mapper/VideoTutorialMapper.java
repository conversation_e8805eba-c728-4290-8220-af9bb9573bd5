package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.VideoTutorial;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 视频教程Mapper接口
 */
@Mapper
public interface VideoTutorialMapper extends BaseMapper<VideoTutorial> {

    /**
     * 根据项目ID获取视频教程列表（按创建时间降序）
     */
    @Select("SELECT * FROM video_tutorials WHERE project_id = #{projectId} ORDER BY created_at DESC")
    List<VideoTutorial> findByProjectIdOrderByCreatedAtDesc(@Param("projectId") Long projectId);

    /**
     * 根据项目ID获取视频教程列表
     */
    @Select("SELECT * FROM video_tutorials WHERE project_id = #{projectId} ORDER BY created_at DESC")
    List<VideoTutorial> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据ID和项目ID查找单个视频教程
     */
    @Select("SELECT * FROM video_tutorials WHERE id = #{id} AND project_id = #{projectId}")
    VideoTutorial findByIdAndProjectId(@Param("id") Long id, @Param("projectId") Long projectId);

    /**
     * 根据项目ID和知识点ID查找视频教程
     */
    @Select("SELECT * FROM video_tutorials WHERE project_id = #{projectId} AND knowledge_point_id = #{knowledgePointId} ORDER BY created_at DESC LIMIT 1")
    VideoTutorial findByProjectIdAndKnowledgePointId(@Param("projectId") Long projectId,
                                                     @Param("knowledgePointId") String knowledgePointId);

    /**
     * 根据项目ID和知识点路径查找视频教程
     */
    @Select("SELECT * FROM video_tutorials WHERE project_id = #{projectId} AND knowledge_point_path = #{knowledgePointPath} ORDER BY created_at DESC")
    List<VideoTutorial> findByProjectIdAndKnowledgePointPath(@Param("projectId") Long projectId,
                                                             @Param("knowledgePointPath") String knowledgePointPath);

    /**
     * 根据项目ID删除所有视频教程
     */
    @Delete("DELETE FROM video_tutorials WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") Long projectId);
}
