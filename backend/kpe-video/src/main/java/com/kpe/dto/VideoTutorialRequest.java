package com.kpe.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 视频教程请求DTO
 */
public class VideoTutorialRequest {

    private Long projectId;

    @NotBlank(message = "知识点ID不能为空")
    @Size(max = 200, message = "知识点ID长度不能超过200字符")
    private String knowledgePointId;

    @Size(max = 500, message = "标题长度不能超过500字符")
    private String title;

    // Constructors
    public VideoTutorialRequest() {
    }

    public VideoTutorialRequest(Long projectId, String knowledgePointId) {
        this.projectId = projectId;
        this.knowledgePointId = knowledgePointId;
    }

    public VideoTutorialRequest(Long projectId, String knowledgePointId, String title) {
        this.projectId = projectId;
        this.knowledgePointId = knowledgePointId;
        this.title = title;
    }

    // Getters and Setters
    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return "VideoTutorialRequest{" +
                "projectId=" + projectId +
                ", knowledgePointId='" + knowledgePointId + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
}
