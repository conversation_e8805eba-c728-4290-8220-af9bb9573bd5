package com.kpe.dto;

import com.kpe.entity.VideoTutorial;

/**
 * 视频教程响应DTO
 */
public class VideoTutorialResponse {

    private boolean success;
    private String message;
    private VideoTutorial data;
    private String errorMessage;
    private String taskId;

    // Constructors
    public VideoTutorialResponse() {
    }

    public VideoTutorialResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public VideoTutorialResponse(boolean success, String message, VideoTutorial data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    // Static factory methods
    public static VideoTutorialResponse success(VideoTutorial data) {
        return new VideoTutorialResponse(true, "视频教程获取成功", data);
    }

    public static VideoTutorialResponse success(String message, VideoTutorial data) {
        return new VideoTutorialResponse(true, message, data);
    }

    public static VideoTutorialResponse error(String errorMessage) {
        VideoTutorialResponse response = new VideoTutorialResponse(false, "视频教程获取失败");
        response.setErrorMessage(errorMessage);
        return response;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public VideoTutorial getData() {
        return data;
    }

    public void setData(VideoTutorial data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @Override
    public String toString() {
        return "VideoTutorialResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", errorMessage='" + errorMessage + '\'' +
                ", taskId='" + taskId + '\'' +
                '}';
    }
}
