<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.kpe</groupId>
    <artifactId>kpe-backend-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>KPE Backend Parent</name>
    <description>KPE Backend Parent Project with Multi-Module Structure</description>

    <modules>
        <module>kpe-common</module>
        <module>kpe-project</module>
        <module>kpe-knowledge</module>
        <module>kpe-exam</module>
        <module>kpe-flashcard</module>
        <module>kpe-ai</module>
        <module>kpe-quiz</module>
        <module>kpe-review</module>
        <module>kpe-video</module>
        <module>kpe-web</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.12</version>
        <relativePath/>
    </parent>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <fastjson2.version>2.0.43</fastjson2.version>
        <aliyun-oss.version>3.17.4</aliyun-oss.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Internal Dependencies -->
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-project</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-knowledge</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-exam</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-flashcard</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-ai</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-quiz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-review</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kpe</groupId>
                <artifactId>kpe-video</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- External Dependencies -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
