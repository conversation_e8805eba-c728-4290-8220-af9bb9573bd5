---
alwaysApply: true
---
# AI 助手工作指南（后端部分）

## 必须遵守的规则
### 后端
- **技术栈**: Java 17 + Spring Boot 2.7.18 + PostgreSQL 14 + MyBatis Plus
- **JSON处理**: fastjson2 (全项目统一使用)
- **缓存**: Spring Cache (内存缓存)
- **文件存储**: 阿里云OSS (条件化配置)
- **AI集成**: 扣子(Coze) API - 非流式轮询
- **JDK 17特性**: 推荐使用switch表达式、var关键字、Pattern Matching、Text Blocks等新特性
- **JSON处理**: 必须使用fastjson2 + JsonUtils工具类
- **缓存策略**: 使用`@Cacheable`缓存，`@CacheEvict`清理
- **异步处理**: AI调用使用`CompletableFuture`，超时5分钟
- **事务管理**: AI调用移出事务边界，避免数据库连接超时
- **异常处理**: 使用`GlobalExceptionHandler`统一处理
- **文件上传**: OSS服务条件化配置，需要`oss.enabled=true`

## 严格禁止事项 
- 禁止数据库设计使用外键和存储过程，都用代码解决