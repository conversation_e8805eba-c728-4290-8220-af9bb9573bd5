package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Project Mapper接口
 */
@Mapper
public interface ProjectMapper extends BaseMapper<Project> {

    /**
     * 查找所有活跃的项目
     */
    @Select("SELECT * FROM projects WHERE status = #{status} ORDER BY created_at DESC")
    List<Project> findByStatusOrderByCreatedAtDesc(@Param("status") String status);

    /**
     * 根据ID查找活跃的项目
     */
    @Select("SELECT * FROM projects WHERE id = #{id} AND status = #{status}")
    Project findByIdAndStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 根据名称查找项目（忽略大小写）
     */
    @Select("SELECT * FROM projects WHERE LOWER(name) = LOWER(#{name}) AND status = #{status}")
    Project findByNameIgnoreCaseAndStatus(@Param("name") String name, @Param("status") String status);

    /**
     * 根据创建者查找项目
     */
    @Select("SELECT * FROM projects WHERE created_by = #{createdBy} AND status = #{status} ORDER BY created_at DESC")
    List<Project> findByCreatedByAndStatusOrderByCreatedAtDesc(@Param("createdBy") String createdBy, @Param("status") String status);
}
