package com.kpe.controller;

import com.kpe.common.BaseController;
import com.kpe.dto.ApiResponse;
import com.kpe.entity.Project;
import com.kpe.service.ProjectService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/projects")
@CrossOrigin(origins = "*")
public class ProjectController extends BaseController {

    @Autowired
    private ProjectService projectService;

    /**
     * 获取项目列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<Project>>> getProjects() {
        return executeWithResponse(() -> projectService.getAllProjects(), "获取项目列表");
    }

    /**
     * 获取单个项目
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Project>> getProject(@PathVariable Long id) {
        return executeWithResponse(() -> {
            Optional<Project> project = projectService.getProjectById(id);
            return validateEntityExists(project.orElse(null), "项目", id);
        }, "获取项目详情");
    }

    /**
     * 创建项目
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Project>> createProject(@Valid @RequestBody CreateProjectRequest request) {
        return executeWithResponse(() ->
                        projectService.createProject(request.getName().trim(), request.getDescription(), "user"),
                "创建项目");
    }

    /**
     * 更新项目
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Project>> updateProject(@PathVariable Long id,
                                                              @Valid @RequestBody UpdateProjectRequest request) {
        return executeWithResponse(() -> {
            Optional<Project> project = projectService.updateProject(id, request.getName().trim(), request.getDescription());
            return validateEntityExists(project.orElse(null), "项目", id);
        }, "更新项目");
    }

    /**
     * 删除项目
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteProject(@PathVariable Long id) {
        return executeWithResponse(() -> {
            boolean deleted = projectService.deleteProject(id);
            if (!deleted) {
                throw new IllegalArgumentException("项目不存在");
            }
        }, "删除项目", "项目删除成功");
    }

    // Request DTOs
    public static class CreateProjectRequest {
        @NotBlank(message = "项目名称不能为空")
        private String name;
        private String description;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    public static class UpdateProjectRequest {
        @NotBlank(message = "项目名称不能为空")
        private String name;
        private String description;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
