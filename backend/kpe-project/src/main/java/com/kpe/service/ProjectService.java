package com.kpe.service;

import com.kpe.common.BaseCrudService;
import com.kpe.config.CacheConfig;
import com.kpe.entity.Project;
import com.kpe.mapper.ProjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ProjectService extends BaseCrudService<Project, Long, ProjectMapper> {

    @Autowired
    private ProjectMapper projectMapper;

    @Override
    protected ProjectMapper getMapper() {
        return projectMapper;
    }

    @Override
    protected String getEntityName() {
        return "项目";
    }

    @Override
    protected void setTimestamps(Project entity, boolean isCreate) {
        LocalDateTime now = LocalDateTime.now();
        if (isCreate) {
            entity.setCreatedAt(now);
        }
        entity.setUpdatedAt(now);
    }

    @Override
    protected String[] getRelatedCacheNames() {
        return new String[]{
                CacheConfig.PROJECT_LIST_CACHE,
                CacheConfig.PROJECT_CACHE
        };
    }

    @Override
    protected void validateBeforeCreate(Project entity) {
        super.validateBeforeCreate(entity);
        validateNotBlank(entity.getName(), "项目名称");

        // 检查项目名称是否已存在
        Project existingProject = projectMapper.findByNameIgnoreCaseAndStatus(entity.getName(), "active");
        validateEntityUniqueness(existingProject != null, "项目", "名称", entity.getName());
    }

    /**
     * 获取所有活跃项目
     */
    @Cacheable(value = CacheConfig.PROJECT_LIST_CACHE, key = "'all_active_projects'")
    public List<Project> getAllProjects() {
        return executeWithExceptionHandling("获取项目列表", () -> {
            List<Project> projects = projectMapper.findByStatusOrderByCreatedAtDesc("active");
            logInfo("查询完成，返回 {} 条记录", projects.size());
            logInfo("🗄️ 项目列表已缓存");
            return projects;
        });
    }

    /**
     * 获取所有活跃项目ID
     */
    public List<Long> getAllProjectIds() {
        return executeWithExceptionHandling("获取项目ID列表", () -> {
            List<Project> projects = projectMapper.findByStatusOrderByCreatedAtDesc("active");
            return projects.stream()
                    .map(Project::getId)
                    .collect(java.util.stream.Collectors.toList());
        });
    }

    /**
     * 根据ID获取项目
     */
    @Cacheable(value = CacheConfig.PROJECT_CACHE, key = "#id")
    public Optional<Project> getProjectById(Long id) {
        logger.info("📋 获取项目详情: {}", id);
        Project project = projectMapper.findByIdAndStatus(id, "active");
        if (project != null) {
            logger.info("✅ 项目查询成功: {}", project.getName());
            logger.info("🗄️ 项目详情已缓存: {}", id);
            return Optional.of(project);
        } else {
            logger.warn("⚠️  项目不存在或已删除: {}", id);
            return Optional.empty();
        }
    }

    /**
     * 创建项目
     */
    @CacheEvict(value = CacheConfig.PROJECT_LIST_CACHE, allEntries = true)
    public Project createProject(String name, String description, String createdBy) {
        Project project = new Project(name, description, createdBy);
        return create(project);
    }

    /**
     * 更新项目
     */
    @CacheEvict(value = {CacheConfig.PROJECT_CACHE, CacheConfig.PROJECT_WITH_KNOWLEDGE_CACHE}, key = "#id")
    public Optional<Project> updateProject(Long id, String name, String description) {
        // 检查新名称是否与其他项目冲突
        Project existingProject = projectMapper.findByNameIgnoreCaseAndStatus(name, "active");
        if (existingProject != null && !existingProject.getId().equals(id)) {
            throw new IllegalArgumentException("项目名称已存在: " + name);
        }

        Project project = new Project();
        project.setId(id);
        project.setName(name);
        project.setDescription(description);

        return update(id, project);
    }

    /**
     * 删除项目（软删除）
     */
    @CacheEvict(value = {CacheConfig.PROJECT_CACHE, CacheConfig.PROJECT_LIST_CACHE}, allEntries = true)
    public boolean deleteProject(Long id) {
        return executeWithExceptionHandling("删除项目", () -> {
            Project project = projectMapper.findByIdAndStatus(id, "active");
            if (project == null) {
                logger.warn("项目不存在或已删除: {}", id);
                return false;
            }
            
            // 软删除项目
            project.setStatus("deleted");
            project.setUpdatedAt(LocalDateTime.now());
            int rows = projectMapper.updateById(project);
            
            if (rows > 0) {
                logger.info("✅ 项目删除成功: {} (ID: {})", project.getName(), id);
                return true;
            } else {
                logger.error("项目删除失败: {}", id);
                return false;
            }
        });
    }

    /**
     * 根据创建者获取项目
     */
    @Cacheable(value = CacheConfig.PROJECT_LIST_CACHE, key = "'user_projects_' + #createdBy")
    public List<Project> getProjectsByCreatedBy(String createdBy) {
        logger.info("📋 获取用户项目列表: {}", createdBy);
        List<Project> projects = projectMapper.findByCreatedByAndStatusOrderByCreatedAtDesc(createdBy, "active");
        logger.info("🗄️ 用户项目列表已缓存: {}", createdBy);
        return projects;
    }
}
