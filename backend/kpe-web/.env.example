# KPE 系统环境配置文件示例
# 复制此文件为 .env 并根据实际环境修改以下配置

# ===========================================
# 服务器配置
# ===========================================
SERVER_PORT=3000

# Tomcat 配置
SERVER_TOMCAT_CONNECTION_TIMEOUT=60000
SERVER_TOMCAT_MAX_CONNECTIONS=8192
SERVER_TOMCAT_MAX_THREADS=200
SERVER_TOMCAT_MIN_SPARE_THREADS=10

# ===========================================
# 应用配置
# ===========================================
SPRING_APPLICATION_NAME=kpe-backend

# 异步请求超时时间（毫秒）
SPRING_MVC_ASYNC_REQUEST_TIMEOUT=300000

# 文件上传配置
SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=10MB
SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=50MB
SPRING_SERVLET_MULTIPART_FILE_SIZE_THRESHOLD=2KB

# ===========================================
# 数据库配置
# ===========================================
DB_URL=************************************
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_DRIVER_CLASS_NAME=org.postgresql.Driver

# HikariCP 连接池配置
DB_HIKARI_MAXIMUM_POOL_SIZE=20
DB_HIKARI_MINIMUM_IDLE=5
DB_HIKARI_IDLE_TIMEOUT=30000
DB_HIKARI_CONNECTION_TIMEOUT=30000
DB_HIKARI_MAX_LIFETIME=1800000
DB_HIKARI_LEAK_DETECTION_THRESHOLD=60000

# ===========================================
# MyBatis Plus 配置
# ===========================================
MYBATIS_PLUS_MAP_UNDERSCORE_TO_CAMEL_CASE=true
MYBATIS_PLUS_LOG_IMPL=org.apache.ibatis.logging.stdout.StdOutImpl
MYBATIS_PLUS_ID_TYPE=auto
MYBATIS_PLUS_LOGIC_DELETE_FIELD=deleted
MYBATIS_PLUS_LOGIC_DELETE_VALUE=1
MYBATIS_PLUS_LOGIC_NOT_DELETE_VALUE=0

# ===========================================
# 日志配置
# ===========================================
LOGGING_LEVEL_KPE=DEBUG
LOGGING_LEVEL_SPRING_WEB=INFO
LOGGING_LEVEL_HIBERNATE_SQL=DEBUG
LOGGING_LEVEL_HIBERNATE_TYPE=TRACE
LOGGING_LEVEL_SPRING_CACHE=DEBUG

# ===========================================
# 缓存配置
# ===========================================
CACHE_ENABLED=true
CACHE_STATISTICS=true

# ===========================================
# Coze API 配置
# ===========================================
COZE_API_BASE_URL=https://api.coze.cn
COZE_API_KEY=your_coze_api_key
COZE_SPACE_ID=your_space_id
COZE_BOT_ID=your_bot_id
COZE_EXAM_ANALYSIS_BOT_ID=your_exam_analysis_bot_id
COZE_EXAM_GENERATION_BOT_ID=your_exam_generation_bot_id
COZE_VIDEO_TUTORIAL_BOT_ID=your_video_tutorial_bot_id
COZE_API_TIMEOUT=300000
COZE_POLLING_INTERVAL=10000
COZE_POLLING_MAX_ATTEMPTS=30
