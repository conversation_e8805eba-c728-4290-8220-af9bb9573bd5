package com.kpe;

import com.kpe.config.EnvironmentConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableAsync
public class KpeBackendApplication {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(KpeBackendApplication.class);

        // 注册环境配置初始化器
        app.addInitializers(new EnvironmentConfig());

        app.run(args);
    }
}
