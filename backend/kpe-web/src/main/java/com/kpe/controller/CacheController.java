package com.kpe.controller;

import com.kpe.common.CacheHelper;
import com.kpe.config.CacheConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 缓存管理控制器
 * 提供缓存状态查看和清理功能
 */
@RestController
@RequestMapping("/api/cache")
public class CacheController {

    private static final Logger logger = LoggerFactory.getLogger(CacheController.class);

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private CacheHelper cacheHelper;

    /**
     * 获取缓存状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        logger.info("📊 获取缓存状态");

        Map<String, Object> status = new HashMap<>();

        // 检查各个缓存的状态
        status.put("cacheManager", cacheManager.getClass().getSimpleName());

        Map<String, Object> caches = new HashMap<>();
        caches.put(CacheConfig.PROJECT_CACHE, getCacheInfo(CacheConfig.PROJECT_CACHE));
        caches.put(CacheConfig.PROJECT_LIST_CACHE, getCacheInfo(CacheConfig.PROJECT_LIST_CACHE));
        caches.put(CacheConfig.KNOWLEDGE_CONFIG_CACHE, getCacheInfo(CacheConfig.KNOWLEDGE_CONFIG_CACHE));
        caches.put(CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE, getCacheInfo(CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE));
        caches.put(CacheConfig.KNOWLEDGE_PATHS_CACHE, getCacheInfo(CacheConfig.KNOWLEDGE_PATHS_CACHE));
        caches.put(CacheConfig.PROJECT_WITH_KNOWLEDGE_CACHE, getCacheInfo(CacheConfig.PROJECT_WITH_KNOWLEDGE_CACHE));
        status.put("caches", caches);

        return ResponseEntity.ok(status);
    }

    /**
     * 清除指定项目的所有相关缓存
     */
    @DeleteMapping("/project/{projectId}")
    public ResponseEntity<Map<String, Object>> clearProjectCache(@PathVariable Long projectId) {
        logger.info("🗑️ 清除项目缓存: {}", projectId);

        try {
            // 使用统一的缓存清理方法
            cacheHelper.clearProjectRelatedCaches(projectId);

            logger.info("✅ 项目缓存清除成功: {}", projectId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "项目缓存清除成功");
            result.put("projectId", projectId);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("❌ 清除项目缓存失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "清除项目缓存失败: " + e.getMessage());
            errorResult.put("projectId", projectId);
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 清除项目列表缓存
     */
    @DeleteMapping("/project-list")
    public ResponseEntity<Map<String, Object>> clearProjectListCache() {
        logger.info("🗑️ 清除项目列表缓存");

        try {
            clearCache(CacheConfig.PROJECT_LIST_CACHE);
            logger.info("✅ 项目列表缓存清除成功");

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "项目列表缓存清除成功");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("❌ 清除项目列表缓存失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "清除项目列表缓存失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 清除所有缓存
     */
    @DeleteMapping("/all")
    public ResponseEntity<Map<String, Object>> clearAllCache() {
        logger.info("🗑️ 清除所有缓存");

        try {
            clearCache(CacheConfig.PROJECT_CACHE);
            clearCache(CacheConfig.PROJECT_LIST_CACHE);
            clearCache(CacheConfig.KNOWLEDGE_CONFIG_CACHE);
            clearCache(CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE);
            clearCache(CacheConfig.KNOWLEDGE_PATHS_CACHE);
            clearCache(CacheConfig.PROJECT_WITH_KNOWLEDGE_CACHE);

            logger.info("✅ 所有缓存清除成功");

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "所有缓存清除成功");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("❌ 清除所有缓存失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "清除所有缓存失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取缓存信息
     */
    private Map<String, Object> getCacheInfo(String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Map<String, Object> info = new HashMap<>();
                info.put("exists", true);
                info.put("type", cache.getClass().getSimpleName());
                return info;
            } else {
                Map<String, Object> info = new HashMap<>();
                info.put("exists", false);
                return info;
            }
        } catch (Exception e) {
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("exists", false);
            errorInfo.put("error", e.getMessage());
            return errorInfo;
        }
    }

    /**
     * 清除指定缓存的指定键
     */
    @SuppressWarnings("unused")
    private void evictCache(String cacheName, Object key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evict(key);
            logger.debug("🗑️ 清除缓存项: {} - {}", cacheName, key);
        }
    }

    /**
     * 清除指定缓存的所有内容
     */
    private void clearCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            logger.debug("🗑️ 清空缓存: {}", cacheName);
        }
    }
}
