server:
  port: ${SERVER_PORT:3000}
  # Tomcat 配置
  tomcat:
    # 连接超时时间
    connection-timeout: ${SERVER_TOMCAT_CONNECTION_TIMEOUT:60000}
    # 最大连接数
    max-connections: ${SERVER_TOMCAT_MAX_CONNECTIONS:8192}
    # 最大线程数
    threads:
      max: ${SERVER_TOMCAT_MAX_THREADS:200}
      min-spare: ${SERVER_TOMCAT_MIN_SPARE_THREADS:10}

spring:
  application:
    name: ${SPRING_APPLICATION_NAME:kpe-backend}

  # 异步配置
  mvc:
    async:
      # 异步请求超时时间（毫秒）
      request-timeout: ${SPRING_MVC_ASYNC_REQUEST_TIMEOUT:300000}  # 5分钟

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: ${SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE:10MB}
      max-request-size: ${SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE:50MB}
      file-size-threshold: ${SPRING_SERVLET_MULTIPART_FILE_SIZE_THRESHOLD:2KB}
      resolve-lazily: false

  datasource:
    url: ${DB_URL:************************************************************************}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:wmc68s8p}
    driver-class-name: ${DB_DRIVER_CLASS_NAME:org.postgresql.Driver}
    hikari:
      maximum-pool-size: ${DB_HIKARI_MAXIMUM_POOL_SIZE:20}
      minimum-idle: ${DB_HIKARI_MINIMUM_IDLE:5}
      idle-timeout: ${DB_HIKARI_IDLE_TIMEOUT:30000}
      connection-timeout: ${DB_HIKARI_CONNECTION_TIMEOUT:30000}  # 30秒连接超时
      max-lifetime: ${DB_HIKARI_MAX_LIFETIME:1800000}      # 30分钟最大生命周期
      leak-detection-threshold: ${DB_HIKARI_LEAK_DETECTION_THRESHOLD:60000}  # 60秒泄漏检测

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: ${MYBATIS_PLUS_MAP_UNDERSCORE_TO_CAMEL_CASE:true}
    log-impl: ${MYBATIS_PLUS_LOG_IMPL:org.apache.ibatis.logging.stdout.StdOutImpl}
  global-config:
    db-config:
      id-type: ${MYBATIS_PLUS_ID_TYPE:auto}
      logic-delete-field: ${MYBATIS_PLUS_LOGIC_DELETE_FIELD:deleted}
      logic-delete-value: ${MYBATIS_PLUS_LOGIC_DELETE_VALUE:1}
      logic-not-delete-value: ${MYBATIS_PLUS_LOGIC_NOT_DELETE_VALUE:0}
  mapper-locations: classpath*:/mapper/**/*.xml
  type-handlers-package: com.kpe.config

logging:
  level:
    com.kpe: ${LOGGING_LEVEL_KPE:DEBUG}
    org.springframework.web: ${LOGGING_LEVEL_SPRING_WEB:INFO}
    org.hibernate.SQL: ${LOGGING_LEVEL_HIBERNATE_SQL:DEBUG}
    org.hibernate.type.descriptor.sql.BasicBinder: ${LOGGING_LEVEL_HIBERNATE_TYPE:TRACE}
    org.springframework.cache: ${LOGGING_LEVEL_SPRING_CACHE:DEBUG}

# Cache Configuration
cache:
  # 缓存配置
  enabled: ${CACHE_ENABLED:true}
  # 缓存统计
  statistics: ${CACHE_STATISTICS:true}

# Coze API Configuration
coze:
  api:
    base-url: ${COZE_API_BASE_URL:https://api.coze.cn}
    key: ${COZE_API_KEY:pat_9rb3oBr0YM7ByR1FJLH9213uWF07iC4Y2kkfcInRft1AzBDdbGUocgjd4EQPsvlc}
    space-id: ${COZE_SPACE_ID:7331640682861412352}
    bot-id: ${COZE_BOT_ID:7511928696224153619}
    exam-analysis-bot-id: ${COZE_EXAM_ANALYSIS_BOT_ID:7512076224383352842}
    exam-generation-bot-id: ${COZE_EXAM_GENERATION_BOT_ID:7512458051824730146}
    video-tutorial-bot-id: ${COZE_VIDEO_TUTORIAL_BOT_ID:7513133921983299596}
    timeout: ${COZE_API_TIMEOUT:300000}  # 5 minutes
    polling:
      interval: ${COZE_POLLING_INTERVAL:10000}   # 10 seconds
      max-attempts: ${COZE_POLLING_MAX_ATTEMPTS:30}   # 5 minutes total (30 * 10s = 300s)

# OSS Configuration
oss:
  enabled: ${OSS_ENABLED:true}
  endpoint: ${OSS_ENDPOINT:oss-cn-shanghai.aliyuncs.com}
  access-key-id: ${OSS_ACCESS_KEY_ID:LTAI5t6kHqkgzhiNRfCPX3Ls}
  access-key-secret: ${OSS_ACCESS_KEY_SECRET:******************************}
  bucket-name: ${OSS_BUCKET_NAME:kpe-system}
