spring:
  application:
    name: kpe-backend-test
  
  datasource:
    url: ***********************************************************************
    username: fang<PERSON><PERSON><PERSON>
    password: fang259758@@
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 2
      idle-timeout: 30000
      connection-timeout: 10000

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-handlers-package: com.kpe.config

logging:
  level:
    com.kpe: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.test: DEBUG

# Coze API Configuration (for testing)
coze:
  api:
    base-url: https://api.coze.cn
    key: test-key
    space-id: test-space
    bot-id: 7511928696224153619
    timeout: 300000
    polling:
      interval: 5000
      max-attempts: 10
