<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.kpe</groupId>
        <artifactId>kpe-backend-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>kpe-web</artifactId>
    <name>KPE Web</name>
    <description>KPE Web Entry Module - Main application entry point</description>

    <dependencies>
        <!-- Internal Dependencies - All Business Modules -->
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-project</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-knowledge</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-exam</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-flashcard</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-quiz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-review</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kpe</groupId>
            <artifactId>kpe-video</artifactId>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project> 