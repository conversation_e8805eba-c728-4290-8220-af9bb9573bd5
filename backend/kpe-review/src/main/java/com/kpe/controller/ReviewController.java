package com.kpe.controller;

import com.kpe.dto.ApiResponse;
import com.kpe.dto.review.ReviewConfigurationRequest;
import com.kpe.dto.review.ReviewConfigurationResponse;
import com.kpe.dto.review.ReviewWrongQuestionResponse;
import com.kpe.service.ReviewService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 复习管理控制器
 */
@RestController
@RequestMapping("/api/projects/{projectId}/reviews")
public class ReviewController {

    private static final Logger logger = LoggerFactory.getLogger(ReviewController.class);

    @Autowired
    private ReviewService reviewService;

    /**
     * 获取复习配置
     */
    @GetMapping("/configuration")
    public ResponseEntity<ApiResponse<ReviewConfigurationResponse>> getReviewConfiguration(
            @PathVariable Long projectId) {

        try {
            logger.info("📋 获取复习配置: 项目ID={}", projectId);

            ReviewConfigurationResponse response = reviewService.getReviewConfiguration(projectId);

            return ResponseEntity.ok(ApiResponse.success(response, "获取复习配置成功"));
        } catch (Exception e) {
            logger.error("❌ 获取复习配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取复习配置失败: " + e.getMessage()));
        }
    }

    /**
     * 保存复习配置
     */
    @PostMapping("/configuration")
    public ResponseEntity<ApiResponse<ReviewConfigurationResponse>> saveReviewConfiguration(
            @PathVariable Long projectId,
            @Valid @RequestBody ReviewConfigurationRequest request) {

        try {
            logger.info("💾 保存复习配置: 项目ID={}", projectId);

            // 设置项目ID
            request.setProjectId(projectId);

            ReviewConfigurationResponse response = reviewService.saveReviewConfiguration(request, "user");

            return ResponseEntity.ok(ApiResponse.success(response, "复习配置保存成功"));
        } catch (Exception e) {
            logger.error("❌ 保存复习配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("保存复习配置失败: " + e.getMessage()));
        }
    }

    /**
     * 更新复习配置
     */
    @PutMapping("/configuration")
    public ResponseEntity<ApiResponse<ReviewConfigurationResponse>> updateReviewConfiguration(
            @PathVariable Long projectId,
            @Valid @RequestBody ReviewConfigurationRequest request) {

        try {
            logger.info("🔄 更新复习配置: 项目ID={}", projectId);

            // 设置项目ID
            request.setProjectId(projectId);

            ReviewConfigurationResponse response = reviewService.saveReviewConfiguration(request, "user");

            return ResponseEntity.ok(ApiResponse.success(response, "复习配置更新成功"));
        } catch (Exception e) {
            logger.error("❌ 更新复习配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新复习配置失败: " + e.getMessage()));
        }
    }

    /**
     * 重置复习配置为默认值
     */
    @PostMapping("/configuration/reset")
    public ResponseEntity<ApiResponse<ReviewConfigurationResponse>> resetReviewConfiguration(
            @PathVariable Long projectId) {

        try {
            logger.info("🔄 重置复习配置: 项目ID={}", projectId);

            ReviewConfigurationResponse response = reviewService.resetToDefaultConfiguration(projectId, "user");

            return ResponseEntity.ok(ApiResponse.success(response, "复习配置已重置为默认值"));
        } catch (Exception e) {
            logger.error("❌ 重置复习配置失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("重置复习配置失败: " + e.getMessage()));
        }
    }

    /**
     * 获取今日需要复习的错题
     */
    @GetMapping("/today")
    public ResponseEntity<ApiResponse<List<ReviewWrongQuestionResponse>>> getTodayReviewQuestions(
            @PathVariable Long projectId) {

        try {
            logger.info("📚 获取今日复习错题: 项目ID={}", projectId);

            List<ReviewWrongQuestionResponse> questions = reviewService.getTodayReviewQuestions(projectId);

            return ResponseEntity.ok(ApiResponse.success(questions, "获取今日复习错题成功"));
        } catch (Exception e) {
            logger.error("❌ 获取今日复习错题失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取今日复习错题失败: " + e.getMessage()));
        }
    }

    /**
     * 标记复习完成
     */
    @PostMapping("/complete/{reviewId}")
    public ResponseEntity<ApiResponse<String>> markReviewCompleted(
            @PathVariable Long projectId,
            @PathVariable Long reviewId) {

        try {
            logger.info("✅ 标记复习完成: 项目ID={}, 复习记录ID={}", projectId, reviewId);

            reviewService.markReviewCompleted(reviewId, "user");

            return ResponseEntity.ok(ApiResponse.success("复习完成", "复习标记成功"));
        } catch (Exception e) {
            logger.error("❌ 标记复习完成失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("标记复习完成失败: " + e.getMessage()));
        }
    }

    /**
     * 获取复习统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getReviewStatistics(
            @PathVariable Long projectId) {

        try {
            logger.info("📊 获取复习统计信息: 项目ID={}", projectId);

            Long todayCount = reviewService.countTodayReviewQuestions(projectId);

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("todayReviewCount", todayCount);

            return ResponseEntity.ok(ApiResponse.success(statistics, "获取复习统计信息成功"));
        } catch (Exception e) {
            logger.error("❌ 获取复习统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取复习统计信息失败: " + e.getMessage()));
        }
    }
}
