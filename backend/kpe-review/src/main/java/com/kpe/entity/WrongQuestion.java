package com.kpe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 错题实体类
 */
@TableName("wrong_questions")
public class WrongQuestion {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("project_id")
    private Long projectId;

    @TableField("knowledge_point_id")
    private String knowledgePointId;

    @TableField("content_markdown")
    private String contentMarkdown;

    @TableField("wrong_reason_markdown")
    private String wrongReasonMarkdown;

    @TableField("correct_solution_markdown")
    private String correctSolutionMarkdown;

    @TableField("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @TableField("created_by")
    private String createdBy;

    // Constructors
    public WrongQuestion() {
    }

    public WrongQuestion(Long projectId, String knowledgePointId, String contentMarkdown,
                         String wrongReasonMarkdown, String correctSolutionMarkdown, String createdBy) {
        this.projectId = projectId;
        this.knowledgePointId = knowledgePointId;
        this.contentMarkdown = contentMarkdown;
        this.wrongReasonMarkdown = wrongReasonMarkdown;
        this.correctSolutionMarkdown = correctSolutionMarkdown;
        this.createdBy = createdBy;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }


    public String getContentMarkdown() {
        return contentMarkdown;
    }

    public void setContentMarkdown(String contentMarkdown) {
        this.contentMarkdown = contentMarkdown;
    }

    public String getWrongReasonMarkdown() {
        return wrongReasonMarkdown;
    }

    public void setWrongReasonMarkdown(String wrongReasonMarkdown) {
        this.wrongReasonMarkdown = wrongReasonMarkdown;
    }

    public String getCorrectSolutionMarkdown() {
        return correctSolutionMarkdown;
    }

    public void setCorrectSolutionMarkdown(String correctSolutionMarkdown) {
        this.correctSolutionMarkdown = correctSolutionMarkdown;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return "WrongQuestion{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", knowledgePointId='" + knowledgePointId + '\'' +
                ", contentMarkdown='" + contentMarkdown + '\'' +
                ", wrongReasonMarkdown='" + wrongReasonMarkdown + '\'' +
                ", correctSolutionMarkdown='" + correctSolutionMarkdown + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                '}';
    }
}
