package com.kpe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 错题复习记录实体类
 */
@TableName("wrong_question_reviews")
public class WrongQuestionReview {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("wrong_question_id")
    private Long wrongQuestionId;

    @TableField("project_id")
    private Long projectId;

    @TableField("review_round")
    private Integer reviewRound; // 第几轮复习

    @TableField("review_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reviewDate; // 复习日期

    @TableField("next_review_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextReviewDate; // 下次复习日期

    @TableField("is_completed")
    private Boolean isCompleted; // 是否已完成复习

    @TableField("completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt; // 完成复习时间

    @TableField("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @TableField("created_by")
    private String createdBy;

    // Constructors
    public WrongQuestionReview() {
    }

    public WrongQuestionReview(Long wrongQuestionId, Long projectId, Integer reviewRound,
                               LocalDate reviewDate, LocalDate nextReviewDate, String createdBy) {
        this.wrongQuestionId = wrongQuestionId;
        this.projectId = projectId;
        this.reviewRound = reviewRound;
        this.reviewDate = reviewDate;
        this.nextReviewDate = nextReviewDate;
        this.isCompleted = false;
        this.createdBy = createdBy;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWrongQuestionId() {
        return wrongQuestionId;
    }

    public void setWrongQuestionId(Long wrongQuestionId) {
        this.wrongQuestionId = wrongQuestionId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Integer getReviewRound() {
        return reviewRound;
    }

    public void setReviewRound(Integer reviewRound) {
        this.reviewRound = reviewRound;
    }

    public LocalDate getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(LocalDate reviewDate) {
        this.reviewDate = reviewDate;
    }

    public LocalDate getNextReviewDate() {
        return nextReviewDate;
    }

    public void setNextReviewDate(LocalDate nextReviewDate) {
        this.nextReviewDate = nextReviewDate;
    }

    public Boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return "WrongQuestionReview{" +
                "id=" + id +
                ", wrongQuestionId=" + wrongQuestionId +
                ", projectId=" + projectId +
                ", reviewRound=" + reviewRound +
                ", reviewDate=" + reviewDate +
                ", nextReviewDate=" + nextReviewDate +
                ", isCompleted=" + isCompleted +
                ", completedAt=" + completedAt +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                '}';
    }
}
