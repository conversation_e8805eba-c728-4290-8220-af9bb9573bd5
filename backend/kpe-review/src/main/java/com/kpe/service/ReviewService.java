package com.kpe.service;

import com.alibaba.fastjson2.JSON;
import com.kpe.dto.review.ReviewConfigurationRequest;
import com.kpe.dto.review.ReviewConfigurationResponse;
import com.kpe.dto.review.ReviewWrongQuestionResponse;
import com.kpe.entity.ReviewConfiguration;
import com.kpe.entity.WrongQuestionReview;
import com.kpe.mapper.ReviewConfigurationMapper;
import com.kpe.mapper.WrongQuestionReviewMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 复习服务类
 */
@Service
public class ReviewService {

    private static final Logger logger = LoggerFactory.getLogger(ReviewService.class);

    @Autowired
    private ReviewConfigurationMapper reviewConfigurationMapper;

    @Autowired
    private WrongQuestionReviewMapper wrongQuestionReviewMapper;

    /**
     * 获取项目的复习配置
     */
    public ReviewConfigurationResponse getReviewConfiguration(Long projectId) {
        logger.info("📋 获取项目复习配置: {}", projectId);

        ReviewConfiguration config = reviewConfigurationMapper.findByProjectId(projectId);
        if (config == null) {
            // 如果没有配置，创建默认配置
            config = createDefaultReviewConfiguration(projectId, "system");
        }

        return convertToResponse(config);
    }

    /**
     * 保存复习配置
     */
    @Transactional
    public ReviewConfigurationResponse saveReviewConfiguration(ReviewConfigurationRequest request, String userId) {
        logger.info("💾 保存复习配置: 项目{}, 配置{}", request.getProjectId(), request.getReviewIntervals());

        try {
            ReviewConfiguration existingConfig = reviewConfigurationMapper.findByProjectId(request.getProjectId());
            LocalDateTime now = LocalDateTime.now();

            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setReviewIntervals(JSON.toJSONString(request.getReviewIntervals()));
                existingConfig.setUpdatedAt(now);
                reviewConfigurationMapper.updateById(existingConfig);
                logger.info("✅ 复习配置更新成功: ID={}", existingConfig.getId());
                return convertToResponse(existingConfig);
            } else {
                // 创建新配置
                ReviewConfiguration newConfig = new ReviewConfiguration(
                        request.getProjectId(),
                        JSON.toJSONString(request.getReviewIntervals()),
                        userId
                );
                newConfig.setCreatedAt(now);
                newConfig.setUpdatedAt(now);
                reviewConfigurationMapper.insert(newConfig);
                logger.info("✅ 复习配置创建成功: ID={}", newConfig.getId());
                return convertToResponse(newConfig);
            }
        } catch (Exception e) {
            logger.error("❌ 保存复习配置失败: {}", e.getMessage());
            throw new RuntimeException("保存复习配置失败: " + e.getMessage());
        }
    }

    /**
     * 为新创建的错题初始化复习计划
     */
    @Transactional
    public void initializeReviewPlan(Long wrongQuestionId, Long projectId, String userId) {
        logger.info("🔄 初始化错题复习计划: 错题{}, 项目{}", wrongQuestionId, projectId);

        try {
            // 获取复习配置
            ReviewConfiguration config = reviewConfigurationMapper.findByProjectId(projectId);
            if (config == null) {
                config = createDefaultReviewConfiguration(projectId, userId);
            }

            List<Integer> intervals = JSON.parseArray(config.getReviewIntervals(), Integer.class);
            LocalDate createdDate = LocalDate.now();

            // 为每个复习间隔创建复习记录
            for (int i = 0; i < intervals.size(); i++) {
                LocalDate reviewDate = createdDate.plusDays(intervals.get(i));
                LocalDate nextReviewDate = null;

                // 计算下次复习日期
                if (i < intervals.size() - 1) {
                    nextReviewDate = createdDate.plusDays(intervals.get(i + 1));
                }

                WrongQuestionReview review = new WrongQuestionReview(
                        wrongQuestionId,
                        projectId,
                        i + 1, // 复习轮次从1开始
                        reviewDate,
                        nextReviewDate,
                        userId
                );

                LocalDateTime now = LocalDateTime.now();
                review.setCreatedAt(now);
                review.setUpdatedAt(now);

                wrongQuestionReviewMapper.insert(review);
                logger.info("📅 创建复习记录: 轮次{}, 复习日期{}", i + 1, reviewDate);
            }

            logger.info("✅ 错题复习计划初始化完成: 共{}轮复习", intervals.size());
        } catch (Exception e) {
            logger.error("❌ 初始化复习计划失败: {}", e.getMessage());
            throw new RuntimeException("初始化复习计划失败: " + e.getMessage());
        }
    }

    /**
     * 获取今日需要复习的错题
     */
    public List<ReviewWrongQuestionResponse> getTodayReviewQuestions(Long projectId) {
        logger.info("📚 获取今日复习错题: 项目{}", projectId);

        LocalDate today = LocalDate.now();
        List<ReviewWrongQuestionResponse> questions = wrongQuestionReviewMapper.findTodayReviewQuestions(projectId, today);

        logger.info("📊 今日需复习错题数量: {}", questions.size());
        return questions;
    }

    /**
     * 标记错题复习完成
     */
    @Transactional
    public void markReviewCompleted(Long reviewId, String userId) {
        logger.info("✅ 标记复习完成: 复习记录{}", reviewId);

        try {
            WrongQuestionReview review = wrongQuestionReviewMapper.selectById(reviewId);
            if (review == null) {
                throw new RuntimeException("复习记录不存在");
            }

            if (review.getIsCompleted()) {
                logger.warn("⚠️ 复习记录已经完成: {}", reviewId);
                return;
            }

            LocalDateTime now = LocalDateTime.now();
            review.setIsCompleted(true);
            review.setCompletedAt(now);
            review.setUpdatedAt(now);

            wrongQuestionReviewMapper.updateById(review);
            logger.info("✅ 复习完成标记成功: 复习记录{}", reviewId);
        } catch (Exception e) {
            logger.error("❌ 标记复习完成失败: {}", e.getMessage());
            throw new RuntimeException("标记复习完成失败: " + e.getMessage());
        }
    }

    /**
     * 统计今日待复习错题数量
     */
    public Long countTodayReviewQuestions(Long projectId) {
        logger.info("📊 统计今日待复习错题数量: 项目{}", projectId);

        LocalDate today = LocalDate.now();
        Long count = wrongQuestionReviewMapper.countTodayReviewQuestions(projectId, today);

        logger.info("📈 今日待复习错题数量: {}", count);
        return count;
    }

    /**
     * 重置复习配置为默认值
     */
    @Transactional
    public ReviewConfigurationResponse resetToDefaultConfiguration(Long projectId, String userId) {
        logger.info("🔄 重置复习配置为默认值: 项目{}", projectId);

        try {
            ReviewConfiguration existingConfig = reviewConfigurationMapper.findByProjectId(projectId);
            LocalDateTime now = LocalDateTime.now();

            if (existingConfig != null) {
                // 更新为默认配置
                existingConfig.setReviewIntervals("[1, 3, 7, 15, 30, 60]");
                existingConfig.setUpdatedAt(now);
                reviewConfigurationMapper.updateById(existingConfig);
                logger.info("✅ 复习配置重置成功: ID={}", existingConfig.getId());
                return convertToResponse(existingConfig);
            } else {
                // 创建默认配置
                ReviewConfiguration newConfig = createDefaultReviewConfiguration(projectId, userId);
                logger.info("✅ 默认复习配置创建成功: ID={}", newConfig.getId());
                return convertToResponse(newConfig);
            }
        } catch (Exception e) {
            logger.error("❌ 重置复习配置失败: {}", e.getMessage());
            throw new RuntimeException("重置复习配置失败: " + e.getMessage());
        }
    }

    /**
     * 验证复习间隔配置
     */
    public boolean validateReviewIntervals(List<Integer> intervals) {
        if (intervals == null || intervals.isEmpty()) {
            return false;
        }

        // 检查是否有负数或零
        for (Integer interval : intervals) {
            if (interval == null || interval <= 0) {
                return false;
            }
        }

        // 检查是否按升序排列
        for (int i = 1; i < intervals.size(); i++) {
            if (intervals.get(i) <= intervals.get(i - 1)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 创建默认复习配置
     */
    private ReviewConfiguration createDefaultReviewConfiguration(Long projectId, String userId) {
        logger.info("🔧 创建默认复习配置: 项目{}", projectId);

        ReviewConfiguration config = new ReviewConfiguration(
                projectId,
                "[1, 3, 7, 15, 30, 60]", // 默认复习间隔：1天、3天、7天、15天、30天、60天
                userId
        );

        LocalDateTime now = LocalDateTime.now();
        config.setCreatedAt(now);
        config.setUpdatedAt(now);

        reviewConfigurationMapper.insert(config);
        logger.info("✅ 默认复习配置创建成功: ID={}", config.getId());

        return config;
    }

    /**
     * 转换为响应DTO
     */
    private ReviewConfigurationResponse convertToResponse(ReviewConfiguration config) {
        ReviewConfigurationResponse response = new ReviewConfigurationResponse();
        BeanUtils.copyProperties(config, response);

        // 解析JSON数组
        List<Integer> intervals = JSON.parseArray(config.getReviewIntervals(), Integer.class);
        response.setReviewIntervals(intervals);

        return response;
    }
}
