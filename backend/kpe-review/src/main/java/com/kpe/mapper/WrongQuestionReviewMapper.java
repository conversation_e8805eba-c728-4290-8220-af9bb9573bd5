package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.dto.review.ReviewWrongQuestionResponse;
import com.kpe.entity.WrongQuestionReview;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 错题复习记录 Mapper接口
 */
@Mapper
public interface WrongQuestionReviewMapper extends BaseMapper<WrongQuestionReview> {

    /**
     * 根据错题ID查询最新的复习记录
     */
    @Select("SELECT * FROM wrong_question_reviews WHERE wrong_question_id = #{wrongQuestionId} ORDER BY review_round DESC LIMIT 1")
    WrongQuestionReview findLatestByWrongQuestionId(@Param("wrongQuestionId") Long wrongQuestionId);

    /**
     * 查询项目中今日需要复习的错题
     */
    @Select("SELECT " +
            "wq.id as wrong_question_id, " +
            "wq.project_id, " +
            "wq.knowledge_point_id, " +
            "wq.content_markdown, " +
            "wq.wrong_reason_markdown, " +
            "wq.correct_solution_markdown, " +
            "wq.created_at as wrong_question_created_at, " +
            "wqr.id as review_id, " +
            "wqr.review_round, " +
            "wqr.review_date, " +
            "wqr.next_review_date, " +
            "wqr.is_completed, " +
            "wqr.completed_at " +
            "FROM wrong_questions wq " +
            "INNER JOIN wrong_question_reviews wqr ON wq.id = wqr.wrong_question_id " +
            "WHERE wq.project_id = #{projectId} " +
            "AND wqr.review_date <= #{reviewDate} " +
            "AND wqr.is_completed = false " +
            "ORDER BY wqr.review_date ASC, wq.created_at ASC")
    List<ReviewWrongQuestionResponse> findTodayReviewQuestions(@Param("projectId") Long projectId,
                                                               @Param("reviewDate") LocalDate reviewDate);

    /**
     * 查询错题的所有复习记录
     */
    @Select("SELECT * FROM wrong_question_reviews WHERE wrong_question_id = #{wrongQuestionId} ORDER BY review_round ASC")
    List<WrongQuestionReview> findByWrongQuestionId(@Param("wrongQuestionId") Long wrongQuestionId);

    /**
     * 统计项目中待复习错题数量
     */
    @Select("SELECT COUNT(*) FROM wrong_question_reviews " +
            "WHERE project_id = #{projectId} " +
            "AND review_date <= #{reviewDate} " +
            "AND is_completed = false")
    Long countTodayReviewQuestions(@Param("projectId") Long projectId,
                                   @Param("reviewDate") LocalDate reviewDate);
}
