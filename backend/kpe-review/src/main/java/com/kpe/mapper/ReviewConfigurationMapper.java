package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.ReviewConfiguration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 复习配置 Mapper接口
 */
@Mapper
public interface ReviewConfigurationMapper extends BaseMapper<ReviewConfiguration> {

    /**
     * 根据项目ID查询复习配置
     */
    @Select("SELECT * FROM review_configurations WHERE project_id = #{projectId}")
    ReviewConfiguration findByProjectId(@Param("projectId") Long projectId);
}
