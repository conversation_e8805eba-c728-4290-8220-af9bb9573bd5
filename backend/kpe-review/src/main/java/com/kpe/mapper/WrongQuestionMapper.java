package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.WrongQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 错题 Mapper接口
 */
@Mapper
public interface WrongQuestionMapper extends BaseMapper<WrongQuestion> {

    /**
     * 根据项目ID查询错题列表
     */
    @Select("SELECT * FROM wrong_questions WHERE project_id = #{projectId} ORDER BY created_at DESC")
    List<WrongQuestion> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和知识点ID查询错题列表
     */
    @Select("SELECT * FROM wrong_questions WHERE project_id = #{projectId} AND knowledge_point_id = #{knowledgePointId} ORDER BY created_at DESC")
    List<WrongQuestion> findByProjectIdAndKnowledgePointId(@Param("projectId") Long projectId,
                                                           @Param("knowledgePointId") String knowledgePointId);

    /**
     * 根据项目ID统计错题数量
     */
    @Select("SELECT COUNT(*) FROM wrong_questions WHERE project_id = #{projectId}")
    Long countByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和知识点ID统计错题数量
     */
    @Select("SELECT COUNT(*) FROM wrong_questions WHERE project_id = #{projectId} AND knowledge_point_id = #{knowledgePointId}")
    Long countByProjectIdAndKnowledgePointId(@Param("projectId") Long projectId,
                                             @Param("knowledgePointId") String knowledgePointId);

    /**
     * 获取项目中所有涉及的知识点ID（去重）
     */
    @Select("SELECT DISTINCT knowledge_point_id " +
            "FROM wrong_questions WHERE project_id = #{projectId} " +
            "ORDER BY knowledge_point_id")
    List<String> findDistinctKnowledgePointIdsByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和关键词搜索错题
     */
    @Select("SELECT * FROM wrong_questions " +
            "WHERE project_id = #{projectId} " +
            "AND (content_markdown LIKE CONCAT('%', #{keyword}, '%') " +
            "     OR wrong_reason_markdown LIKE CONCAT('%', #{keyword}, '%') " +
            "     OR correct_solution_markdown LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY created_at DESC")
    List<WrongQuestion> searchByKeyword(@Param("projectId") Long projectId,
                                        @Param("keyword") String keyword);

    /**
     * 分页查询项目错题
     */
    @Select("SELECT * FROM wrong_questions WHERE project_id = #{projectId} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<WrongQuestion> findByProjectIdWithPagination(@Param("projectId") Long projectId,
                                                      @Param("limit") Integer limit,
                                                      @Param("offset") Integer offset);

    /**
     * 根据创建时间范围查询错题
     */
    @Select("SELECT * FROM wrong_questions " +
            "WHERE project_id = #{projectId} " +
            "AND created_at >= #{startDate} AND created_at <= #{endDate} " +
            "ORDER BY created_at DESC")
    List<WrongQuestion> findByProjectIdAndDateRange(@Param("projectId") Long projectId,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);
}
