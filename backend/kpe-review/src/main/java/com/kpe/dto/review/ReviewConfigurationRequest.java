package com.kpe.dto.review;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 复习配置请求DTO
 */
public class ReviewConfigurationRequest {

    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @NotNull(message = "复习间隔配置不能为空")
    @Size(min = 1, max = 20, message = "复习间隔配置数量应在1-20之间")
    private List<Integer> reviewIntervals; // 复习间隔天数数组

    // Constructors
    public ReviewConfigurationRequest() {
    }

    public ReviewConfigurationRequest(Long projectId, List<Integer> reviewIntervals) {
        this.projectId = projectId;
        this.reviewIntervals = reviewIntervals;
    }

    // Getters and Setters
    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public List<Integer> getReviewIntervals() {
        return reviewIntervals;
    }

    public void setReviewIntervals(List<Integer> reviewIntervals) {
        this.reviewIntervals = reviewIntervals;
    }

    @Override
    public String toString() {
        return "ReviewConfigurationRequest{" +
                "projectId=" + projectId +
                ", reviewIntervals=" + reviewIntervals +
                '}';
    }
}
