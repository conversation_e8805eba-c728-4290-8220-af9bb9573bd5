package com.kpe.dto.review;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 待复习错题响应DTO
 */
public class ReviewWrongQuestionResponse {

    // 错题信息
    private Long wrongQuestionId;
    private Long projectId;
    private String knowledgePointId;
    private String contentMarkdown;
    private String wrongReasonMarkdown;
    private String correctSolutionMarkdown;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wrongQuestionCreatedAt;

    // 复习记录信息
    private Long reviewId;
    private Integer reviewRound; // 第几轮复习

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reviewDate; // 复习日期

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextReviewDate; // 下次复习日期

    private Boolean isCompleted; // 是否已完成复习

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt; // 完成复习时间

    // Constructors
    public ReviewWrongQuestionResponse() {
    }

    // Getters and Setters
    public Long getWrongQuestionId() {
        return wrongQuestionId;
    }

    public void setWrongQuestionId(Long wrongQuestionId) {
        this.wrongQuestionId = wrongQuestionId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }

    public String getContentMarkdown() {
        return contentMarkdown;
    }

    public void setContentMarkdown(String contentMarkdown) {
        this.contentMarkdown = contentMarkdown;
    }

    public String getWrongReasonMarkdown() {
        return wrongReasonMarkdown;
    }

    public void setWrongReasonMarkdown(String wrongReasonMarkdown) {
        this.wrongReasonMarkdown = wrongReasonMarkdown;
    }

    public String getCorrectSolutionMarkdown() {
        return correctSolutionMarkdown;
    }

    public void setCorrectSolutionMarkdown(String correctSolutionMarkdown) {
        this.correctSolutionMarkdown = correctSolutionMarkdown;
    }

    public LocalDateTime getWrongQuestionCreatedAt() {
        return wrongQuestionCreatedAt;
    }

    public void setWrongQuestionCreatedAt(LocalDateTime wrongQuestionCreatedAt) {
        this.wrongQuestionCreatedAt = wrongQuestionCreatedAt;
    }

    public Long getReviewId() {
        return reviewId;
    }

    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }

    public Integer getReviewRound() {
        return reviewRound;
    }

    public void setReviewRound(Integer reviewRound) {
        this.reviewRound = reviewRound;
    }

    public LocalDate getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(LocalDate reviewDate) {
        this.reviewDate = reviewDate;
    }

    public LocalDate getNextReviewDate() {
        return nextReviewDate;
    }

    public void setNextReviewDate(LocalDate nextReviewDate) {
        this.nextReviewDate = nextReviewDate;
    }

    public Boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    @Override
    public String toString() {
        return "ReviewWrongQuestionResponse{" +
                "wrongQuestionId=" + wrongQuestionId +
                ", projectId=" + projectId +
                ", knowledgePointId='" + knowledgePointId + '\'' +
                ", reviewRound=" + reviewRound +
                ", reviewDate=" + reviewDate +
                ", nextReviewDate=" + nextReviewDate +
                ", isCompleted=" + isCompleted +
                '}';
    }
}
