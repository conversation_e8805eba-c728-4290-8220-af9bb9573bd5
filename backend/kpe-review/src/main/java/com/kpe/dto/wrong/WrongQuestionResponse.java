package com.kpe.dto.wrong;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 错题响应DTO
 */
public class WrongQuestionResponse {

    private Long id;
    private Long projectId;
    private String knowledgePointId;
    private String contentMarkdown;
    private String wrongReasonMarkdown;
    private String correctSolutionMarkdown;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    private String createdBy;

    // Constructors
    public WrongQuestionResponse() {
    }

    public WrongQuestionResponse(Long id, Long projectId, String knowledgePointId, String contentMarkdown,
                                 String wrongReasonMarkdown, String correctSolutionMarkdown, LocalDateTime createdAt,
                                 LocalDateTime updatedAt, String createdBy) {
        this.id = id;
        this.projectId = projectId;
        this.knowledgePointId = knowledgePointId;
        this.contentMarkdown = contentMarkdown;
        this.wrongReasonMarkdown = wrongReasonMarkdown;
        this.correctSolutionMarkdown = correctSolutionMarkdown;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.createdBy = createdBy;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }


    public String getContentMarkdown() {
        return contentMarkdown;
    }

    public void setContentMarkdown(String contentMarkdown) {
        this.contentMarkdown = contentMarkdown;
    }

    public String getWrongReasonMarkdown() {
        return wrongReasonMarkdown;
    }

    public void setWrongReasonMarkdown(String wrongReasonMarkdown) {
        this.wrongReasonMarkdown = wrongReasonMarkdown;
    }

    public String getCorrectSolutionMarkdown() {
        return correctSolutionMarkdown;
    }

    public void setCorrectSolutionMarkdown(String correctSolutionMarkdown) {
        this.correctSolutionMarkdown = correctSolutionMarkdown;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return "WrongQuestionResponse{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", knowledgePointId='" + knowledgePointId + '\'' +
                ", contentMarkdown='" + contentMarkdown + '\'' +
                ", wrongReasonMarkdown='" + wrongReasonMarkdown + '\'' +
                ", correctSolutionMarkdown='" + correctSolutionMarkdown + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                '}';
    }
}
