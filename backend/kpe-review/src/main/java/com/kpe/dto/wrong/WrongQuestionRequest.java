package com.kpe.dto.wrong;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 错题创建/更新请求DTO
 */
public class WrongQuestionRequest {

    private Long projectId;

    @NotBlank(message = "知识点ID不能为空")
    @Size(max = 200, message = "知识点ID长度不能超过200字符")
    private String knowledgePointId;

    @NotBlank(message = "错题内容不能为空")
    private String contentMarkdown;

    private String wrongReasonMarkdown;

    private String correctSolutionMarkdown;

    // Constructors
    public WrongQuestionRequest() {
    }

    public WrongQuestionRequest(Long projectId, String knowledgePointId, String contentMarkdown,
                                String wrongReasonMarkdown, String correctSolutionMarkdown) {
        this.projectId = projectId;
        this.knowledgePointId = knowledgePointId;
        this.contentMarkdown = contentMarkdown;
        this.wrongReasonMarkdown = wrongReasonMarkdown;
        this.correctSolutionMarkdown = correctSolutionMarkdown;
    }

    // Getters and Setters
    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }

    public String getContentMarkdown() {
        return contentMarkdown;
    }

    public void setContentMarkdown(String contentMarkdown) {
        this.contentMarkdown = contentMarkdown;
    }

    public String getWrongReasonMarkdown() {
        return wrongReasonMarkdown;
    }

    public void setWrongReasonMarkdown(String wrongReasonMarkdown) {
        this.wrongReasonMarkdown = wrongReasonMarkdown;
    }

    public String getCorrectSolutionMarkdown() {
        return correctSolutionMarkdown;
    }

    public void setCorrectSolutionMarkdown(String correctSolutionMarkdown) {
        this.correctSolutionMarkdown = correctSolutionMarkdown;
    }

    @Override
    public String toString() {
        return "WrongQuestionRequest{" +
                "projectId=" + projectId +
                ", knowledgePointId='" + knowledgePointId + '\'' +
                ", contentMarkdown='" + contentMarkdown + '\'' +
                ", wrongReasonMarkdown='" + wrongReasonMarkdown + '\'' +
                ", correctSolutionMarkdown='" + correctSolutionMarkdown + '\'' +
                '}';
    }
}
