package com.kpe.controller;

import com.kpe.dto.ApiResponse;
import com.kpe.dto.exam.ExamRecordRequest;
import com.kpe.dto.exam.ExamRecordStatistics;
import com.kpe.entity.ExamRecord;
import com.kpe.service.ExamRecordService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考试记录控制器
 */
@RestController
@RequestMapping("/api/projects/{projectId}/exams/{examId}/records")
@CrossOrigin(origins = "*")
public class ExamRecordController {

    private static final Logger logger = LoggerFactory.getLogger(ExamRecordController.class);

    @Autowired
    private ExamRecordService examRecordService;

    /**
     * 添加考试记录
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ExamRecord>> addExamRecord(
            @PathVariable Long projectId,
            @PathVariable Long examId,
            @Valid @RequestBody ExamRecordRequest request) {
        try {
            logger.info("📝 添加考试记录: 项目{}, 考卷{}", projectId, examId);

            ExamRecord examRecord = examRecordService.addExamRecord(examId, request, "user");
            return ResponseEntity.ok(ApiResponse.success(examRecord));
        } catch (Exception e) {
            logger.error("❌ 添加考试记录失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新考试记录
     */
    @PutMapping("/{recordId}")
    public ResponseEntity<ApiResponse<String>> updateExamRecord(
            @PathVariable Long projectId,
            @PathVariable Long examId,
            @PathVariable Long recordId,
            @Valid @RequestBody ExamRecordRequest request) {
        try {
            logger.info("📝 更新考试记录: 项目{}, 考卷{}, 记录{}", projectId, examId, recordId);

            boolean updated = examRecordService.updateExamRecord(recordId, request);
            if (updated) {
                return ResponseEntity.ok(ApiResponse.success("考试记录更新成功"));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("考试记录不存在"));
            }
        } catch (Exception e) {
            logger.error("❌ 更新考试记录失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除考试记录
     */
    @DeleteMapping("/{recordId}")
    public ResponseEntity<ApiResponse<String>> deleteExamRecord(
            @PathVariable Long projectId,
            @PathVariable Long examId,
            @PathVariable Long recordId) {
        try {
            logger.info("🗑️ 删除考试记录: 项目{}, 考卷{}, 记录{}", projectId, examId, recordId);

            boolean deleted = examRecordService.deleteExamRecord(recordId);
            if (deleted) {
                return ResponseEntity.ok(ApiResponse.success("考试记录删除成功"));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("考试记录不存在"));
            }
        } catch (Exception e) {
            logger.error("❌ 删除考试记录失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取考试记录详情
     */
    @GetMapping("/{recordId}")
    public ResponseEntity<ApiResponse<ExamRecord>> getExamRecord(
            @PathVariable Long projectId,
            @PathVariable Long examId,
            @PathVariable Long recordId) {
        try {
            logger.info("📋 获取考试记录详情: 项目{}, 考卷{}, 记录{}", projectId, examId, recordId);

            ExamRecord examRecord = examRecordService.getExamRecordById(recordId);
            if (examRecord == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("考试记录不存在"));
            }

            return ResponseEntity.ok(ApiResponse.success(examRecord));
        } catch (Exception e) {
            logger.error("❌ 获取考试记录详情失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取考卷的考试记录列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<ExamRecord>>> getExamRecords(
            @PathVariable Long projectId,
            @PathVariable Long examId) {
        try {
            logger.info("📋 获取考试记录列表: 项目{}, 考卷{}", projectId, examId);

            List<ExamRecord> examRecords = examRecordService.getExamRecordsByPaperId(examId);
            return ResponseEntity.ok(ApiResponse.success(examRecords));
        } catch (Exception e) {
            logger.error("❌ 获取考试记录列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取考试统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<ExamRecordStatistics>> getExamStatistics(
            @PathVariable Long projectId,
            @PathVariable Long examId) {
        try {
            logger.info("📊 获取考试统计信息: 项目{}, 考卷{}", projectId, examId);

            ExamRecordStatistics statistics = examRecordService.getExamStatistics(examId);
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            logger.error("❌ 获取考试统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
