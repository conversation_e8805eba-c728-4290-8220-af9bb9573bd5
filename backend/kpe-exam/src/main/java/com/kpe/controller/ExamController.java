package com.kpe.controller;

import com.kpe.dto.ApiResponse;
import com.kpe.dto.exam.ExamGenerationRequest;
import com.kpe.dto.exam.ExamPaperWithStatistics;
import com.kpe.entity.ExamPaper;
import com.kpe.service.ExamService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 考卷控制器
 */
@RestController
@RequestMapping("/api/projects/{projectId}/exams")
@CrossOrigin(origins = "*")
public class ExamController {

    private static final Logger logger = LoggerFactory.getLogger(ExamController.class);

    @Autowired
    private ExamService examService;

    /**
     * 生成考卷
     */
    @PostMapping("/generate")
    public CompletableFuture<ResponseEntity<? extends ApiResponse<? extends Object>>> generateExamPaper(
            @PathVariable Long projectId,
            @Valid @RequestBody ExamGenerationRequest request) {

        if (request.getTitle() == null || request.getTitle().trim().isEmpty()) {
            return CompletableFuture.completedFuture(
                    ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponse.error("考卷标题不能为空"))
            );
        }

        if (request.getKnowledgePoints() == null || request.getKnowledgePoints().isEmpty()) {
            return CompletableFuture.completedFuture(
                    ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponse.error("知识点列表不能为空"))
            );
        }

        logger.info("🎯 生成考卷: 项目{}, 标题: {}", projectId, request.getTitle());

        return examService.generateExamPaper(projectId, request, "user")
                .thenApply(result -> {
                    if (result.isSuccess()) {
                        return ResponseEntity.ok(ApiResponse.success(result));
                    } else {
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(ApiResponse.error(result.getErrorMessage()));
                    }
                })
                .exceptionally(throwable -> {
                    logger.error("❌ 生成考卷异常: {}", throwable.getMessage(), throwable);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(ApiResponse.error("生成考卷失败: " + throwable.getMessage()));
                });
    }

    /**
     * 获取项目的考卷列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<ExamPaper>>> getExamPapers(@PathVariable Long projectId) {
        try {
            logger.info("📋 获取项目考卷列表: {}", projectId);
            List<ExamPaper> examPapers = examService.getExamPapersByProjectId(projectId);
            return ResponseEntity.ok(ApiResponse.success(examPapers));
        } catch (Exception e) {
            logger.error("❌ 获取考卷列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取项目的考卷管理数据（包含统计信息）
     */
    @GetMapping("/management")
    public ResponseEntity<ApiResponse<List<ExamPaperWithStatistics>>> getExamPapersWithStatistics(@PathVariable Long projectId) {
        try {
            logger.info("📊 获取项目考卷管理数据: {}", projectId);
            List<ExamPaperWithStatistics> examPapersWithStats = examService.getExamPapersWithStatistics(projectId);
            return ResponseEntity.ok(ApiResponse.success(examPapersWithStats));
        } catch (Exception e) {
            logger.error("❌ 获取考卷管理数据失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取考卷详情
     */
    @GetMapping("/{examId}")
    public ResponseEntity<ApiResponse<ExamPaper>> getExamPaper(
            @PathVariable Long projectId,
            @PathVariable Long examId) {
        try {
            logger.info("📋 获取考卷详情: 项目{}, 考卷{}", projectId, examId);
            ExamPaper examPaper = examService.getExamPaperById(examId);

            if (examPaper == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("考卷不存在"));
            }

            // 验证考卷是否属于指定项目
            if (!examPaper.getProjectId().equals(projectId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("无权访问该考卷"));
            }

            return ResponseEntity.ok(ApiResponse.success(examPaper));
        } catch (Exception e) {
            logger.error("❌ 获取考卷详情失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新考卷状态
     */
    @PutMapping("/{examId}/status")
    public ResponseEntity<ApiResponse<String>> updateExamPaperStatus(
            @PathVariable Long projectId,
            @PathVariable Long examId,
            @RequestParam String status) {
        try {
            logger.info("📝 更新考卷状态: 项目{}, 考卷{}, 状态{}", projectId, examId, status);

            // 验证状态值
            if (!isValidStatus(status)) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("无效的状态值"));
            }

            // 验证考卷是否存在且属于指定项目
            ExamPaper examPaper = examService.getExamPaperById(examId);
            if (examPaper == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("考卷不存在"));
            }

            if (!examPaper.getProjectId().equals(projectId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("无权操作该考卷"));
            }

            boolean updated = examService.updateExamPaperStatus(examId, status);
            if (updated) {
                return ResponseEntity.ok(ApiResponse.success("考卷状态更新成功"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ApiResponse.error("考卷状态更新失败"));
            }
        } catch (Exception e) {
            logger.error("❌ 更新考卷状态失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除考卷
     */
    @DeleteMapping("/{examId}")
    public ResponseEntity<ApiResponse<String>> deleteExamPaper(
            @PathVariable Long projectId,
            @PathVariable Long examId) {
        try {
            logger.info("🗑️ 删除考卷: 项目{}, 考卷{}", projectId, examId);

            // 验证考卷是否存在且属于指定项目
            ExamPaper examPaper = examService.getExamPaperById(examId);
            if (examPaper == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("考卷不存在"));
            }

            if (!examPaper.getProjectId().equals(projectId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("无权删除该考卷"));
            }

            boolean deleted = examService.deleteExamPaper(examId);
            if (deleted) {
                return ResponseEntity.ok(ApiResponse.success("考卷删除成功"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ApiResponse.error("考卷删除失败"));
            }
        } catch (Exception e) {
            logger.error("❌ 删除考卷失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 验证状态值是否有效
     */
    private boolean isValidStatus(String status) {
        return status != null &&
                (status.equals("generated") || status.equals("reviewed") || status.equals("published"));
    }
}
