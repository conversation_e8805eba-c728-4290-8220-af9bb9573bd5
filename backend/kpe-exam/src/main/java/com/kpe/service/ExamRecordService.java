package com.kpe.service;

import com.kpe.dto.exam.ExamRecordRequest;
import com.kpe.dto.exam.ExamRecordStatistics;
import com.kpe.entity.ExamRecord;
import com.kpe.mapper.ExamRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 考试记录服务类
 */
@Service
public class ExamRecordService {

    private static final Logger logger = LoggerFactory.getLogger(ExamRecordService.class);

    @Autowired
    private ExamRecordMapper examRecordMapper;

    /**
     * 添加考试记录
     */
    @Transactional
    public ExamRecord addExamRecord(Long examPaperId, ExamRecordRequest request, String createdBy) {
        logger.info("📝 添加考试记录: 考卷ID={}, 日期={}, 成绩={}", examPaperId, request.getExamDate(), request.getScore());

        ExamRecord examRecord = new ExamRecord(
                examPaperId,
                request.getExamDate(),
                request.getScore(),
                request.getRemarks(),
                createdBy
        );

        LocalDateTime now = LocalDateTime.now();
        examRecord.setCreatedAt(now);
        examRecord.setUpdatedAt(now);

        examRecordMapper.insert(examRecord);
        logger.info("✅ 考试记录添加成功: ID={}", examRecord.getId());

        return examRecord;
    }

    /**
     * 更新考试记录
     */
    @Transactional
    public boolean updateExamRecord(Long recordId, ExamRecordRequest request) {
        logger.info("📝 更新考试记录: ID={}, 日期={}, 成绩={}", recordId, request.getExamDate(), request.getScore());

        ExamRecord examRecord = examRecordMapper.selectById(recordId);
        if (examRecord == null) {
            logger.warn("⚠️ 考试记录不存在: ID={}", recordId);
            return false;
        }

        examRecord.setExamDate(request.getExamDate());
        examRecord.setScore(request.getScore());
        examRecord.setRemarks(request.getRemarks());
        examRecord.setUpdatedAt(LocalDateTime.now());

        int updated = examRecordMapper.updateById(examRecord);
        boolean success = updated > 0;

        if (success) {
            logger.info("✅ 考试记录更新成功: ID={}", recordId);
        } else {
            logger.warn("⚠️ 考试记录更新失败: ID={}", recordId);
        }

        return success;
    }

    /**
     * 删除考试记录
     */
    @Transactional
    public boolean deleteExamRecord(Long recordId) {
        logger.info("🗑️ 删除考试记录: ID={}", recordId);

        int deleted = examRecordMapper.deleteById(recordId);
        boolean success = deleted > 0;

        if (success) {
            logger.info("✅ 考试记录删除成功: ID={}", recordId);
        } else {
            logger.warn("⚠️ 考试记录删除失败: ID={}", recordId);
        }

        return success;
    }

    /**
     * 根据ID获取考试记录
     */
    public ExamRecord getExamRecordById(Long recordId) {
        logger.info("📋 获取考试记录: ID={}", recordId);
        return examRecordMapper.selectById(recordId);
    }

    /**
     * 根据考卷ID获取考试记录列表
     */
    public List<ExamRecord> getExamRecordsByPaperId(Long examPaperId) {
        logger.info("📋 获取考卷考试记录列表: 考卷ID={}", examPaperId);
        return examRecordMapper.findByExamPaperId(examPaperId);
    }

    /**
     * 根据考卷ID获取考试统计信息
     */
    public ExamRecordStatistics getExamStatistics(Long examPaperId) {
        logger.info("📊 获取考试统计信息: 考卷ID={}", examPaperId);

        Long totalCount = examRecordMapper.countByExamPaperId(examPaperId);
        Long passedCount = examRecordMapper.countPassedByExamPaperId(examPaperId);
        BigDecimal maxScore = examRecordMapper.getMaxScoreByExamPaperId(examPaperId);
        BigDecimal avgScore = examRecordMapper.getAvgScoreByExamPaperId(examPaperId);

        // 处理空值
        if (totalCount == null) totalCount = 0L;
        if (passedCount == null) passedCount = 0L;
        if (maxScore == null) maxScore = BigDecimal.ZERO;
        if (avgScore == null) avgScore = BigDecimal.ZERO;

        return new ExamRecordStatistics(totalCount, passedCount, maxScore, avgScore);
    }

    /**
     * 根据考卷ID获取最近一次考试记录
     */
    public ExamRecord getLatestExamRecord(Long examPaperId) {
        logger.info("📋 获取最近考试记录: 考卷ID={}", examPaperId);
        return examRecordMapper.getLatestByExamPaperId(examPaperId);
    }

    /**
     * 根据项目ID获取所有考试记录
     */
    public List<ExamRecord> getExamRecordsByProjectId(Long projectId) {
        logger.info("📋 获取项目所有考试记录: 项目ID={}", projectId);
        return examRecordMapper.findByProjectId(projectId);
    }

    /**
     * 根据项目ID获取考试统计信息
     */
    public ExamRecordStatistics getProjectExamStatistics(Long projectId) {
        logger.info("📊 获取项目考试统计信息: 项目ID={}", projectId);

        Long totalCount = examRecordMapper.countByProjectId(projectId);
        Long passedCount = examRecordMapper.countPassedByProjectId(projectId);
        BigDecimal maxScore = examRecordMapper.getMaxScoreByProjectId(projectId);
        BigDecimal avgScore = examRecordMapper.getAvgScoreByProjectId(projectId);

        // 处理空值
        if (totalCount == null) totalCount = 0L;
        if (passedCount == null) passedCount = 0L;
        if (maxScore == null) maxScore = BigDecimal.ZERO;
        if (avgScore == null) avgScore = BigDecimal.ZERO;

        return new ExamRecordStatistics(totalCount, passedCount, maxScore, avgScore);
    }
}
