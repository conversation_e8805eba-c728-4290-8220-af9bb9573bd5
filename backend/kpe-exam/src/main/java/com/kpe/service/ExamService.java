package com.kpe.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kpe.common.BaseService;
import com.kpe.dto.coze.CozeChatResult;
import com.kpe.dto.exam.*;
import com.kpe.entity.AIGenerationLog;
import com.kpe.entity.ExamPaper;
import com.kpe.mapper.AIGenerationLogMapper;
import com.kpe.mapper.ExamPaperMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 考卷服务
 */
@Service
public class ExamService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(ExamService.class);

    @Autowired
    private ExamPaperMapper examPaperMapper;

    @Autowired
    private AIGenerationLogMapper aiGenerationLogMapper;

    @Autowired
    private CozeService cozeService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ExamRecordService examRecordService;

    @Value("${coze.api.exam-generation-bot-id}")
    private String examGenerationBotId;

    /**
     * 生成考卷
     */
    public CompletableFuture<ExamGenerationResult> generateExamPaper(Long projectId, ExamGenerationRequest request, String userId) {
        logger.info("🎯 开始生成考卷: 项目{}, 标题: {}", projectId, request.getTitle());

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建提示词
                String prompt = buildExamPrompt(request);
                logger.info("📝 考卷生成提示词: {}", prompt);

                // 记录开始生成
                AIGenerationLog log = new AIGenerationLog(
                        projectId, prompt, null, examGenerationBotId,
                        "exam_generation", "processing", null, userId
                );
                log.setCreatedAt(LocalDateTime.now());
                aiGenerationLogMapper.insert(log);

                // 调用扣子API生成考卷
                CozeChatResult chatResult = cozeService.chatAndWaitForResult(examGenerationBotId, userId, prompt, null, null).join();

                if ("completed".equals(chatResult.getStatus()) && chatResult.getMessage() != null) {
                    String examContent = extractExamContent(chatResult.getMessage());

                    if (examContent != null && !examContent.trim().isEmpty()) {

                        // 保存考卷到数据库
                        ExamPaper examPaper = saveExamPaper(projectId, request, examContent, userId);

                        // 更新生成日志 - 存储完整的CozeChatResult对象
                        String responseJson = objectMapper.writeValueAsString(chatResult);
                        log.setResponse(responseJson);
                        log.setStatus("completed");
                        aiGenerationLogMapper.updateWithJsonb(log);

                        logger.info("✅ 考卷生成成功: ID={}", examPaper.getId());
                        return ExamGenerationResult.success(examContent, examPaper.getId());
                    } else {
                        String errorMsg = "AI返回的考卷内容为空";
                        // 存储完整的CozeChatResult对象，即使内容为空
                        String responseJson = objectMapper.writeValueAsString(chatResult);
                        log.setResponse(responseJson);
                        log.setStatus("failed");
                        log.setErrorMessage(errorMsg);
                        aiGenerationLogMapper.updateWithJsonb(log);
                        return ExamGenerationResult.error(errorMsg);
                    }
                } else {
                    String errorMsg = "AI生成考卷失败，状态: " + chatResult.getStatus();
                    // 存储完整的CozeChatResult对象
                    String responseJson = objectMapper.writeValueAsString(chatResult);
                    log.setResponse(responseJson);
                    log.setStatus("failed");
                    log.setErrorMessage(errorMsg);
                    aiGenerationLogMapper.updateWithJsonb(log);
                    return ExamGenerationResult.error(errorMsg);
                }

            } catch (Exception e) {
                logger.error("❌ 考卷生成异常: {}", e.getMessage());
                return ExamGenerationResult.error("考卷生成异常: " + e.getMessage());
            }
        });
    }

    /**
     * 构建考卷生成提示词
     */
    private String buildExamPrompt(ExamGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("请根据以下要求生成一份考卷，返回Markdown格式：\n\n");

        prompt.append("**考卷信息：**\n");
        prompt.append("- 标题：").append(request.getTitle()).append("\n");
        if (request.getDescription() != null && !request.getDescription().trim().isEmpty()) {
            prompt.append("- 描述：").append(request.getDescription()).append("\n");
        }
        prompt.append("- 目标等级：").append(request.getTargetLevel()).append("\n");
        prompt.append("- 难度等级：").append(getDifficultyText(request.getDifficulty())).append("\n\n");

        prompt.append("**涉及知识点：**\n");
        for (KnowledgePointInfo knowledgePoint : request.getKnowledgePoints()) {
            prompt.append("- **").append(knowledgePoint.getFullPath()).append("**");
            if (knowledgePoint.getDescription() != null && !knowledgePoint.getDescription().trim().isEmpty()) {
                prompt.append("：").append(knowledgePoint.getDescription());
            }
            prompt.append("\n");
        }
        prompt.append("\n");

        if (request.getScoreFilters() != null && !request.getScoreFilters().isEmpty()) {
            prompt.append("**分数筛选：**\n");
            prompt.append("重点关注掌握程度为 ");
            prompt.append(request.getScoreFilters().stream()
                    .map(score -> score + "分")
                    .collect(Collectors.joining("、")));
            prompt.append(" 的知识点\n\n");
        }

        if (request.getAdditionalRequirements() != null && !request.getAdditionalRequirements().trim().isEmpty()) {
            prompt.append("**额外要求：**\n");
            prompt.append(request.getAdditionalRequirements()).append("\n\n");
        }

        prompt.append("**输出要求：**\n");
        prompt.append("1. 返回标准的Markdown格式考卷\n");
        prompt.append("2. 包含考卷标题、考试说明、题目和答题区域\n");
        prompt.append("3. 题目类型可包括：选择题、填空题、简答题、计算题等\n");
        prompt.append("4. 题目难度要符合指定的目标等级和难度要求\n");
        prompt.append("5. 题目内容要覆盖指定的知识点\n");
        prompt.append("6. 格式要适合打印，包含适当的空白区域供答题\n");
        prompt.append("7. 在考卷末尾提供参考答案（可选）\n\n");

        prompt.append("请直接返回Markdown格式的考卷内容，不要包含其他说明文字。");

        return prompt.toString();
    }

    /**
     * 获取难度等级文本
     */
    private String getDifficultyText(String difficulty) {
        return switch (difficulty.toLowerCase()) {
            case "easy" -> "简单";
            case "medium" -> "中等";
            case "hard" -> "困难";
            case "competition" -> "竞赛";
            case "middle_school_exam" -> "中考";
            case "high_school_exam" -> "高考";
            default -> difficulty;
        };
    }

    /**
     * 提取考卷内容
     */
    private String extractExamContent(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }

        logger.debug("🔍 开始提取考卷内容，原始响应长度: {}", response.length());
        String text = response.trim();
        //如果text由 ```markdown```包裹，要去除
        if (text.startsWith("```markdown") && text.endsWith("```")) {
            text = text.substring(11, text.length() - 3);
        }
        return text;
    }

    /**
     * 从消息列表JSON中提取AI回复的content字段
     */
    @SuppressWarnings("unused")
    private String extractContentFromMessageList(String jsonString) {
        try {
            logger.debug("🔍 尝试解析消息列表JSON...");

            // 尝试解析为消息列表数组
            if (jsonString.trim().startsWith("[")) {
                com.fasterxml.jackson.databind.JsonNode messagesArray = objectMapper.readTree(jsonString);
                if (messagesArray.isArray()) {
                    logger.debug("📋 检测到消息数组，消息数量: {}", messagesArray.size());

                    // 查找AI助手的回复消息（role="assistant"）
                    for (com.fasterxml.jackson.databind.JsonNode message : messagesArray) {
                        String role = message.path("role").asText();
                        if ("assistant".equals(role)) {
                            String content = message.path("content").asText();
                            if (content != null && !content.trim().isEmpty()) {
                                logger.debug("✅ 找到AI助手回复，内容长度: {}", content.length());
                                return content;
                            }
                        }
                    }
                    logger.debug("⚠️ 未找到AI助手的回复消息");
                }
            }

            // 如果不是数组格式，尝试解析为单个消息对象
            else if (jsonString.trim().startsWith("{")) {
                com.fasterxml.jackson.databind.JsonNode messageObj = objectMapper.readTree(jsonString);
                String role = messageObj.path("role").asText();
                if ("assistant".equals(role)) {
                    String content = messageObj.path("content").asText();
                    if (content != null && !content.trim().isEmpty()) {
                        logger.debug("✅ 找到单个AI助手回复，内容长度: {}", content.length());
                        return content;
                    }
                }
            }

        } catch (Exception e) {
            logger.debug("📝 不是有效的消息列表JSON格式: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 移除扣子API的技术字段前缀
     */
    @SuppressWarnings("unused")
    private String removeCozeApiPrefix(String content) {
        logger.debug("🧹 清理前的内容: {}", content.length() > 100 ? content.substring(0, 100) + "..." : content);

        // 移除类似 "[bot_id=751245805182473146, chat_id=751247819717952652, content=" 的前缀
        String pattern1 = "\\[bot_id=\\d+,\\s*chat_id=\\d+,\\s*content=";
        content = content.replaceFirst(pattern1, "");

        // 移除类似 "bot_id=xxx, chat_id=xxx, content=" 的前缀（没有方括号）
        String pattern2 = "bot_id=\\d+,\\s*chat_id=\\d+,\\s*content=";
        content = content.replaceFirst(pattern2, "");

        // 移除其他可能的技术字段前缀
        content = content.replaceFirst("^\\[.*?content=", "");
        content = content.replaceFirst("^content=", "");

        // 移除可能的结尾方括号
        if (content.endsWith("]")) {
            content = content.substring(0, content.length() - 1);
        }

        // 移除可能的引号包装
        if (content.startsWith("\"") && content.endsWith("\"")) {
            content = content.substring(1, content.length() - 1);
        }

        // 处理转义字符
        content = content.replace("\\n", "\n");
        content = content.replace("\\\"", "\"");
        content = content.replace("\\\\", "\\");

        String cleaned = content.trim();
        logger.debug("🧹 清理后的内容: {}", cleaned.length() > 100 ? cleaned.substring(0, 100) + "..." : cleaned);

        return cleaned;
    }

    /**
     * 提取markdown代码块中的内容
     */
    @SuppressWarnings("unused")
    private String extractMarkdownBlock(String content) {
        // 匹配 ```markdown ... ``` 格式
        String markdownPattern = "```markdown\\s*([\\s\\S]*?)```";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(markdownPattern, java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = pattern.matcher(content);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // 匹配 ``` ... ``` 格式（没有语言标识）
        String codePattern = "```\\s*([\\s\\S]*?)```";
        pattern = java.util.regex.Pattern.compile(codePattern);
        matcher = pattern.matcher(content);

        if (matcher.find()) {
            String blockContent = matcher.group(1).trim();
            // 检查是否是markdown内容
            if (isValidMarkdownContent(blockContent)) {
                return blockContent;
            }
        }

        return null;
    }

    /**
     * 检查是否是有效的markdown内容
     */
    private boolean isValidMarkdownContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含markdown标题
        if (content.contains("#") && (content.startsWith("#") || content.contains("\n#"))) {
            return true;
        }

        // 检查是否包含markdown列表
        if (content.contains("- ") || content.contains("* ") || content.matches(".*\\d+\\.\\s.*")) {
            return true;
        }

        // 检查是否包含markdown表格
        if (content.contains("|") && content.contains("---")) {
            return true;
        }

        // 检查是否包含考卷相关的关键词
        String[] examKeywords = {"考试", "题目", "选择题", "填空题", "简答题", "计算题", "答案", "分数", "时间"};
        for (String keyword : examKeywords) {
            if (content.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从混合内容中提取markdown部分
     */
    @SuppressWarnings("unused")
    private String extractMarkdownFromMixedContent(String content) {
        // 尝试找到markdown内容的开始位置
        String[] startMarkers = {"# ", "## ", "### ", "**", "- ", "* ", "1. "};

        int earliestStart = -1;
        for (String marker : startMarkers) {
            int index = content.indexOf(marker);
            if (index >= 0 && (earliestStart == -1 || index < earliestStart)) {
                earliestStart = index;
            }
        }

        if (earliestStart >= 0) {
            String extracted = content.substring(earliestStart);

            // 尝试找到内容的结束位置（移除可能的后缀）
            String[] endMarkers = {"```", "---END---", "结束", "完成"};
            for (String marker : endMarkers) {
                int endIndex = extracted.lastIndexOf(marker);
                if (endIndex > 0) {
                    extracted = extracted.substring(0, endIndex);
                    break;
                }
            }

            return extracted.trim();
        }

        return null;
    }


    /**
     * 保存考卷到数据库
     */
    @Transactional
    public ExamPaper saveExamPaper(Long projectId, ExamGenerationRequest request, String examContent, String userId) {
        try {
            String knowledgePointsJson = objectMapper.writeValueAsString(request.getKnowledgePoints());

            ExamPaper examPaper = new ExamPaper(
                    projectId,
                    request.getTitle(),
                    request.getDescription(),
                    request.getTargetLevel(),
                    request.getDifficulty(),
                    knowledgePointsJson,
                    examContent,
                    userId
            );

            LocalDateTime now = LocalDateTime.now();
            examPaper.setCreatedAt(now);
            examPaper.setUpdatedAt(now);

            examPaperMapper.insertWithJsonb(examPaper);
            logger.info("💾 考卷保存成功: ID={}", examPaper.getId());

            return examPaper;
        } catch (JsonProcessingException e) {
            logger.error("❌ 保存考卷失败: {}", e.getMessage());
            throw new RuntimeException("保存考卷失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目的考卷列表
     */
    public List<ExamPaper> getExamPapersByProjectId(Long projectId) {
        return executeWithExceptionHandling("获取项目考卷列表", () -> {
            validateNotNull(projectId, "项目ID");
            List<ExamPaper> examPapers = examPaperMapper.findByProjectId(projectId);
            logInfo("获取到 {} 个考卷", examPapers.size());
            return examPapers;
        });
    }

    /**
     * 根据ID获取考卷
     */
    public ExamPaper getExamPaperById(Long id) {
        return executeWithExceptionHandling("获取考卷详情", () -> {
            validateNotNull(id, "考卷ID");
            ExamPaper examPaper = examPaperMapper.selectById(id);
            if (examPaper != null) {
                logInfo("考卷查询成功: {}", id);
            } else {
                logWarning("考卷不存在: {}", id);
            }
            return examPaper;
        });
    }

    /**
     * 更新考卷状态
     */
    public boolean updateExamPaperStatus(Long id, String status) {
        logger.info("📝 更新考卷状态: ID={}, 状态={}", id, status);
        return examPaperMapper.updateStatus(id, status, LocalDateTime.now()) > 0;
    }

    /**
     * 删除考卷
     */
    public boolean deleteExamPaper(Long id) {
        logger.info("🗑️ 删除考卷: {}", id);
        return examPaperMapper.deleteById(id) > 0;
    }

    /**
     * 获取项目的考卷管理数据（包含统计信息）
     */
    public List<ExamPaperWithStatistics> getExamPapersWithStatistics(Long projectId) {
        logger.info("📊 获取项目考卷管理数据: {}", projectId);

        List<ExamPaper> examPapers = examPaperMapper.findByProjectId(projectId);
        List<ExamPaperWithStatistics> result = new ArrayList<>();

        for (ExamPaper examPaper : examPapers) {
            ExamRecordStatistics statistics = examRecordService.getExamStatistics(examPaper.getId());
            result.add(new ExamPaperWithStatistics(examPaper, statistics));
        }

        logger.info("✅ 获取到 {} 个考卷的管理数据", result.size());
        return result;
    }
}
