package com.kpe.dto.exam;

import java.util.List;

/**
 * 考卷分析响应DTO
 */
public class ExamAnalysisResponse {

    private List<KnowledgePointScore> scores;
    private Object updatedConfiguration;
    private String message;
    private boolean success;

    // 构造函数
    public ExamAnalysisResponse() {
    }

    public ExamAnalysisResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public ExamAnalysisResponse(List<KnowledgePointScore> scores, Object updatedConfiguration, String message) {
        this.scores = scores;
        this.updatedConfiguration = updatedConfiguration;
        this.message = message;
        this.success = true;
    }

    // Getters and Setters
    public List<KnowledgePointScore> getScores() {
        return scores;
    }

    public void setScores(List<KnowledgePointScore> scores) {
        this.scores = scores;
    }

    public Object getUpdatedConfiguration() {
        return updatedConfiguration;
    }

    public void setUpdatedConfiguration(Object updatedConfiguration) {
        this.updatedConfiguration = updatedConfiguration;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    @Override
    public String toString() {
        return "ExamAnalysisResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", scoresCount=" + (scores != null ? scores.size() : 0) +
                '}';
    }
}
