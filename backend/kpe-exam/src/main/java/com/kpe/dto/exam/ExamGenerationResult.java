package com.kpe.dto.exam;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 考卷生成结果DTO
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamGenerationResult {

    private boolean success;
    private String examContent; // Markdown格式的考卷内容
    private String chatId;
    private String conversationId;
    private String status;
    private String message;
    private String fullContent;
    private String errorMessage;
    private Long examPaperId; // 保存后的考卷ID

    // Constructors
    public ExamGenerationResult() {
    }

    private ExamGenerationResult(boolean success) {
        this.success = success;
    }

    // Static factory methods
    public static ExamGenerationResult success(String examContent) {
        ExamGenerationResult result = new ExamGenerationResult(true);
        result.examContent = examContent;
        result.message = "考卷生成成功";
        return result;
    }

    public static ExamGenerationResult success(String examContent, Long examPaperId) {
        ExamGenerationResult result = success(examContent);
        result.examPaperId = examPaperId;
        return result;
    }

    public static ExamGenerationResult error(String errorMessage) {
        ExamGenerationResult result = new ExamGenerationResult(false);
        result.errorMessage = errorMessage;
        result.message = "考卷生成失败";
        return result;
    }

    public static ExamGenerationResult processing(String chatId, String conversationId) {
        ExamGenerationResult result = new ExamGenerationResult(false);
        result.chatId = chatId;
        result.conversationId = conversationId;
        result.status = "processing";
        result.message = "考卷生成中...";
        return result;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getExamContent() {
        return examContent;
    }

    public void setExamContent(String examContent) {
        this.examContent = examContent;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getFullContent() {
        return fullContent;
    }

    public void setFullContent(String fullContent) {
        this.fullContent = fullContent;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getExamPaperId() {
        return examPaperId;
    }

    public void setExamPaperId(Long examPaperId) {
        this.examPaperId = examPaperId;
    }

    @Override
    public String toString() {
        return "ExamGenerationResult{" +
                "success=" + success +
                ", examContent='" + (examContent != null ? examContent.substring(0, Math.min(100, examContent.length())) + "..." : null) + '\'' +
                ", chatId='" + chatId + '\'' +
                ", conversationId='" + conversationId + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", examPaperId=" + examPaperId +
                '}';
    }
}
