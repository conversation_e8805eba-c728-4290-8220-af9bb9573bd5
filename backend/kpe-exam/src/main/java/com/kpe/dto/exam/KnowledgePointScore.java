package com.kpe.dto.exam;

/**
 * 知识点评分DTO
 */
public class KnowledgePointScore {

    private String uid;
    private String name;
    private String description;
    private String path; // 知识点路径（不包含根路径）
    private Integer score; // 1-5分
    private Integer originalScore; // 原始分数
    private Integer newScore; // 新分数（平均后）

    // 构造函数
    public KnowledgePointScore() {
    }

    public KnowledgePointScore(String uid, String name, String description) {
        this.uid = uid;
        this.name = name;
        this.description = description;
    }

    public KnowledgePointScore(String uid, String name, String description, String path) {
        this.uid = uid;
        this.name = name;
        this.description = description;
        this.path = path;
    }

    public KnowledgePointScore(String uid, String name, String description, Integer score) {
        this.uid = uid;
        this.name = name;
        this.description = description;
        this.score = score;
    }

    public KnowledgePointScore(String uid, String name, String description, String path, Integer score) {
        this.uid = uid;
        this.name = name;
        this.description = description;
        this.path = path;
        this.score = score;
    }

    // Getters and Setters
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getOriginalScore() {
        return originalScore;
    }

    public void setOriginalScore(Integer originalScore) {
        this.originalScore = originalScore;
    }

    public Integer getNewScore() {
        return newScore;
    }

    public void setNewScore(Integer newScore) {
        this.newScore = newScore;
    }

    @Override
    public String toString() {
        return "KnowledgePointScore{" +
                "uid='" + uid + '\'' +
                ", name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", score=" + score +
                ", originalScore=" + originalScore +
                ", newScore=" + newScore +
                '}';
    }
}
