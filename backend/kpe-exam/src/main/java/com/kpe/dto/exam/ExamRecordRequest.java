package com.kpe.dto.exam;

import com.alibaba.fastjson2.annotation.JSONField;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 考试记录请求DTO
 */
public class ExamRecordRequest {

    @NotNull(message = "考试日期不能为空")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate examDate;

    @NotNull(message = "考试成绩不能为空")
    @DecimalMin(value = "0", message = "考试成绩不能小于0分")
    @DecimalMax(value = "100", message = "考试成绩不能大于100分")
    private BigDecimal score;

    private String remarks;

    // Constructors
    public ExamRecordRequest() {
    }

    public ExamRecordRequest(LocalDate examDate, BigDecimal score, String remarks) {
        this.examDate = examDate;
        this.score = score;
        this.remarks = remarks;
    }

    // Getters and Setters
    public LocalDate getExamDate() {
        return examDate;
    }

    public void setExamDate(LocalDate examDate) {
        this.examDate = examDate;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "ExamRecordRequest{" +
                "examDate=" + examDate +
                ", score=" + score +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
