package com.kpe.dto.exam;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 考试记录统计DTO
 */
public class ExamRecordStatistics {

    private Long totalCount;        // 总考试次数
    private Long passedCount;       // 及格次数
    private BigDecimal maxScore;    // 最高分
    private BigDecimal avgScore;    // 平均分
    private BigDecimal passRate;    // 及格率

    // Constructors
    public ExamRecordStatistics() {
    }

    public ExamRecordStatistics(Long totalCount, Long passedCount, BigDecimal maxScore, BigDecimal avgScore) {
        this.totalCount = totalCount;
        this.passedCount = passedCount;
        this.maxScore = maxScore;
        this.avgScore = avgScore;

        // 计算及格率
        if (totalCount != null && totalCount > 0) {
            this.passRate = new BigDecimal(passedCount).divide(new BigDecimal(totalCount), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
        } else {
            this.passRate = BigDecimal.ZERO;
        }
    }

    // Getters and Setters
    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getPassedCount() {
        return passedCount;
    }

    public void setPassedCount(Long passedCount) {
        this.passedCount = passedCount;
    }

    public BigDecimal getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(BigDecimal maxScore) {
        this.maxScore = maxScore;
    }

    public BigDecimal getAvgScore() {
        return avgScore;
    }

    public void setAvgScore(BigDecimal avgScore) {
        this.avgScore = avgScore;
    }

    public BigDecimal getPassRate() {
        return passRate;
    }

    public void setPassRate(BigDecimal passRate) {
        this.passRate = passRate;
    }

    @Override
    public String toString() {
        return "ExamRecordStatistics{" +
                "totalCount=" + totalCount +
                ", passedCount=" + passedCount +
                ", maxScore=" + maxScore +
                ", avgScore=" + avgScore +
                ", passRate=" + passRate +
                '}';
    }
}
