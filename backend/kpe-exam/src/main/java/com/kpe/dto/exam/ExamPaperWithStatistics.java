package com.kpe.dto.exam;

import com.kpe.entity.ExamPaper;

import java.math.BigDecimal;

/**
 * 考卷及其统计信息DTO
 */
public class ExamPaperWithStatistics {

    private ExamPaper examPaper;        // 考卷信息
    private Long examCount;             // 考试次数
    private BigDecimal avgScore;        // 平均分
    private BigDecimal maxScore;        // 最高分
    private Long passedCount;           // 及格次数
    private BigDecimal passRate;        // 及格率

    // Constructors
    public ExamPaperWithStatistics() {
    }

    public ExamPaperWithStatistics(ExamPaper examPaper, ExamRecordStatistics statistics) {
        this.examPaper = examPaper;
        if (statistics != null) {
            this.examCount = statistics.getTotalCount();
            this.avgScore = statistics.getAvgScore();
            this.maxScore = statistics.getMaxScore();
            this.passedCount = statistics.getPassedCount();
            this.passRate = statistics.getPassRate();
        } else {
            this.examCount = 0L;
            this.avgScore = BigDecimal.ZERO;
            this.maxScore = BigDecimal.ZERO;
            this.passedCount = 0L;
            this.passRate = BigDecimal.ZERO;
        }
    }

    // Getters and Setters
    public ExamPaper getExamPaper() {
        return examPaper;
    }

    public void setExamPaper(ExamPaper examPaper) {
        this.examPaper = examPaper;
    }

    public Long getExamCount() {
        return examCount;
    }

    public void setExamCount(Long examCount) {
        this.examCount = examCount;
    }

    public BigDecimal getAvgScore() {
        return avgScore;
    }

    public void setAvgScore(BigDecimal avgScore) {
        this.avgScore = avgScore;
    }

    public BigDecimal getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(BigDecimal maxScore) {
        this.maxScore = maxScore;
    }

    public Long getPassedCount() {
        return passedCount;
    }

    public void setPassedCount(Long passedCount) {
        this.passedCount = passedCount;
    }

    public BigDecimal getPassRate() {
        return passRate;
    }

    public void setPassRate(BigDecimal passRate) {
        this.passRate = passRate;
    }

    @Override
    public String toString() {
        return "ExamPaperWithStatistics{" +
                "examPaper=" + examPaper +
                ", examCount=" + examCount +
                ", avgScore=" + avgScore +
                ", maxScore=" + maxScore +
                ", passedCount=" + passedCount +
                ", passRate=" + passRate +
                '}';
    }
}
