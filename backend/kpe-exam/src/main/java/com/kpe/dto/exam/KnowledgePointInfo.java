package com.kpe.dto.exam;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 知识点信息DTO
 */
public class KnowledgePointInfo {

    @NotBlank(message = "知识点名称不能为空")
    @Size(max = 200, message = "知识点名称长度不能超过200字符")
    private String name;

    @NotBlank(message = "知识点完整路径不能为空")
    @Size(max = 500, message = "知识点完整路径长度不能超过500字符")
    private String fullPath;

    @Size(max = 1000, message = "知识点描述长度不能超过1000字符")
    private String description;

    private Integer score; // 1-5分

    // Constructors
    public KnowledgePointInfo() {
    }

    public KnowledgePointInfo(String name, String fullPath) {
        this.name = name;
        this.fullPath = fullPath;
    }

    public KnowledgePointInfo(String name, String fullPath, String description) {
        this.name = name;
        this.fullPath = fullPath;
        this.description = description;
    }

    public KnowledgePointInfo(String name, String fullPath, String description, Integer score) {
        this.name = name;
        this.fullPath = fullPath;
        this.description = description;
        this.score = score;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    @Override
    public String toString() {
        return "KnowledgePointInfo{" +
                "name='" + name + '\'' +
                ", fullPath='" + fullPath + '\'' +
                ", description='" + description + '\'' +
                ", score=" + score +
                '}';
    }
}
