package com.kpe.dto.exam;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 考卷分析请求DTO
 */
public class ExamAnalysisRequest {

    @NotNull(message = "考卷图片不能为空")
    @Size(min = 1, message = "至少需要上传一张考卷图片")
    private List<String> examImages;

    // 构造函数
    public ExamAnalysisRequest() {
    }

    public ExamAnalysisRequest(List<String> examImages) {
        this.examImages = examImages;
    }

    // Getters and Setters
    public List<String> getExamImages() {
        return examImages;
    }

    public void setExamImages(List<String> examImages) {
        this.examImages = examImages;
    }

    @Override
    public String toString() {
        return "ExamAnalysisRequest{" +
                "examImages=" + (examImages != null ? examImages.size() + " images" : "null") +
                '}';
    }
}
