package com.kpe.dto.exam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 考卷生成请求DTO
 */
public class ExamGenerationRequest {

    @NotBlank(message = "考卷标题不能为空")
    @Size(max = 200, message = "考卷标题长度不能超过200字符")
    private String title;

    @Size(max = 500, message = "考卷描述长度不能超过500字符")
    private String description;

    @NotBlank(message = "目标等级不能为空")
    @Size(max = 100, message = "目标等级长度不能超过100字符")
    private String targetLevel;

    @NotBlank(message = "难度等级不能为空")
    private String difficulty; // easy, medium, hard

    @NotEmpty(message = "知识点列表不能为空")
    @Valid
    private List<KnowledgePointInfo> knowledgePoints;

    @Size(max = 1000, message = "额外要求长度不能超过1000字符")
    private String additionalRequirements;

    // 快捷选择相关字段
    private List<Integer> scoreFilters; // 1-5分的筛选条件

    // Constructors
    public ExamGenerationRequest() {
    }

    public ExamGenerationRequest(String title, String description, String targetLevel,
                                 String difficulty, List<KnowledgePointInfo> knowledgePoints) {
        this.title = title;
        this.description = description;
        this.targetLevel = targetLevel;
        this.difficulty = difficulty;
        this.knowledgePoints = knowledgePoints;
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTargetLevel() {
        return targetLevel;
    }

    public void setTargetLevel(String targetLevel) {
        this.targetLevel = targetLevel;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public List<KnowledgePointInfo> getKnowledgePoints() {
        return knowledgePoints;
    }

    public void setKnowledgePoints(List<KnowledgePointInfo> knowledgePoints) {
        this.knowledgePoints = knowledgePoints;
    }

    public String getAdditionalRequirements() {
        return additionalRequirements;
    }

    public void setAdditionalRequirements(String additionalRequirements) {
        this.additionalRequirements = additionalRequirements;
    }

    public List<Integer> getScoreFilters() {
        return scoreFilters;
    }

    public void setScoreFilters(List<Integer> scoreFilters) {
        this.scoreFilters = scoreFilters;
    }

    @Override
    public String toString() {
        return "ExamGenerationRequest{" +
                "title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", targetLevel='" + targetLevel + '\'' +
                ", difficulty='" + difficulty + '\'' +
                ", knowledgePoints=" + knowledgePoints +
                ", additionalRequirements='" + additionalRequirements + '\'' +
                ", scoreFilters=" + scoreFilters +
                '}';
    }
}
