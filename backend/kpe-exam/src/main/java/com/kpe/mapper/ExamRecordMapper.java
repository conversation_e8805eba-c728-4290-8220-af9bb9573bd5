package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.ExamRecord;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考试记录 Mapper 接口
 */
@Mapper
public interface ExamRecordMapper extends BaseMapper<ExamRecord> {

    /**
     * 根据考卷ID查找考试记录列表
     */
    @Select("SELECT * FROM exam_records WHERE exam_paper_id = #{examPaperId} ORDER BY exam_date DESC, created_at DESC")
    List<ExamRecord> findByExamPaperId(@Param("examPaperId") Long examPaperId);

    /**
     * 根据考卷ID统计考试次数
     */
    @Select("SELECT COUNT(*) FROM exam_records WHERE exam_paper_id = #{examPaperId}")
    Long countByExamPaperId(@Param("examPaperId") Long examPaperId);

    /**
     * 根据考卷ID获取最高分
     */
    @Select("SELECT MAX(score) FROM exam_records WHERE exam_paper_id = #{examPaperId}")
    BigDecimal getMaxScoreByExamPaperId(@Param("examPaperId") Long examPaperId);

    /**
     * 根据考卷ID获取平均分
     */
    @Select("SELECT AVG(score) FROM exam_records WHERE exam_paper_id = #{examPaperId}")
    BigDecimal getAvgScoreByExamPaperId(@Param("examPaperId") Long examPaperId);

    /**
     * 根据考卷ID获取最近一次考试记录
     */
    @Select("SELECT * FROM exam_records WHERE exam_paper_id = #{examPaperId} ORDER BY exam_date DESC, created_at DESC LIMIT 1")
    ExamRecord getLatestByExamPaperId(@Param("examPaperId") Long examPaperId);

    /**
     * 根据考卷ID和分数范围查找考试记录
     */
    @Select("SELECT * FROM exam_records WHERE exam_paper_id = #{examPaperId} AND score >= #{minScore} AND score <= #{maxScore} ORDER BY exam_date DESC")
    List<ExamRecord> findByExamPaperIdAndScoreRange(@Param("examPaperId") Long examPaperId,
                                                    @Param("minScore") BigDecimal minScore,
                                                    @Param("maxScore") BigDecimal maxScore);

    /**
     * 根据考卷ID统计及格次数（>=60分）
     */
    @Select("SELECT COUNT(*) FROM exam_records WHERE exam_paper_id = #{examPaperId} AND score >= 60")
    Long countPassedByExamPaperId(@Param("examPaperId") Long examPaperId);

    /**
     * 根据项目ID查找所有考试记录
     */
    @Select("SELECT er.* FROM exam_records er " +
            "INNER JOIN exam_papers ep ON er.exam_paper_id = ep.id " +
            "WHERE ep.project_id = #{projectId} " +
            "ORDER BY er.exam_date DESC, er.created_at DESC")
    List<ExamRecord> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID统计考试次数
     */
    @Select("SELECT COUNT(*) FROM exam_records er " +
            "INNER JOIN exam_papers ep ON er.exam_paper_id = ep.id " +
            "WHERE ep.project_id = #{projectId}")
    Long countByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID获取最高分
     */
    @Select("SELECT MAX(er.score) FROM exam_records er " +
            "INNER JOIN exam_papers ep ON er.exam_paper_id = ep.id " +
            "WHERE ep.project_id = #{projectId}")
    BigDecimal getMaxScoreByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID获取平均分
     */
    @Select("SELECT AVG(er.score) FROM exam_records er " +
            "INNER JOIN exam_papers ep ON er.exam_paper_id = ep.id " +
            "WHERE ep.project_id = #{projectId}")
    BigDecimal getAvgScoreByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID统计及格次数（>=60分）
     */
    @Select("SELECT COUNT(*) FROM exam_records er " +
            "INNER JOIN exam_papers ep ON er.exam_paper_id = ep.id " +
            "WHERE ep.project_id = #{projectId} AND er.score >= 60")
    Long countPassedByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID删除所有考试记录（通过考卷关联）
     */
    @Delete("DELETE FROM exam_records " +
            "WHERE exam_paper_id IN (" +
            "  SELECT id FROM exam_papers WHERE project_id = #{projectId}" +
            ")")
    int deleteByProjectId(@Param("projectId") Long projectId);
}
