package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.ExamPaper;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考卷数据访问层
 */
@Mapper
public interface ExamPaperMapper extends BaseMapper<ExamPaper> {

    /**
     * 根据项目ID获取考卷列表
     */
    @Select("SELECT * FROM exam_papers WHERE project_id = #{projectId} ORDER BY created_at DESC")
    List<ExamPaper> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和状态获取考卷列表
     */
    @Select("SELECT * FROM exam_papers WHERE project_id = #{projectId} AND status = #{status} ORDER BY created_at DESC")
    List<ExamPaper> findByProjectIdAndStatus(@Param("projectId") Long projectId, @Param("status") String status);

    /**
     * 插入考卷（处理jsonb类型）
     */
    @Insert("INSERT INTO exam_papers (project_id, title, description, target_level, difficulty, " +
            "knowledge_points, content_markdown, status, created_at, updated_at, created_by) " +
            "VALUES (#{projectId}, #{title}, #{description}, #{targetLevel}, #{difficulty}, " +
            "CASE WHEN #{knowledgePoints} IS NULL THEN NULL ELSE #{knowledgePoints}::jsonb END, " +
            "#{contentMarkdown}, #{status}, #{createdAt}, #{updatedAt}, #{createdBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertWithJsonb(ExamPaper examPaper);

    /**
     * 更新考卷内容
     */
    @Update("UPDATE exam_papers SET " +
            "title = #{title}, " +
            "description = #{description}, " +
            "target_level = #{targetLevel}, " +
            "difficulty = #{difficulty}, " +
            "knowledge_points = CASE WHEN #{knowledgePoints} IS NULL THEN NULL ELSE #{knowledgePoints}::jsonb END, " +
            "content_markdown = #{contentMarkdown}, " +
            "status = #{status}, " +
            "updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    int updateWithJsonb(ExamPaper examPaper);

    /**
     * 更新考卷状态
     */
    @Update("UPDATE exam_papers SET status = #{status}, updated_at = #{updatedAt} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("updatedAt") LocalDateTime updatedAt);

    /**
     * 根据ID删除考卷
     */
    @Delete("DELETE FROM exam_papers WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 根据项目ID删除所有考卷
     */
    @Delete("DELETE FROM exam_papers WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 统计项目的考卷数量
     */
    @Select("SELECT COUNT(*) FROM exam_papers WHERE project_id = #{projectId}")
    int countByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据状态统计项目的考卷数量
     */
    @Select("SELECT COUNT(*) FROM exam_papers WHERE project_id = #{projectId} AND status = #{status}")
    int countByProjectIdAndStatus(@Param("projectId") Long projectId, @Param("status") String status);
}
