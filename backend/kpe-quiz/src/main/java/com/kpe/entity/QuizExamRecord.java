package com.kpe.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 趣味考试记录实体类（全局通用，不关联项目）
 */
@TableName("quiz_exam_records")
public class QuizExamRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("exam_category")
    private String examCategory;

    @TableField("exam_subcategory")
    private String examSubcategory;

    @TableField("exam_title")
    private String examTitle;

    @TableField("total_questions")
    private Integer totalQuestions;

    @TableField("correct_answers")
    private Integer correctAnswers;

    @TableField("accuracy_rate")
    private BigDecimal accuracyRate;

    @TableField("exam_duration_seconds")
    private Integer examDurationSeconds;

    @TableField("exam_date")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime examDate;

    @TableField(value = "exam_details", jdbcType = org.apache.ibatis.type.JdbcType.OTHER, typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private String examDetails; // JSON格式的详细信息

    @TableField("created_at")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @TableField("created_by")
    private String createdBy;

    // Constructors
    public QuizExamRecord() {
    }

    public QuizExamRecord(String examCategory, String examSubcategory, String examTitle, 
                         Integer totalQuestions, Integer correctAnswers, BigDecimal accuracyRate,
                         Integer examDurationSeconds, String examDetails, String createdBy) {
        this.examCategory = examCategory;
        this.examSubcategory = examSubcategory;
        this.examTitle = examTitle;
        this.totalQuestions = totalQuestions;
        this.correctAnswers = correctAnswers;
        this.accuracyRate = accuracyRate;
        this.examDurationSeconds = examDurationSeconds;
        this.examDetails = examDetails;
        this.createdBy = createdBy;
        this.examDate = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getExamCategory() {
        return examCategory;
    }

    public void setExamCategory(String examCategory) {
        this.examCategory = examCategory;
    }

    public String getExamSubcategory() {
        return examSubcategory;
    }

    public void setExamSubcategory(String examSubcategory) {
        this.examSubcategory = examSubcategory;
    }

    public String getExamTitle() {
        return examTitle;
    }

    public void setExamTitle(String examTitle) {
        this.examTitle = examTitle;
    }

    public Integer getTotalQuestions() {
        return totalQuestions;
    }

    public void setTotalQuestions(Integer totalQuestions) {
        this.totalQuestions = totalQuestions;
    }

    public Integer getCorrectAnswers() {
        return correctAnswers;
    }

    public void setCorrectAnswers(Integer correctAnswers) {
        this.correctAnswers = correctAnswers;
    }

    public BigDecimal getAccuracyRate() {
        return accuracyRate;
    }

    public void setAccuracyRate(BigDecimal accuracyRate) {
        this.accuracyRate = accuracyRate;
    }

    public Integer getExamDurationSeconds() {
        return examDurationSeconds;
    }

    public void setExamDurationSeconds(Integer examDurationSeconds) {
        this.examDurationSeconds = examDurationSeconds;
    }

    public LocalDateTime getExamDate() {
        return examDate;
    }

    public void setExamDate(LocalDateTime examDate) {
        this.examDate = examDate;
    }

    public String getExamDetails() {
        return examDetails;
    }

    public void setExamDetails(String examDetails) {
        this.examDetails = examDetails;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return "QuizExamRecord{" +
                "id=" + id +
                ", examCategory='" + examCategory + '\'' +
                ", examSubcategory='" + examSubcategory + '\'' +
                ", examTitle='" + examTitle + '\'' +
                ", totalQuestions=" + totalQuestions +
                ", correctAnswers=" + correctAnswers +
                ", accuracyRate=" + accuracyRate +
                ", examDurationSeconds=" + examDurationSeconds +
                ", examDate=" + examDate +
                ", createdAt=" + createdAt +
                ", createdBy='" + createdBy + '\'' +
                '}';
    }
} 