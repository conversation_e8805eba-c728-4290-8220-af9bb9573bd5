package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kpe.dto.quiz.QuizExamStatistics;
import com.kpe.entity.QuizExamRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 趣味考试记录 Mapper 接口
 */
@Mapper
public interface QuizExamRecordMapper extends BaseMapper<QuizExamRecord> {

    /**
     * 自定义插入方法，正确处理JSONB字段
     */
    @Insert("INSERT INTO quiz_exam_records " +
            "(exam_category, exam_subcategory, exam_title, total_questions, correct_answers, " +
            "accuracy_rate, exam_duration_seconds, exam_date, exam_details, created_at, updated_at, created_by) " +
            "VALUES (#{examCategory}, #{examSubcategory}, #{examTitle}, #{totalQuestions}, #{correctAnswers}, " +
            "#{accuracyRate}, #{examDurationSeconds}, #{examDate}, #{examDetails}::jsonb, #{createdAt}, #{updatedAt}, #{createdBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertWithJsonb(QuizExamRecord record);

    /**
     * 获取所有考试记录（用于分页）
     */
    @Select("SELECT * FROM quiz_exam_records ORDER BY exam_date DESC, created_at DESC")
    IPage<QuizExamRecord> findAll(Page<QuizExamRecord> page);

    /**
     * 根据类别查询考试记录
     */
    @Select("SELECT * FROM quiz_exam_records WHERE exam_category = #{examCategory} ORDER BY exam_date DESC, created_at DESC")
    IPage<QuizExamRecord> findByCategory(Page<QuizExamRecord> page, @Param("examCategory") String examCategory);

    /**
     * 根据类别和子类别查询考试记录
     */
    @Select("SELECT * FROM quiz_exam_records WHERE exam_category = #{examCategory} AND exam_subcategory = #{examSubcategory} ORDER BY exam_date DESC, created_at DESC")
    IPage<QuizExamRecord> findByCategoryAndSubcategory(Page<QuizExamRecord> page, 
                                                       @Param("examCategory") String examCategory,
                                                       @Param("examSubcategory") String examSubcategory);

    /**
     * 根据考试类别统计信息
     */
    @Select("SELECT " +
            "exam_category, " +
            "exam_subcategory, " +
            "COUNT(*) as total_exams, " +
            "AVG(accuracy_rate) as avg_accuracy_rate, " +
            "MAX(accuracy_rate) as max_accuracy_rate, " +
            "MIN(accuracy_rate) as min_accuracy_rate, " +
            "AVG(exam_duration_seconds) as avg_duration_seconds, " +
            "SUM(total_questions) as total_questions, " +
            "SUM(correct_answers) as total_correct_answers " +
            "FROM quiz_exam_records " +
            "WHERE exam_category = #{examCategory} " +
            "GROUP BY exam_category, exam_subcategory")
    List<QuizExamStatistics> getStatisticsByCategory(@Param("examCategory") String examCategory);

    /**
     * 获取所有统计信息
     */
    @Select("SELECT " +
            "exam_category, " +
            "exam_subcategory, " +
            "COUNT(*) as total_exams, " +
            "AVG(accuracy_rate) as avg_accuracy_rate, " +
            "MAX(accuracy_rate) as max_accuracy_rate, " +
            "MIN(accuracy_rate) as min_accuracy_rate, " +
            "AVG(exam_duration_seconds) as avg_duration_seconds, " +
            "SUM(total_questions) as total_questions, " +
            "SUM(correct_answers) as total_correct_answers " +
            "FROM quiz_exam_records " +
            "GROUP BY exam_category, exam_subcategory")
    List<QuizExamStatistics> getAllStatistics();

    /**
     * 获取所有考试类别
     */
    @Select("SELECT DISTINCT exam_category FROM quiz_exam_records ORDER BY exam_category")
    List<String> getAllCategories();

    /**
     * 根据类别获取子类别
     */
    @Select("SELECT DISTINCT exam_subcategory FROM quiz_exam_records " +
            "WHERE exam_category = #{examCategory} AND exam_subcategory IS NOT NULL " +
            "ORDER BY exam_subcategory")
    List<String> getSubcategoriesByCategory(@Param("examCategory") String examCategory);

    /**
     * 获取用户最近的考试记录
     */
    @Select("SELECT * FROM quiz_exam_records " +
            "WHERE created_by = #{createdBy} " +
            "ORDER BY exam_date DESC, created_at DESC " +
            "LIMIT #{limit}")
    List<QuizExamRecord> getRecentRecordsByUser(@Param("createdBy") String createdBy,
                                               @Param("limit") Integer limit);

    /**
     * 获取用户在特定类别下的最高正确率
     */
    @Select("SELECT MAX(accuracy_rate) FROM quiz_exam_records " +
            "WHERE created_by = #{createdBy} AND exam_category = #{examCategory}")
    BigDecimal getMaxAccuracyRateByUserAndCategory(@Param("createdBy") String createdBy,
                                                  @Param("examCategory") String examCategory);

    /**
     * 统计用户总考试次数
     */
    @Select("SELECT COUNT(*) FROM quiz_exam_records WHERE created_by = #{createdBy}")
    Long countByUser(@Param("createdBy") String createdBy);

    /**
     * 获取用户平均正确率
     */
    @Select("SELECT AVG(accuracy_rate) FROM quiz_exam_records WHERE created_by = #{createdBy}")
    BigDecimal getAvgAccuracyRateByUser(@Param("createdBy") String createdBy);
} 