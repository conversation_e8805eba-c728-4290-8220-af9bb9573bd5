package com.kpe.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kpe.dto.quiz.QuizExamRecordRequest;
import com.kpe.dto.quiz.QuizExamStatistics;
import com.kpe.entity.QuizExamRecord;
import com.kpe.mapper.QuizExamRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 趣味考试记录服务类
 */
@Service
public class QuizExamRecordService {

    private static final Logger logger = LoggerFactory.getLogger(QuizExamRecordService.class);

    @Autowired
    private QuizExamRecordMapper quizExamRecordMapper;

    /**
     * 添加考试记录
     */
    @Transactional
    public QuizExamRecord addExamRecord(QuizExamRecordRequest request, String createdBy) {
        logger.info("📝 添加趣味考试记录: 类别={}, 子类别={}, 标题={}, 正确率={}%", 
                   request.getExamCategory(), request.getExamSubcategory(), 
                   request.getExamTitle(), request.getAccuracyRate());

        QuizExamRecord record = new QuizExamRecord(
                request.getExamCategory(),
                request.getExamSubcategory(),
                request.getExamTitle(),
                request.getTotalQuestions(),
                request.getCorrectAnswers(),
                request.getAccuracyRate(),
                request.getExamDurationSeconds(),
                request.getExamDetails(),
                createdBy
        );

        LocalDateTime now = LocalDateTime.now();
        record.setCreatedAt(now);
        record.setUpdatedAt(now);

        // 使用自定义插入方法处理JSONB字段
        quizExamRecordMapper.insertWithJsonb(record);
        logger.info("✅ 趣味考试记录添加成功: ID={}", record.getId());

        return record;
    }

    /**
     * 分页查询考试记录
     */
    public IPage<QuizExamRecord> getExamRecords(int page, int size, String examCategory, 
                                               String examSubcategory, String createdBy,
                                               BigDecimal minAccuracyRate, BigDecimal maxAccuracyRate) {
        logger.info("🔍 分页查询趣味考试记录: page={}, size={}, category={}, subcategory={}", 
                   page, size, examCategory, examSubcategory);

        Page<QuizExamRecord> pageParam = new Page<>(page, size);
        
        // 简化查询逻辑
        if (examCategory != null && !examCategory.trim().isEmpty()) {
            if (examSubcategory != null && !examSubcategory.trim().isEmpty()) {
                return quizExamRecordMapper.findByCategoryAndSubcategory(pageParam, examCategory, examSubcategory);
            } else {
                return quizExamRecordMapper.findByCategory(pageParam, examCategory);
            }
        } else {
            return quizExamRecordMapper.findAll(pageParam);
        }
    }

    /**
     * 获取考试统计信息
     */
    public List<QuizExamStatistics> getStatistics(String examCategory, String examSubcategory) {
        logger.info("📊 获取考试统计信息: category={}, subcategory={}", examCategory, examSubcategory);
        if (examCategory != null && !examCategory.trim().isEmpty()) {
            return quizExamRecordMapper.getStatisticsByCategory(examCategory);
        } else {
            return quizExamRecordMapper.getAllStatistics();
        }
    }

    /**
     * 获取所有考试类别
     */
    public List<String> getAllCategories() {
        logger.info("📋 获取所有考试类别");
        return quizExamRecordMapper.getAllCategories();
    }

    /**
     * 根据类别获取子类别
     */
    public List<String> getSubcategoriesByCategory(String examCategory) {
        logger.info("📋 获取考试子类别: category={}", examCategory);
        return quizExamRecordMapper.getSubcategoriesByCategory(examCategory);
    }

    /**
     * 获取用户最近的考试记录
     */
    public List<QuizExamRecord> getRecentRecordsByUser(String createdBy, Integer limit) {
        logger.info("🕐 获取用户最近考试记录: user={}, limit={}", createdBy, limit);
        if (limit == null || limit <= 0) {
            limit = 10; // 默认获取最近10条
        }
        return quizExamRecordMapper.getRecentRecordsByUser(createdBy, limit);
    }

    /**
     * 获取用户在特定类别下的最高正确率
     */
    public BigDecimal getMaxAccuracyRateByUserAndCategory(String createdBy, String examCategory) {
        logger.info("🏆 获取用户类别最高正确率: user={}, category={}", createdBy, examCategory);
        BigDecimal maxRate = quizExamRecordMapper.getMaxAccuracyRateByUserAndCategory(createdBy, examCategory);
        return maxRate != null ? maxRate : BigDecimal.ZERO;
    }

    /**
     * 统计用户总考试次数
     */
    public Long countByUser(String createdBy) {
        logger.info("📊 统计用户总考试次数: user={}", createdBy);
        return quizExamRecordMapper.countByUser(createdBy);
    }

    /**
     * 获取用户平均正确率
     */
    public BigDecimal getAvgAccuracyRateByUser(String createdBy) {
        logger.info("📊 获取用户平均正确率: user={}", createdBy);
        BigDecimal avgRate = quizExamRecordMapper.getAvgAccuracyRateByUser(createdBy);
        return avgRate != null ? avgRate : BigDecimal.ZERO;
    }

    /**
     * 根据ID获取考试记录
     */
    public QuizExamRecord getById(Long id) {
        logger.info("🔍 根据ID获取考试记录: id={}", id);
        return quizExamRecordMapper.selectById(id);
    }

    /**
     * 删除考试记录
     */
    @Transactional
    public boolean deleteById(Long id) {
        logger.info("🗑️ 删除考试记录: id={}", id);
        int result = quizExamRecordMapper.deleteById(id);
        if (result > 0) {
            logger.info("✅ 考试记录删除成功: id={}", id);
            return true;
        } else {
            logger.warn("❌ 考试记录删除失败: id={}", id);
            return false;
        }
    }

    /**
     * 更新考试记录
     */
    @Transactional
    public QuizExamRecord updateExamRecord(Long id, QuizExamRecordRequest request, String updatedBy) {
        logger.info("✏️ 更新考试记录: id={}, 类别={}, 标题={}", id, request.getExamCategory(), request.getExamTitle());

        QuizExamRecord record = quizExamRecordMapper.selectById(id);
        if (record == null) {
            logger.error("❌ 考试记录不存在: id={}", id);
            return null;
        }

        // 更新字段
        record.setExamCategory(request.getExamCategory());
        record.setExamSubcategory(request.getExamSubcategory());
        record.setExamTitle(request.getExamTitle());
        record.setTotalQuestions(request.getTotalQuestions());
        record.setCorrectAnswers(request.getCorrectAnswers());
        record.setAccuracyRate(request.getAccuracyRate());
        record.setExamDurationSeconds(request.getExamDurationSeconds());
        record.setExamDetails(request.getExamDetails());
        record.setUpdatedAt(LocalDateTime.now());

        quizExamRecordMapper.updateById(record);
        logger.info("✅ 考试记录更新成功: id={}", id);

        return record;
    }
} 