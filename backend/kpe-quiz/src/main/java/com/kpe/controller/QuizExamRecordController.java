package com.kpe.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kpe.common.BaseController;
import com.kpe.dto.ApiResponse;
import com.kpe.dto.quiz.QuizExamRecordRequest;
import com.kpe.dto.quiz.QuizExamStatistics;
import com.kpe.entity.QuizExamRecord;
import com.kpe.service.QuizExamRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 趣味考试记录控制器
 */
@RestController
@RequestMapping("/api/quiz-exam-records")
@CrossOrigin(origins = "*")
public class QuizExamRecordController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(QuizExamRecordController.class);

    @Autowired
    private QuizExamRecordService quizExamRecordService;

    /**
     * 添加考试记录
     */
    @PostMapping
    public ApiResponse<QuizExamRecord> addExamRecord(@RequestBody QuizExamRecordRequest request) {
        try {
            logger.info("📝 接收添加考试记录请求: {}", request);
            
            // 参数验证
            if (request.getExamCategory() == null || request.getExamCategory().trim().isEmpty()) {
                return ApiResponse.error("考试类别不能为空");
            }
            if (request.getExamTitle() == null || request.getExamTitle().trim().isEmpty()) {
                return ApiResponse.error("考试标题不能为空");
            }
            if (request.getTotalQuestions() == null || request.getTotalQuestions() <= 0) {
                return ApiResponse.error("总题数必须大于0");
            }
            if (request.getCorrectAnswers() == null || request.getCorrectAnswers() < 0) {
                return ApiResponse.error("正确题数不能为负数");
            }
            if (request.getCorrectAnswers() > request.getTotalQuestions()) {
                return ApiResponse.error("正确题数不能大于总题数");
            }
            if (request.getAccuracyRate() == null || request.getAccuracyRate().compareTo(BigDecimal.ZERO) < 0 
                || request.getAccuracyRate().compareTo(new BigDecimal("100")) > 0) {
                return ApiResponse.error("正确率必须在0-100之间");
            }

            String createdBy = "user"; // 当前用户，后续可以从认证系统获取
            QuizExamRecord record = quizExamRecordService.addExamRecord(request, createdBy);
            
            return ApiResponse.success(record, "考试记录添加成功");
        } catch (Exception e) {
            logger.error("❌ 添加考试记录失败", e);
            return ApiResponse.error("添加考试记录失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询考试记录
     */
    @GetMapping
    public ApiResponse<IPage<QuizExamRecord>> getExamRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String examCategory,
            @RequestParam(required = false) String examSubcategory,
            @RequestParam(required = false) String createdBy,
            @RequestParam(required = false) BigDecimal minAccuracyRate,
            @RequestParam(required = false) BigDecimal maxAccuracyRate) {
        try {
            logger.info("🔍 接收查询考试记录请求: page={}, size={}, category={}", page, size, examCategory);
            
            IPage<QuizExamRecord> records = quizExamRecordService.getExamRecords(
                    page, size, examCategory, examSubcategory, createdBy, minAccuracyRate, maxAccuracyRate);
            
            return ApiResponse.success(records, "查询成功");
        } catch (Exception e) {
            logger.error("❌ 查询考试记录失败", e);
            return ApiResponse.error("查询考试记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取考试统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<List<QuizExamStatistics>> getStatistics(
            @RequestParam(required = false) String examCategory,
            @RequestParam(required = false) String examSubcategory) {
        try {
            logger.info("📊 接收获取统计信息请求: category={}, subcategory={}", examCategory, examSubcategory);
            
            List<QuizExamStatistics> statistics = quizExamRecordService.getStatistics(examCategory, examSubcategory);
            
            return ApiResponse.success(statistics, "获取统计信息成功");
        } catch (Exception e) {
            logger.error("❌ 获取统计信息失败", e);
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有考试类别
     */
    @GetMapping("/categories")
    public ApiResponse<List<String>> getAllCategories() {
        try {
            logger.info("📋 接收获取所有类别请求");
            
            List<String> categories = quizExamRecordService.getAllCategories();
            
            return ApiResponse.success(categories, "获取类别成功");
        } catch (Exception e) {
            logger.error("❌ 获取类别失败", e);
            return ApiResponse.error("获取类别失败: " + e.getMessage());
        }
    }

    /**
     * 根据类别获取子类别
     */
    @GetMapping("/subcategories")
    public ApiResponse<List<String>> getSubcategoriesByCategory(@RequestParam String examCategory) {
        try {
            logger.info("📋 接收获取子类别请求: category={}", examCategory);
            
            List<String> subcategories = quizExamRecordService.getSubcategoriesByCategory(examCategory);
            
            return ApiResponse.success(subcategories, "获取子类别成功");
        } catch (Exception e) {
            logger.error("❌ 获取子类别失败", e);
            return ApiResponse.error("获取子类别失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户最近的考试记录
     */
    @GetMapping("/recent")
    public ApiResponse<List<QuizExamRecord>> getRecentRecords(
            @RequestParam(required = false) String createdBy,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            if (createdBy == null || createdBy.trim().isEmpty()) {
                createdBy = "user"; // 当前用户，后续可以从认证系统获取
            }
            
            logger.info("🕐 接收获取最近记录请求: user={}, limit={}", createdBy, limit);
            
            List<QuizExamRecord> records = quizExamRecordService.getRecentRecordsByUser(createdBy, limit);
            
            return ApiResponse.success(records, "获取最近记录成功");
        } catch (Exception e) {
            logger.error("❌ 获取最近记录失败", e);
            return ApiResponse.error("获取最近记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户个人统计信息
     */
    @GetMapping("/user-stats")
    public ApiResponse<Map<String, Object>> getUserStats(@RequestParam(required = false) String createdBy) {
        try {
            if (createdBy == null || createdBy.trim().isEmpty()) {
                createdBy = "user"; // 当前用户，后续可以从认证系统获取
            }
            
            logger.info("📊 接收获取用户统计请求: user={}", createdBy);
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalExams", quizExamRecordService.countByUser(createdBy));
            stats.put("avgAccuracyRate", quizExamRecordService.getAvgAccuracyRateByUser(createdBy));
            stats.put("recentRecords", quizExamRecordService.getRecentRecordsByUser(createdBy, 5));
            
            return ApiResponse.success(stats, "获取用户统计成功");
        } catch (Exception e) {
            logger.error("❌ 获取用户统计失败", e);
            return ApiResponse.error("获取用户统计失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取考试记录
     */
    @GetMapping("/{id}")
    public ApiResponse<QuizExamRecord> getById(@PathVariable Long id) {
        try {
            logger.info("🔍 接收根据ID获取记录请求: id={}", id);
            
            QuizExamRecord record = quizExamRecordService.getById(id);
            if (record == null) {
                return ApiResponse.error("考试记录不存在");
            }
            
            return ApiResponse.success(record, "获取记录成功");
        } catch (Exception e) {
            logger.error("❌ 根据ID获取记录失败", e);
            return ApiResponse.error("获取记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新考试记录
     */
    @PutMapping("/{id}")
    public ApiResponse<QuizExamRecord> updateExamRecord(@PathVariable Long id, 
                                                       @RequestBody QuizExamRecordRequest request) {
        try {
            logger.info("✏️ 接收更新考试记录请求: id={}, {}", id, request);
            
            String updatedBy = "user"; // 当前用户，后续可以从认证系统获取
            QuizExamRecord record = quizExamRecordService.updateExamRecord(id, request, updatedBy);
            
            if (record == null) {
                return ApiResponse.error("考试记录不存在");
            }
            
            return ApiResponse.success(record, "更新成功");
        } catch (Exception e) {
            logger.error("❌ 更新考试记录失败", e);
            return ApiResponse.error("更新考试记录失败: " + e.getMessage());
        }
    }

    /**
     * 删除考试记录
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteById(@PathVariable Long id) {
        try {
            logger.info("🗑️ 接收删除考试记录请求: id={}", id);
            
            boolean success = quizExamRecordService.deleteById(id);
            if (success) {
                return ApiResponse.success(null, "删除成功");
            } else {
                return ApiResponse.error("删除失败，记录不存在");
            }
        } catch (Exception e) {
            logger.error("❌ 删除考试记录失败", e);
            return ApiResponse.error("删除考试记录失败: " + e.getMessage());
        }
    }
} 