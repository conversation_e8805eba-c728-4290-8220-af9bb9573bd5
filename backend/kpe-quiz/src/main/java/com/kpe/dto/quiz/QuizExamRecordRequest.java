package com.kpe.dto.quiz;

import java.math.BigDecimal;

/**
 * 趣味考试记录请求DTO
 */
public class QuizExamRecordRequest {

    private String examCategory;
    private String examSubcategory;
    private String examTitle;
    private Integer totalQuestions;
    private Integer correctAnswers;
    private BigDecimal accuracyRate;
    private Integer examDurationSeconds;
    private String examDetails; // JSON格式的详细信息

    // Constructors
    public QuizExamRecordRequest() {
    }

    public QuizExamRecordRequest(String examCategory, String examSubcategory, String examTitle,
                                Integer totalQuestions, Integer correctAnswers, BigDecimal accuracyRate,
                                Integer examDurationSeconds, String examDetails) {
        this.examCategory = examCategory;
        this.examSubcategory = examSubcategory;
        this.examTitle = examTitle;
        this.totalQuestions = totalQuestions;
        this.correctAnswers = correctAnswers;
        this.accuracyRate = accuracyRate;
        this.examDurationSeconds = examDurationSeconds;
        this.examDetails = examDetails;
    }

    // Getters and Setters
    public String getExamCategory() {
        return examCategory;
    }

    public void setExamCategory(String examCategory) {
        this.examCategory = examCategory;
    }

    public String getExamSubcategory() {
        return examSubcategory;
    }

    public void setExamSubcategory(String examSubcategory) {
        this.examSubcategory = examSubcategory;
    }

    public String getExamTitle() {
        return examTitle;
    }

    public void setExamTitle(String examTitle) {
        this.examTitle = examTitle;
    }

    public Integer getTotalQuestions() {
        return totalQuestions;
    }

    public void setTotalQuestions(Integer totalQuestions) {
        this.totalQuestions = totalQuestions;
    }

    public Integer getCorrectAnswers() {
        return correctAnswers;
    }

    public void setCorrectAnswers(Integer correctAnswers) {
        this.correctAnswers = correctAnswers;
    }

    public BigDecimal getAccuracyRate() {
        return accuracyRate;
    }

    public void setAccuracyRate(BigDecimal accuracyRate) {
        this.accuracyRate = accuracyRate;
    }

    public Integer getExamDurationSeconds() {
        return examDurationSeconds;
    }

    public void setExamDurationSeconds(Integer examDurationSeconds) {
        this.examDurationSeconds = examDurationSeconds;
    }

    public String getExamDetails() {
        return examDetails;
    }

    public void setExamDetails(String examDetails) {
        this.examDetails = examDetails;
    }

    @Override
    public String toString() {
        return "QuizExamRecordRequest{" +
                "examCategory='" + examCategory + '\'' +
                ", examSubcategory='" + examSubcategory + '\'' +
                ", examTitle='" + examTitle + '\'' +
                ", totalQuestions=" + totalQuestions +
                ", correctAnswers=" + correctAnswers +
                ", accuracyRate=" + accuracyRate +
                ", examDurationSeconds=" + examDurationSeconds +
                ", examDetails='" + examDetails + '\'' +
                '}';
    }
} 