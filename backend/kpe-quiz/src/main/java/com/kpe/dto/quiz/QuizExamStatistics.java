package com.kpe.dto.quiz;

import java.math.BigDecimal;

/**
 * 趣味考试统计信息DTO
 */
public class QuizExamStatistics {

    private String examCategory;
    private String examSubcategory;
    private Long totalExams;
    private BigDecimal avgAccuracyRate;
    private BigDecimal maxAccuracyRate;
    private BigDecimal minAccuracyRate;
    private Integer avgDurationSeconds;
    private Long totalQuestions;
    private Long totalCorrectAnswers;

    // Constructors
    public QuizExamStatistics() {
    }

    public QuizExamStatistics(String examCategory, String examSubcategory, Long totalExams,
                             BigDecimal avgAccuracyRate, BigDecimal maxAccuracyRate, BigDecimal minAccuracyRate,
                             Integer avgDurationSeconds, Long totalQuestions, Long totalCorrectAnswers) {
        this.examCategory = examCategory;
        this.examSubcategory = examSubcategory;
        this.totalExams = totalExams;
        this.avgAccuracyRate = avgAccuracyRate;
        this.maxAccuracyRate = maxAccuracyRate;
        this.minAccuracyRate = minAccuracyRate;
        this.avgDurationSeconds = avgDurationSeconds;
        this.totalQuestions = totalQuestions;
        this.totalCorrectAnswers = totalCorrectAnswers;
    }

    // Getters and Setters
    public String getExamCategory() {
        return examCategory;
    }

    public void setExamCategory(String examCategory) {
        this.examCategory = examCategory;
    }

    public String getExamSubcategory() {
        return examSubcategory;
    }

    public void setExamSubcategory(String examSubcategory) {
        this.examSubcategory = examSubcategory;
    }

    public Long getTotalExams() {
        return totalExams;
    }

    public void setTotalExams(Long totalExams) {
        this.totalExams = totalExams;
    }

    public BigDecimal getAvgAccuracyRate() {
        return avgAccuracyRate;
    }

    public void setAvgAccuracyRate(BigDecimal avgAccuracyRate) {
        this.avgAccuracyRate = avgAccuracyRate;
    }

    public BigDecimal getMaxAccuracyRate() {
        return maxAccuracyRate;
    }

    public void setMaxAccuracyRate(BigDecimal maxAccuracyRate) {
        this.maxAccuracyRate = maxAccuracyRate;
    }

    public BigDecimal getMinAccuracyRate() {
        return minAccuracyRate;
    }

    public void setMinAccuracyRate(BigDecimal minAccuracyRate) {
        this.minAccuracyRate = minAccuracyRate;
    }

    public Integer getAvgDurationSeconds() {
        return avgDurationSeconds;
    }

    public void setAvgDurationSeconds(Integer avgDurationSeconds) {
        this.avgDurationSeconds = avgDurationSeconds;
    }

    public Long getTotalQuestions() {
        return totalQuestions;
    }

    public void setTotalQuestions(Long totalQuestions) {
        this.totalQuestions = totalQuestions;
    }

    public Long getTotalCorrectAnswers() {
        return totalCorrectAnswers;
    }

    public void setTotalCorrectAnswers(Long totalCorrectAnswers) {
        this.totalCorrectAnswers = totalCorrectAnswers;
    }

    @Override
    public String toString() {
        return "QuizExamStatistics{" +
                "examCategory='" + examCategory + '\'' +
                ", examSubcategory='" + examSubcategory + '\'' +
                ", totalExams=" + totalExams +
                ", avgAccuracyRate=" + avgAccuracyRate +
                ", maxAccuracyRate=" + maxAccuracyRate +
                ", minAccuracyRate=" + minAccuracyRate +
                ", avgDurationSeconds=" + avgDurationSeconds +
                ", totalQuestions=" + totalQuestions +
                ", totalCorrectAnswers=" + totalCorrectAnswers +
                '}';
    }
} 