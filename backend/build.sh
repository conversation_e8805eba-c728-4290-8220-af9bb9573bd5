#!/bin/bash

echo "🔨 构建KPE后端多模块项目..."

# 检查是否在backend目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 错误：请在backend目录下运行此脚本"
    exit 1
fi

echo "📦 安装所有模块到本地Maven仓库..."
echo "这将按照依赖顺序构建所有模块："
echo "  1. kpe-common (公共模块)"
echo "  2. kpe-ai (AI服务模块)"
echo "  3. kpe-project (项目管理模块)"
echo "  4. kpe-knowledge (知识点管理模块)"
echo "  5. kpe-exam (考试模块)"
echo "  6. kpe-flashcard (闪卡模块)"
echo "  7. kpe-quiz (测验模块)"
echo "  8. kpe-review (复习模块)"
echo "  9. kpe-video (视频教程模块)"
echo " 10. kpe-web (Web入口模块)"

mvn clean install -DskipTests

if [ $? -eq 0 ]; then
    echo "✅ 所有模块构建成功！"
    echo "💡 现在可以使用 ./start-backend.sh 启动应用"
else
    echo "❌ 构建失败，请检查错误信息"
    exit 1
fi 