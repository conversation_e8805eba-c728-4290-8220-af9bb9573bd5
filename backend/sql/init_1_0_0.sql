-- ==========================================
-- 序列创建
-- ==========================================
CREATE SEQUENCE IF NOT EXISTS exam_papers_id_seq;
CREATE SEQUENCE IF NOT EXISTS exam_records_id_seq;
CREATE SEQUENCE IF NOT EXISTS flashcard_study_records_id_seq;
CREATE SEQUENCE IF NOT EXISTS flashcards_id_seq;
CREATE SEQUENCE IF NOT EXISTS knowledge_configurations_id_seq;
CREATE SEQUENCE IF NOT EXISTS knowledge_mastery_id_seq;
CREATE SEQUENCE IF NOT EXISTS projects_id_seq;
CREATE SEQUENCE IF NOT EXISTS review_configurations_id_seq;
CREATE SEQUENCE IF NOT EXISTS video_tutorials_id_seq;
CREATE SEQUENCE IF NOT EXISTS wrong_question_reviews_id_seq;
CREATE SEQUENCE IF NOT EXISTS wrong_questions_id_seq;
CREATE SEQUENCE IF NOT EXISTS quiz_exam_records_id_seq;

-- ==========================================
-- 表创建
-- ==========================================

/******************************************/
/*   TableName = projects   */
/******************************************/
CREATE TABLE "projects"
(
    "id" integer NOT NULL DEFAULT nextval('projects_id_seq'::regclass) ,
    "name" varchar(255) NOT NULL ,
    "description" text ,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) ,
    "status" varchar(20) DEFAULT 'active' ,
    CONSTRAINT "pk_public_projects" PRIMARY KEY ("id")
);

/******************************************/
/*   TableName = exam_papers   */
/******************************************/
CREATE TABLE "exam_papers"
(
    "id" bigint NOT NULL DEFAULT nextval('exam_papers_id_seq'::regclass) ,
    "project_id" bigint NOT NULL ,
    "title" varchar(200) NOT NULL ,
    "description" text ,
    "target_level" varchar(100) NOT NULL ,
    "difficulty" varchar(20) NOT NULL ,
    "knowledge_points" jsonb NOT NULL ,
    "content_markdown" text NOT NULL ,
    "status" varchar(20) NOT NULL DEFAULT 'generated' ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_exam_papers" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "exam_papers"."id" IS '考卷ID';
COMMENT ON COLUMN "exam_papers"."project_id" IS '项目ID';
COMMENT ON COLUMN "exam_papers"."title" IS '考卷标题';
COMMENT ON COLUMN "exam_papers"."description" IS '考卷描述';
COMMENT ON COLUMN "exam_papers"."target_level" IS '目标等级（如：小学三年级上、大学高数等）';
COMMENT ON COLUMN "exam_papers"."difficulty" IS '难度等级（easy/medium/hard/competition/middle_school_exam/high_school_exam）';
COMMENT ON COLUMN "exam_papers"."knowledge_points" IS '涉及的知识点列表（JSON格式）';
COMMENT ON COLUMN "exam_papers"."content_markdown" IS '考卷内容（Markdown格式）';
COMMENT ON COLUMN "exam_papers"."status" IS '考卷状态（generated/reviewed/published）';
COMMENT ON COLUMN "exam_papers"."created_at" IS '创建时间';
COMMENT ON COLUMN "exam_papers"."updated_at" IS '更新时间';
COMMENT ON COLUMN "exam_papers"."created_by" IS '创建者';
COMMENT ON TABLE "exam_papers" IS '考卷表';

/******************************************/
/*   TableName = exam_records   */
/******************************************/
CREATE TABLE "exam_records"
(
    "id" bigint NOT NULL DEFAULT nextval('exam_records_id_seq'::regclass) ,
    "exam_paper_id" bigint NOT NULL ,
    "exam_date" date NOT NULL ,
    "score" numeric(5,2) NOT NULL ,
    "remarks" text ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_exam_records" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "exam_records"."id" IS '考试记录ID';
COMMENT ON COLUMN "exam_records"."exam_paper_id" IS '考卷ID';
COMMENT ON COLUMN "exam_records"."exam_date" IS '考试日期';
COMMENT ON COLUMN "exam_records"."score" IS '得分（0-100分）';
COMMENT ON COLUMN "exam_records"."remarks" IS '备注';
COMMENT ON COLUMN "exam_records"."created_at" IS '创建时间';
COMMENT ON COLUMN "exam_records"."updated_at" IS '更新时间';
COMMENT ON COLUMN "exam_records"."created_by" IS '创建者';
COMMENT ON TABLE "exam_records" IS '考试记录表（个人考试记录）';

/******************************************/
/*   TableName = flashcards   */
/******************************************/
CREATE TABLE "flashcards"
(
    "id" bigint NOT NULL DEFAULT nextval('flashcards_id_seq'::regclass) ,
    "project_id" bigint NOT NULL ,
    "knowledge_point_id" varchar(200) NOT NULL ,
    "title" varchar(200) NOT NULL ,
    "front_content" text NOT NULL ,
    "back_content" text ,
    "difficulty" integer NOT NULL DEFAULT 1 ,
    "status" varchar(20) NOT NULL DEFAULT 'active' ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    "total_study_count" integer NOT NULL DEFAULT 0 ,
    "accuracy_rate" numeric(5,2) NOT NULL DEFAULT 0 ,
    CONSTRAINT "pk_public_flashcards" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "flashcards"."id" IS '卡片ID';
COMMENT ON COLUMN "flashcards"."project_id" IS '项目ID';
COMMENT ON COLUMN "flashcards"."knowledge_point_id" IS '知识点ID（uid）';
COMMENT ON COLUMN "flashcards"."title" IS '卡片标题';
COMMENT ON COLUMN "flashcards"."front_content" IS '卡片正面内容（富文本HTML格式）';
COMMENT ON COLUMN "flashcards"."back_content" IS '卡片背面内容（富文本HTML格式，可选）';
COMMENT ON COLUMN "flashcards"."difficulty" IS '难度等级（1-5：1=绿色/简单，2=蓝色/容易，3=紫色/中等，4=金色/困难，5=红色/极难）';
COMMENT ON COLUMN "flashcards"."status" IS '卡片状态（active/archived/deleted）';
COMMENT ON COLUMN "flashcards"."created_at" IS '创建时间';
COMMENT ON COLUMN "flashcards"."updated_at" IS '更新时间';
COMMENT ON COLUMN "flashcards"."created_by" IS '创建者';
COMMENT ON COLUMN "flashcards"."total_study_count" IS '总做题次数';
COMMENT ON COLUMN "flashcards"."accuracy_rate" IS '当前正确率百分比';
COMMENT ON TABLE "flashcards" IS '卡片表';

/******************************************/
/*   TableName = flashcard_study_records   */
/******************************************/
CREATE TABLE "flashcard_study_records"
(
    "id" bigint NOT NULL DEFAULT nextval('flashcard_study_records_id_seq'::regclass) ,
    "flashcard_id" bigint NOT NULL ,
    "project_id" bigint NOT NULL ,
    "study_date" date NOT NULL ,
    "is_correct" boolean NOT NULL ,
    "response_time_seconds" integer ,
    "difficulty_rating" integer ,
    "next_review_date" date ,
    "review_count" integer NOT NULL DEFAULT 1 ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_flashcard_study_records" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "flashcard_study_records"."id" IS '学习记录ID';
COMMENT ON COLUMN "flashcard_study_records"."flashcard_id" IS '卡片ID';
COMMENT ON COLUMN "flashcard_study_records"."project_id" IS '项目ID';
COMMENT ON COLUMN "flashcard_study_records"."study_date" IS '学习日期';
COMMENT ON COLUMN "flashcard_study_records"."is_correct" IS '是否答对';
COMMENT ON COLUMN "flashcard_study_records"."response_time_seconds" IS '响应时间（秒）';
COMMENT ON COLUMN "flashcard_study_records"."difficulty_rating" IS '主观难度评级（1-5）';
COMMENT ON COLUMN "flashcard_study_records"."next_review_date" IS '下次复习日期';
COMMENT ON COLUMN "flashcard_study_records"."review_count" IS '复习次数';
COMMENT ON COLUMN "flashcard_study_records"."created_at" IS '创建时间';
COMMENT ON COLUMN "flashcard_study_records"."created_by" IS '创建者';
COMMENT ON TABLE "flashcard_study_records" IS '卡片学习记录表';

/******************************************/
/*   TableName = knowledge_configurations   */
/******************************************/
CREATE TABLE "knowledge_configurations"
(
    "id" integer NOT NULL DEFAULT nextval('knowledge_configurations_id_seq'::regclass) ,
    "project_id" integer NOT NULL ,
    "configuration" jsonb NOT NULL ,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) ,
    "notes" text ,
    CONSTRAINT "pk_public_knowledge_configurations" PRIMARY KEY ("id"),
    CONSTRAINT "uk_knowledge_configurations_project_id" UNIQUE ("project_id")
);

/******************************************/
/*   TableName = knowledge_mastery   */
/******************************************/
CREATE TABLE "knowledge_mastery"
(
    "id" bigint NOT NULL DEFAULT nextval('knowledge_mastery_id_seq'::regclass) ,
    "project_id" bigint NOT NULL ,
    "knowledge_point_id" varchar(100) NOT NULL ,
    "mastery_score" integer NOT NULL ,
    "analysis_title" varchar(200) NOT NULL ,
    "analysis_description" text ,
    "total_images" integer DEFAULT 0 ,
    "confidence_level" numeric(3,2) DEFAULT 0.0 ,
    "evidence_text" text ,
    "previous_score" integer ,
    "score_change" integer DEFAULT 0 ,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_knowledge_mastery" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "knowledge_mastery"."mastery_score" IS '掌握程度评分，1-5分，5表示完全掌握，1表示没有掌握';
COMMENT ON COLUMN "knowledge_mastery"."confidence_level" IS 'AI分析的置信度，0.0-1.0';
COMMENT ON COLUMN "knowledge_mastery"."score_change" IS '与上次分析相比的分数变化';
COMMENT ON TABLE "knowledge_mastery" IS '知识点掌握度表';

/******************************************/
/*   TableName = review_configurations   */
/******************************************/
CREATE TABLE "review_configurations"
(
    "id" bigint NOT NULL DEFAULT nextval('review_configurations_id_seq'::regclass) ,
    "project_id" bigint NOT NULL ,
    "review_intervals" text NOT NULL DEFAULT '[1, 3, 7, 15, 30, 60]'::text ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_review_configurations" PRIMARY KEY ("id") ,
    CONSTRAINT "uk_review_configurations_project_id" UNIQUE ("project_id")
);
COMMENT ON COLUMN "review_configurations"."id" IS '配置ID';
COMMENT ON COLUMN "review_configurations"."project_id" IS '项目ID';
COMMENT ON COLUMN "review_configurations"."review_intervals" IS '复习间隔天数配置（JSON数组格式）';
COMMENT ON COLUMN "review_configurations"."created_at" IS '创建时间';
COMMENT ON COLUMN "review_configurations"."updated_at" IS '更新时间';
COMMENT ON COLUMN "review_configurations"."created_by" IS '创建者';
COMMENT ON TABLE "review_configurations" IS '复习配置表';

/******************************************/
/*   TableName = video_tutorials   */
/******************************************/
CREATE TABLE "video_tutorials"
(
    "id" bigint NOT NULL DEFAULT nextval('video_tutorials_id_seq'::regclass) ,
    "project_id" bigint NOT NULL ,
    "knowledge_point_id" varchar(200) NOT NULL ,
    "tutorial_content" text NOT NULL ,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) ,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP ,
    CONSTRAINT "pk_public_video_tutorials" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "video_tutorials"."id" IS '主键ID';
COMMENT ON COLUMN "video_tutorials"."project_id" IS '项目ID';
COMMENT ON COLUMN "video_tutorials"."knowledge_point_id" IS '知识点id';
COMMENT ON COLUMN "video_tutorials"."tutorial_content" IS '教程内容(Markdown格式)';
COMMENT ON COLUMN "video_tutorials"."created_at" IS '创建时间';
COMMENT ON COLUMN "video_tutorials"."created_by" IS '创建者';
COMMENT ON COLUMN "video_tutorials"."updated_at" IS '更新时间';
COMMENT ON TABLE "video_tutorials" IS '视频教程表';

/******************************************/
/*   TableName = wrong_questions   */
/******************************************/
CREATE TABLE "wrong_questions"
(
    "id" bigint NOT NULL DEFAULT nextval('wrong_questions_id_seq'::regclass) ,
    "project_id" bigint NOT NULL ,
    "knowledge_point_id" varchar(200) NOT NULL ,
    "content_markdown" text NOT NULL ,
    "wrong_reason_markdown" text ,
    "correct_solution_markdown" text ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_wrong_questions" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "wrong_questions"."id" IS '错题ID';
COMMENT ON COLUMN "wrong_questions"."project_id" IS '项目ID';
COMMENT ON COLUMN "wrong_questions"."knowledge_point_id" IS '知识点ID（uid）';
COMMENT ON COLUMN "wrong_questions"."content_markdown" IS '错题内容（Markdown格式）';
COMMENT ON COLUMN "wrong_questions"."wrong_reason_markdown" IS '做错原因说明（Markdown格式）';
COMMENT ON COLUMN "wrong_questions"."correct_solution_markdown" IS '正确解题说明（Markdown格式）';
COMMENT ON COLUMN "wrong_questions"."created_at" IS '录入时间';
COMMENT ON COLUMN "wrong_questions"."updated_at" IS '更新时间';
COMMENT ON COLUMN "wrong_questions"."created_by" IS '创建者';
COMMENT ON TABLE "wrong_questions" IS '错题表';

/******************************************/
/*   TableName = wrong_question_reviews   */
/******************************************/
CREATE TABLE "wrong_question_reviews"
(
    "id" bigint NOT NULL DEFAULT nextval('wrong_question_reviews_id_seq'::regclass) ,
    "wrong_question_id" bigint NOT NULL ,
    "project_id" bigint NOT NULL ,
    "review_round" integer NOT NULL DEFAULT 1 ,
    "review_date" date NOT NULL ,
    "next_review_date" date ,
    "is_completed" boolean NOT NULL DEFAULT false ,
    "completed_at" timestamp with time zone ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_wrong_question_reviews" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "wrong_question_reviews"."id" IS '复习记录ID';
COMMENT ON COLUMN "wrong_question_reviews"."wrong_question_id" IS '错题ID';
COMMENT ON COLUMN "wrong_question_reviews"."project_id" IS '项目ID';
COMMENT ON COLUMN "wrong_question_reviews"."review_round" IS '复习轮次';
COMMENT ON COLUMN "wrong_question_reviews"."review_date" IS '复习日期';
COMMENT ON COLUMN "wrong_question_reviews"."next_review_date" IS '下次复习日期';
COMMENT ON COLUMN "wrong_question_reviews"."is_completed" IS '是否已完成复习';
COMMENT ON COLUMN "wrong_question_reviews"."completed_at" IS '完成复习时间';
COMMENT ON COLUMN "wrong_question_reviews"."created_at" IS '创建时间';
COMMENT ON COLUMN "wrong_question_reviews"."updated_at" IS '更新时间';
COMMENT ON COLUMN "wrong_question_reviews"."created_by" IS '创建者';
COMMENT ON TABLE "wrong_question_reviews" IS '错题复习记录表';

/******************************************/
/*   TableName = quiz_exam_records   */
/******************************************/
CREATE TABLE "quiz_exam_records"
(
    "id" bigint NOT NULL DEFAULT nextval('quiz_exam_records_id_seq'::regclass) ,
    "exam_category" varchar(50) NOT NULL ,
    "exam_subcategory" varchar(100) NULL ,
    "exam_title" varchar(200) NOT NULL ,
    "total_questions" integer NOT NULL ,
    "correct_answers" integer NOT NULL ,
    "accuracy_rate" numeric(5,2) NOT NULL ,
    "exam_duration_seconds" integer NULL ,
    "exam_date" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "exam_details" jsonb NULL ,
    "created_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "updated_at" timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    "created_by" varchar(100) NOT NULL ,
    CONSTRAINT "pk_public_quiz_exam_records" PRIMARY KEY ("id")
);
COMMENT ON COLUMN "quiz_exam_records"."id" IS '考试记录ID';
COMMENT ON COLUMN "quiz_exam_records"."exam_category" IS '考试大类（如：卡片考试、数学口算、趣味考试、拼音考试等）';
COMMENT ON COLUMN "quiz_exam_records"."exam_subcategory" IS '考试小类（如：加法、减法、乘法、除法等）';
COMMENT ON COLUMN "quiz_exam_records"."exam_title" IS '考试标题';
COMMENT ON COLUMN "quiz_exam_records"."total_questions" IS '总题数';
COMMENT ON COLUMN "quiz_exam_records"."correct_answers" IS '正确题数';
COMMENT ON COLUMN "quiz_exam_records"."accuracy_rate" IS '正确率百分比';
COMMENT ON COLUMN "quiz_exam_records"."exam_duration_seconds" IS '考试用时（秒）';
COMMENT ON COLUMN "quiz_exam_records"."exam_date" IS '考试日期时间';
COMMENT ON COLUMN "quiz_exam_records"."exam_details" IS '考试详细信息（JSON格式，包含题目、答案等）';
COMMENT ON COLUMN "quiz_exam_records"."created_at" IS '创建时间';
COMMENT ON COLUMN "quiz_exam_records"."updated_at" IS '更新时间';
COMMENT ON COLUMN "quiz_exam_records"."created_by" IS '创建者';
COMMENT ON TABLE "quiz_exam_records" IS '趣味考试记录表（全局通用，不关联项目）';

-- ==========================================
-- 索引创建
-- ==========================================

-- projects 索引
CREATE INDEX "idx_projects_created_at"
    ON "projects" USING btree ( "created_at" );
CREATE INDEX "idx_projects_status"
    ON "projects" USING btree ( "status" );

-- exam_papers 索引
CREATE INDEX "idx_exam_papers_created_at"
    ON "exam_papers" USING btree ( "created_at" );
CREATE INDEX "idx_exam_papers_project_id"
    ON "exam_papers" USING btree ( "project_id" );
CREATE INDEX "idx_exam_papers_project_status"
    ON "exam_papers" USING btree ( "project_id" ,"status" );
CREATE INDEX "idx_exam_papers_status"
    ON "exam_papers" USING btree ( "status" );

-- exam_records 索引
CREATE INDEX "idx_exam_records_exam_date"
    ON "exam_records" USING btree ( "exam_date" );
CREATE INDEX "idx_exam_records_exam_paper_id"
    ON "exam_records" USING btree ( "exam_paper_id" );
CREATE INDEX "idx_exam_records_score"
    ON "exam_records" USING btree ( "score" );

-- flashcards 索引
CREATE INDEX "idx_flashcards_created_at"
    ON "flashcards" USING btree ( "created_at" );
CREATE INDEX "idx_flashcards_difficulty"
    ON "flashcards" USING btree ( "difficulty" );
CREATE INDEX "idx_flashcards_has_back_content"
    ON "flashcards" USING btree ( "project_id" ,(back_content IS NOT NULL) );
CREATE INDEX "idx_flashcards_knowledge_point_id"
    ON "flashcards" USING btree ( "knowledge_point_id" );
CREATE INDEX "idx_flashcards_project_accuracy"
    ON "flashcards" USING btree ( "project_id" ,"accuracy_rate" );
CREATE INDEX "idx_flashcards_project_difficulty"
    ON "flashcards" USING btree ( "project_id" ,"difficulty" );
CREATE INDEX "idx_flashcards_project_id"
    ON "flashcards" USING btree ( "project_id" );
CREATE INDEX "idx_flashcards_project_knowledge"
    ON "flashcards" USING btree ( "project_id" ,"knowledge_point_id" );
CREATE INDEX "idx_flashcards_project_status"
    ON "flashcards" USING btree ( "project_id" ,"status" );
CREATE INDEX "idx_flashcards_project_study_count"
    ON "flashcards" USING btree ( "project_id" ,"total_study_count" );
CREATE INDEX "idx_flashcards_status"
    ON "flashcards" USING btree ( "status" );

-- flashcard_study_records 索引
CREATE INDEX "idx_flashcard_study_records_flashcard_id"
    ON "flashcard_study_records" USING btree ( "flashcard_id" );
CREATE INDEX "idx_flashcard_study_records_is_correct"
    ON "flashcard_study_records" USING btree ( "is_correct" );
CREATE INDEX "idx_flashcard_study_records_next_review_date"
    ON "flashcard_study_records" USING btree ( "next_review_date" );
CREATE INDEX "idx_flashcard_study_records_project_id"
    ON "flashcard_study_records" USING btree ( "project_id" );
CREATE INDEX "idx_flashcard_study_records_study_date"
    ON "flashcard_study_records" USING btree ( "study_date" );

-- knowledge_configurations 索引
CREATE INDEX "idx_knowledge_configurations_project_id"
    ON "knowledge_configurations" USING btree ( "project_id" );

-- knowledge_mastery 索引
CREATE INDEX "idx_knowledge_mastery_created_at"
    ON "knowledge_mastery" USING btree ( "created_at" );
CREATE INDEX "idx_knowledge_mastery_project_id"
    ON "knowledge_mastery" USING btree ( "project_id" );
CREATE INDEX "idx_knowledge_mastery_score"
    ON "knowledge_mastery" USING btree ( "mastery_score" );

-- review_configurations 索引
CREATE INDEX "idx_review_configurations_project_id"
    ON "review_configurations" USING btree ( "project_id" );

-- video_tutorials 索引
CREATE INDEX "idx_video_tutorials_created_at"
    ON "video_tutorials" USING btree ( "created_at" );
CREATE INDEX "idx_video_tutorials_project_id"
    ON "video_tutorials" USING btree ( "project_id" );

-- wrong_questions 索引
CREATE INDEX "idx_wrong_questions_created_at"
    ON "wrong_questions" USING btree ( "created_at" );
CREATE INDEX "idx_wrong_questions_knowledge_point_id"
    ON "wrong_questions" USING btree ( "knowledge_point_id" );
CREATE INDEX "idx_wrong_questions_project_id"
    ON "wrong_questions" USING btree ( "project_id" );
CREATE INDEX "idx_wrong_questions_project_knowledge"
    ON "wrong_questions" USING btree ( "project_id" ,"knowledge_point_id" );

-- wrong_question_reviews 索引
CREATE INDEX "idx_wrong_question_reviews_is_completed"
    ON "wrong_question_reviews" USING btree ( "is_completed" );
CREATE INDEX "idx_wrong_question_reviews_next_review_date"
    ON "wrong_question_reviews" USING btree ( "next_review_date" );
CREATE INDEX "idx_wrong_question_reviews_project_id"
    ON "wrong_question_reviews" USING btree ( "project_id" );
CREATE INDEX "idx_wrong_question_reviews_review_date"
    ON "wrong_question_reviews" USING btree ( "review_date" );
CREATE INDEX "idx_wrong_question_reviews_wrong_question_id"
    ON "wrong_question_reviews" USING btree ( "wrong_question_id" );

-- quiz_exam_records 索引
CREATE INDEX "idx_quiz_exam_records_category"
    ON "quiz_exam_records" USING btree ( "exam_category" );
CREATE INDEX "idx_quiz_exam_records_subcategory"
    ON "quiz_exam_records" USING btree ( "exam_subcategory" );
CREATE INDEX "idx_quiz_exam_records_exam_date"
    ON "quiz_exam_records" USING btree ( "exam_date" );
CREATE INDEX "idx_quiz_exam_records_accuracy_rate"
    ON "quiz_exam_records" USING btree ( "accuracy_rate" );
CREATE INDEX "idx_quiz_exam_records_category_subcategory"
    ON "quiz_exam_records" USING btree ( "exam_category" ,"exam_subcategory" );
CREATE INDEX "idx_quiz_exam_records_created_by"
    ON "quiz_exam_records" USING btree ( "created_by" );
