#!/bin/bash

echo "🚀 启动KPE后端应用（模块化版本）..."

# 检查是否在backend目录
if [ ! -f "pom.xml" ]; then
    echo "❌ 错误：请在backend目录下运行此脚本"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "❌ 错误：需要Java 17或更高版本，当前版本：$JAVA_VERSION"
    exit 1
fi

echo "✅ Java版本检查通过：$JAVA_VERSION"

# 首先安装所有模块到本地仓库
echo "📦 安装所有模块到本地仓库..."
mvn clean install -DskipTests -q
if [ $? -ne 0 ]; then
    echo "❌ 项目安装失败"
    exit 1
fi

echo "✅ 项目安装成功"

# 启动web模块
echo "🌐 启动Web模块..."
cd kpe-web

# 检查配置文件
if [ ! -f "src/main/resources/application.yml" ]; then
    echo "❌ 错误：找不到配置文件 application.yml"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 启动应用
echo "🚀 正在启动KPE后端应用..."
echo "📍 启动类：com.kpe.KpeBackendApplication"
echo "📂 模块：kpe-web"
echo "⏰ 请等待启动完成..."

mvn spring-boot:run 