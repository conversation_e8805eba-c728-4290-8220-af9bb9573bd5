package com.kpe.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

/**
 * Service基类，提供通用的服务方法
 */
public abstract class BaseService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 记录操作开始日志
     */
    protected void logOperationStart(String operation, Object... params) {
        logger.info("🎯 开始{}: {}", operation, formatParams(params));
    }

    /**
     * 记录操作成功日志
     */
    protected void logOperationSuccess(String operation, Object result) {
        if (result != null) {
            logger.info("✅ {}成功: {}", operation, result);
        } else {
            logger.info("✅ {}成功", operation);
        }
    }

    /**
     * 记录操作失败日志
     */
    protected void logOperationError(String operation, Exception e) {
        logger.error("❌ {}失败: {}", operation, e.getMessage(), e);
    }

    /**
     * 记录警告日志
     */
    protected void logWarning(String message, Object... params) {
        logger.warn("⚠️ " + message, params);
    }

    /**
     * 记录信息日志
     */
    protected void logInfo(String message, Object... params) {
        logger.info("📋 " + message, params);
    }

    /**
     * 记录调试日志
     */
    protected void logDebug(String message, Object... params) {
        logger.debug("🔍 " + message, params);
    }

    /**
     * 验证参数不为空
     */
    protected void validateNotNull(Object value, String paramName) {
        if (value == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 验证字符串不为空
     */
    protected void validateNotEmpty(String value, String paramName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 验证数值为正数
     */
    protected void validatePositive(Number value, String paramName) {
        if (value == null || value.doubleValue() <= 0) {
            throw new IllegalArgumentException(paramName + "必须为正数");
        }
    }

    /**
     * 设置实体的创建时间和更新时间
     */
    protected void setEntityTimestamps(Object entity) {
        LocalDateTime now = LocalDateTime.now();
        try {
            // 使用反射设置时间字段
            entity.getClass().getMethod("setCreatedAt", LocalDateTime.class).invoke(entity, now);
            entity.getClass().getMethod("setUpdatedAt", LocalDateTime.class).invoke(entity, now);
        } catch (Exception e) {
            // 如果实体没有这些字段，忽略异常
            logDebug("实体{}没有时间字段或设置失败: {}", entity.getClass().getSimpleName(), e.getMessage());
        }
    }

    /**
     * 设置实体的更新时间
     */
    protected void setEntityUpdateTime(Object entity) {
        LocalDateTime now = LocalDateTime.now();
        try {
            entity.getClass().getMethod("setUpdatedAt", LocalDateTime.class).invoke(entity, now);
        } catch (Exception e) {
            logDebug("实体{}没有更新时间字段或设置失败: {}", entity.getClass().getSimpleName(), e.getMessage());
        }
    }

    /**
     * 格式化参数用于日志输出
     */
    private String formatParams(Object... params) {
        if (params == null || params.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < params.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(params[i]);
        }
        return sb.toString();
    }

    /**
     * 执行操作并处理异常
     */
    protected <T> T executeWithExceptionHandling(String operation, ExceptionSupplier<T> supplier) {
        try {
            logOperationStart(operation);
            T result = supplier.get();
            logOperationSuccess(operation, result);
            return result;
        } catch (Exception e) {
            logOperationError(operation, e);
            throw new RuntimeException(operation + "失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行操作并处理异常（无返回值）
     */
    protected void executeWithExceptionHandling(String operation, ExceptionRunnable runnable) {
        try {
            logOperationStart(operation);
            runnable.run();
            logOperationSuccess(operation, null);
        } catch (Exception e) {
            logOperationError(operation, e);
            throw new RuntimeException(operation + "失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用的实体存在性验证
     */
    protected <T> T validateEntityExists(T entity, String entityName, Object entityId) {
        if (entity == null) {
            throw new IllegalArgumentException(entityName + "不存在: " + entityId);
        }
        return entity;
    }

    /**
     * 通用的实体唯一性验证
     */
    protected void validateEntityUniqueness(boolean exists, String entityName, String fieldName, Object fieldValue) {
        if (exists) {
            throw new IllegalArgumentException(entityName + "的" + fieldName + "已存在: " + fieldValue);
        }
    }


    /**
     * 通用的字符串参数验证
     */
    protected void validateNotBlank(String value, String paramName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 支持抛出异常的Supplier接口
     */
    @FunctionalInterface
    protected interface ExceptionSupplier<T> {
        T get() throws Exception;
    }

    /**
     * 支持抛出异常的Runnable接口
     */
    @FunctionalInterface
    protected interface ExceptionRunnable {
        void run() throws Exception;
    }
}
