package com.kpe.common;

import com.kpe.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.function.Supplier;

/**
 * Controller基类，提供通用的响应处理方法
 */
public abstract class BaseController {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 执行操作并返回统一响应格式
     */
    protected <T> ResponseEntity<ApiResponse<T>> executeWithResponse(Supplier<T> operation, String operationName) {
        try {
            logger.info("🎯 开始执行: {}", operationName);
            T result = operation.get();
            logger.info("✅ 执行成功: {}", operationName);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (IllegalArgumentException e) {
            logger.warn("⚠️ 参数错误 - {}: {}", operationName, e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("❌ 执行失败 - {}: {}", operationName, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 执行操作并返回统一响应格式（无返回值）
     */
    protected ResponseEntity<ApiResponse<String>> executeWithResponse(Runnable operation, String operationName, String successMessage) {
        try {
            logger.info("🎯 开始执行: {}", operationName);
            operation.run();
            logger.info("✅ 执行成功: {}", operationName);
            return ResponseEntity.ok(ApiResponse.success(successMessage));
        } catch (IllegalArgumentException e) {
            logger.warn("⚠️ 参数错误 - {}: {}", operationName, e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            logger.error("❌ 执行失败 - {}: {}", operationName, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 创建DeferredResult并设置通用配置
     */
    protected <T> DeferredResult<ResponseEntity<ApiResponse<T>>> createDeferredResult(long timeoutMs, String operationName) {
        DeferredResult<ResponseEntity<ApiResponse<T>>> deferredResult = new DeferredResult<>(timeoutMs);

        // 设置超时回调
        deferredResult.onTimeout(() -> {
            logger.warn("⏰ {} 超时", operationName);
            deferredResult.setResult(ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                    .body(ApiResponse.error(operationName + "超时，请稍后重试")));
        });

        // 设置完成回调
        deferredResult.onCompletion(() -> {
            logger.debug("🏁 {} 处理完成", operationName);
        });

        return deferredResult;
    }

    /**
     * 设置DeferredResult成功结果
     */
    protected <T> void setDeferredSuccess(DeferredResult<ResponseEntity<ApiResponse<T>>> deferredResult,
                                          T data, String operationName) {
        if (!deferredResult.isSetOrExpired()) {
            logger.info("✅ {} 完成", operationName);
            deferredResult.setResult(ResponseEntity.ok(ApiResponse.success(data)));
        } else {
            logger.warn("⚠️ {} 完成但DeferredResult已超时或已设置", operationName);
        }
    }

    /**
     * 设置DeferredResult错误结果
     */
    protected <T> void setDeferredError(DeferredResult<ResponseEntity<ApiResponse<T>>> deferredResult,
                                        Throwable throwable, String operationName) {
        if (!deferredResult.isSetOrExpired()) {
            logger.error("❌ {} 失败: {}", operationName, throwable.getMessage(), throwable);
            deferredResult.setResult(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(operationName + "失败: " + throwable.getMessage())));
        } else {
            logger.warn("⚠️ {} 失败但DeferredResult已超时或已设置: {}", operationName, throwable.getMessage());
        }
    }

    /**
     * 验证路径参数是否匹配
     */
    protected void validatePathParameters(Long pathProjectId, Long entityProjectId, String entityName) {
        if (!pathProjectId.equals(entityProjectId)) {
            throw new IllegalArgumentException(entityName + "不属于指定项目");
        }
    }

    /**
     * 验证实体是否存在
     */
    protected <T> T validateEntityExists(T entity, String entityName, Long entityId) {
        if (entity == null) {
            throw new IllegalArgumentException(entityName + "不存在: " + entityId);
        }
        return entity;
    }
}
