package com.kpe.common;

import com.kpe.config.CacheConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

/**
 * 缓存操作工具类
 * 提供统一的缓存操作方法，避免重复代码
 */
@Component
public class CacheHelper {

    private static final Logger logger = LoggerFactory.getLogger(CacheHelper.class);

    @Autowired
    private CacheManager cacheManager;

    /**
     * 清除单个缓存
     */
    public void evictCache(String cacheName, Object key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.evict(key);
                logger.info("🗄️ 已清除缓存: {} - {}", cacheName, key);
            }
        } catch (Exception e) {
            logger.warn("⚠️ 清除缓存失败: {} - {}: {}", cacheName, key, e.getMessage());
        }
    }

    /**
     * 清除整个缓存区域
     */
    public void clearCache(String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                logger.info("🗄️ 已清除整个缓存区域: {}", cacheName);
            }
        } catch (Exception e) {
            logger.warn("⚠️ 清除缓存区域失败: {}: {}", cacheName, e.getMessage());
        }
    }

    /**
     * 清除多个缓存区域
     */
    public void clearCaches(String... cacheNames) {
        for (String cacheName : cacheNames) {
            clearCache(cacheName);
        }
    }

    /**
     * 清除项目相关的所有缓存
     */
    public void clearProjectRelatedCaches(Long projectId) {
        logger.info("🗄️ 开始清除项目相关缓存: {}", projectId);

        // 清除项目基本信息缓存
        evictCache(CacheConfig.PROJECT_CACHE, projectId);

        // 清除项目列表缓存
        clearCache(CacheConfig.PROJECT_LIST_CACHE);

        // 清除知识点相关缓存（使用统一的清理方法）
        clearKnowledgeRelatedCaches(projectId);

        logger.info("✅ 项目相关缓存清除完成: {}", projectId);
    }

    /**
     * 清除知识点相关缓存
     */
    public void clearKnowledgeRelatedCaches(Long projectId) {
        logger.info("🗄️ 开始清除知识点相关缓存: {}", projectId);

        // 清除知识点配置缓存
        evictCache(CacheConfig.KNOWLEDGE_CONFIG_CACHE, projectId);

        // 清除知识点路径缓存
        evictCache(CacheConfig.KNOWLEDGE_PATHS_CACHE, projectId);

        // 清除项目完整信息缓存
        evictCache(CacheConfig.PROJECT_WITH_KNOWLEDGE_CACHE, projectId);

        // 清除叶子节点缓存 - 修复缓存键匹配问题
        // 1. 清除带路径的叶子节点缓存 (key = projectId + '_with_paths')
        evictCache(CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE, projectId + "_with_paths");

        // 2. 由于基于configuration.hashCode()的缓存键无法预测，直接清空整个叶子节点缓存区域
        clearCache(CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE);

        logger.info("✅ 知识点相关缓存清除完成: {}", projectId);
    }

    /**
     * 构建缓存键
     */
    public String buildCacheKey(String prefix, Object... parts) {
        StringBuilder keyBuilder = new StringBuilder(prefix);
        for (Object part : parts) {
            keyBuilder.append("_").append(part);
        }
        return keyBuilder.toString();
    }

    /**
     * 检查缓存是否存在
     */
    public boolean isCacheExists(String cacheName, Object key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Cache.ValueWrapper valueWrapper = cache.get(key);
                return valueWrapper != null;
            }
        } catch (Exception e) {
            logger.debug("检查缓存存在性失败: {} - {}: {}", cacheName, key, e.getMessage());
        }
        return false;
    }
}
