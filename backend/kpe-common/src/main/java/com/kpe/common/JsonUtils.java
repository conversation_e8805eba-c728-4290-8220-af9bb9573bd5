package com.kpe.common;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * JSON处理工具类
 */
public class JsonUtils {

    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);

    /**
     * 递归遍历JSON节点
     */
    public static void traverseJsonNode(Object node, Consumer<Object> nodeProcessor) {
        if (node == null) {
            return;
        }

        // 处理当前节点
        nodeProcessor.accept(node);

        // 递归处理子节点
        if (node instanceof JSONArray array) {
            for (Object child : array) {
                traverseJsonNode(child, nodeProcessor);
            }
        } else if (node instanceof JSONObject jsonObject) {
            // 处理children字段
            Object children = jsonObject.get("children");
            if (children instanceof JSONArray childrenArray) {
                for (Object child : childrenArray) {
                    traverseJsonNode(child, nodeProcessor);
                }
            }
        }
    }

    /**
     * 递归遍历JSON节点并收集结果
     */
    public static <T> List<T> collectFromJsonNode(Object node, Function<Object, T> collector) {
        List<T> results = new ArrayList<>();
        traverseJsonNode(node, jsonNode -> {
            T result = collector.apply(jsonNode);
            if (result != null) {
                results.add(result);
            }
        });
        return results;
    }

    /**
     * 递归遍历JSON节点并修改
     */
    public static void modifyJsonNode(Object node, Consumer<JSONObject> modifier) {
        traverseJsonNode(node, jsonNode -> {
            if (jsonNode instanceof JSONObject) {
                modifier.accept((JSONObject) jsonNode);
            }
        });
    }

    /**
     * 为JSON节点添加UID
     */
    public static void addUidToJsonNode(Object node) {
        modifyJsonNode(node, jsonObject -> {
            if (jsonObject.containsKey("name")) {
                String name = jsonObject.getString("name");
                String uid = generateMd5(name);
                jsonObject.put("uid", uid);
                logger.debug("为节点 '{}' 生成uid: {}", name, uid);
            }
        });
    }

    /**
     * 清空JSON节点中的评分
     */
    public static void clearScoresInJsonNode(Object node) {
        modifyJsonNode(node, jsonObject -> {
            if (jsonObject.containsKey("score")) {
                jsonObject.remove("score");
                logger.debug("清空节点评分: {}", jsonObject.containsKey("name") ? jsonObject.getString("name") : "未知");
            }
        });
    }

    /**
     * 提取叶子节点
     */
    public static <T> List<T> extractLeafNodes(Object node, Function<Object, T> leafProcessor) {
        List<T> leafNodes = new ArrayList<>();

        traverseJsonNode(node, jsonNode -> {
            if (isLeafNode(jsonNode)) {
                T result = leafProcessor.apply(jsonNode);
                if (result != null) {
                    leafNodes.add(result);
                }
            }
        });

        return leafNodes;
    }

    /**
     * 提取叶子节点（带父级路径信息，不包含叶子节点本身）
     */
    public static <T> List<T> extractLeafNodesWithPath(Object node, BiFunction<Object, String, T> leafProcessor) {
        List<T> leafNodes = new ArrayList<>();
        extractLeafNodesRecursive(node, leafProcessor, leafNodes, new ArrayList<>(), true, false);
        return leafNodes;
    }

    /**
     * 提取叶子节点（带完整路径信息，包含叶子节点本身）
     */
    public static <T> List<T> extractLeafNodesWithFullPath(Object node, BiFunction<Object, String, T> leafProcessor) {
        List<T> leafNodes = new ArrayList<>();
        extractLeafNodesRecursive(node, leafProcessor, leafNodes, new ArrayList<>(), true, true);
        return leafNodes;
    }

    /**
     * 递归提取叶子节点（带路径信息）
     */
    private static <T> void extractLeafNodesRecursive(Object node, BiFunction<Object, String, T> leafProcessor,
                                                      List<T> results, List<String> currentPath, boolean isRoot, boolean includeLeafInPath) {
        if (node == null) {
            return;
        }

        if (node instanceof JSONArray array) {
            for (Object child : array) {
                // 数组中的元素不应该被当作根节点，除非这是最顶层的调用
                extractLeafNodesRecursive(child, leafProcessor, results, currentPath, false, includeLeafInPath);
            }
        } else if (node instanceof JSONObject jsonObject) {
            String name = jsonObject.getString("name");

            if (isLeafNode(jsonObject)) {
                // 叶子节点：根据includeLeafInPath参数决定是否包含叶子节点本身
                String path;
                if (includeLeafInPath) {
                    // 包含叶子节点本身的完整路径
                    List<String> fullPath = new ArrayList<>(currentPath);
                    if (name != null && !name.trim().isEmpty()) {
                        fullPath.add(name);
                    }
                    path = generatePathString(fullPath, true, false); // 排除根节点，只显示子路径
                    logger.debug("🔍 叶子节点完整路径生成: {} - 完整路径: {} - 生成路径: '{}'",
                            name, fullPath, path);
                } else {
                    // 只包含父级路径，不包含叶子节点本身
                    path = generatePathString(currentPath, true, false); // 排除根节点，只显示子路径
                    logger.debug("🔍 叶子节点父级路径生成: {} - 父级路径: {} - 生成路径: '{}'",
                            name, currentPath, path);
                }

                T result = leafProcessor.apply(jsonObject, path);
                if (result != null) {
                    results.add(result);
                }
            } else {
                // 非叶子节点：继续递归处理子节点
                Object children = jsonObject.get("children");
                if (children instanceof JSONArray childrenArray) {
                    // 构建传递给子节点的路径：包含当前节点名称（除非是根节点）
                    List<String> pathForChildren = new ArrayList<>(currentPath);
                    if (name != null && !name.trim().isEmpty() && !isRoot) {
                        pathForChildren.add(name);
                        logger.debug("🔍 添加路径节点: {} - 当前路径: {}", name, pathForChildren);
                    } else if (isRoot) {
                        logger.debug("🔍 跳过根节点: {} - 当前路径: {}", name, pathForChildren);
                    }

                    for (Object child : childrenArray) {
                        extractLeafNodesRecursive(child, leafProcessor, results, pathForChildren, false, includeLeafInPath);
                    }
                }
            }
        }
    }

    /**
     * 生成路径字符串
     */
    private static String generatePathString(List<String> pathArray, boolean excludeRoot, boolean excludeSelf) {
        if (pathArray == null || pathArray.isEmpty()) {
            return "";
        }

        List<String> path = new ArrayList<>(pathArray);

        if (excludeRoot && path.size() > 0) {
            path = path.subList(1, path.size());
        }

        if (excludeSelf && path.size() > 0) {
            path = path.subList(0, path.size() - 1);
        }

        String result = String.join(" > ", path);
        logger.debug("🔍 路径字符串生成: 输入={}, excludeRoot={}, excludeSelf={}, 结果='{}'",
                pathArray, excludeRoot, excludeSelf, result);
        return result;
    }

    /**
     * 判断是否为叶子节点
     */
    public static boolean isLeafNode(Object node) {
        if (!(node instanceof JSONObject jsonObject)) {
            return false;
        }

        var children = jsonObject.get("children");
        return children == null || !(children instanceof JSONArray childrenArray) || childrenArray.size() == 0;
    }

    /**
     * 深拷贝JSON节点
     */
    public static Object deepCopy(Object node) {
        if (node == null) {
            return null;
        }

        try {
            String jsonString = JSON.toJSONString(node);
            return JSON.parse(jsonString);
        } catch (Exception e) {
            logger.error("JSON深拷贝失败: {}", e.getMessage());
            return node;
        }
    }

    /**
     * 检查字符串是否是有效的JSON
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }

        try {
            JSON.parse(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从文本中提取JSON内容
     */
    public static String extractJsonFromText(String content) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        // 尝试提取```json```包裹的内容
        var jsonPattern = "```json\\s*([\\s\\S]*?)```";
        var pattern = java.util.regex.Pattern.compile(jsonPattern);
        var matcher = pattern.matcher(content);

        if (matcher.find()) {
            var jsonContent = matcher.group(1).trim();
            logger.debug("提取到JSON内容: {}", jsonContent.length() > 200 ? jsonContent.substring(0, 200) + "..." : jsonContent);
            return jsonContent;
        }

        // 尝试提取普通```包裹的内容
        var codePattern = "```\\s*([\\s\\S]*?)```";
        pattern = java.util.regex.Pattern.compile(codePattern);
        matcher = pattern.matcher(content);

        if (matcher.find()) {
            var codeContent = matcher.group(1).trim();
            if (isValidJson(codeContent)) {
                logger.debug("提取到代码块中的JSON内容: {}", codeContent.length() > 200 ? codeContent.substring(0, 200) + "..." : codeContent);
                return codeContent;
            }
        }

        logger.debug("未找到JSON包裹，返回原内容");
        return content;
    }

    /**
     * 生成MD5哈希值
     */
    public static String generateMd5(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        try {
            var md = MessageDigest.getInstance("MD5");
            var hashBytes = md.digest(input.getBytes());

            var sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            logger.error("MD5算法不可用: {}", e.getMessage());
            return input.hashCode() + "";
        }
    }

    /**
     * 解析JSON字符串为Object
     */
    public static Object parseJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            return JSON.parse(jsonString);
        } catch (Exception e) {
            logger.error("JSON解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            logger.error("对象转JSON字符串失败: {}", e.getMessage());
            return null;
        }
    }
}
