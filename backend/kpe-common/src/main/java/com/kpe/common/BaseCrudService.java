package com.kpe.common;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

/**
 * 通用CRUD服务基类
 * 提供标准的增删改查操作模板，减少重复代码
 *
 * @param <T>  实体类型
 * @param <ID> 主键类型，必须实现Serializable
 * @param <M>  Mapper类型
 */
public abstract class BaseCrudService<T, ID extends Serializable, M extends BaseMapper<T>> extends BaseService {

    @Autowired
    protected CacheHelper cacheHelper;

    /**
     * 获取Mapper实例
     * 子类需要实现此方法返回对应的Mapper
     */
    protected abstract M getMapper();

    /**
     * 获取实体名称，用于日志记录
     */
    protected abstract String getEntityName();

    /**
     * 设置实体的创建时间和更新时间
     * 子类可以重写此方法来自定义时间字段设置
     */
    protected void setTimestamps(T entity, boolean isCreate) {
        // 默认实现为空，子类可以重写
        // 例如：
        // if (entity instanceof BaseEntity) {
        //     BaseEntity baseEntity = (BaseEntity) entity;
        //     LocalDateTime now = LocalDateTime.now();
        //     if (isCreate) {
        //         baseEntity.setCreatedAt(now);
        //     }
        //     baseEntity.setUpdatedAt(now);
        // }
    }

    /**
     * 获取相关的缓存名称列表
     * 子类可以重写此方法来指定需要清除的缓存
     */
    protected String[] getRelatedCacheNames() {
        return new String[0];
    }

    /**
     * 清除相关缓存
     */
    protected void clearRelatedCaches(ID id) {
        String[] cacheNames = getRelatedCacheNames();
        if (cacheNames.length > 0) {
            cacheHelper.clearCaches(cacheNames);
        }
    }

    /**
     * 创建实体前的验证
     * 子类可以重写此方法来添加自定义验证逻辑
     */
    protected void validateBeforeCreate(T entity) {
        validateNotNull(entity, getEntityName());
    }

    /**
     * 更新实体前的验证
     * 子类可以重写此方法来添加自定义验证逻辑
     */
    protected void validateBeforeUpdate(ID id, T entity) {
        validateNotNull(id, getEntityName() + "ID");
        validateNotNull(entity, getEntityName());
    }

    /**
     * 删除实体前的验证
     * 子类可以重写此方法来添加自定义验证逻辑
     */
    protected void validateBeforeDelete(ID id) {
        validateNotNull(id, getEntityName() + "ID");
    }

    /**
     * 通用创建方法
     */
    @Transactional
    public T create(T entity) {
        return executeWithExceptionHandling("创建" + getEntityName(), () -> {
            validateBeforeCreate(entity);
            setTimestamps(entity, true);

            int result = getMapper().insert(entity);
            if (result > 0) {
                clearRelatedCaches(null);
                logInfo("{}创建成功", getEntityName());
                return entity;
            } else {
                throw new RuntimeException("创建" + getEntityName() + "失败");
            }
        });
    }

    /**
     * 通用根据ID查询方法
     */
    public Optional<T> findById(ID id) {
        return executeWithExceptionHandling("查询" + getEntityName(), () -> {
            validateNotNull(id, getEntityName() + "ID");
            T entity = getMapper().selectById(id);
            if (entity != null) {
                logInfo("{}查询成功: {}", getEntityName(), id);
                return Optional.of(entity);
            } else {
                logWarning("{}不存在: {}", getEntityName(), id);
                return Optional.empty();
            }
        });
    }

    /**
     * 通用查询所有方法
     */
    public List<T> findAll() {
        return executeWithExceptionHandling("查询所有" + getEntityName(), () -> {
            List<T> entities = getMapper().selectList(null);
            logInfo("查询完成，返回 {} 条{}记录", entities.size(), getEntityName());
            return entities;
        });
    }

    /**
     * 通用更新方法
     */
    @Transactional
    public Optional<T> update(ID id, T entity) {
        return executeWithExceptionHandling("更新" + getEntityName(), () -> {
            validateBeforeUpdate(id, entity);

            // 检查实体是否存在
            T existingEntity = getMapper().selectById(id);
            validateEntityExists(existingEntity, getEntityName(), id);

            setTimestamps(entity, false);

            int result = getMapper().updateById(entity);
            if (result > 0) {
                clearRelatedCaches(id);
                logInfo("{}更新成功: {}", getEntityName(), id);
                return Optional.of(entity);
            } else {
                throw new RuntimeException("更新" + getEntityName() + "失败");
            }
        });
    }

    /**
     * 通用删除方法
     */
    @Transactional
    public boolean delete(ID id) {
        return executeWithExceptionHandling("删除" + getEntityName(), () -> {
            validateBeforeDelete(id);

            // 检查实体是否存在
            T existingEntity = getMapper().selectById(id);
            validateEntityExists(existingEntity, getEntityName(), id);

            int result = getMapper().deleteById(id);
            if (result > 0) {
                clearRelatedCaches(id);
                logInfo("{}删除成功: {}", getEntityName(), id);
                return true;
            } else {
                throw new RuntimeException("删除" + getEntityName() + "失败");
            }
        });
    }

    /**
     * 通用批量删除方法
     */
    @Transactional
    public int deleteBatch(List<ID> ids) {
        return executeWithExceptionHandling("批量删除" + getEntityName(), () -> {
            validateNotNull(ids, getEntityName() + "ID列表");
            if (ids.isEmpty()) {
                return 0;
            }

            int result = getMapper().deleteBatchIds(ids);
            if (result > 0) {
                clearRelatedCaches(null);
                logInfo("批量删除{}成功: {} 条记录", getEntityName(), result);
            }
            return result;
        });
    }
}
