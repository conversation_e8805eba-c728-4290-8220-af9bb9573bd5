package com.kpe.dto;

/**
 * 知识点分数DTO
 */
public class KnowledgePointScore {
    private String uid;
    private String path;
    private String name;
    private Integer score;
    private String level;

    public KnowledgePointScore() {
    }

    public KnowledgePointScore(String uid, String path, String name, Integer score, String level) {
        this.uid = uid;
        this.path = path;
        this.name = name;
        this.score = score;
        this.level = level;
    }

    public KnowledgePointScore(String path, String name, String level, String uid) {
        this.uid = uid;
        this.path = path;
        this.name = name;
        this.level = level;
        this.score = 0; // 默认分数
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    @Override
    public String toString() {
        return "KnowledgePointScore{" +
                "path='" + path + '\'' +
                ", name='" + name + '\'' +
                ", score=" + score +
                ", level='" + level + '\'' +
                '}';
    }
} 