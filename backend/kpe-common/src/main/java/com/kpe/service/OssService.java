package com.kpe.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.kpe.config.OssConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * 阿里云OSS文件上传服务
 */
@Service
@ConditionalOnBean(OSS.class)
public class OssService {

    private static final Logger logger = LoggerFactory.getLogger(OssService.class);

    @Autowired
    private OSS ossClient;

    @Autowired
    private OssConfig ossConfig;

    /**
     * 上传文件到OSS
     *
     * @param file   文件
     * @param folder 文件夹路径（如：wrong-questions/images/）
     * @return 文件访问URL
     */
    public String uploadFile(MultipartFile file, String folder) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        String fileName = UUID.randomUUID() + extension;
        String objectKey = folder + fileName;

        try (InputStream inputStream = file.getInputStream()) {
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    ossConfig.getBucketName(), objectKey, inputStream, metadata);

            ossClient.putObject(putObjectRequest);

            // 返回文件访问URL
            String fileUrl = "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint() + "/" + objectKey;
            logger.info("✅ 文件上传成功: {}", fileUrl);

            return fileUrl;
        } catch (Exception e) {
            logger.error("❌ 文件上传失败: {}", e.getMessage());
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传字节数组到OSS
     *
     * @param bytes       文件字节数组
     * @param fileName    文件名
     * @param folder      文件夹路径
     * @param contentType 文件类型
     * @return 文件访问URL
     */
    public String uploadBytes(byte[] bytes, String fileName, String folder, String contentType) {
        if (bytes == null || bytes.length == 0) {
            throw new IllegalArgumentException("文件内容不能为空");
        }

        // 生成唯一文件名
        String extension = "";
        if (fileName != null && fileName.contains(".")) {
            extension = fileName.substring(fileName.lastIndexOf("."));
        }
        String uniqueFileName = UUID.randomUUID() + extension;
        String objectKey = folder + uniqueFileName;

        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(bytes.length);
            if (contentType != null) {
                metadata.setContentType(contentType);
            }

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    ossConfig.getBucketName(), objectKey, inputStream, metadata);

            ossClient.putObject(putObjectRequest);

            // 返回文件访问URL
            String fileUrl = "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint() + "/" + objectKey;
            logger.info("✅ 字节数组上传成功: {}", fileUrl);

            return fileUrl;
        } catch (Exception e) {
            logger.error("❌ 字节数组上传失败: {}", e.getMessage());
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除OSS文件
     *
     * @param fileUrl 文件URL
     */
    public void deleteFile(String fileUrl) {
        try {
            if (fileUrl == null || fileUrl.isEmpty()) {
                return;
            }

            // 从URL中提取objectKey
            String bucketDomain = "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint() + "/";
            if (fileUrl.startsWith(bucketDomain)) {
                String objectKey = fileUrl.substring(bucketDomain.length());
                ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
                logger.info("✅ 文件删除成功: {}", fileUrl);
            }
        } catch (Exception e) {
            logger.error("❌ 文件删除失败: {}", e.getMessage());
        }
    }
}
