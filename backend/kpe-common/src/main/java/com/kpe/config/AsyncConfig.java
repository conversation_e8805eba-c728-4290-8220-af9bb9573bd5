package com.kpe.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executor;

/**
 * 异步配置类
 * 配置异步请求处理和线程池
 */
@Configuration
public class AsyncConfig implements AsyncConfigurer, WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(AsyncConfig.class);

    @Value("${coze.api.polling.max-attempts:30}")
    private int maxPollingAttempts;

    @Value("${coze.api.polling.interval:10000}")
    private long pollingInterval;

    /**
     * 配置异步支持
     */
    @Override
    public void configureAsyncSupport(@NonNull AsyncSupportConfigurer configurer) {
        // 计算超时时间：轮询总时间 + 额外缓冲时间
        long totalPollingTime = maxPollingAttempts * pollingInterval;
        long bufferTime = 60000; // 1分钟缓冲
        long asyncTimeout = totalPollingTime + bufferTime;

        logger.info("🔧 配置异步支持");
        logger.info("   轮询总时间: {}ms ({}分钟)", totalPollingTime, totalPollingTime / 60000);
        logger.info("   异步超时时间: {}ms ({}分钟)", asyncTimeout, asyncTimeout / 60000);

        configurer.setDefaultTimeout(asyncTimeout);
        configurer.registerCallableInterceptors(timeoutInterceptor());
    }

    /**
     * 异步任务执行器
     */
    @Override
    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("Async-");
        executor.setRejectedExecutionHandler((r, executor1) -> {
            logger.warn("⚠️  异步任务被拒绝执行: {}", r.toString());
        });
        executor.initialize();

        logger.info("🔧 异步任务执行器配置完成");
        logger.info("   核心线程数: {}", executor.getCorePoolSize());
        logger.info("   最大线程数: {}", executor.getMaxPoolSize());
        logger.info("   队列容量: {}", executor.getQueueCapacity());

        return executor;
    }

    /**
     * 超时拦截器
     */
    @Bean
    public TimeoutCallableProcessingInterceptor timeoutInterceptor() {
        return new TimeoutCallableProcessingInterceptor();
    }
}
