package com.kpe.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置
 * 注意：Spring Boot 2.x 默认使用StandardServletMultipartResolver
 * 我们只需要在application.yml中配置即可，不需要额外的Bean配置
 */
@Configuration
public class MultipartConfig {

    private static final Logger logger = LoggerFactory.getLogger(MultipartConfig.class);

    public MultipartConfig() {
        logger.info("🔧 文件上传配置类已加载");
        logger.info("   使用Spring Boot默认的multipart配置");
        logger.info("   配置参数在application.yml中设置");
    }
}
