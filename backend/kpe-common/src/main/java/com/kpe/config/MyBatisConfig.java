package com.kpe.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.context.annotation.Configuration;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;

/**
 * MyBatis配置类
 * 处理PostgreSQL TIMESTAMPTZ与LocalDateTime的转换
 */
@Configuration
public class MyBatisConfig {

    /**
     * PostgreSQL TIMESTAMPTZ 到 LocalDateTime 的类型处理器
     */
    @MappedTypes(LocalDateTime.class)
    @MappedJdbcTypes({JdbcType.TIMESTAMP_WITH_TIMEZONE, JdbcType.TIMESTAMP})
    public static class PostgreSQLLocalDateTimeTypeHandler extends BaseTypeHandler<LocalDateTime> {

        @Override
        public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
            // 将LocalDateTime转换为OffsetDateTime（使用系统默认时区）
            OffsetDateTime offsetDateTime = parameter.atZone(ZoneId.systemDefault()).toOffsetDateTime();
            ps.setObject(i, offsetDateTime);
        }

        @Override
        public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
            Object value = rs.getObject(columnName);
            return convertToLocalDateTime(value);
        }

        @Override
        public LocalDateTime getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
            Object value = rs.getObject(columnIndex);
            return convertToLocalDateTime(value);
        }

        @Override
        public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
            Object value = cs.getObject(columnIndex);
            return convertToLocalDateTime(value);
        }

        /**
         * 将数据库返回的时间对象转换为LocalDateTime
         */
        private LocalDateTime convertToLocalDateTime(Object value) throws SQLException {
            if (value == null) {
                return null;
            }

            if (value instanceof LocalDateTime) {
                return (LocalDateTime) value;
            }

            if (value instanceof OffsetDateTime) {
                // 将OffsetDateTime转换为系统默认时区的LocalDateTime
                return ((OffsetDateTime) value).atZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
            }

            if (value instanceof Timestamp) {
                return ((Timestamp) value).toLocalDateTime();
            }

            // 如果是其他类型，尝试转换为字符串再解析
            try {
                return LocalDateTime.parse(value.toString());
            } catch (Exception e) {
                throw new SQLException("Cannot convert " + value.getClass().getName() + " to LocalDateTime: " + value, e);
            }
        }
    }
}
