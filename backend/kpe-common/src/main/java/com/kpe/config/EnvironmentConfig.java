package com.kpe.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * 环境配置类
 * 用于加载 .env 文件中的环境变量
 */
@Component
public class EnvironmentConfig implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    private static final Logger logger = LoggerFactory.getLogger(EnvironmentConfig.class);

    @Override
    public void initialize(@NonNull ConfigurableApplicationContext applicationContext) {
        ConfigurableEnvironment environment = applicationContext.getEnvironment();

        // 尝试加载 .env 文件
        loadDotEnvFile(environment);
    }

    /**
     * 加载 .env 文件
     */
    private void loadDotEnvFile(ConfigurableEnvironment environment) {
        // 获取项目根目录
        String userDir = System.getProperty("user.dir");

        // 尝试多个可能的 .env 文件位置
        String[] possiblePaths = {
                userDir + "/.env",                    // 项目根目录
                userDir + "/backend/.env",            // backend 目录
                System.getProperty("user.home") + "/.env"  // 用户主目录
        };

        for (String path : possiblePaths) {
            File envFile = new File(path);
            if (envFile.exists() && envFile.isFile()) {
                try {
                    loadEnvFile(envFile, environment);
                    logger.info("✅ 成功加载环境配置文件: {}", path);
                    return;
                } catch (IOException e) {
                    logger.warn("⚠️ 加载环境配置文件失败: {} - {}", path, e.getMessage());
                }
            }
        }

        logger.info("ℹ️ 未找到 .env 文件，使用默认配置");
    }

    /**
     * 读取并解析 .env 文件
     */
    private void loadEnvFile(File envFile, ConfigurableEnvironment environment) throws IOException {
        Properties properties = new Properties();

        try (FileInputStream fis = new FileInputStream(envFile)) {
            // 逐行读取文件
            String content = new String(fis.readAllBytes());
            String[] lines = content.split("\n");

            for (String line : lines) {
                line = line.trim();

                // 跳过空行和注释行
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                // 解析键值对
                int equalIndex = line.indexOf('=');
                if (equalIndex > 0) {
                    String key = line.substring(0, equalIndex).trim();
                    String value = line.substring(equalIndex + 1).trim();

                    // 移除值两端的引号（如果有）
                    if ((value.startsWith("\"") && value.endsWith("\"")) ||
                            (value.startsWith("'") && value.endsWith("'"))) {
                        value = value.substring(1, value.length() - 1);
                    }

                    properties.setProperty(key, value);

                    // 同时设置为系统属性，确保 ${} 占位符能够解析
                    System.setProperty(key, value);
                }
            }
        }

        // 将属性添加到 Spring 环境中
        if (!properties.isEmpty()) {
            PropertiesPropertySource propertySource = new PropertiesPropertySource("dotenv", properties);
            environment.getPropertySources().addFirst(propertySource);
            logger.info("📝 加载了 {} 个环境变量", properties.size());
        }
    }
}
