package com.kpe.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 缓存配置类
 * 配置项目和知识点相关的缓存策略
 */
@Configuration
@EnableCaching
public class CacheConfig {

    // 缓存名称常量
    public static final String PROJECT_CACHE = "projects";
    public static final String PROJECT_LIST_CACHE = "projectList";
    public static final String KNOWLEDGE_CONFIG_CACHE = "knowledgeConfig";
    public static final String KNOWLEDGE_LEAF_POINTS_CACHE = "knowledgeLeafPoints";
    public static final String KNOWLEDGE_PATHS_CACHE = "knowledgePaths";
    public static final String PROJECT_WITH_KNOWLEDGE_CACHE = "projectWithKnowledge";
    private static final Logger logger = LoggerFactory.getLogger(CacheConfig.class);

    /**
     * 配置缓存管理器
     * 使用内存缓存，适合单机部署
     */
    @Bean
    public CacheManager cacheManager() {
        logger.info("🗄️ 初始化缓存管理器");

        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager(
                PROJECT_CACHE,
                PROJECT_LIST_CACHE,
                KNOWLEDGE_CONFIG_CACHE,
                KNOWLEDGE_LEAF_POINTS_CACHE,
                KNOWLEDGE_PATHS_CACHE,
                PROJECT_WITH_KNOWLEDGE_CACHE
        );

        // 设置允许空值缓存
        cacheManager.setAllowNullValues(true);

        logger.info("✅ 缓存管理器初始化完成");
        logger.info("   缓存类型: 内存缓存 (ConcurrentMapCacheManager)");
        logger.info("   项目缓存: {}", PROJECT_CACHE);
        logger.info("   项目列表缓存: {}", PROJECT_LIST_CACHE);
        logger.info("   知识点配置缓存: {}", KNOWLEDGE_CONFIG_CACHE);
        logger.info("   知识点叶子节点缓存: {}", KNOWLEDGE_LEAF_POINTS_CACHE);
        logger.info("   知识点路径缓存: {}", KNOWLEDGE_PATHS_CACHE);
        logger.info("   项目完整信息缓存: {}", PROJECT_WITH_KNOWLEDGE_CACHE);

        return cacheManager;
    }
}
