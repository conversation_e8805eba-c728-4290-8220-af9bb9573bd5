package com.kpe.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * 阿里云OSS配置
 */
@Configuration
public class OssConfig {

    private static final Logger logger = LoggerFactory.getLogger(OssConfig.class);

    @Value("${oss.endpoint:oss-cn-shanghai.aliyuncs.com}")
    private String endpoint;

    @Value("${oss.access-key-id:}")
    private String accessKeyId;

    @Value("${oss.access-key-secret:}")
    private String accessKeySecret;

    @Value("${oss.bucket-name:kpe-system}")
    private String bucketName;

    @Value("${oss.enabled:false}")
    private boolean ossEnabled;

    @Bean
    @ConditionalOnProperty(name = "oss.enabled", havingValue = "true")
    public OSS ossClient() {
        // 验证必要的配置参数
        if (!StringUtils.hasText(accessKeyId)) {
            logger.error("❌ OSS配置错误: access-key-id 不能为空");
            throw new IllegalArgumentException("OSS access-key-id 不能为空");
        }

        if (!StringUtils.hasText(accessKeySecret)) {
            logger.error("❌ OSS配置错误: access-key-secret 不能为空");
            throw new IllegalArgumentException("OSS access-key-secret 不能为空");
        }

        logger.info("🔧 初始化OSS客户端");
        logger.info("   Endpoint: {}", endpoint);
        logger.info("   Bucket: {}", bucketName);
        logger.info("   Access Key ID: {}***", accessKeyId.substring(0, Math.min(8, accessKeyId.length())));

        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        logger.info("✅ OSS客户端初始化成功");

        return ossClient;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getBucketName() {
        return bucketName;
    }
}
