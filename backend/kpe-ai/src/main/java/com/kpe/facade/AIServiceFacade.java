package com.kpe.facade;

import com.kpe.common.BaseService;
import com.kpe.dto.coze.CozeChatResult;
import com.kpe.entity.AIGenerationLog;
import com.kpe.mapper.AIGenerationLogMapper;
import com.kpe.strategy.AIServiceStrategy;
import com.kpe.strategy.AIServiceStrategyFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * AI服务门面，统一处理AI相关操作
 */
@Service
public class AIServiceFacade extends BaseService {

    @Autowired
    private AIServiceStrategyFactory strategyFactory;

    @Autowired
    private AIGenerationLogMapper aiGenerationLogMapper;

    /**
     * 执行AI对话
     */
    public CompletableFuture<CozeChatResult> executeAIChat(String strategyType, Long projectId,
                                                           String prompt, String userId,
                                                           List<String> fileIds, String conversationId) {
        return executeWithExceptionHandling("AI对话", () -> {
            validateNotEmpty(strategyType, "策略类型");
            validateNotNull(projectId, "项目ID");
            validateNotEmpty(prompt, "提示词");
            validateNotEmpty(userId, "用户ID");

            AIServiceStrategy strategy = strategyFactory.getStrategy(strategyType);

            // 记录开始生成
            AIGenerationLog log = createGenerationLog(projectId, prompt, strategy.getBotId(),
                    strategyType, "processing", userId);
            aiGenerationLogMapper.insert(log);

            logInfo("开始执行AI对话: 策略={}, 项目={}, Bot={}", strategyType, projectId, strategy.getBotId());

            return strategy.executeChat(strategy.getBotId(), userId, prompt, fileIds, conversationId)
                    .thenApply(result -> {
                        // 更新日志状态
                        updateGenerationLog(log.getId(), result.getMessage(), "completed", null);
                        logInfo("AI对话完成: 策略={}, 项目={}", strategyType, projectId);
                        return result;
                    })
                    .exceptionally(throwable -> {
                        // 更新日志状态
                        updateGenerationLog(log.getId(), null, "failed", throwable.getMessage());
                        logOperationError("AI对话", new RuntimeException(throwable));
                        throw new RuntimeException("AI对话失败: " + throwable.getMessage(), throwable);
                    });
        });
    }

    /**
     * 获取AI服务策略
     */
    public AIServiceStrategy getStrategy(String strategyType) {
        return strategyFactory.getStrategy(strategyType);
    }

    /**
     * 获取所有可用的策略类型
     */
    public List<String> getAvailableStrategyTypes() {
        return strategyFactory.getAvailableStrategyTypes();
    }

    /**
     * 检查策略是否支持文件上传
     */
    public boolean supportsFileUpload(String strategyType) {
        AIServiceStrategy strategy = strategyFactory.getStrategy(strategyType);
        return strategy.supportsFileUpload();
    }

    /**
     * 获取策略的超时时间
     */
    public long getStrategyTimeout(String strategyType) {
        AIServiceStrategy strategy = strategyFactory.getStrategy(strategyType);
        return strategy.getTimeoutMs();
    }

    /**
     * 创建AI生成日志
     */
    private AIGenerationLog createGenerationLog(Long projectId, String prompt, String botId,
                                                String generationType, String status, String createdBy) {
        AIGenerationLog log = new AIGenerationLog(projectId, prompt, null, botId,
                generationType, status, null, createdBy);
        log.setCreatedAt(LocalDateTime.now());
        return log;
    }

    /**
     * 更新AI生成日志
     */
    private void updateGenerationLog(Long logId, String response, String status, String errorMessage) {
        try {
            AIGenerationLog log = aiGenerationLogMapper.selectById(logId);
            if (log != null) {
                log.setResponse(response);
                log.setStatus(status);
                log.setErrorMessage(errorMessage);
                aiGenerationLogMapper.updateById(log);
            }
        } catch (Exception e) {
            logWarning("更新AI生成日志失败: {}", e.getMessage());
        }
    }

    /**
     * 获取AI生成记录
     */
    public List<AIGenerationLog> getAIGenerationLogs(Long projectId, int limit) {
        return executeWithExceptionHandling("获取AI生成记录", () -> {
            validateNotNull(projectId, "项目ID");
            validatePositive(limit, "限制数量");

            logInfo("获取AI生成记录: 项目={}, 限制={}", projectId, limit);
            return aiGenerationLogMapper.findByProjectIdOrderByCreatedAtDesc(projectId, limit);
        });
    }
}
