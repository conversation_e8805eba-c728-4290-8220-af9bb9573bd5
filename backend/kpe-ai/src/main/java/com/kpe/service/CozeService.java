package com.kpe.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.TypeReference;
import com.kpe.common.JsonUtils;
import com.kpe.dto.coze.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
public class CozeService {

    private static final Logger logger = LoggerFactory.getLogger(CozeService.class);

    private final WebClient webClient;

    @Value("${coze.api.key}")
    private String apiKey;

    @Value("${coze.api.bot-id}")
    private String defaultBotId;

    @Value("${coze.api.exam-analysis-bot-id}")
    private String examAnalysisBotId;

    @Value("${coze.api.polling.interval:10000}")
    private long pollingInterval;

    @Value("${coze.api.polling.max-attempts:30}")
    private int maxPollingAttempts;

    public CozeService(@Value("${coze.api.base-url}") String baseUrl,
                       @Value("${coze.api.timeout:180000}") int timeout) {
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
                .build();

        logger.info("🤖 扣子服务初始化完成");
        logger.info("   Base URL: {}", baseUrl);
        logger.info("   Timeout: {}ms", timeout);
        logger.info("   Polling Interval: {}ms ({}秒)", pollingInterval, pollingInterval / 1000);
        logger.info("   Max Polling Attempts: {} (总计{}分钟)", maxPollingAttempts, (maxPollingAttempts * pollingInterval) / 60000);
    }

    /**
     * 发起非流式对话并轮询获取结果
     *
     * @param botId          智能体ID
     * @param userId         用户ID
     * @param message        消息内容
     * @param conversationId 会话ID（可选）
     * @return 对话结果
     */
    public CompletableFuture<CozeChatResult> chatAndWaitForResult(String botId, String userId,
                                                                  String message, List<String> fileIfs, String conversationId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("🤖 发起扣子智能体对话...");
                logger.info("   Bot ID: {}", botId);
                logger.info("   User ID: {}", userId);
                logger.info("   Message: {}", message.length() > 100 ? message.substring(0, 100) + "..." : message);

                // 1. 发起对话
                CozeChatRequest request = new CozeChatRequest(botId, userId, message, fileIfs);
                if (conversationId != null) {
                    request.setConversationId(conversationId);
                }

                logger.info("📤 发送非流式请求...");
                CozeApiResponse<CozeChatData> chatResponse = sendChatRequest(request);

                if (!chatResponse.isSuccess()) {
                    throw new RuntimeException("发起对话失败: " + chatResponse.getMsg());
                }

                CozeChatData chatData = chatResponse.getData();
                String chatId = chatData.getChatId();
                String finalConversationId = chatData.getConversationId();

                logger.info("✅ 对话发起成功");
                logger.info("   Chat ID: {}", chatId);
                logger.info("   Conversation ID: {}", finalConversationId);
                logger.info("   初始状态: {}", chatData.getStatus());

                // 2. 轮询获取结果
                logger.info("🔄 开始轮询获取对话结果...");
                CozeRetrieveData retrieveData = pollForResult(finalConversationId, chatId);

                // 3. 处理结果
                return processChatResult(retrieveData, chatId, finalConversationId);

            } catch (Exception e) {
                logger.error("❌ 扣子对话失败: {}", e.getMessage());
                throw new RuntimeException("扣子对话失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 发送聊天请求
     */
    private CozeApiResponse<CozeChatData> sendChatRequest(CozeChatRequest request) {
        try {
            String requestJson = JSON.toJSONString(request);
            logger.debug("📤 请求数据: {}", requestJson);

            return webClient.post()
                    .uri("/v3/chat")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(requestJson)
                    .retrieve()
                    .bodyToMono(String.class)
                    .retryWhen(Retry.backoff(3, Duration.ofSeconds(1)))
                    .map(responseBody -> {
                        try {
                            logger.debug("📥 聊天响应原始数据: {}", responseBody);
                            TypeReference<CozeApiResponse<CozeChatData>> typeRef =
                                    new TypeReference<CozeApiResponse<CozeChatData>>() {
                                    };
                            CozeApiResponse<CozeChatData> chatResponse = JSON.parseObject(responseBody, typeRef);
                            logger.debug("📋 解析后的聊天响应: success={}, code={}, msg={}",
                                    chatResponse.isSuccess(), chatResponse.getCode(), chatResponse.getMsg());
                            if (chatResponse.getData() != null) {
                                CozeChatData data = chatResponse.getData();
                                logger.debug("📊 聊天数据详情: chatId={}, conversationId={}, status={}",
                                        data.getChatId(), data.getConversationId(), data.getStatus());
                            } else {
                                logger.warn("⚠️  聊天响应数据为空");
                            }
                            return chatResponse;
                        } catch (Exception e) {
                            logger.error("❌ 解析聊天响应失败: {}", e.getMessage());
                            logger.error("❌ 原始响应内容: {}", responseBody);
                            throw new RuntimeException("解析响应失败: " + e.getMessage(), e);
                        }
                    })
                    .block(Duration.ofMinutes(3));

        } catch (WebClientResponseException e) {
            logger.error("❌ HTTP请求失败: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("HTTP请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("❌ 发送请求失败: {}", e.getMessage());
            throw new RuntimeException("发送请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 轮询获取对话结果
     */
    private CozeRetrieveData pollForResult(String conversationId, String chatId) {
        int attempts = 0;

        while (attempts < maxPollingAttempts) {
            // 检查线程是否被中断
            if (Thread.currentThread().isInterrupted()) {
                logger.warn("⚠️  轮询任务被中断");
                throw new RuntimeException("轮询任务被中断");
            }

            attempts++;

            try {
                logger.debug("🔍 轮询第{}次，获取对话详情... ({}秒后下次轮询)", attempts, pollingInterval / 1000);

                CozeRetrieveResponse response = webClient.get()
                        .uri(uriBuilder -> uriBuilder
                                .path("/v3/chat/retrieve")
                                .queryParam("conversation_id", conversationId)
                                .queryParam("chat_id", chatId)
                                .build())
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                        .retrieve()
                        .bodyToMono(String.class)
                        .retryWhen(Retry.backoff(2, Duration.ofSeconds(1)))
                        .map(responseBody -> {
                            try {
                                logger.debug("📥 轮询响应原始数据: {}", responseBody);
                                CozeRetrieveResponse retrieveResponse = JSON.parseObject(responseBody, CozeRetrieveResponse.class);
                                logger.debug("📋 解析后的响应对象: success={}, code={}, msg={}",
                                        retrieveResponse.isSuccess(), retrieveResponse.getCode(), retrieveResponse.getMsg());
                                if (retrieveResponse.getData() != null) {
                                    logger.debug("📊 响应数据详情: status={}, chatId={}, conversationId={}",
                                            retrieveResponse.getData().getStatus(),
                                            retrieveResponse.getData().getChatId(),
                                            retrieveResponse.getData().getConversationId());
                                } else {
                                    logger.warn("⚠️  响应数据为空");
                                }
                                return retrieveResponse;
                            } catch (Exception e) {
                                logger.error("❌ 解析轮询响应失败: {}", e.getMessage());
                                logger.error("❌ 原始响应内容: {}", responseBody);
                                throw new RuntimeException("解析轮询响应失败: " + e.getMessage(), e);
                            }
                        })
                        .block(Duration.ofSeconds(30));

                if (response == null || !response.isSuccess()) {
                    logger.warn("⚠️  获取对话详情失败: {}", response != null ? response.getMsg() : "响应为空");
                    Thread.sleep(pollingInterval);
                    continue;
                }

                CozeRetrieveData data = response.getData();
                String status = data.getStatus();

                logger.debug("   状态: {}, 尝试次数: {}/{}", status, attempts, maxPollingAttempts);

                // 检查是否完成
                if ("completed".equals(status)) {
                    logger.info("✅ 对话完成，总轮询次数: {}", attempts);
                    logger.debug("   对话详情: {}", data);

                    // 获取真正的消息数据
                    logger.info("📋 获取完整的消息列表...");
                    String json = getChatMessages(conversationId, chatId);
                    data.setMessage(json);
                    return data;
                } else if ("failed".equals(status) || "cancelled".equals(status)) {
                    String errorMsg = data.getLastError() != null ?
                            data.getLastError().getMsg() : "对话失败";
                    throw new RuntimeException("对话失败: " + errorMsg);
                }

                // 等待下次轮询
                try {
                    Thread.sleep(pollingInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("⚠️  轮询等待被中断");
                    throw new RuntimeException("轮询被中断", e);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("⚠️  轮询被中断");
                throw new RuntimeException("轮询被中断", e);
            } catch (Exception e) {
                logger.warn("⚠️  轮询出错: {}", e.getMessage());
                if (attempts >= maxPollingAttempts) {
                    throw new RuntimeException("轮询超时，对话可能仍在处理中", e);
                }
                try {
                    Thread.sleep(pollingInterval);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    logger.warn("⚠️  轮询等待被中断");
                    throw new RuntimeException("轮询被中断", ie);
                }
            }
        }

        throw new RuntimeException("轮询超时，已尝试 " + maxPollingAttempts + " 次");
    }

    /**
     * 获取对话消息列表
     */
    private String getChatMessages(String conversationId, String chatId) {
        try {
            logger.debug("🔍 获取消息列表: conversationId={}, chatId={}", conversationId, chatId);

            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path("/v1/conversation/message/list")
                            .queryParam("conversation_id", conversationId)
                            .queryParam("chat_id", chatId)
                            .build())
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                    .retrieve()
                    .bodyToMono(String.class)
                    .retryWhen(Retry.backoff(2, Duration.ofSeconds(1)))
                    .block(Duration.ofSeconds(30));

            //response：{"code":0,"data":[],"detail":{"logid":"2025060419430711EE4EF4B74969244973"},"first_id":"7512058167224975397","has_more":false,"last_id":"7512058155619221541","msg":""}
            //解析response提取data返回
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            if (responseMap.get("code").equals(0)) {
                return responseMap.get("data").toString();
            }
            return null;
        } catch (Exception e) {
            logger.error("❌ 获取消息列表失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 处理消息内容，提取```json```包裹的内容
     */
    private String processMessageContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        JSONArray array = JSON.parseArray(content);
        content = array.getJSONObject(0).getString("content");

        logger.debug("🔍 处理消息内容: {}", content.length() > 200 ? content.substring(0, 200) + "..." : content);

        // 查找```json```包裹的内容
        String jsonPattern = "```json\\s*([\\s\\S]*?)```";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(jsonPattern, java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = pattern.matcher(content);

        if (content.contains("```json") && matcher.find()) {
            String jsonContent = matcher.group(1).trim();
            logger.debug("🎯 提取到JSON内容: {}", jsonContent.length() > 200 ? jsonContent.substring(0, 200) + "..." : jsonContent);
            return jsonContent;
        }

        // 如果没有找到```json```包裹，尝试查找普通的```包裹
        String codePattern = "```\\s*([\\s\\S]*?)```";
        pattern = java.util.regex.Pattern.compile(codePattern);
        matcher = pattern.matcher(content);

        if (matcher.find()) {
            String codeContent = matcher.group(1).trim();
            // 检查是否是JSON格式
            if (isValidJson(codeContent)) {
                logger.debug("🎯 提取到代码块中的JSON内容: {}", codeContent.length() > 200 ? codeContent.substring(0, 200) + "..." : codeContent);
                return codeContent;
            }
        }

        // 如果都没有找到，返回原内容
        logger.debug("📝 未找到JSON包裹，返回原内容");
        return content;
    }

    /**
     * 检查字符串是否是有效的JSON
     */
    private boolean isValidJson(String jsonString) {
        return JsonUtils.isValidJson(jsonString);
    }

    /**
     * 处理聊天结果
     */
    private CozeChatResult processChatResult(CozeRetrieveData data, String chatId, String conversationId) {
        logger.debug("🔄 开始处理对话结果...");
        logger.debug("📊 原始数据状态: {}", data.getStatus());

        CozeChatResult result = new CozeChatResult();
        result.setChatId(chatId);
        result.setConversationId(conversationId);
        result.setStatus(data.getStatus());
        result.setCompletedAt(data.getCompletedAt());
        result.setUsage(data.getUsage());

        // 提取AI回复内容
        if (data.getMessage() != null) {
            //如果内容是```json```包裹的，需要提取出来
            String processedMessage = processMessageContent(data.getMessage());
            result.setMessage(processedMessage);

            logger.info("📝 AI回复内容: {}", processedMessage.length() > 200 ?
                    processedMessage.substring(0, 200) + "..." : processedMessage);
        } else {
            logger.warn("⚠️  没有找到任何消息");
        }

        logger.debug("✅ 对话结果处理完成");
        return result;
    }

    public String getDefaultBotId() {
        return defaultBotId;
    }

    public String getExamAnalysisBotId() {
        return examAnalysisBotId;
    }

    /**
     * 上传文件到扣子
     *
     * @param fileBytes 文件字节数组
     * @param fileName  文件名
     * @return 文件ID
     */
    public String uploadFile(byte[] fileBytes, String fileName) {
        try {
            logger.info("📤 上传文件到扣子...");
            logger.info("   文件名: {}", fileName);
            logger.info("   文件大小: {} bytes", fileBytes.length);

            // 创建multipart请求
            MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
            parts.add("file", new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return fileName;
                }
            });

            String response = webClient.post()
                    .uri("/v1/files/upload")
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .bodyValue(parts)
                    .retrieve()
                    .bodyToMono(String.class)
                    .retryWhen(Retry.backoff(3, Duration.ofSeconds(1)))
                    .block(Duration.ofMinutes(2));

            logger.debug("📥 文件上传响应: {}", response);

            // 解析响应获取文件ID
            @SuppressWarnings("unchecked")
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            if (responseMap.containsKey("data")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
                if (dataMap != null && dataMap.containsKey("id")) {
                    String fileId = dataMap.get("id").toString();
                    logger.info("✅ 文件上传成功，文件ID: {}", fileId);
                    return fileId;
                }
            }
            throw new RuntimeException("文件上传失败，响应中没有文件ID");

        } catch (Exception e) {
            logger.error("❌ 文件上传失败: {}", e.getMessage());
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }
}
