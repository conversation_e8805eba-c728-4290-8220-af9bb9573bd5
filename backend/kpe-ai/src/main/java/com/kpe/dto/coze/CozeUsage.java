package com.kpe.dto.coze;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 扣子使用统计
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CozeUsage {
    @JsonProperty("token_count")
    private Integer tokenCount;

    @JsonProperty("output_count")
    private Integer outputCount;

    @JsonProperty("input_count")
    private Integer inputCount;

    public Integer getTokenCount() {
        return tokenCount;
    }

    public void setTokenCount(Integer tokenCount) {
        this.tokenCount = tokenCount;
    }

    public Integer getOutputCount() {
        return outputCount;
    }

    public void setOutputCount(Integer outputCount) {
        this.outputCount = outputCount;
    }

    public Integer getInputCount() {
        return inputCount;
    }

    public void setInputCount(Integer inputCount) {
        this.inputCount = inputCount;
    }
}
