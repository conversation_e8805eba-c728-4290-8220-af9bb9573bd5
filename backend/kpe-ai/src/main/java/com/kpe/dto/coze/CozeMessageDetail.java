package com.kpe.dto.coze;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * 扣子消息详情
 */
public class CozeMessageDetail {

    private String id;

    @JSONField(name = "conversation_id")
    private String conversationId;

    @JSONField(name = "bot_id")
    private String botId;

    @JSO<PERSON>ield(name = "chat_id")
    private String chatId;

    @JSONField(name = "meta_data")
    private Object metaData;

    private String role;
    private String content;

    @JSO<PERSON>ield(name = "content_type")
    private String contentType;

    @JSONField(name = "created_at")
    private Long createdAt;

    @JSONField(name = "updated_at")
    private Long updatedAt;

    private String type;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getBotId() {
        return botId;
    }

    public void setBotId(String botId) {
        this.botId = botId;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public Object getMetaData() {
        return metaData;
    }

    public void setMetaData(Object metaData) {
        this.metaData = metaData;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
