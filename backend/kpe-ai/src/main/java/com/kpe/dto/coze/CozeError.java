package com.kpe.dto.coze;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 扣子错误信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CozeError {
    private int code;
    private String msg;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
