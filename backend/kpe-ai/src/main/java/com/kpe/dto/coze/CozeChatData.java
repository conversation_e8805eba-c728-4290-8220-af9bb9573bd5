package com.kpe.dto.coze;

import com.alibaba.fastjson2.annotation.JSONField;

/**
 * 扣子聊天响应数据
 */
public class CozeChatData {

    @J<PERSON>NField(name = "id")
    private String chatId;

    @J<PERSON>NField(name = "conversation_id")
    private String conversationId;

    private String status;

    @JSONField(name = "created_at")
    private Long createdAt;

    @JSONField(name = "completed_at")
    private Long completedAt;

    @JSONField(name = "failed_at")
    private Long failedAt;

    @JSONField(name = "meta_data")
    private Object metaData;

    @J<PERSON>NField(name = "last_error")
    private CozeError lastError;

    @J<PERSON><PERSON>ield(name = "required_action")
    private Object requiredAction;

    @JSONField(name = "usage")
    private CozeUsage usage;

    // Getters and Setters
    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(Long completedAt) {
        this.completedAt = completedAt;
    }

    public Long getFailedAt() {
        return failedAt;
    }

    public void setFailedAt(Long failedAt) {
        this.failedAt = failedAt;
    }

    public Object getMetaData() {
        return metaData;
    }

    public void setMetaData(Object metaData) {
        this.metaData = metaData;
    }

    public CozeError getLastError() {
        return lastError;
    }

    public void setLastError(CozeError lastError) {
        this.lastError = lastError;
    }

    public Object getRequiredAction() {
        return requiredAction;
    }

    public void setRequiredAction(Object requiredAction) {
        this.requiredAction = requiredAction;
    }

    public CozeUsage getUsage() {
        return usage;
    }

    public void setUsage(CozeUsage usage) {
        this.usage = usage;
    }
}
