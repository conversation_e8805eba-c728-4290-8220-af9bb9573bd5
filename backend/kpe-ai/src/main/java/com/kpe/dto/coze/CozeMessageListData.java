package com.kpe.dto.coze;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 扣子消息列表数据
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CozeMessageListData {

    private List<CozeMessageDetail> data;

    @JsonProperty("first_id")
    private String firstId;

    @JsonProperty("last_id")
    private String lastId;

    @JsonProperty("has_more")
    private Boolean hasMore;

    // Constructors
    public CozeMessageListData() {
    }

    // Getters and Setters
    public List<CozeMessageDetail> getData() {
        return data;
    }

    public void setData(List<CozeMessageDetail> data) {
        this.data = data;
    }

    public String getFirstId() {
        return firstId;
    }

    public void setFirstId(String firstId) {
        this.firstId = firstId;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }
}
