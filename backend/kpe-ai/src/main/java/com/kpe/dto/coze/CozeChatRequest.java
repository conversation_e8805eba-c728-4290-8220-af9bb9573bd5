package com.kpe.dto.coze;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;

import java.util.Collections;
import java.util.List;

/**
 * 扣子聊天请求
 */
public class CozeChatRequest {

    @J<PERSON><PERSON>ield(name = "bot_id")
    private String botId;

    @JSONField(name = "user_id")
    private String userId;

    @JSO<PERSON>ield(name = "stream")
    private boolean stream = false;  // 非流式

    @<PERSON>SO<PERSON>ield(name = "auto_save_history")
    private boolean autoSaveHistory = true;

    @<PERSON><PERSON><PERSON><PERSON>(name = "conversation_id")
    private String conversationId;

    @J<PERSON><PERSON>ield(name = "additional_messages")
    private List<CozeMessage> additionalMessages;

    // Constructors
    public CozeChatRequest() {
    }

    public CozeChatRequest(String botId, String userId, String message, List<String> fileIds) {
        this.botId = botId;
        this.userId = userId;
        if (fileIds != null) {
            this.additionalMessages = Collections.singletonList(new MultiModalMessage("user", message, fileIds, "object_string"));
        } else {
            this.additionalMessages = Collections.singletonList(new TextMessage("user", message, "text"));
        }
    }

    // Getters and Setters
    public String getBotId() {
        return botId;
    }

    public void setBotId(String botId) {
        this.botId = botId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public boolean isStream() {
        return stream;
    }

    public void setStream(boolean stream) {
        this.stream = stream;
    }

    public boolean isAutoSaveHistory() {
        return autoSaveHistory;
    }

    public void setAutoSaveHistory(boolean autoSaveHistory) {
        this.autoSaveHistory = autoSaveHistory;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public List<CozeMessage> getAdditionalMessages() {
        return additionalMessages;
    }

    public void setAdditionalMessages(List<CozeMessage> additionalMessages) {
        this.additionalMessages = additionalMessages;
    }
}

abstract class CozeMessage {

}

/**
 * 扣子多模态消息
 * 范例
 * {
 * "role": "user",
 * "content": "[{\"type\":\"text\",\"text\":\"你好我有一个帽衫，我想问问它好看么，你帮我看看\"},{\"type\":\"image\",\"file_id\":\"{{file_id_2}}\"},{\"type\":\"file\",\"file_id\":\"{{file_id_1}}\"}]",
 * "content_type": "object_string"
 * }
 */
class MultiModalMessage extends CozeMessage {
    private String role;
    private String content;

    @JSONField(name = "content_type")
    private String contentType;

    public MultiModalMessage() {
    }

    public MultiModalMessage(String role, String text, List<String> fileIds, String contentType) {
        this.role = role;
        this.contentType = contentType;
        JSONArray content = new JSONArray();
        JSONObject textJSON = new JSONObject();
        textJSON.put("type", "text");
        textJSON.put("text", text);
        content.add(textJSON);
        if (fileIds != null) {
            for (String fileId : fileIds) {
                JSONObject fileJSON = new JSONObject();
                fileJSON.put("type", "image");
                fileJSON.put("file_id", fileId);
                content.add(fileJSON);
            }
        }
        this.content = content.toJSONString();
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
}

/**
 * 扣子文本消息
 * 范例
 * {
 * "role": "user",
 * "content": "搜几个最新的军事新闻",
 * "content_type": "text"
 * }
 */
class TextMessage extends CozeMessage {
    private String role;
    private String content;

    @JSONField(name = "content_type")
    private String contentType;

    public TextMessage() {
    }

    public TextMessage(String role, String content, String contentType) {
        this.role = role;
        this.content = content;
        this.contentType = contentType;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
}
