package com.kpe.dto.coze;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 扣子聊天结果
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CozeChatResult {

    private String chatId;
    private String conversationId;
    private String status;
    private String message;
    private Long completedAt;
    private CozeUsage usage;

    // Constructors
    public CozeChatResult() {
    }

    // Getters and Setters
    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(Long completedAt) {
        this.completedAt = completedAt;
    }

    public CozeUsage getUsage() {
        return usage;
    }

    public void setUsage(CozeUsage usage) {
        this.usage = usage;
    }
}
