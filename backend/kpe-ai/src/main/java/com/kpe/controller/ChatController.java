package com.kpe.controller;

import com.kpe.common.BaseController;
import com.kpe.dto.ApiResponse;
import com.kpe.dto.coze.CozeChatResult;
import com.kpe.facade.AIServiceFacade;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;

@RestController
@RequestMapping("/api/chat")
@CrossOrigin(origins = "*")
public class ChatController extends BaseController {

    @Autowired
    private AIServiceFacade aiServiceFacade;

    /**
     * 扣子智能体对话接口
     */
    @PostMapping
    public DeferredResult<ResponseEntity<ApiResponse<CozeChatResult>>> chat(
            @Valid @RequestBody ChatRequest request) {

        // 获取策略超时时间
        long timeoutMs = aiServiceFacade.getStrategyTimeout("knowledge_generation");
        DeferredResult<ResponseEntity<ApiResponse<CozeChatResult>>> deferredResult =
                createDeferredResult(timeoutMs, "对话");

        // 确定使用的User ID
        String targetUserId = (request.getUserId() != null && !request.getUserId().isEmpty())
                ? request.getUserId()
                : "chat_user_" + System.currentTimeMillis();

        logger.info("🤖 开始对话: User={}, 消息={}", targetUserId, request.getMessage());

        // 异步执行对话任务
        aiServiceFacade.executeAIChat("knowledge_generation", 1L, request.getMessage(),
                        targetUserId, request.getFileIds(), request.getConversationId())
                .thenAccept(chatResult -> setDeferredSuccess(deferredResult, chatResult, "对话"))
                .exceptionally(throwable -> {
                    setDeferredError(deferredResult, throwable, "对话");
                    return null;
                });

        return deferredResult;
    }

    // Request DTO
    public static class ChatRequest {
        @NotBlank(message = "消息内容不能为空")
        private String message;
        private List<String> fileIds;
        private String botId;
        private String userId;
        private String conversationId;

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getBotId() {
            return botId;
        }

        public void setBotId(String botId) {
            this.botId = botId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public List<String> getFileIds() {
            return fileIds;
        }

        public void setFileIds(List<String> fileIds) {
            this.fileIds = fileIds;
        }
    }
}
