package com.kpe.strategy;

import com.kpe.dto.coze.CozeChatResult;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * AI服务策略接口
 */
public interface AIServiceStrategy {

    /**
     * 获取策略类型
     */
    String getStrategyType();

    /**
     * 执行AI对话
     */
    CompletableFuture<CozeChatResult> executeChat(String botId, String userId, String message,
                                                  List<String> fileIds, String conversationId);

    /**
     * 获取Bot ID
     */
    String getBotId();

    /**
     * 是否支持文件上传
     */
    boolean supportsFileUpload();

    /**
     * 获取超时时间（毫秒）
     */
    long getTimeoutMs();
}
