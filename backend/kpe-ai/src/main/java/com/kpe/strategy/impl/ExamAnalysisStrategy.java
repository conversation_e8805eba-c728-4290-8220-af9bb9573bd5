package com.kpe.strategy.impl;

import com.kpe.dto.coze.CozeChatResult;
import com.kpe.service.CozeService;
import com.kpe.strategy.AIServiceStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 考卷分析策略
 */
@Component
public class ExamAnalysisStrategy implements AIServiceStrategy {

    @Autowired
    private CozeService cozeService;

    @Value("${coze.api.exam-analysis-bot-id}")
    private String examAnalysisBotId;

    @Override
    public String getStrategyType() {
        return "exam_analysis";
    }

    @Override
    public CompletableFuture<CozeChatResult> executeChat(String botId, String userId, String message,
                                                         List<String> fileIds, String conversationId) {
        String targetBotId = (botId != null && !botId.isEmpty()) ? botId : examAnalysisBotId;
        return cozeService.chatAndWaitForResult(targetBotId, userId, message, fileIds, conversationId);
    }

    @Override
    public String getBotId() {
        return examAnalysisBotId;
    }

    @Override
    public boolean supportsFileUpload() {
        return true;
    }

    @Override
    public long getTimeoutMs() {
        return 300000; // 5分钟
    }
}
