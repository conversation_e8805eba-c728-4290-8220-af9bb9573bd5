package com.kpe.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * AI服务策略工厂
 */
@Component
public class AIServiceStrategyFactory {

    private final Map<String, AIServiceStrategy> strategies;

    @Autowired
    public AIServiceStrategyFactory(List<AIServiceStrategy> strategyList) {
        this.strategies = strategyList.stream()
                .collect(Collectors.toMap(AIServiceStrategy::getStrategyType, Function.identity()));
    }

    /**
     * 根据策略类型获取策略实例
     */
    public AIServiceStrategy getStrategy(String strategyType) {
        AIServiceStrategy strategy = strategies.get(strategyType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的AI服务策略类型: " + strategyType);
        }
        return strategy;
    }

    /**
     * 获取所有可用的策略类型
     */
    public List<String> getAvailableStrategyTypes() {
        return strategies.keySet().stream().sorted().collect(Collectors.toList());
    }

    /**
     * 检查策略类型是否存在
     */
    public boolean hasStrategy(String strategyType) {
        return strategies.containsKey(strategyType);
    }
}
