package com.kpe.strategy.impl;

import com.kpe.dto.coze.CozeChatResult;
import com.kpe.service.CozeService;
import com.kpe.strategy.AIServiceStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 知识点生成策略
 */
@Component
public class KnowledgeGenerationStrategy implements AIServiceStrategy {

    @Autowired
    private CozeService cozeService;

    @Value("${coze.api.bot-id}")
    private String defaultBotId;

    @Override
    public String getStrategyType() {
        return "knowledge_generation";
    }

    @Override
    public CompletableFuture<CozeChatResult> executeChat(String botId, String userId, String message,
                                                         List<String> fileIds, String conversationId) {
        String targetBotId = (botId != null && !botId.isEmpty()) ? botId : defaultBotId;
        return cozeService.chatAndWaitForResult(targetBotId, userId, message, fileIds, conversationId);
    }

    @Override
    public String getBotId() {
        return defaultBotId;
    }

    @Override
    public boolean supportsFileUpload() {
        return false;
    }

    @Override
    public long getTimeoutMs() {
        return 300000; // 5分钟
    }
}
