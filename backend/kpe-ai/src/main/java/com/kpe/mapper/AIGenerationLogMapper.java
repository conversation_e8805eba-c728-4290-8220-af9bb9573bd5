package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.AIGenerationLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * AIGenerationLog Mapper接口
 */
@Mapper
public interface AIGenerationLogMapper extends BaseMapper<AIGenerationLog> {

    /**
     * 插入AI生成日志（处理jsonb类型）
     */
    @Insert("INSERT INTO ai_generation_logs (project_id, prompt, response, bot_id, generation_type, status, error_message, created_at, created_by) " +
            "VALUES (#{projectId}, #{prompt}, " +
            "CASE WHEN #{response} IS NULL THEN NULL ELSE #{response}::jsonb END, " +
            "#{botId}, #{generationType}, #{status}, #{errorMessage}, #{createdAt}, #{createdBy})")
    int insertWithJsonb(AIGenerationLog log);

    /**
     * 根据项目ID查找AI生成日志（分页）
     */
    @Select("SELECT * FROM ai_generation_logs WHERE project_id = #{projectId} ORDER BY created_at DESC LIMIT #{limit}")
    List<AIGenerationLog> findByProjectIdOrderByCreatedAtDesc(@Param("projectId") Long projectId, @Param("limit") int limit);

    /**
     * 根据项目ID和状态查找AI生成日志（分页）
     */
    @Select("SELECT * FROM ai_generation_logs WHERE project_id = #{projectId} AND status = #{status} ORDER BY created_at DESC LIMIT #{limit}")
    List<AIGenerationLog> findByProjectIdAndStatusOrderByCreatedAtDesc(@Param("projectId") Long projectId, @Param("status") String status, @Param("limit") int limit);

    /**
     * 根据项目ID和生成类型查找AI生成日志（分页）
     */
    @Select("SELECT * FROM ai_generation_logs WHERE project_id = #{projectId} AND generation_type = #{generationType} ORDER BY created_at DESC LIMIT #{limit}")
    List<AIGenerationLog> findByProjectIdAndGenerationTypeOrderByCreatedAtDesc(@Param("projectId") Long projectId, @Param("generationType") String generationType, @Param("limit") int limit);

    /**
     * 更新AI生成日志（处理jsonb类型）
     */
    @Update("UPDATE ai_generation_logs SET " +
            "project_id = #{projectId}, " +
            "prompt = #{prompt}, " +
            "response = CASE WHEN #{response} IS NULL THEN NULL ELSE #{response}::jsonb END, " +
            "bot_id = #{botId}, " +
            "generation_type = #{generationType}, " +
            "status = #{status}, " +
            "error_message = #{errorMessage}, " +
            "created_at = #{createdAt}, " +
            "created_by = #{createdBy} " +
            "WHERE id = #{id}")
    int updateWithJsonb(AIGenerationLog log);

    /**
     * 根据项目ID删除所有AI生成日志
     */
    @Delete("DELETE FROM ai_generation_logs WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") Long projectId);
}
