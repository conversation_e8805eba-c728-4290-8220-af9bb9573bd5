package com.kpe.dto.flashcard;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 卡片请求DTO
 */
public class FlashcardRequest {

    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @NotBlank(message = "知识点ID不能为空")
    private String knowledgePointId;

    @NotBlank(message = "卡片标题不能为空")
    private String title;

    @NotBlank(message = "正面内容不能为空")
    private String frontContent;

    private String backContent;

    @NotNull(message = "难度等级不能为空")
    @Min(value = 1, message = "难度等级最小为1")
    @Max(value = 5, message = "难度等级最大为5")
    private Integer difficulty;

    // 构造函数
    public FlashcardRequest() {
    }

    // Getters and Setters
    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFrontContent() {
        return frontContent;
    }

    public void setFrontContent(String frontContent) {
        this.frontContent = frontContent;
    }

    public String getBackContent() {
        return backContent;
    }

    public void setBackContent(String backContent) {
        this.backContent = backContent;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    @Override
    public String toString() {
        return "FlashcardRequest{" +
                "projectId=" + projectId +
                ", knowledgePointId='" + knowledgePointId + '\'' +
                ", title='" + title + '\'' +
                ", difficulty=" + difficulty +
                '}';
    }
}

/**
 * 卡片更新请求DTO
 */
class FlashcardUpdateRequest {

    @NotBlank(message = "卡片标题不能为空")
    private String title;

    @NotBlank(message = "正面内容不能为空")
    private String frontContent;

    private String backContent;

    @NotNull(message = "难度等级不能为空")
    @Min(value = 1, message = "难度等级最小为1")
    @Max(value = 5, message = "难度等级最大为5")
    private Integer difficulty;

    // 构造函数
    public FlashcardUpdateRequest() {
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFrontContent() {
        return frontContent;
    }

    public void setFrontContent(String frontContent) {
        this.frontContent = frontContent;
    }

    public String getBackContent() {
        return backContent;
    }

    public void setBackContent(String backContent) {
        this.backContent = backContent;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    @Override
    public String toString() {
        return "FlashcardUpdateRequest{" +
                "title='" + title + '\'' +
                ", difficulty=" + difficulty +
                '}';
    }
}

/**
 * 卡片完整更新请求DTO（包含知识点信息）
 */
class FlashcardFullUpdateRequest {

    private String knowledgePointId;

    @NotBlank(message = "卡片标题不能为空")
    private String title;

    @NotBlank(message = "正面内容不能为空")
    private String frontContent;

    private String backContent;

    @NotNull(message = "难度等级不能为空")
    @Min(value = 1, message = "难度等级最小为1")
    @Max(value = 5, message = "难度等级最大为5")
    private Integer difficulty;

    // 构造函数
    public FlashcardFullUpdateRequest() {
    }

    // Getters and Setters
    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFrontContent() {
        return frontContent;
    }

    public void setFrontContent(String frontContent) {
        this.frontContent = frontContent;
    }

    public String getBackContent() {
        return backContent;
    }

    public void setBackContent(String backContent) {
        this.backContent = backContent;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    @Override
    public String toString() {
        return "FlashcardFullUpdateRequest{" +
                "knowledgePointId='" + knowledgePointId + '\'' +
                ", title='" + title + '\'' +
                ", difficulty=" + difficulty +
                '}';
    }
}

/**
 * 学习记录请求DTO
 */
class StudyRecordRequest {

    @NotNull(message = "卡片ID不能为空")
    private Long flashcardId;

    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @NotNull(message = "答题结果不能为空")
    private Boolean isCorrect;

    private Integer responseTimeSeconds;

    @Min(value = 1, message = "难度评级最小为1")
    @Max(value = 5, message = "难度评级最大为5")
    private Integer difficultyRating;

    // 构造函数
    public StudyRecordRequest() {
    }

    // Getters and Setters
    public Long getFlashcardId() {
        return flashcardId;
    }

    public void setFlashcardId(Long flashcardId) {
        this.flashcardId = flashcardId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Boolean getIsCorrect() {
        return isCorrect;
    }

    public void setIsCorrect(Boolean isCorrect) {
        this.isCorrect = isCorrect;
    }

    public Integer getResponseTimeSeconds() {
        return responseTimeSeconds;
    }

    public void setResponseTimeSeconds(Integer responseTimeSeconds) {
        this.responseTimeSeconds = responseTimeSeconds;
    }

    public Integer getDifficultyRating() {
        return difficultyRating;
    }

    public void setDifficultyRating(Integer difficultyRating) {
        this.difficultyRating = difficultyRating;
    }

    @Override
    public String toString() {
        return "StudyRecordRequest{" +
                "flashcardId=" + flashcardId +
                ", projectId=" + projectId +
                ", isCorrect=" + isCorrect +
                ", responseTimeSeconds=" + responseTimeSeconds +
                ", difficultyRating=" + difficultyRating +
                '}';
    }
}
