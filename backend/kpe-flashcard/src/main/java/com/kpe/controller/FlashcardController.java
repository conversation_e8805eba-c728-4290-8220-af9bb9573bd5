package com.kpe.controller;

import com.kpe.common.BaseController;
import com.kpe.dto.ApiResponse;
import com.kpe.dto.flashcard.FlashcardRequest;
import com.kpe.entity.Flashcard;
import com.kpe.entity.FlashcardStudyRecord;
import com.kpe.service.FlashcardService;
import com.kpe.service.OssService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 卡片控制器
 */
@RestController
@RequestMapping("/api/flashcards")
@CrossOrigin(origins = "*")
public class FlashcardController extends BaseController {

    @Autowired
    private FlashcardService flashcardService;

    @Autowired(required = false)
    private OssService ossService;

    /**
     * 创建卡片
     */
    @PostMapping
    public ApiResponse<Flashcard> createFlashcard(@Valid @RequestBody FlashcardRequest request) {
        try {
            logger.info("📝 接收创建卡片请求: {}", request);

            Flashcard flashcard = flashcardService.createFlashcard(
                    request.getProjectId(),
                    request.getKnowledgePointId(),
                    request.getTitle(),
                    request.getFrontContent(),
                    request.getBackContent(),
                    request.getDifficulty(),
                    "system" // 暂时使用系统用户，后续接入认证系统后获取真实用户
            );

            return ApiResponse.success(flashcard, "卡片创建成功");

        } catch (Exception e) {
            logger.error("❌ 创建卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("创建卡片失败: " + e.getMessage());
        }
    }

    /**
     * 更新卡片
     */
    @PutMapping("/{id}")
    public ApiResponse<Flashcard> updateFlashcard(@PathVariable Long id,
                                                  @RequestParam Long projectId,
                                                  @Valid @RequestBody FlashcardUpdateRequest request) {
        try {
            logger.info("📝 接收更新卡片请求: id={}, projectId={}", id, projectId);

            Optional<Flashcard> flashcard = flashcardService.updateFlashcard(
                    id, projectId, request.getTitle(), request.getFrontContent(),
                    request.getBackContent(), request.getDifficulty()
            );

            if (flashcard.isPresent()) {
                return ApiResponse.success(flashcard.get(), "卡片更新成功");
            } else {
                return ApiResponse.error("卡片不存在或无权限访问");
            }

        } catch (Exception e) {
            logger.error("❌ 更新卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("更新卡片失败: " + e.getMessage());
        }
    }

    /**
     * 完整更新卡片（包含知识点信息）
     */
    @PutMapping("/{id}/full")
    public ApiResponse<Flashcard> updateFlashcardFull(@PathVariable Long id,
                                                      @RequestParam Long projectId,
                                                      @Valid @RequestBody FlashcardFullUpdateRequest request) {
        try {
            logger.info("📝 接收完整更新卡片请求: id={}, projectId={}", id, projectId);

            Optional<Flashcard> flashcard = flashcardService.updateFlashcard(
                    id, projectId, request.getKnowledgePointId(), request.getTitle(),
                    request.getFrontContent(), request.getBackContent(), request.getDifficulty()
            );

            if (flashcard.isPresent()) {
                return ApiResponse.success(flashcard.get(), "卡片更新成功");
            } else {
                return ApiResponse.error("卡片不存在或无权限访问");
            }

        } catch (Exception e) {
            logger.error("❌ 完整更新卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("完整更新卡片失败: " + e.getMessage());
        }
    }

    /**
     * 删除卡片
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteFlashcard(@PathVariable Long id, @RequestParam Long projectId) {
        try {
            logger.info("🗑️ 接收删除卡片请求: id={}, projectId={}", id, projectId);

            boolean success = flashcardService.deleteFlashcard(id, projectId);
            if (success) {
                return ApiResponse.success(null, "卡片删除成功");
            } else {
                return ApiResponse.error("卡片不存在或无权限访问");
            }

        } catch (Exception e) {
            logger.error("❌ 删除卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("删除卡片失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目的所有卡片
     */
    @GetMapping
    public ApiResponse<List<Flashcard>> getFlashcards(@RequestParam Long projectId) {
        try {
            logger.info("📋 获取项目卡片列表: projectId={}", projectId);

            List<Flashcard> flashcards = flashcardService.getFlashcardsByProjectId(projectId);
            return ApiResponse.success(flashcards, "获取卡片列表成功");

        } catch (Exception e) {
            logger.error("❌ 获取卡片列表失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取卡片列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据知识点获取卡片
     */
    @GetMapping("/knowledge-point/{knowledgePointId}")
    public ApiResponse<List<Flashcard>> getFlashcardsByKnowledgePoint(@PathVariable String knowledgePointId,
                                                                      @RequestParam Long projectId) {
        try {
            logger.info("📋 获取知识点卡片: projectId={}, knowledgePointId={}", projectId, knowledgePointId);

            List<Flashcard> flashcards = flashcardService.getFlashcardsByKnowledgePoint(projectId, knowledgePointId);
            return ApiResponse.success(flashcards, "获取知识点卡片成功");

        } catch (Exception e) {
            logger.error("❌ 获取知识点卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取知识点卡片失败: " + e.getMessage());
        }
    }

    /**
     * 根据难度获取卡片
     */
    @GetMapping("/difficulty/{difficulty}")
    public ApiResponse<List<Flashcard>> getFlashcardsByDifficulty(@PathVariable Integer difficulty,
                                                                  @RequestParam Long projectId) {
        try {
            logger.info("📋 获取难度卡片: projectId={}, difficulty={}", projectId, difficulty);

            List<Flashcard> flashcards = flashcardService.getFlashcardsByDifficulty(projectId, difficulty);
            return ApiResponse.success(flashcards, "获取难度卡片成功");

        } catch (Exception e) {
            logger.error("❌ 获取难度卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取难度卡片失败: " + e.getMessage());
        }
    }

    /**
     * 获取需要复习的卡片
     */
    @GetMapping("/need-review")
    public ApiResponse<List<Flashcard>> getCardsNeedReview(@RequestParam Long projectId) {
        try {
            logger.info("📋 获取需要复习的卡片: projectId={}", projectId);

            List<Flashcard> flashcards = flashcardService.getCardsNeedReview(projectId);
            return ApiResponse.success(flashcards, "获取需要复习的卡片成功");

        } catch (Exception e) {
            logger.error("❌ 获取需要复习的卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取需要复习的卡片失败: " + e.getMessage());
        }
    }

    /**
     * 获取从未学习过的卡片
     */
    @GetMapping("/never-studied")
    public ApiResponse<List<Flashcard>> getNeverStudiedCards(@RequestParam Long projectId) {
        try {
            logger.info("📋 获取从未学习的卡片: projectId={}", projectId);

            List<Flashcard> flashcards = flashcardService.getNeverStudiedCards(projectId);
            return ApiResponse.success(flashcards, "获取从未学习的卡片成功");

        } catch (Exception e) {
            logger.error("❌ 获取从未学习的卡片失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取从未学习的卡片失败: " + e.getMessage());
        }
    }

    /**
     * 记录学习结果
     */
    @PostMapping("/study-record")
    public ApiResponse<FlashcardStudyRecord> recordStudyResult(@Valid @RequestBody StudyRecordRequest request) {
        try {
            logger.info("📝 记录学习结果: {}", request);

            FlashcardStudyRecord record = flashcardService.recordStudyResult(
                    request.getFlashcardId(),
                    request.getProjectId(),
                    request.getIsCorrect(),
                    request.getResponseTimeSeconds(),
                    request.getDifficultyRating(),
                    "system" // 暂时使用系统用户，后续接入认证系统后获取真实用户
            );

            return ApiResponse.success(record, "学习记录保存成功");

        } catch (Exception e) {
            logger.error("❌ 记录学习结果失败: {}", e.getMessage(), e);
            return ApiResponse.error("记录学习结果失败: " + e.getMessage());
        }
    }

    /**
     * 获取卡片统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getFlashcardStats(@RequestParam Long projectId) {
        try {
            logger.info("📊 获取卡片统计信息: projectId={}", projectId);

            Map<String, Object> stats = flashcardService.getFlashcardStats(projectId);
            return ApiResponse.success(stats, "获取统计信息成功");

        } catch (Exception e) {
            logger.error("❌ 获取统计信息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取项目中所有卡片的正确率信息
     */
    @GetMapping("/accuracy-rates")
    public ApiResponse<Map<Long, Double>> getFlashcardAccuracyRates(@RequestParam Long projectId) {
        try {
            logger.info("📊 获取卡片正确率信息: projectId={}", projectId);
            Map<Long, Double> accuracyRates = flashcardService.getFlashcardAccuracyRates(projectId);
            return ApiResponse.success(accuracyRates, "获取正确率信息成功");
        } catch (Exception e) {
            logger.error("❌ 获取正确率信息失败: {}", e.getMessage(), e);
            return ApiResponse.error("获取正确率信息失败: " + e.getMessage());
        }
    }

    /**
     * 重置单个卡片的统计信息（正确率归零）
     */
    @PostMapping("/{id}/reset-stats")
    public ApiResponse<Void> resetFlashcardStats(@PathVariable Long id, @RequestParam Long projectId) {
        try {
            logger.info("🔄 接收重置卡片统计信息请求: flashcardId={}, projectId={}", id, projectId);

            boolean success = flashcardService.resetFlashcardStats(id, projectId);
            if (success) {
                return ApiResponse.success(null, "卡片统计信息重置成功");
            } else {
                return ApiResponse.error("卡片不存在或无权限访问");
            }

        } catch (Exception e) {
            logger.error("❌ 重置卡片统计信息失败: {}", e.getMessage(), e);
            return ApiResponse.error("重置卡片统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 上传卡片图片
     */
    @PostMapping("/{projectId}/upload-image")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadImage(
            @PathVariable Long projectId,
            @RequestParam("file") MultipartFile file) {

        try {
            logger.info("📤 上传卡片图片请求: 项目ID={}", projectId);
            logger.info("   文件名: {}", file.getOriginalFilename());
            logger.info("   文件大小: {} bytes", file.getSize());
            logger.info("   文件类型: {}", file.getContentType());

            // 验证文件
            if (file.isEmpty()) {
                logger.warn("⚠️  上传的文件为空");
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("上传的文件为空"));
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                logger.warn("⚠️  文件类型不支持: {}", contentType);
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("只支持图片文件上传"));
            }

            // 验证文件大小 (10MB)
            if (file.getSize() > 10 * 1024 * 1024) {
                logger.warn("⚠️  文件大小超过限制: {} bytes", file.getSize());
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件大小不能超过10MB"));
            }

            // 检查OSS服务是否可用
            if (ossService == null) {
                logger.warn("⚠️  OSS服务未配置，无法上传图片");
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("文件上传服务未配置，请联系管理员设置OSS配置"));
            }

            // 上传到OSS
            String imageUrl = ossService.uploadFile(file, "flashcards/images/");

            Map<String, String> result = new HashMap<>();
            result.put("url", imageUrl);
            result.put("filename", file.getOriginalFilename());

            logger.info("✅ 卡片图片上传成功: {}", imageUrl);
            return ResponseEntity.ok(ApiResponse.success(result, "图片上传成功"));
        } catch (Exception e) {
            logger.error("❌ 上传卡片图片失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("上传图片失败: " + e.getMessage()));
        }
    }

    /**
     * 卡片更新请求DTO（内部类）
     */
    public static class FlashcardUpdateRequest {
        private String title;
        private String frontContent;
        private String backContent;
        private Integer difficulty;

        // Getters and Setters
        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getFrontContent() {
            return frontContent;
        }

        public void setFrontContent(String frontContent) {
            this.frontContent = frontContent;
        }

        public String getBackContent() {
            return backContent;
        }

        public void setBackContent(String backContent) {
            this.backContent = backContent;
        }

        public Integer getDifficulty() {
            return difficulty;
        }

        public void setDifficulty(Integer difficulty) {
            this.difficulty = difficulty;
        }
    }

    /**
     * 卡片完整更新请求DTO（内部类）
     */
    public static class FlashcardFullUpdateRequest {
        private String knowledgePointId;
        private String title;
        private String frontContent;
        private String backContent;
        private Integer difficulty;

        // Getters and Setters
        public String getKnowledgePointId() {
            return knowledgePointId;
        }

        public void setKnowledgePointId(String knowledgePointId) {
            this.knowledgePointId = knowledgePointId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getFrontContent() {
            return frontContent;
        }

        public void setFrontContent(String frontContent) {
            this.frontContent = frontContent;
        }

        public String getBackContent() {
            return backContent;
        }

        public void setBackContent(String backContent) {
            this.backContent = backContent;
        }

        public Integer getDifficulty() {
            return difficulty;
        }

        public void setDifficulty(Integer difficulty) {
            this.difficulty = difficulty;
        }
    }

    /**
     * 学习记录请求DTO（内部类）
     */
    public static class StudyRecordRequest {
        private Long flashcardId;
        private Long projectId;
        private Boolean isCorrect;
        private Integer responseTimeSeconds;
        private Integer difficultyRating;

        // Getters and Setters
        public Long getFlashcardId() {
            return flashcardId;
        }

        public void setFlashcardId(Long flashcardId) {
            this.flashcardId = flashcardId;
        }

        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }

        public Boolean getIsCorrect() {
            return isCorrect;
        }

        public void setIsCorrect(Boolean isCorrect) {
            this.isCorrect = isCorrect;
        }

        public Integer getResponseTimeSeconds() {
            return responseTimeSeconds;
        }

        public void setResponseTimeSeconds(Integer responseTimeSeconds) {
            this.responseTimeSeconds = responseTimeSeconds;
        }

        public Integer getDifficultyRating() {
            return difficultyRating;
        }

        public void setDifficultyRating(Integer difficultyRating) {
            this.difficultyRating = difficultyRating;
        }
    }
}
