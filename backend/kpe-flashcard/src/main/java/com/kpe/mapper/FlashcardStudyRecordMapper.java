package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.FlashcardStudyRecord;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * FlashcardStudyRecord Mapper接口
 */
@Mapper
public interface FlashcardStudyRecordMapper extends BaseMapper<FlashcardStudyRecord> {

    /**
     * 根据卡片ID查找所有学习记录
     */
    @Select("SELECT * FROM flashcard_study_records WHERE flashcard_id = #{flashcardId} ORDER BY study_date DESC")
    List<FlashcardStudyRecord> findByFlashcardId(@Param("flashcardId") Long flashcardId);

    /**
     * 根据项目ID查找所有学习记录
     */
    @Select("SELECT * FROM flashcard_study_records WHERE project_id = #{projectId} ORDER BY study_date DESC")
    List<FlashcardStudyRecord> findByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据卡片ID查找最新的学习记录
     */
    @Select("SELECT * FROM flashcard_study_records WHERE flashcard_id = #{flashcardId} ORDER BY study_date DESC, created_at DESC LIMIT 1")
    FlashcardStudyRecord findLatestByFlashcardId(@Param("flashcardId") Long flashcardId);

    /**
     * 根据项目ID和学习日期查找学习记录
     */
    @Select("SELECT * FROM flashcard_study_records WHERE project_id = #{projectId} AND study_date = #{studyDate} ORDER BY created_at DESC")
    List<FlashcardStudyRecord> findByProjectIdAndStudyDate(@Param("projectId") Long projectId, @Param("studyDate") LocalDate studyDate);

    /**
     * 根据项目ID和日期范围查找学习记录
     */
    @Select("SELECT * FROM flashcard_study_records WHERE project_id = #{projectId} AND study_date >= #{startDate} AND study_date <= #{endDate} ORDER BY study_date DESC")
    List<FlashcardStudyRecord> findByProjectIdAndDateRange(@Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据项目ID统计学习记录数量
     */
    @Select("SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId}")
    int countByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID统计正确答题数量
     */
    @Select("SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId} AND is_correct = true")
    int countCorrectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID统计今日学习记录数量
     */
    @Select("SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId} AND study_date = CURRENT_DATE")
    int countTodayByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID统计今日正确答题数量
     */
    @Select("SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId} AND study_date = CURRENT_DATE AND is_correct = true")
    int countTodayCorrectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID查找今日需要复习的卡片ID列表
     */
    @Select("SELECT DISTINCT flashcard_id FROM flashcard_study_records WHERE project_id = #{projectId} AND next_review_date = CURRENT_DATE")
    List<Long> findTodayReviewFlashcardIds(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和卡片ID统计学习次数
     */
    @Select("SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId} AND flashcard_id = #{flashcardId}")
    int countByProjectIdAndFlashcardId(@Param("projectId") Long projectId, @Param("flashcardId") Long flashcardId);

    /**
     * 根据项目ID和卡片ID统计正确次数
     */
    @Select("SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId} AND flashcard_id = #{flashcardId} AND is_correct = true")
    int countCorrectByProjectIdAndFlashcardId(@Param("projectId") Long projectId, @Param("flashcardId") Long flashcardId);

    /**
     * 根据项目ID统计每日学习数量（最近30天）
     */
    @Select("SELECT study_date, COUNT(*) as count FROM flashcard_study_records " +
            "WHERE project_id = #{projectId} AND study_date >= CURRENT_DATE - INTERVAL '30 days' " +
            "GROUP BY study_date ORDER BY study_date DESC")
    List<java.util.Map<String, Object>> getDailyStudyStats(@Param("projectId") Long projectId);

    /**
     * 根据项目ID统计每日正确率（最近30天）
     */
    @Select("SELECT study_date, " +
            "COUNT(*) as total_count, " +
            "COUNT(CASE WHEN is_correct = true THEN 1 END) as correct_count, " +
            "ROUND(COUNT(CASE WHEN is_correct = true THEN 1 END) * 100.0 / COUNT(*), 2) as accuracy_rate " +
            "FROM flashcard_study_records " +
            "WHERE project_id = #{projectId} AND study_date >= CURRENT_DATE - INTERVAL '30 days' " +
            "GROUP BY study_date ORDER BY study_date DESC")
    List<java.util.Map<String, Object>> getDailyAccuracyStats(@Param("projectId") Long projectId);

    /**
     * 根据项目ID删除所有学习记录（用于项目级联删除）
     */
    @Delete("DELETE FROM flashcard_study_records WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据卡片ID删除所有学习记录（用于卡片删除时的级联删除）
     */
    @Delete("DELETE FROM flashcard_study_records WHERE flashcard_id = #{flashcardId}")
    int deleteByFlashcardId(@Param("flashcardId") Long flashcardId);

    /**
     * 根据项目ID和卡片ID查找学习记录（安全查询）
     */
    @Select("SELECT * FROM flashcard_study_records WHERE project_id = #{projectId} AND flashcard_id = #{flashcardId} ORDER BY study_date DESC")
    List<FlashcardStudyRecord> findByProjectIdAndFlashcardId(@Param("projectId") Long projectId, @Param("flashcardId") Long flashcardId);
}
