package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.Flashcard;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Flashcard Mapper接口
 */
@Mapper
public interface FlashcardMapper extends BaseMapper<Flashcard> {

    /**
     * 根据项目ID查找所有活跃的卡片
     */
    @Select("SELECT * FROM flashcards WHERE project_id = #{projectId} AND status = 'active' ORDER BY created_at DESC")
    List<Flashcard> findByProjectIdAndStatusActive(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和知识点ID查找卡片
     */
    @Select("SELECT * FROM flashcards WHERE project_id = #{projectId} AND knowledge_point_id = #{knowledgePointId} AND status = 'active' ORDER BY created_at DESC")
    List<Flashcard> findByProjectIdAndKnowledgePointId(@Param("projectId") Long projectId, @Param("knowledgePointId") String knowledgePointId);

    /**
     * 根据项目ID和难度等级查找卡片
     */
    @Select("SELECT * FROM flashcards WHERE project_id = #{projectId} AND difficulty = #{difficulty} AND status = 'active' ORDER BY created_at DESC")
    List<Flashcard> findByProjectIdAndDifficulty(@Param("projectId") Long projectId, @Param("difficulty") Integer difficulty);

    /**
     * 根据项目ID和难度等级范围查找卡片
     */
    @Select("SELECT * FROM flashcards WHERE project_id = #{projectId} AND difficulty >= #{minDifficulty} AND difficulty <= #{maxDifficulty} AND status = 'active' ORDER BY created_at DESC")
    List<Flashcard> findByProjectIdAndDifficultyRange(@Param("projectId") Long projectId, @Param("minDifficulty") Integer minDifficulty, @Param("maxDifficulty") Integer maxDifficulty);

    /**
     * 根据项目ID和知识点ID列表查找卡片
     */
    @Select("<script>" +
            "SELECT * FROM flashcards WHERE project_id = #{projectId} AND status = 'active'" +
            "<if test='knowledgePointIds != null and knowledgePointIds.size() > 0'>" +
            " AND knowledge_point_id IN " +
            "<foreach collection='knowledgePointIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</if>" +
            " ORDER BY created_at DESC" +
            "</script>")
    List<Flashcard> findByProjectIdAndKnowledgePointIds(@Param("projectId") Long projectId, @Param("knowledgePointIds") List<String> knowledgePointIds);

    /**
     * 根据项目ID和标题模糊查找卡片
     */
    @Select("SELECT * FROM flashcards WHERE project_id = #{projectId} AND title LIKE CONCAT('%', #{title}, '%') AND status = 'active' ORDER BY created_at DESC")
    List<Flashcard> findByProjectIdAndTitleLike(@Param("projectId") Long projectId, @Param("title") String title);

    /**
     * 根据项目ID统计各难度等级的卡片数量
     */
    @Select("SELECT difficulty, COUNT(*) as count FROM flashcards WHERE project_id = #{projectId} AND status = 'active' GROUP BY difficulty ORDER BY difficulty")
    List<java.util.Map<String, Object>> countByProjectIdAndDifficulty(@Param("projectId") Long projectId);

    /**
     * 根据项目ID统计各知识点的卡片数量
     */
    @Select("SELECT knowledge_point_id, COUNT(*) as count FROM flashcards WHERE project_id = #{projectId} AND status = 'active' GROUP BY knowledge_point_id ORDER BY count DESC")
    List<java.util.Map<String, Object>> countByProjectIdAndKnowledgePoint(@Param("projectId") Long projectId);

    /**
     * 根据项目ID查找需要复习的卡片（基于学习记录）
     */
    @Select("SELECT DISTINCT f.* FROM flashcards f " +
            "LEFT JOIN flashcard_study_records fsr ON f.id = fsr.flashcard_id " +
            "WHERE f.project_id = #{projectId} AND f.status = 'active' " +
            "AND (fsr.next_review_date IS NULL OR fsr.next_review_date <= CURRENT_DATE) " +
            "ORDER BY f.created_at DESC")
    List<Flashcard> findCardsNeedReview(@Param("projectId") Long projectId);

    /**
     * 根据项目ID查找从未学习过的卡片
     */
    @Select("SELECT f.* FROM flashcards f " +
            "LEFT JOIN flashcard_study_records fsr ON f.id = fsr.flashcard_id " +
            "WHERE f.project_id = #{projectId} AND f.status = 'active' " +
            "AND fsr.flashcard_id IS NULL " +
            "ORDER BY f.created_at DESC")
    List<Flashcard> findNeverStudiedCards(@Param("projectId") Long projectId);

    /**
     * 根据项目ID删除所有卡片（用于项目级联删除）
     */
    @Delete("DELETE FROM flashcards WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID软删除所有卡片
     */
    @Select("UPDATE flashcards SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE project_id = #{projectId}")
    int softDeleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据ID和项目ID查找卡片（安全查询，确保用户只能访问自己项目的卡片）
     */
    @Select("SELECT * FROM flashcards WHERE id = #{id} AND project_id = #{projectId} AND status = 'active'")
    Flashcard findByIdAndProjectId(@Param("id") Long id, @Param("projectId") Long projectId);

    /**
     * 批量获取项目统计信息（优化性能，减少SQL查询次数）
     */
    @Select("SELECT " +
            "(SELECT COUNT(*) FROM flashcards WHERE project_id = #{projectId} AND status = 'active') as totalCards, " +
            "(SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId}) as totalStudyRecords, " +
            "(SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId} AND is_correct = true) as correctRecords, " +
            "(SELECT COUNT(*) FROM flashcard_study_records WHERE project_id = #{projectId} AND study_date = CURRENT_DATE) as todayStudyRecords, " +
            "(SELECT COUNT(DISTINCT f.id) FROM flashcards f " +
            " LEFT JOIN flashcard_study_records fsr ON f.id = fsr.flashcard_id " +
            " WHERE f.project_id = #{projectId} AND f.status = 'active' " +
            " AND (fsr.next_review_date IS NULL OR fsr.next_review_date <= CURRENT_DATE)) as needReviewCards, " +
            "(SELECT COUNT(*) FROM flashcards f " +
            " LEFT JOIN flashcard_study_records fsr ON f.id = fsr.flashcard_id " +
            " WHERE f.project_id = #{projectId} AND f.status = 'active' " +
            " AND fsr.flashcard_id IS NULL) as neverStudiedCards")
    java.util.Map<String, Object> getBatchStatsForProject(@Param("projectId") Long projectId);
}
