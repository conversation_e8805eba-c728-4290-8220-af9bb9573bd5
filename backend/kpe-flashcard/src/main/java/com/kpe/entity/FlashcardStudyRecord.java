package com.kpe.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 卡片学习记录实体类
 */
@TableName("flashcard_study_records")
public class FlashcardStudyRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("flashcard_id")
    private Long flashcardId;

    @TableField("project_id")
    private Long projectId;

    @TableField("study_date")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate studyDate;

    @TableField("is_correct")
    private Boolean isCorrect;

    @TableField("response_time_seconds")
    private Integer responseTimeSeconds;

    @TableField("difficulty_rating")
    private Integer difficultyRating; // 主观难度评级 1-5

    @TableField("next_review_date")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate nextReviewDate;

    @TableField("review_count")
    private Integer reviewCount = 1;

    @TableField("created_at")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @TableField("created_by")
    private String createdBy;

    // 构造函数
    public FlashcardStudyRecord() {
    }

    public FlashcardStudyRecord(Long flashcardId, Long projectId, LocalDate studyDate,
                                Boolean isCorrect, Integer responseTimeSeconds,
                                Integer difficultyRating, String createdBy) {
        this.flashcardId = flashcardId;
        this.projectId = projectId;
        this.studyDate = studyDate;
        this.isCorrect = isCorrect;
        this.responseTimeSeconds = responseTimeSeconds;
        this.difficultyRating = difficultyRating;
        this.createdBy = createdBy;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFlashcardId() {
        return flashcardId;
    }

    public void setFlashcardId(Long flashcardId) {
        this.flashcardId = flashcardId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public LocalDate getStudyDate() {
        return studyDate;
    }

    public void setStudyDate(LocalDate studyDate) {
        this.studyDate = studyDate;
    }

    public Boolean getIsCorrect() {
        return isCorrect;
    }

    public void setIsCorrect(Boolean isCorrect) {
        this.isCorrect = isCorrect;
    }

    public Integer getResponseTimeSeconds() {
        return responseTimeSeconds;
    }

    public void setResponseTimeSeconds(Integer responseTimeSeconds) {
        this.responseTimeSeconds = responseTimeSeconds;
    }

    public Integer getDifficultyRating() {
        return difficultyRating;
    }

    public void setDifficultyRating(Integer difficultyRating) {
        this.difficultyRating = difficultyRating;
    }

    public LocalDate getNextReviewDate() {
        return nextReviewDate;
    }

    public void setNextReviewDate(LocalDate nextReviewDate) {
        this.nextReviewDate = nextReviewDate;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return "FlashcardStudyRecord{" +
                "id=" + id +
                ", flashcardId=" + flashcardId +
                ", projectId=" + projectId +
                ", studyDate=" + studyDate +
                ", isCorrect=" + isCorrect +
                ", responseTimeSeconds=" + responseTimeSeconds +
                ", difficultyRating=" + difficultyRating +
                ", nextReviewDate=" + nextReviewDate +
                ", reviewCount=" + reviewCount +
                ", createdAt=" + createdAt +
                ", createdBy='" + createdBy + '\'' +
                '}';
    }
}
