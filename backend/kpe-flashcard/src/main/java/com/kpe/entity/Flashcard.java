package com.kpe.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * 卡片实体类
 */
@TableName("flashcards")
public class Flashcard {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("project_id")
    private Long projectId;

    @TableField("knowledge_point_id")
    private String knowledgePointId;

    private String title;

    @TableField("front_content")
    private String frontContent;

    @TableField("back_content")
    private String backContent;

    private Integer difficulty = 1; // 1-5难度等级

    private String status = "active"; // active, archived, deleted

    @TableField("total_study_count")
    private Integer totalStudyCount = 0; // 总做题次数

    @TableField("accuracy_rate")
    private Double accuracyRate = 0.0; // 当前正确率百分比

    @TableField("created_at")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @TableField("created_by")
    private String createdBy;

    // 构造函数
    public Flashcard() {
    }

    public Flashcard(Long projectId, String knowledgePointId, String title, String frontContent,
                     String backContent, Integer difficulty, String createdBy) {
        this.projectId = projectId;
        this.knowledgePointId = knowledgePointId;
        this.title = title;
        this.frontContent = frontContent;
        this.backContent = backContent;
        this.difficulty = difficulty;
        this.createdBy = createdBy;
        this.totalStudyCount = 0;
        this.accuracyRate = 0.0;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getKnowledgePointId() {
        return knowledgePointId;
    }

    public void setKnowledgePointId(String knowledgePointId) {
        this.knowledgePointId = knowledgePointId;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFrontContent() {
        return frontContent;
    }

    public void setFrontContent(String frontContent) {
        this.frontContent = frontContent;
    }

    public String getBackContent() {
        return backContent;
    }

    public void setBackContent(String backContent) {
        this.backContent = backContent;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getTotalStudyCount() {
        return totalStudyCount;
    }

    public void setTotalStudyCount(Integer totalStudyCount) {
        this.totalStudyCount = totalStudyCount;
    }

    public Double getAccuracyRate() {
        return accuracyRate;
    }

    public void setAccuracyRate(Double accuracyRate) {
        this.accuracyRate = accuracyRate;
    }

    /**
     * 获取难度等级对应的颜色
     */
    public String getDifficultyColor() {
        return switch (difficulty) {
            case 1 -> "#4CAF50"; // 绿色
            case 2 -> "#2196F3"; // 蓝色
            case 3 -> "#9C27B0"; // 紫色
            case 4 -> "#FF9800"; // 金色
            case 5 -> "#F44336"; // 红色
            default -> "#9E9E9E"; // 灰色
        };
    }

    /**
     * 获取难度等级对应的文本
     */
    public String getDifficultyText() {
        return switch (difficulty) {
            case 1 -> "简单";
            case 2 -> "容易";
            case 3 -> "中等";
            case 4 -> "困难";
            case 5 -> "极难";
            default -> "未知";
        };
    }

    @Override
    public String toString() {
        return "Flashcard{" +
                "id=" + id +
                ", projectId=" + projectId +
                ", knowledgePointId='" + knowledgePointId + '\'' +
                ", title='" + title + '\'' +
                ", difficulty=" + difficulty +
                ", status='" + status + '\'' +
                ", totalStudyCount=" + totalStudyCount +
                ", accuracyRate=" + accuracyRate +
                ", createdAt=" + createdAt +
                ", createdBy='" + createdBy + '\'' +
                '}';
    }
}
