package com.kpe.service;

import com.kpe.common.BaseCrudService;
import com.kpe.entity.Flashcard;
import com.kpe.entity.FlashcardStudyRecord;
import com.kpe.mapper.FlashcardMapper;
import com.kpe.mapper.FlashcardStudyRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 卡片服务类
 */
@Service
public class FlashcardService extends BaseCrudService<Flashcard, Long, FlashcardMapper> {

    private static final Logger logger = LoggerFactory.getLogger(FlashcardService.class);

    @Autowired
    private FlashcardMapper flashcardMapper;

    @Autowired
    private FlashcardStudyRecordMapper studyRecordMapper;

    @Override
    protected FlashcardMapper getMapper() {
        return flashcardMapper;
    }

    @Override
    protected String getEntityName() {
        return "卡片";
    }

    @Override
    protected void setTimestamps(Flashcard entity, boolean isCreate) {
        LocalDateTime now = LocalDateTime.now();
        if (isCreate) {
            entity.setCreatedAt(now);
        }
        entity.setUpdatedAt(now);
    }

    @Override
    protected void validateBeforeCreate(Flashcard entity) {
        super.validateBeforeCreate(entity);
        validateNotNull(entity.getProjectId(), "项目ID");
        validateNotBlank(entity.getTitle(), "卡片标题");
        validateNotBlank(entity.getFrontContent(), "卡片正面内容");
        // 背面内容现在是可选的，不需要验证
    }

    /**
     * 创建卡片
     */
    @Transactional
    public Flashcard createFlashcard(Long projectId, String knowledgePointId, String title, String frontContent,
                                     String backContent, Integer difficulty, String createdBy) {

        Flashcard flashcard = new Flashcard(projectId, knowledgePointId, title, frontContent,
                backContent, difficulty, createdBy);
        return create(flashcard);
    }

    /**
     * 更新卡片
     */
    @Transactional
    public Optional<Flashcard> updateFlashcard(Long id, Long projectId, String title, String frontContent,
                                               String backContent, Integer difficulty) {
        // 验证卡片是否属于指定项目
        Flashcard existingFlashcard = flashcardMapper.findByIdAndProjectId(id, projectId);
        if (existingFlashcard == null) {
            throw new IllegalArgumentException("卡片不存在或不属于指定项目: " + id);
        }

        Flashcard flashcard = new Flashcard();
        flashcard.setId(id);
        flashcard.setTitle(title);
        flashcard.setFrontContent(frontContent);
        flashcard.setBackContent(backContent);
        flashcard.setDifficulty(difficulty);

        return update(id, flashcard);
    }

    /**
     * 更新卡片（包含知识点信息）
     */
    @Transactional
    public Optional<Flashcard> updateFlashcard(Long id, Long projectId, String knowledgePointId,
                                               String title, String frontContent, String backContent,
                                               Integer difficulty) {
        // 验证卡片是否属于指定项目
        Flashcard existingFlashcard = flashcardMapper.findByIdAndProjectId(id, projectId);
        if (existingFlashcard == null) {
            throw new IllegalArgumentException("卡片不存在或不属于指定项目: " + id);
        }

        Flashcard flashcard = new Flashcard();
        flashcard.setId(id);
        flashcard.setKnowledgePointId(knowledgePointId);
        flashcard.setTitle(title);
        flashcard.setFrontContent(frontContent);
        flashcard.setBackContent(backContent);
        flashcard.setDifficulty(difficulty);

        return update(id, flashcard);
    }

    /**
     * 删除卡片（软删除）
     */
    @Transactional
    public boolean deleteFlashcard(Long id, Long projectId) {
        // 验证卡片是否属于指定项目
        Flashcard flashcard = flashcardMapper.findByIdAndProjectId(id, projectId);
        if (flashcard == null) {
            throw new IllegalArgumentException("卡片不存在或不属于指定项目: " + id);
        }

        // 软删除卡片
        flashcard.setStatus("deleted");
        flashcard.setUpdatedAt(LocalDateTime.now());
        flashcardMapper.updateById(flashcard);

        logInfo("卡片删除成功: {}", flashcard.getTitle());
        return true;
    }

    /**
     * 根据项目ID获取所有活跃卡片
     */
    public List<Flashcard> getFlashcardsByProjectId(Long projectId) {
        logger.info("📋 获取项目卡片列表: projectId={}", projectId);
        return flashcardMapper.findByProjectIdAndStatusActive(projectId);
    }

    /**
     * 根据项目ID和知识点ID获取卡片
     */
    public List<Flashcard> getFlashcardsByKnowledgePoint(Long projectId, String knowledgePointId) {
        logger.info("📋 获取知识点卡片列表: projectId={}, knowledgePointId={}", projectId, knowledgePointId);
        return flashcardMapper.findByProjectIdAndKnowledgePointId(projectId, knowledgePointId);
    }

    /**
     * 根据项目ID和难度等级获取卡片
     */
    public List<Flashcard> getFlashcardsByDifficulty(Long projectId, Integer difficulty) {
        logger.info("📋 获取难度卡片列表: projectId={}, difficulty={}", projectId, difficulty);
        return flashcardMapper.findByProjectIdAndDifficulty(projectId, difficulty);
    }

    /**
     * 根据项目ID获取需要复习的卡片
     */
    public List<Flashcard> getCardsNeedReview(Long projectId) {
        logger.info("📋 获取需要复习的卡片: projectId={}", projectId);
        return flashcardMapper.findCardsNeedReview(projectId);
    }

    /**
     * 根据项目ID获取从未学习过的卡片
     */
    public List<Flashcard> getNeverStudiedCards(Long projectId) {
        logger.info("📋 获取从未学习的卡片: projectId={}", projectId);
        return flashcardMapper.findNeverStudiedCards(projectId);
    }

    /**
     * 记录学习结果（优化版本 - 同时更新卡片统计信息）
     */
    @Transactional
    public FlashcardStudyRecord recordStudyResult(Long flashcardId, Long projectId, Boolean isCorrect,
                                                  Integer responseTimeSeconds, Integer difficultyRating,
                                                  String createdBy) {
        logger.info("📝 记录学习结果: flashcardId={}, isCorrect={}", flashcardId, isCorrect);

        // 1. 获取卡片信息
        Flashcard flashcard = flashcardMapper.findByIdAndProjectId(flashcardId, projectId);
        if (flashcard == null) {
            throw new RuntimeException("卡片不存在: flashcardId=" + flashcardId + ", projectId=" + projectId);
        }

        // 2. 更新卡片统计信息
        updateFlashcardStats(flashcard, isCorrect);

        // 3. 保存学习记录（可选，如果需要保留详细记录）
        FlashcardStudyRecord record = new FlashcardStudyRecord(flashcardId, projectId, LocalDate.now(),
                isCorrect, responseTimeSeconds, difficultyRating, createdBy);

        // 计算下次复习日期（简单的间隔重复算法）
        LocalDate nextReviewDate = calculateNextReviewDate(flashcardId, isCorrect, difficultyRating);
        record.setNextReviewDate(nextReviewDate);

        // 设置复习次数（基于卡片统计信息）
        record.setReviewCount(flashcard.getTotalStudyCount());

        record.setCreatedAt(LocalDateTime.now());
        studyRecordMapper.insert(record);

        logger.info("✅ 学习记录创建成功: ID={}, 卡片统计已更新, 下次复习日期={}",
                record.getId(), nextReviewDate);
        return record;
    }

    /**
     * 更新卡片统计信息
     */
    private void updateFlashcardStats(Flashcard flashcard, Boolean isCorrect) {
        // 增加总做题次数
        int newTotalCount = flashcard.getTotalStudyCount() + 1;
        flashcard.setTotalStudyCount(newTotalCount);

        // 重新计算正确率
        if (isCorrect) {
            // 计算新的正确率：(原正确率 * 原总次数 + 1) / 新总次数
            double oldCorrectCount = flashcard.getAccuracyRate() * (newTotalCount - 1) / 100.0;
            double newCorrectCount = oldCorrectCount + 1;
            double newAccuracyRate = (newCorrectCount / newTotalCount) * 100.0;
            flashcard.setAccuracyRate(Math.round(newAccuracyRate * 100.0) / 100.0);
        } else {
            // 计算新的正确率：(原正确率 * 原总次数) / 新总次数
            double oldCorrectCount = flashcard.getAccuracyRate() * (newTotalCount - 1) / 100.0;
            double newAccuracyRate = (oldCorrectCount / newTotalCount) * 100.0;
            flashcard.setAccuracyRate(Math.round(newAccuracyRate * 100.0) / 100.0);
        }

        // 更新时间戳
        flashcard.setUpdatedAt(LocalDateTime.now());

        // 保存到数据库
        flashcardMapper.updateById(flashcard);

        logger.info("📊 卡片统计已更新: flashcardId={}, 总次数={}, 正确率={}%",
                flashcard.getId(), newTotalCount, flashcard.getAccuracyRate());
    }

    /**
     * 计算下次复习日期（简单的间隔重复算法）
     */
    private LocalDate calculateNextReviewDate(Long flashcardId, Boolean isCorrect, Integer difficultyRating) {
        // 获取该卡片的学习次数
        FlashcardStudyRecord latestRecord = studyRecordMapper.findLatestByFlashcardId(flashcardId);
        int reviewCount = latestRecord != null ? latestRecord.getReviewCount() : 0;

        LocalDate today = LocalDate.now();
        int daysToAdd;

        if (!isCorrect) {
            // 答错了，1天后再复习
            daysToAdd = 1;
        } else {
            // 答对了，根据复习次数和难度评级计算间隔
            daysToAdd = switch (reviewCount) {
                case 0 -> 1;
                case 1 -> 3;
                case 2 -> 7;
                case 3 -> 15;
                case 4 -> 30;
                default -> 60;
            };

            // 根据主观难度评级调整间隔
            if (difficultyRating != null) {
                if (difficultyRating >= 4) {
                    daysToAdd = Math.max(1, daysToAdd / 2); // 觉得难的话，缩短间隔
                } else if (difficultyRating <= 2) {
                    daysToAdd = daysToAdd * 2; // 觉得容易的话，延长间隔
                }
            }
        }

        return today.plusDays(daysToAdd);
    }

    /**
     * 获取项目的卡片统计信息（优化版本 - 直接从卡片表统计）
     */
    public Map<String, Object> getFlashcardStats(Long projectId) {
        logger.info("📊 获取卡片统计信息: projectId={}", projectId);

        // 直接从卡片表获取统计信息，避免复杂的关联查询
        List<Flashcard> allCards = flashcardMapper.findByProjectIdAndStatusActive(projectId);

        int totalCards = allCards.size();
        int totalStudyRecords = allCards.stream().mapToInt(card -> card.getTotalStudyCount() != null ? card.getTotalStudyCount() : 0).sum();
        int neverStudiedCards = (int) allCards.stream().filter(card -> card.getTotalStudyCount() == null || card.getTotalStudyCount() == 0).count();

        // 计算整体正确率（加权平均）
        double totalCorrectAnswers = allCards.stream()
                .mapToDouble(card -> {
                    int count = card.getTotalStudyCount() != null ? card.getTotalStudyCount() : 0;
                    double rate = card.getAccuracyRate() != null ? card.getAccuracyRate() : 0.0;
                    return count * rate / 100.0;
                }).sum();
        double overallAccuracyRate = totalStudyRecords > 0 ? (totalCorrectAnswers / totalStudyRecords) * 100 : 0;

        // 今日学习记录和需要复习的卡片仍需要从学习记录表查询
        int todayStudyRecords = studyRecordMapper.countTodayByProjectId(projectId);
        int needReviewCards = flashcardMapper.findCardsNeedReview(projectId).size();

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCards", totalCards);
        stats.put("totalStudyRecords", totalStudyRecords);
        stats.put("correctRecords", (int) totalCorrectAnswers);
        stats.put("accuracyRate", Math.round(overallAccuracyRate * 100.0) / 100.0);
        stats.put("todayStudyRecords", todayStudyRecords);
        stats.put("needReviewCards", needReviewCards);
        stats.put("neverStudiedCards", neverStudiedCards);

        logger.info("✅ 统计信息获取完成（优化版本）: 总卡片={}, 今日学习={}, 待复习={}, 正确率={}%",
                totalCards, todayStudyRecords, needReviewCards, Math.round(overallAccuracyRate * 100.0) / 100.0);
        return stats;
    }

    /**
     * 获取项目中所有卡片的正确率信息（优化版本 - 直接从卡片表读取）
     */
    public Map<Long, Double> getFlashcardAccuracyRates(Long projectId) {
        logger.info("📊 获取卡片正确率信息: projectId={}", projectId);

        List<Flashcard> allCards = flashcardMapper.findByProjectIdAndStatusActive(projectId);
        Map<Long, Double> accuracyRates = new HashMap<>();

        for (Flashcard card : allCards) {
            // 直接从卡片表读取正确率，无需实时计算
            accuracyRates.put(card.getId(), card.getAccuracyRate() != null ? card.getAccuracyRate() : 0.0);
        }

        logger.info("✅ 获取到{}张卡片的正确率信息（优化版本）", accuracyRates.size());
        return accuracyRates;
    }

    /**
     * 重置单个卡片的统计信息（正确率归零）
     */
    @Transactional
    public boolean resetFlashcardStats(Long flashcardId, Long projectId) {
        logger.info("🔄 重置卡片统计信息: flashcardId={}, projectId={}", flashcardId, projectId);

        // 验证卡片是否存在且属于指定项目
        Flashcard flashcard = flashcardMapper.findByIdAndProjectId(flashcardId, projectId);
        if (flashcard == null) {
            logger.warn("⚠️ 卡片不存在或无权限访问: flashcardId={}, projectId={}", flashcardId, projectId);
            return false;
        }

        // 重置统计信息
        flashcard.setTotalStudyCount(0);
        flashcard.setAccuracyRate(0.0);
        flashcard.setUpdatedAt(LocalDateTime.now());

        // 保存到数据库
        flashcardMapper.updateById(flashcard);

        logger.info("✅ 卡片统计信息重置成功: flashcardId={}, title={}", flashcardId, flashcard.getTitle());
        return true;
    }

    /**
     * 根据项目ID删除所有卡片（用于项目级联删除）
     */
    @Transactional
    public int deleteFlashcardsByProjectId(Long projectId) {
        logger.info("🗑️ 删除项目所有卡片: projectId={}", projectId);

        // 先删除学习记录
        int deletedRecords = studyRecordMapper.deleteByProjectId(projectId);
        logger.info("✅ 删除了 {} 条学习记录", deletedRecords);

        // 再删除卡片
        int deletedCards = flashcardMapper.deleteByProjectId(projectId);
        logger.info("✅ 删除了 {} 张卡片", deletedCards);

        return deletedCards;
    }
}
