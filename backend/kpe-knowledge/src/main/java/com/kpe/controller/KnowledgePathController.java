package com.kpe.controller;

import com.kpe.common.BaseController;
import com.kpe.dto.ApiResponse;
import com.kpe.dto.KnowledgePointScore;
import com.kpe.service.KnowledgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 知识点路径控制器
 * 提供知识点路径的批量获取功能
 */
@RestController
@RequestMapping("/api/projects/{projectId}/knowledge-paths")
public class KnowledgePathController extends BaseController {

    @Autowired
    private KnowledgeService knowledgeService;

    /**
     * 批量获取知识点路径信息（父级路径，用于知识点管理）
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Map<String, String>>> getKnowledgePointPaths(@PathVariable Long projectId) {
        return executeWithResponse(() -> knowledgeService.getKnowledgePointPaths(projectId), "批量获取知识点父级路径");
    }

    /**
     * 批量获取知识点完整路径信息（包含叶子节点本身，用于卡片管理）
     */
    @GetMapping("/full")
    public ResponseEntity<ApiResponse<Map<String, String>>> getKnowledgePointFullPaths(@PathVariable Long projectId) {
        return executeWithResponse(() -> knowledgeService.getKnowledgePointFullPaths(projectId), "批量获取知识点完整路径");
    }

    /**
     * 获取带路径的叶子节点知识点列表
     */
    @GetMapping("/leaf-points")
    public ResponseEntity<ApiResponse<List<KnowledgePointScore>>> getLeafKnowledgePointsWithPaths(@PathVariable Long projectId) {
        try {
            logger.info("🔍 获取带路径的叶子节点知识点，项目ID: {}", projectId);

            List<KnowledgePointScore> leafPoints = knowledgeService.extractLeafKnowledgePointsWithPaths(projectId);

            logger.info("✅ 成功获取 {} 个带路径的叶子节点知识点", leafPoints.size());
            return ResponseEntity.ok(ApiResponse.success(leafPoints));

        } catch (Exception e) {
            logger.error("❌ 获取带路径的叶子节点知识点失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取带路径的叶子节点知识点失败: " + e.getMessage()));
        }
    }

    /**
     * 获取指定知识点的路径信息
     */
    @GetMapping("/{uid}")
    public ResponseEntity<ApiResponse<String>> getKnowledgePointPath(
            @PathVariable Long projectId,
            @PathVariable String uid) {
        try {
            logger.info("🔍 获取知识点路径，项目ID: {}, UID: {}", projectId, uid);

            Map<String, String> pathMap = knowledgeService.getKnowledgePointPaths(projectId);
            String path = pathMap.get(uid);

            if (path != null) {
                logger.info("✅ 成功获取知识点路径: {}", path);
                return ResponseEntity.ok(ApiResponse.success(path));
            } else {
                logger.warn("⚠️  未找到知识点路径，UID: {}", uid);
                return ResponseEntity.ok(ApiResponse.error("未找到指定知识点的路径"));
            }

        } catch (Exception e) {
            logger.error("❌ 获取知识点路径失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("获取知识点路径失败: " + e.getMessage()));
        }
    }

    /**
     * 清理知识点路径缓存
     */
    @PostMapping("/clear-cache")
    public ResponseEntity<ApiResponse<String>> clearKnowledgePathCache(@PathVariable Long projectId) {
        try {
            logger.info("🧹 清理知识点路径缓存，项目ID: {}", projectId);

            knowledgeService.clearKnowledgeCache(projectId);

            logger.info("✅ 知识点路径缓存清理成功");
            return ResponseEntity.ok(ApiResponse.success("知识点路径缓存清理成功"));

        } catch (Exception e) {
            logger.error("❌ 清理知识点路径缓存失败: {}", e.getMessage(), e);
            return ResponseEntity.ok(ApiResponse.error("清理知识点路径缓存失败: " + e.getMessage()));
        }
    }
}
