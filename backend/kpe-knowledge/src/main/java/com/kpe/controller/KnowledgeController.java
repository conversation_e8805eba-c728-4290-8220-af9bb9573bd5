package com.kpe.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kpe.dto.ApiResponse;
import com.kpe.dto.KnowledgeConfigurationResponse;
import com.kpe.entity.KnowledgeConfiguration;
import com.kpe.service.KnowledgeService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/projects/{projectId}/knowledge")
@CrossOrigin(origins = "*")
public class KnowledgeController {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgeController.class);

    @Autowired
    private KnowledgeService knowledgeService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 获取知识点配置
     */
    @GetMapping
    public ResponseEntity<ApiResponse<KnowledgeConfigurationResponse>> getKnowledgeConfiguration(
            @PathVariable Long projectId) {
        try {
            logger.info("📋 获取知识点配置: 项目{}", projectId);
            Optional<KnowledgeConfiguration> config = knowledgeService.getKnowledgeConfiguration(projectId);

            if (config.isPresent()) {
                KnowledgeConfiguration kc = config.get();
                JsonNode configJson = objectMapper.readTree(kc.getConfiguration());

                KnowledgeConfigurationResponse response = new KnowledgeConfigurationResponse();
                response.setId(kc.getId());
                response.setProjectId(kc.getProjectId());
                response.setConfiguration(configJson);
                response.setCreatedAt(kc.getCreatedAt());
                response.setCreatedBy(kc.getCreatedBy());
                response.setNotes(kc.getNotes());

                return ResponseEntity.ok(ApiResponse.success(response));
            } else {
                return ResponseEntity.ok(ApiResponse.success(null));
            }
        } catch (Exception e) {
            logger.error("❌ 获取知识点配置失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 保存知识点配置
     */
    @PostMapping
    public ResponseEntity<ApiResponse<KnowledgeConfiguration>> saveKnowledgeConfiguration(
            @PathVariable Long projectId,
            @Valid @RequestBody SaveKnowledgeConfigurationRequest request) {
        try {
            if (request.getConfiguration() == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("知识点配置不能为空"));
            }

            logger.info("💾 保存知识点配置: 项目{}", projectId);
            KnowledgeConfiguration config = knowledgeService.saveKnowledgeConfiguration(
                    projectId,
                    request.getConfiguration(),
                    "user",
                    request.getNotes() != null ? request.getNotes() : ""
            );

            return ResponseEntity.ok(ApiResponse.success(config));
        } catch (Exception e) {
            logger.error("❌ 保存知识点配置失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }



    /**
     * 清空项目知识点相关缓存
     */
    @DeleteMapping("/cache")
    public ResponseEntity<ApiResponse<String>> clearKnowledgeCache(@PathVariable Long projectId) {
        try {
            logger.info("🗑️ 清空项目知识点缓存: 项目{}", projectId);
            knowledgeService.clearKnowledgeCache(projectId);

            return ResponseEntity.ok(ApiResponse.success("知识点缓存清空成功"));
        } catch (Exception e) {
            logger.error("❌ 清空知识点缓存失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("清空缓存失败: " + e.getMessage()));
        }
    }

    // Request DTOs
    public static class SaveKnowledgeConfigurationRequest {
        private JsonNode configuration;
        private String notes;

        public JsonNode getConfiguration() {
            return configuration;
        }

        public void setConfiguration(JsonNode configuration) {
            this.configuration = configuration;
        }

        public String getNotes() {
            return notes;
        }

        public void setNotes(String notes) {
            this.notes = notes;
        }
    }


}
