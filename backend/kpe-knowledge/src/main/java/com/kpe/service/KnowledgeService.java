package com.kpe.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kpe.common.BaseService;
import com.kpe.common.CacheHelper;
import com.kpe.common.JsonUtils;
import com.kpe.config.CacheConfig;
import com.kpe.dto.KnowledgePointScore;
import com.kpe.entity.KnowledgeConfiguration;
import com.kpe.mapper.KnowledgeConfigurationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class KnowledgeService extends BaseService {

    @Autowired
    private KnowledgeConfigurationMapper knowledgeConfigurationMapper;

    @Autowired
    @Lazy
    private KnowledgePointCacheService knowledgePointCacheService;



    @Autowired
    private CacheHelper cacheHelper;




    /**
     * 保存知识点配置
     */
    @Transactional
    public KnowledgeConfiguration saveKnowledgeConfiguration(Long projectId, Object knowledgePoints,
                                                             String userId, String notes) {
        logger.info("💾 保存知识点配置...");
        logger.info("   项目ID: {}", projectId);

        try {
            // 为知识点添加uid
            Object processedKnowledgePoints = addUidsToKnowledgePoints(knowledgePoints);

            String configJson = JsonUtils.toJsonString(processedKnowledgePoints);

            // 检查是否已存在配置
            KnowledgeConfiguration existingConfig = knowledgeConfigurationMapper
                    .findByProjectId(projectId);

            KnowledgeConfiguration config;
            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setConfiguration(configJson);
                existingConfig.setNotes(notes);
                existingConfig.setCreatedAt(LocalDateTime.now());
                knowledgeConfigurationMapper.updateById(existingConfig);
                config = existingConfig;
                logger.info("✅ 知识点配置更新成功");
            } else {
                // 创建新配置
                config = new KnowledgeConfiguration(
                        projectId, configJson, userId, notes
                );
                config.setCreatedAt(LocalDateTime.now());

                knowledgeConfigurationMapper.insertWithJsonb(config);
                logger.info("✅ 知识点配置创建成功");
            }

            // 清除知识点相关缓存
            cacheHelper.clearKnowledgeRelatedCaches(projectId);

            // 更新知识点缓存
            knowledgePointCacheService.refreshProjectCache(projectId);

            return config;

        } catch (Exception e) {
            logger.error("❌ 序列化知识点配置失败: {}", e.getMessage());
            throw new RuntimeException("保存知识点配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取知识点配置
     */
    @Cacheable(value = CacheConfig.KNOWLEDGE_CONFIG_CACHE, key = "#projectId")
    public Optional<KnowledgeConfiguration> getKnowledgeConfiguration(Long projectId) {
        logger.info("📋 获取知识点配置...");
        logger.info("   项目ID: {}", projectId);

        KnowledgeConfiguration config = knowledgeConfigurationMapper
                .findByProjectId(projectId);

        if (config != null) {
            logger.info("✅ 知识点配置获取成功");
            logger.info("🗄️ 知识点配置已缓存: {}", projectId);
            return Optional.of(config);
        } else {
            logger.info("⚠️  未找到知识点配置");
            return Optional.empty();
        }
    }



    /**
     * 为知识点添加uid
     */
    private Object addUidsToKnowledgePoints(Object knowledgePoints) {
        if (knowledgePoints == null) {
            return knowledgePoints;
        }

        Object copy = JsonUtils.deepCopy(knowledgePoints);
        JsonUtils.addUidToJsonNode(copy);
        return copy;
    }


    /**
     * 提取知识点配置中的叶子节点（带路径信息）
     */
    @Cacheable(value = CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE, key = "#configuration.hashCode()")
    public List<KnowledgePointScore> extractLeafKnowledgePoints(Object configuration) {
        if (configuration == null) {
            logWarning("知识点配置为空");
            return new ArrayList<>();
        }

        logInfo("开始提取叶子节点，配置类型: {}", configuration.getClass().getSimpleName());

        List<KnowledgePointScore> leafPoints = JsonUtils.extractLeafNodesWithPath(configuration, this::createKnowledgePointScoreWithPath);

        logInfo("提取到 {} 个叶子节点知识点", leafPoints.size());
        logInfo("🗄️ 叶子节点知识点已缓存");
        return leafPoints;
    }

    /**
     * 提取知识点配置中的叶子节点（带父级路径信息，用于知识点管理）
     */
    @Cacheable(value = CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE, key = "#projectId + '_with_paths'")
    public List<KnowledgePointScore> extractLeafKnowledgePointsWithPaths(Long projectId) {
        logInfo("开始提取带父级路径的叶子节点，项目ID: {}", projectId);

        Optional<KnowledgeConfiguration> configOpt = getKnowledgeConfiguration(projectId);
        if (!configOpt.isPresent()) {
            logWarning("项目 {} 没有知识点配置", projectId);
            return new ArrayList<>();
        }

        Object configuration = JsonUtils.parseJson(configOpt.get().getConfiguration());
        if (configuration == null) {
            logWarning("知识点配置解析失败");
            return new ArrayList<>();
        }

        List<KnowledgePointScore> leafPoints = JsonUtils.extractLeafNodesWithPath(configuration, this::createKnowledgePointScoreWithPath);

        logInfo("提取到 {} 个带父级路径的叶子节点知识点", leafPoints.size());
        logInfo("🗄️ 带父级路径的叶子节点已缓存");
        return leafPoints;
    }

    /**
     * 提取知识点配置中的叶子节点（带完整路径信息，用于卡片管理）
     */
    @Cacheable(value = CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE, key = "#projectId + '_with_full_paths'")
    public List<KnowledgePointScore> extractLeafKnowledgePointsWithFullPaths(Long projectId) {
        logInfo("开始提取带完整路径的叶子节点，项目ID: {}", projectId);

        Optional<KnowledgeConfiguration> configOpt = getKnowledgeConfiguration(projectId);
        if (!configOpt.isPresent()) {
            logWarning("项目 {} 没有知识点配置", projectId);
            return new ArrayList<>();
        }

        Object configuration = JsonUtils.parseJson(configOpt.get().getConfiguration());
        if (configuration == null) {
            logWarning("知识点配置解析失败");
            return new ArrayList<>();
        }

        List<KnowledgePointScore> leafPoints = JsonUtils.extractLeafNodesWithFullPath(configuration, this::createKnowledgePointScoreWithPath);

        logInfo("提取到 {} 个带完整路径的叶子节点知识点", leafPoints.size());
        logInfo("🗄️ 带完整路径的叶子节点已缓存");
        return leafPoints;
    }

    /**
     * 批量获取知识点路径信息（父级路径，用于知识点管理）
     */
    @Cacheable(value = CacheConfig.KNOWLEDGE_PATHS_CACHE, key = "#projectId")
    public Map<String, String> getKnowledgePointPaths(Long projectId) {
        logInfo("开始批量获取知识点父级路径，项目ID: {}", projectId);

        List<KnowledgePointScore> leafPoints = extractLeafKnowledgePointsWithPaths(projectId);
        Map<String, String> pathMap = new HashMap<>();

        for (KnowledgePointScore point : leafPoints) {
            if (point.getUid() != null) {
                pathMap.put(point.getUid(), point.getPath() != null ? point.getPath() : "");
            }
        }

        logInfo("批量获取到 {} 个知识点父级路径", pathMap.size());
        logInfo("🗄️ 知识点父级路径已缓存");
        return pathMap;
    }

    /**
     * 批量获取知识点完整路径信息（包含叶子节点本身，用于卡片管理）
     */
    @Cacheable(value = CacheConfig.KNOWLEDGE_PATHS_CACHE, key = "#projectId + '_full'")
    public Map<String, String> getKnowledgePointFullPaths(Long projectId) {
        logInfo("开始批量获取知识点完整路径，项目ID: {}", projectId);

        List<KnowledgePointScore> leafPoints = extractLeafKnowledgePointsWithFullPaths(projectId);
        Map<String, String> pathMap = new HashMap<>();

        for (KnowledgePointScore point : leafPoints) {
            if (point.getUid() != null) {
                pathMap.put(point.getUid(), point.getPath() != null ? point.getPath() : "");
            }
        }

        logInfo("批量获取到 {} 个知识点完整路径", pathMap.size());
        logInfo("🗄️ 知识点完整路径已缓存");
        return pathMap;
    }

    /**
     * 创建知识点评分对象
     */
    @SuppressWarnings("unused")
    private KnowledgePointScore createKnowledgePointScore(Object node) {
        if (!JsonUtils.isLeafNode(node)) {
            return null;
        }

        if (!(node instanceof JSONObject jsonObject)) {
            return null;
        }
        String uid = jsonObject.getString("uid");
        String name = jsonObject.getString("name");
        String description = jsonObject.getString("description");

        if (name == null) name = "";
        if (description == null) description = "";

        // 如果没有uid，根据name生成一个
        if (uid == null || uid.isEmpty()) {
            if (name != null && !name.isEmpty()) {
                uid = JsonUtils.generateMd5(name);
                logInfo("为叶子节点生成uid: {} -> {}", name, uid);
            } else {
                logWarning("叶子节点缺少name和uid，跳过");
                return null;
            }
        }

        String path = ""; // 暂时为空，需要根据具体需求实现

        KnowledgePointScore score = new KnowledgePointScore(uid, name, description, path);
        logDebug("添加叶子节点: {} ({})", name, uid);
        return score;
    }

    /**
     * 创建带路径的知识点评分对象
     */
    private KnowledgePointScore createKnowledgePointScoreWithPath(Object node, String path) {
        if (!JsonUtils.isLeafNode(node)) {
            return null;
        }

        if (!(node instanceof JSONObject jsonObject)) {
            return null;
        }
        String uid = jsonObject.getString("uid");
        String name = jsonObject.getString("name");
        String description = jsonObject.getString("description");

        if (name == null) name = "";
        if (description == null) description = "";

        // 如果没有uid，根据name生成一个
        if (uid == null || uid.isEmpty()) {
            if (name != null && !name.isEmpty()) {
                uid = JsonUtils.generateMd5(name);
                logInfo("为叶子节点生成uid: {} -> {}", name, uid);
            } else {
                logWarning("叶子节点缺少name和uid，跳过");
                return null;
            }
        }

        KnowledgePointScore score = new KnowledgePointScore(uid, name, description, path);
        logInfo("添加带路径的叶子节点: {} ({}) - 路径: '{}'", name, uid, path);
        return score;
    }

    /**
     * 更新知识点配置中的评分
     */
    public Object updateKnowledgePointScores(Object configuration, Map<String, Integer> scoreMap) {
        if (configuration == null || scoreMap == null || scoreMap.isEmpty()) {
            return configuration;
        }

        try {
            // 深拷贝JSON节点
            Object copy = JsonUtils.deepCopy(configuration);

            logger.warn("⚠️  这个方法需要重新实现以支持fastjson2");

            return copy;
        } catch (Exception e) {
            logger.error("❌ 更新知识点评分失败: {}", e.getMessage());
            return configuration; // 返回原始数据
        }
    }

    /**
     * 更新知识点评分并保存配置（带缓存清理）
     */
    @Transactional
    public KnowledgeConfiguration updateKnowledgePointScoresAndSave(Long projectId, Map<String, Integer> scoreMap, String userId, String notes) {
        logger.info("📊 更新知识点评分并保存配置...");
        logger.info("   项目ID: {}", projectId);
        logger.info("   评分数量: {}", scoreMap != null ? scoreMap.size() : 0);

        try {
            // 1. 获取当前知识点配置
            Optional<KnowledgeConfiguration> configOpt = getKnowledgeConfiguration(projectId);
            if (!configOpt.isPresent()) {
                throw new RuntimeException("项目尚未配置知识点");
            }

            KnowledgeConfiguration currentConfig = configOpt.get();
            Object configData = JsonUtils.parseJson(currentConfig.getConfiguration());

            // 2. 更新评分
            Object updatedConfig = updateKnowledgePointScores(configData, scoreMap);

            // 3. 保存更新后的配置
            KnowledgeConfiguration savedConfig = saveKnowledgeConfiguration(projectId, updatedConfig, userId, notes);

            logger.info("✅ 知识点评分更新并保存成功");

            // 清除知识点相关缓存
            cacheHelper.clearKnowledgeRelatedCaches(projectId);

            return savedConfig;

        } catch (Exception e) {
            logger.error("❌ 更新知识点评分失败: {}", e.getMessage());
            throw new RuntimeException("更新知识点评分失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清空知识点评分并保存配置（带缓存清理）
     */
    @Transactional
    public KnowledgeConfiguration clearKnowledgePointScoresAndSave(Long projectId, String userId, String notes) {
        logger.info("🧹 清空知识点评分并保存配置...");
        logger.info("   项目ID: {}", projectId);

        try {
            // 1. 获取当前知识点配置
            Optional<KnowledgeConfiguration> configOpt = getKnowledgeConfiguration(projectId);
            if (!configOpt.isPresent()) {
                throw new RuntimeException("项目尚未配置知识点");
            }

            KnowledgeConfiguration currentConfig = configOpt.get();
            Object configData = JsonUtils.parseJson(currentConfig.getConfiguration());

            // 2. 清空所有评分
            Object clearedConfig = clearAllScores(configData);

            // 3. 保存更新后的配置
            KnowledgeConfiguration savedConfig = saveKnowledgeConfiguration(projectId, clearedConfig, userId, notes);

            logger.info("✅ 知识点评分清空并保存成功");

            // 清除知识点相关缓存
            cacheHelper.clearKnowledgeRelatedCaches(projectId);

            return savedConfig;

        } catch (Exception e) {
            logger.error("❌ 清空知识点评分失败: {}", e.getMessage());
            throw new RuntimeException("清空知识点评分失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清空知识点配置中的所有评分
     */
    public Object clearAllScores(Object configuration) {
        if (configuration == null) {
            return configuration;
        }

        try {
            // 深拷贝JSON节点
            Object copy = JsonUtils.deepCopy(configuration);

            // 使用JsonUtils清空评分
            JsonUtils.clearScoresInJsonNode(copy);

            logger.info("✅ 已清空所有知识点评分");
            return copy;
        } catch (Exception e) {
            logger.error("❌ 清空知识点评分失败: {}", e.getMessage());
            return configuration; // 返回原始数据
        }
    }

    /**
     * 清空项目的所有知识点相关缓存
     */
    public void clearKnowledgeCache(Long projectId) {
        cacheHelper.clearKnowledgeRelatedCaches(projectId);
    }

    /**
     * 清空所有知识点相关缓存（全局清理）
     */
    public void clearAllKnowledgeCache() {
        cacheHelper.clearCaches(
                CacheConfig.KNOWLEDGE_CONFIG_CACHE,
                CacheConfig.KNOWLEDGE_LEAF_POINTS_CACHE,
                CacheConfig.KNOWLEDGE_PATHS_CACHE,
                CacheConfig.PROJECT_WITH_KNOWLEDGE_CACHE
        );
    }
}
