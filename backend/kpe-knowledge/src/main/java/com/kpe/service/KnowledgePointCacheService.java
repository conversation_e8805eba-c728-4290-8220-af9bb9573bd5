package com.kpe.service;

import com.kpe.dto.KnowledgePointScore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 知识点缓存服务
 * 在系统启动时缓存所有项目的知识点信息，支持批量获取路径
 */
@Service
public class KnowledgePointCacheService implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(KnowledgePointCacheService.class);
    // 缓存：项目ID -> 知识点ID -> 知识点路径
    private final Map<Long, Map<String, String>> knowledgePointPathsCache = new ConcurrentHashMap<>();
    // 缓存：项目ID -> 知识点ID -> 知识点信息
    private final Map<Long, Map<String, KnowledgePointScore>> knowledgePointsCache = new ConcurrentHashMap<>();
    @Autowired
    private KnowledgeService knowledgeService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("🚀 知识点缓存服务启动完成");
    }

    /**
     * 刷新指定项目的知识点缓存
     */
    public void refreshAllCache(List<Long> projectIds) {
        try {
            for (Long projectId : projectIds) {
                refreshProjectCache(projectId);
            }

            logger.info("🔄 已刷新 {} 个项目的知识点缓存", projectIds.size());
        } catch (Exception e) {
            logger.error("❌ 刷新知识点缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 刷新指定项目的知识点缓存
     */
    public void refreshProjectCache(Long projectId) {
        try {
            logger.debug("🔄 刷新项目 {} 的知识点缓存", projectId);

            // 获取知识点路径映射
            Map<String, String> pathMap = knowledgeService.getKnowledgePointPaths(projectId);
            knowledgePointPathsCache.put(projectId, new ConcurrentHashMap<>(pathMap));

            // 获取叶子知识点信息
            List<KnowledgePointScore> leafPoints = knowledgeService.extractLeafKnowledgePointsWithPaths(projectId);
            Map<String, KnowledgePointScore> pointsMap = leafPoints.stream()
                    .collect(Collectors.toMap(KnowledgePointScore::getUid, point -> point));
            knowledgePointsCache.put(projectId, new ConcurrentHashMap<>(pointsMap));

            logger.debug("✅ 项目 {} 缓存刷新完成，知识点数量: {}", projectId, pathMap.size());
        } catch (Exception e) {
            logger.error("❌ 刷新项目 {} 知识点缓存失败: {}", projectId, e.getMessage(), e);
        }
    }

    /**
     * 获取单个知识点路径（从缓存）
     */
    public String getKnowledgePointPath(Long projectId, String knowledgePointId) {
        Map<String, String> projectCache = knowledgePointPathsCache.get(projectId);
        if (projectCache == null) {
            // 缓存不存在，尝试刷新
            refreshProjectCache(projectId);
            projectCache = knowledgePointPathsCache.get(projectId);
        }

        return projectCache != null ? projectCache.get(knowledgePointId) : null;
    }

    /**
     * 批量获取知识点路径（从缓存）
     */
    public Map<String, String> getKnowledgePointPaths(Long projectId, List<String> knowledgePointIds) {
        Map<String, String> projectCache = knowledgePointPathsCache.get(projectId);
        if (projectCache == null) {
            // 缓存不存在，尝试刷新
            refreshProjectCache(projectId);
            projectCache = knowledgePointPathsCache.get(projectId);
        }

        if (projectCache == null) {
            return new HashMap<>();
        }

        Map<String, String> result = new HashMap<>();
        for (String knowledgePointId : knowledgePointIds) {
            String path = projectCache.get(knowledgePointId);
            if (path != null) {
                result.put(knowledgePointId, path);
            }
        }

        return result;
    }

    /**
     * 获取项目所有知识点路径（从缓存）
     */
    public Map<String, String> getAllKnowledgePointPaths(Long projectId) {
        Map<String, String> projectCache = knowledgePointPathsCache.get(projectId);
        if (projectCache == null) {
            // 缓存不存在，尝试刷新
            refreshProjectCache(projectId);
            projectCache = knowledgePointPathsCache.get(projectId);
        }

        return projectCache != null ? new HashMap<>(projectCache) : new HashMap<>();
    }

    /**
     * 获取知识点信息（从缓存）
     */
    public KnowledgePointScore getKnowledgePoint(Long projectId, String knowledgePointId) {
        Map<String, KnowledgePointScore> projectCache = knowledgePointsCache.get(projectId);
        if (projectCache == null) {
            // 缓存不存在，尝试刷新
            refreshProjectCache(projectId);
            projectCache = knowledgePointsCache.get(projectId);
        }

        return projectCache != null ? projectCache.get(knowledgePointId) : null;
    }

    /**
     * 批量获取知识点信息（从缓存）
     */
    public Map<String, KnowledgePointScore> getKnowledgePoints(Long projectId, List<String> knowledgePointIds) {
        Map<String, KnowledgePointScore> projectCache = knowledgePointsCache.get(projectId);
        if (projectCache == null) {
            // 缓存不存在，尝试刷新
            refreshProjectCache(projectId);
            projectCache = knowledgePointsCache.get(projectId);
        }

        if (projectCache == null) {
            return new HashMap<>();
        }

        Map<String, KnowledgePointScore> result = new HashMap<>();
        for (String knowledgePointId : knowledgePointIds) {
            KnowledgePointScore point = projectCache.get(knowledgePointId);
            if (point != null) {
                result.put(knowledgePointId, point);
            }
        }

        return result;
    }

    /**
     * 获取项目所有知识点信息（从缓存）
     */
    public List<KnowledgePointScore> getAllKnowledgePoints(Long projectId) {
        Map<String, KnowledgePointScore> projectCache = knowledgePointsCache.get(projectId);
        if (projectCache == null) {
            // 缓存不存在，尝试刷新
            refreshProjectCache(projectId);
            projectCache = knowledgePointsCache.get(projectId);
        }

        return projectCache != null ? new ArrayList<>(projectCache.values()) : new ArrayList<>();
    }

    /**
     * 清除指定项目的缓存
     */
    public void clearProjectCache(Long projectId) {
        knowledgePointPathsCache.remove(projectId);
        knowledgePointsCache.remove(projectId);
        logger.info("🗑️ 已清除项目 {} 的知识点缓存", projectId);
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        knowledgePointPathsCache.clear();
        knowledgePointsCache.clear();
        logger.info("🗑️ 已清除所有知识点缓存");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cachedProjects", knowledgePointPathsCache.size());

        int totalKnowledgePoints = knowledgePointPathsCache.values().stream()
                .mapToInt(Map::size)
                .sum();
        stats.put("totalKnowledgePoints", totalKnowledgePoints);

        Map<Long, Integer> projectStats = new HashMap<>();
        knowledgePointPathsCache.forEach((projectId, cache) ->
                projectStats.put(projectId, cache.size()));
        stats.put("projectStats", projectStats);

        return stats;
    }
}
