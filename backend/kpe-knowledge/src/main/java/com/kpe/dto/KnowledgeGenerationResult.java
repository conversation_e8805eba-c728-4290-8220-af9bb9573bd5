package com.kpe.dto;

/**
 * 知识点生成结果
 */
public class KnowledgeGenerationResult {

    private boolean success;
    private Object knowledgePoints;
    private String chatId;
    private String conversationId;
    private String status;
    private boolean hasValidJson;
    private String message;
    private String fullContent;
    private String errorMessage;

    // Constructors
    public KnowledgeGenerationResult() {
    }

    private KnowledgeGenerationResult(boolean success) {
        this.success = success;
    }

    // Static factory methods
    public static KnowledgeGenerationResult success(Object knowledgePoints, String chatId,
                                                    String conversationId, String message) {
        KnowledgeGenerationResult result = new KnowledgeGenerationResult(true);
        result.knowledgePoints = knowledgePoints;
        result.chatId = chatId;
        result.conversationId = conversationId;
        result.status = "completed";
        result.hasValidJson = true;
        result.message = message;
        return result;
    }

    public static KnowledgeGenerationResult manualParse(String fullContent, String chatId,
                                                        String conversationId, String message) {
        KnowledgeGenerationResult result = new KnowledgeGenerationResult(true);
        result.fullContent = fullContent;
        result.chatId = chatId;
        result.conversationId = conversationId;
        result.status = "completed_manual_parse";
        result.hasValidJson = false;
        result.message = message;
        return result;
    }

    public static KnowledgeGenerationResult error(String errorMessage) {
        KnowledgeGenerationResult result = new KnowledgeGenerationResult(false);
        result.errorMessage = errorMessage;
        result.status = "failed";
        result.hasValidJson = false;
        return result;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Object getKnowledgePoints() {
        return knowledgePoints;
    }

    public void setKnowledgePoints(Object knowledgePoints) {
        this.knowledgePoints = knowledgePoints;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isHasValidJson() {
        return hasValidJson;
    }

    public void setHasValidJson(boolean hasValidJson) {
        this.hasValidJson = hasValidJson;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getFullContent() {
        return fullContent;
    }

    public void setFullContent(String fullContent) {
        this.fullContent = fullContent;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
