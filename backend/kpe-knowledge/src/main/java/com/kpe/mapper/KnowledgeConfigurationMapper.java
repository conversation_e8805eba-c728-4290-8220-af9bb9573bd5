package com.kpe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kpe.entity.KnowledgeConfiguration;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * KnowledgeConfiguration Mapper接口
 */
@Mapper
public interface KnowledgeConfigurationMapper extends BaseMapper<KnowledgeConfiguration> {

    /**
     * 查找项目的知识点配置
     */
    @Select("SELECT * FROM knowledge_configurations WHERE project_id = #{projectId} ORDER BY created_at DESC LIMIT 1")
    KnowledgeConfiguration findByProjectId(@Param("projectId") Long projectId);



    /**
     * 插入知识点配置（处理jsonb类型）
     */
    @Insert("INSERT INTO knowledge_configurations (project_id, configuration, created_at, created_by, notes) " +
            "VALUES (#{projectId}, " +
            "CASE WHEN #{configuration} IS NULL THEN NULL ELSE #{configuration}::jsonb END, " +
            "#{createdAt}, #{createdBy}, #{notes})")
    int insertWithJsonb(KnowledgeConfiguration config);

    /**
     * 根据项目ID删除所有知识点配置
     */
    @Delete("DELETE FROM knowledge_configurations WHERE project_id = #{projectId}")
    int deleteByProjectId(@Param("projectId") Long projectId);
}
