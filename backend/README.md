# KPE Backend (Java Spring Boot)

## 技术栈

- **Java 17**
- **Spring Boot 3.1.12**
- **Spring Data JPA**
- **PostgreSQL**
- **WebFlux** (用于HTTP客户端)
- **Maven**

## 扣子API调用方式

与Node.js版本的流式调用不同，Java版本采用**非流式轮询**方式：

1. **发起对话**：调用 `/v3/chat` 接口，设置 `stream=false`
2. **轮询结果**：定期调用 `/v3/chat/retrieve` 接口检查对话状态
3. **获取结果**：当状态为 `completed` 时，提取AI回复内容

### 轮询配置
```yaml
coze:
  api:
    polling:
      interval: 10000   # 轮询间隔3秒
      max-attempts: 100  # 最大轮询次数（总计5分钟）
```

## 配置
application.yml

## 运行
### 1. 使用Maven
```bash
# 编译
mvn clean compile

# 运行
mvn spring-boot:run

# 打包
mvn clean package
java -jar target/kpe-backend-1.0.0.jar
```

### 2. 使用IDE
## API接口

### 项目管理
- `GET /api/projects` - 获取项目列表
- `GET /api/projects/{id}` - 获取项目详情
- `POST /api/projects` - 创建项目
- `PUT /api/projects/{id}` - 更新项目
- `DELETE /api/projects/{id}` - 删除项目

### 知识点管理
- `GET /api/projects/{id}/knowledge` - 获取知识点配置
- `POST /api/projects/{id}/knowledge` - 保存知识点配置
- `POST /api/projects/{id}/knowledge/generate` - AI生成知识点（非流式）
- `GET /api/projects/{id}/knowledge/history` - 获取配置历史
- `GET /api/projects/{id}/ai-logs` - 获取AI生成记录

### 扣子智能体
- `POST /api/chat` - 智能体对话（非流式）

### 健康检查
- `GET /health` - 健康检查

## 与Node.js版本的差异

1. **API调用方式**：
   - Node.js: 流式调用 + 实时处理
   - Java: 非流式调用 + 轮询获取结果

2. **并发处理**：
   - Node.js: 事件驱动 + 回调
   - Java: CompletableFuture + 异步处理

3. **JSON处理**：
   - Node.js: 原生JSON支持
   - Java: Jackson + JsonNode

4. **数据库访问**：
   - Node.js: 原生SQL + pg库
   - Java: JPA + Hibernate

## 日志
应用使用SLF4J + Logback进行日志记录：

## 部署

### 1. 生产环境配置
```yaml
spring:
  profiles:
    active: prod
  jpa:
    show-sql: false
logging:
  level:
    com.kpe: INFO
```

### 2. Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/kpe-backend-1.0.0.jar app.jar
EXPOSE 3000
ENTRYPOINT ["java", "-jar", "/app.jar"]
```