import axios from 'axios'
import { ElMessage } from 'element-plus'
import { validateAndConvertId, isValidId } from '@/utils/validation'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 180000
})

// 响应拦截器 - 处理后端统一响应格式
api.interceptors.response.use(
  response => {
    // 如果响应数据有success字段，说明是后端统一格式，提取data字段
    if (response.data && typeof response.data === 'object' && 'success' in response.data) {
      return {
        ...response,
        data: response.data.data // 提取实际数据
      }
    }
    return response
  },
  error => {
    console.error('❌ Flashcard API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// 参数验证辅助函数
const validateIdParam = (id, paramName = 'ID') => {
  if (!isValidId(id, paramName)) {
    throw new Error(`${paramName} 参数无效: ${id}`)
  }
  return validateAndConvertId(id, paramName)
}

/**
 * 卡片相关API
 */
export const flashcardAPI = {
  /**
   * 获取项目的所有卡片
   */
  getFlashcards(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get('/flashcards', { params: { projectId: validId } })
  },

  /**
   * 根据知识点获取卡片
   */
  getFlashcardsByKnowledgePoint(projectId, knowledgePointId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    if (!knowledgePointId) {
      throw new Error('知识点ID不能为空')
    }
    return api.get(`/flashcards/knowledge-point/${knowledgePointId}`, {
      params: { projectId: validProjectId }
    })
  },

  /**
   * 根据难度获取卡片
   */
  getFlashcardsByDifficulty(projectId, difficulty) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    if (!difficulty || difficulty < 1 || difficulty > 5) {
      throw new Error('难度等级必须在1-5之间')
    }
    return api.get(`/flashcards/difficulty/${difficulty}`, {
      params: { projectId: validProjectId }
    })
  },

  /**
   * 获取需要复习的卡片
   */
  getCardsNeedReview(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get('/flashcards/need-review', { params: { projectId: validId } })
  },

  /**
   * 获取从未学习过的卡片
   */
  getNeverStudiedCards(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get('/flashcards/never-studied', { params: { projectId: validId } })
  },

  /**
   * 创建卡片
   */
  createFlashcard(data) {
    if (!data || !data.projectId || !data.title || !data.frontContent) {
      throw new Error('卡片数据不完整')
    }
    // backContent 现在是可选的，不需要验证
    return api.post('/flashcards', data)
  },

  /**
   * 更新卡片
   */
  updateFlashcard(id, projectId, data) {
    const validId = validateIdParam(id, '卡片ID')
    const validProjectId = validateIdParam(projectId, '项目ID')
    if (!data || !data.title || !data.frontContent) {
      throw new Error('卡片数据不完整')
    }
    // backContent 现在是可选的，不需要验证
    return api.put(`/flashcards/${validId}`, data, {
      params: { projectId: validProjectId }
    })
  },

  /**
   * 删除卡片
   */
  deleteFlashcard(id, projectId) {
    const validId = validateIdParam(id, '卡片ID')
    const validProjectId = validateIdParam(projectId, '项目ID')
    return api.delete(`/flashcards/${validId}`, {
      params: { projectId: validProjectId }
    })
  },

  /**
   * 记录学习结果
   */
  recordStudyResult(data) {
    if (!data || !data.flashcardId || !data.projectId || data.isCorrect === undefined) {
      throw new Error('学习记录数据不完整')
    }
    return api.post('/flashcards/study-record', data)
  },

  /**
   * 获取卡片统计信息
   */
  getStats(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get('/flashcards/stats', { params: { projectId: validId } })
  },

  /**
   * 获取项目中所有卡片的正确率信息
   */
  getAccuracyRates(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get('/flashcards/accuracy-rates', { params: { projectId: validId } })
  },

  /**
   * 重置单个卡片的统计信息（正确率归零）
   */
  resetFlashcardStats(id, projectId) {
    const validId = validateIdParam(id, '卡片ID')
    const validProjectId = validateIdParam(projectId, '项目ID')
    return api.post(`/flashcards/${validId}/reset-stats`, null, {
      params: { projectId: validProjectId }
    })
  }
}

/**
 * 卡片难度配置
 */
export const DIFFICULTY_CONFIG = {
  1: {
    text: '简单',
    color: '#4CAF50',
    bgColor: '#E8F5E8'
  },
  2: {
    text: '容易',
    color: '#2196F3',
    bgColor: '#E3F2FD'
  },
  3: {
    text: '中等',
    color: '#9C27B0',
    bgColor: '#F3E5F5'
  },
  4: {
    text: '困难',
    color: '#FF9800',
    bgColor: '#FFF3E0'
  },
  5: {
    text: '极难',
    color: '#F44336',
    bgColor: '#FFEBEE'
  }
}

/**
 * 获取难度配置
 */
export const getDifficultyConfig = (difficulty) => {
  return DIFFICULTY_CONFIG[difficulty] || {
    text: '未知',
    color: '#9E9E9E',
    bgColor: '#F5F5F5'
  }
}
