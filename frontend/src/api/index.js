import axios from 'axios'
import { ElMessage } from 'element-plus'
import { validateAndConvertId, isValidId } from '@/utils/validation'

// API配置 - 简化配置，只使用baseURL和固定超时时间
const getApiConfig = () => {
  return {
    baseURL: '/api', // 固定使用/api路径，通过Vite代理到后端
    timeout: 180000  // 固定3分钟超时
  }
}

// 创建axios实例
const api = axios.create(getApiConfig())

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('🚀 API请求:', config.method?.toUpperCase(), config.url, config.data)

    // 验证URL中的ID参数
    const urlParts = config.url.split('/')
    for (let i = 0; i < urlParts.length; i++) {
      const part = urlParts[i]
      // 检查是否为ID参数（纯数字或看起来像ID的部分）
      if (/^\d+$/.test(part)) {
        const id = parseInt(part, 10)
        if (isNaN(id) || id <= 0) {
          console.error('❌ URL中包含无效ID:', part, '完整URL:', config.url)
          return Promise.reject(new Error(`URL中包含无效ID: ${part}`))
        }
      }
    }

    return config
  },
  error => {
    console.error('❌ 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('✅ API响应:', response.config.url, response.data)
    return response
  },
  error => {
    console.error('❌ 响应错误:', error.response?.data || error.message)

    let message = '请求失败'

    // 处理不同类型的错误
    if (error.response?.data?.error?.message) {
      message = error.response.data.error.message
    } else if (error.response?.data?.message) {
      message = error.response.data.message
    } else if (error.message) {
      message = error.message
    }

    // 特殊处理类型转换错误
    if (message.includes('Failed to convert value of type') && message.includes('java.lang.String') && message.includes('java.lang.Long')) {
      message = '参数格式错误：ID必须是有效的数字'
    }

    ElMessage.error(message)

    return Promise.reject(error)
  }
)

// 参数验证辅助函数
const validateIdParam = (id, paramName = 'ID') => {
  if (!isValidId(id, paramName)) {
    throw new Error(`${paramName} 参数无效: ${id}`)
  }
  return validateAndConvertId(id, paramName)
}

// 项目管理API
export const projectAPI = {
  // 获取项目列表
  getProjects() {
    return api.get('/projects')
  },

  // 获取项目详情
  getProject(id) {
    const validId = validateIdParam(id, '项目ID')
    return api.get(`/projects/${validId}`)
  },

  // 创建项目
  createProject(data) {
    if (!data || !data.name || !data.name.trim()) {
      throw new Error('项目名称不能为空')
    }
    return api.post('/projects', data)
  },

  // 更新项目
  updateProject(id, data) {
    const validId = validateIdParam(id, '项目ID')
    if (!data || !data.name || !data.name.trim()) {
      throw new Error('项目名称不能为空')
    }
    return api.put(`/projects/${validId}`, data)
  },

  // 删除项目
  deleteProject(id) {
    const validId = validateIdParam(id, '项目ID')
    return api.delete(`/projects/${validId}`)
  }
}

// 知识点管理API
export const knowledgeAPI = {
  // 获取知识点配置
  getKnowledgeConfiguration(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/knowledge`)
  },

  // 保存知识点配置
  saveKnowledgeConfiguration(projectId, data) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!data || !data.configuration) {
      throw new Error('知识点配置数据不能为空')
    }
    return api.post(`/projects/${validId}/knowledge`, data)
  },



  // AI流式生成知识点
  generateKnowledgePointsStream(projectId, prompt, onData, onComplete, onError) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!prompt || !prompt.trim()) {
      throw new Error('生成提示不能为空')
    }
    const url = `/api/projects/${validId}/knowledge/generate`

    // 使用fetch + ReadableStream（EventSource不支持POST）
    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt })
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      const processStream = () => {
        return reader.read().then(({ done, value }) => {
          if (done) {
            console.log('🏁 流式响应结束')
            return
          }

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留最后一行（可能不完整）

          for (const line of lines) {
            if (line.trim() === '') continue

            if (line.startsWith('data: ')) {
              const dataStr = line.substring(6)

              if (dataStr === '[DONE]') {
                console.log('✅ 流式数据传输完成')
                return
              }

              try {
                const data = JSON.parse(dataStr)

                if (data.type === 'start') {
                  console.log('🌊 开始接收流式数据')
                } else if (data.type === 'content') {
                  onData && onData(data)
                } else if (data.type === 'message_completed') {
                  console.log('💬 消息完成')
                  onData && onData(data)
                } else if (data.type === 'chat_completed') {
                  console.log('🎯 对话完成')
                  onData && onData(data)
                } else if (data.type === 'complete') {
                  console.log('✅ AI生成真正完成')
                  onComplete && onComplete(data)
                  return
                } else if (data.type === 'error') {
                  onError && onError(new Error(data.message))
                  return
                }
              } catch (parseError) {
                console.warn('⚠️  解析SSE数据失败:', parseError.message, 'Data:', dataStr)
              }
            }
          }

          return processStream()
        })
      }

      return processStream()
    }).catch(error => {
      console.error('❌ 流式请求失败:', error)
      onError && onError(error)
    })
  },



  // 获取知识点叶子节点列表
  getLeafPoints(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/knowledge-paths/leaf-points`)
  },

  // 清空知识点缓存
  clearKnowledgeCache(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.delete(`/projects/${validId}/knowledge/cache`)
  }
}

// 考卷API
export const examAPI = {
  // 生成考卷
  generateExamPaper(projectId, examRequest) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!examRequest || !examRequest.title || !examRequest.title.trim()) {
      throw new Error('考卷标题不能为空')
    }
    return api.post(`/projects/${validId}/exams/generate`, examRequest)
  },

  // 获取项目的考卷列表
  getExamPapers(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/exams`)
  },

  // 获取项目的考卷管理数据（包含统计信息）
  getExamPapersWithStatistics(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/exams/management`)
  },

  // 获取考卷详情
  getExamPaper(projectId, examId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    return api.get(`/projects/${validProjectId}/exams/${validExamId}`)
  },

  // 更新考卷状态
  updateExamPaperStatus(projectId, examId, status) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    if (!status || !status.trim()) {
      throw new Error('状态不能为空')
    }
    return api.put(`/projects/${validProjectId}/exams/${validExamId}/status?status=${status}`)
  },

  // 删除考卷
  deleteExamPaper(projectId, examId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    return api.delete(`/projects/${validProjectId}/exams/${validExamId}`)
  }
}

// 考试记录API
export const examRecordAPI = {
  // 添加考试记录
  addExamRecord(projectId, examId, recordData) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    if (!recordData || recordData.score === undefined || recordData.score === null) {
      throw new Error('考试记录数据不完整')
    }
    return api.post(`/projects/${validProjectId}/exams/${validExamId}/records`, recordData)
  },

  // 获取考试记录列表
  getExamRecords(projectId, examId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    return api.get(`/projects/${validProjectId}/exams/${validExamId}/records`)
  },

  // 获取考试记录详情
  getExamRecord(projectId, examId, recordId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    const validRecordId = validateIdParam(recordId, '记录ID')
    return api.get(`/projects/${validProjectId}/exams/${validExamId}/records/${validRecordId}`)
  },

  // 更新考试记录
  updateExamRecord(projectId, examId, recordId, recordData) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    const validRecordId = validateIdParam(recordId, '记录ID')
    if (!recordData || recordData.score === undefined || recordData.score === null) {
      throw new Error('考试记录数据不完整')
    }
    return api.put(`/projects/${validProjectId}/exams/${validExamId}/records/${validRecordId}`, recordData)
  },

  // 删除考试记录
  deleteExamRecord(projectId, examId, recordId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    const validRecordId = validateIdParam(recordId, '记录ID')
    return api.delete(`/projects/${validProjectId}/exams/${validExamId}/records/${validRecordId}`)
  },

  // 获取考试统计信息
  getExamStatistics(projectId, examId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validExamId = validateIdParam(examId, '考卷ID')
    return api.get(`/projects/${validProjectId}/exams/${validExamId}/records/statistics`)
  }
}

// 视频教程API
export const videoTutorialAPI = {
  // 生成视频教程
  generateVideoTutorial(projectId, tutorialRequest) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!tutorialRequest || !tutorialRequest.knowledgePointName || !tutorialRequest.knowledgePointName.trim()) {
      throw new Error('知识点名称不能为空')
    }
    if (!tutorialRequest.knowledgePointPath || !tutorialRequest.knowledgePointPath.trim()) {
      throw new Error('知识点路径不能为空')
    }
    return api.post(`/projects/${validId}/video-tutorials/generate`, tutorialRequest)
  },

  // 获取项目的视频教程列表
  getVideoTutorials(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/video-tutorials`)
  },

  // 获取视频教程详情
  getVideoTutorial(projectId, tutorialId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validTutorialId = validateIdParam(tutorialId, '教程ID')
    return api.get(`/projects/${validProjectId}/video-tutorials/${validTutorialId}`)
  },

  // 根据知识点名称获取视频教程
  getVideoTutorialByKnowledgePoint(projectId, knowledgePointName) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!knowledgePointName || !knowledgePointName.trim()) {
      throw new Error('知识点名称不能为空')
    }
    return api.get(`/projects/${validId}/video-tutorials/by-knowledge-point?knowledgePointName=${encodeURIComponent(knowledgePointName)}`)
  },

  // 删除视频教程
  deleteTutorial(tutorialId) {
    const validId = validateIdParam(tutorialId, '教程ID')
    return api.delete(`/video-tutorials/${validId}`)
  }
}

// 错题管理API
export const wrongQuestionAPI = {
  // 创建错题
  createWrongQuestion(projectId, questionData) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!questionData || !questionData.knowledgePointId || !questionData.contentMarkdown) {
      throw new Error('错题数据不完整')
    }
    // 确保请求数据中包含 projectId
    const requestData = {
      ...questionData,
      projectId: validId
    }
    return api.post(`/projects/${validId}/wrong-questions`, requestData)
  },

  // 获取错题列表
  getWrongQuestions(projectId, params = {}) {
    const validId = validateIdParam(projectId, '项目ID')
    const queryString = new URLSearchParams(params).toString()
    return api.get(`/projects/${validId}/wrong-questions${queryString ? `?${queryString}` : ''}`)
  },

  // 获取错题详情
  getWrongQuestion(projectId, questionId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validQuestionId = validateIdParam(questionId, '错题ID')
    return api.get(`/projects/${validProjectId}/wrong-questions/${validQuestionId}`)
  },

  // 更新错题
  updateWrongQuestion(projectId, questionId, questionData) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validQuestionId = validateIdParam(questionId, '错题ID')
    if (!questionData || !questionData.knowledgePointId || !questionData.contentMarkdown) {
      throw new Error('错题数据不完整')
    }
    // 确保请求数据中包含 projectId
    const requestData = {
      ...questionData,
      projectId: validProjectId
    }
    return api.put(`/projects/${validProjectId}/wrong-questions/${validQuestionId}`, requestData)
  },

  // 删除错题
  deleteWrongQuestion(projectId, questionId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validQuestionId = validateIdParam(questionId, '错题ID')
    return api.delete(`/projects/${validProjectId}/wrong-questions/${validQuestionId}`)
  },

  // 获取错题统计信息
  getWrongQuestionStatistics(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/wrong-questions/statistics`)
  },

  // 获取错题相关的知识点列表
  getWrongQuestionKnowledgePoints(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/wrong-questions/knowledge-points`)
  }
}

// 复习管理API
export const reviewAPI = {
  // 获取复习配置
  getReviewConfiguration(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/reviews/configuration`)
  },

  // 保存复习配置
  saveReviewConfiguration(projectId, configData) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!configData || !configData.reviewIntervals) {
      throw new Error('复习配置数据不完整')
    }
    return api.post(`/projects/${validId}/reviews/configuration`, configData)
  },

  // 更新复习配置
  updateReviewConfiguration(projectId, configData) {
    const validId = validateIdParam(projectId, '项目ID')
    if (!configData || !configData.reviewIntervals) {
      throw new Error('复习配置数据不完整')
    }
    return api.put(`/projects/${validId}/reviews/configuration`, configData)
  },

  // 重置复习配置为默认值
  resetReviewConfiguration(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.post(`/projects/${validId}/reviews/configuration/reset`)
  },

  // 获取今日需要复习的错题
  getTodayReviewQuestions(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/reviews/today`)
  },

  // 标记复习完成
  markReviewCompleted(projectId, reviewId) {
    const validProjectId = validateIdParam(projectId, '项目ID')
    const validReviewId = validateIdParam(reviewId, '复习记录ID')
    return api.post(`/projects/${validProjectId}/reviews/complete/${validReviewId}`)
  },

  // 获取复习统计信息
  getReviewStatistics(projectId) {
    const validId = validateIdParam(projectId, '项目ID')
    return api.get(`/projects/${validId}/reviews/statistics`)
  }
}

// 扣子智能体API
export const cozeAPI = {
  // 智能体对话
  chat(message, botId, userId) {
    return api.post('/chat', { message, botId, userId })
  },

  // 获取智能体列表
  getBots(spaceId) {
    return api.get(`/bots${spaceId ? `?spaceId=${spaceId}` : ''}`)
  }
}

export default api
