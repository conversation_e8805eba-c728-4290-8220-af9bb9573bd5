import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { VALIDATION_RULES } from '@/utils/constants'

/**
 * 表单验证 Composable
 * 提供统一的表单验证逻辑和规则管理
 */
export function useFormValidation() {
  
  // 验证状态
  const validationErrors = ref({})
  const isValidating = ref(false)
  
  // 清除验证错误
  const clearValidationErrors = (field = null) => {
    if (field) {
      delete validationErrors.value[field]
    } else {
      validationErrors.value = {}
    }
  }
  
  // 设置验证错误
  const setValidationError = (field, message) => {
    validationErrors.value[field] = message
  }
  
  // 验证单个字段
  const validateField = (value, rules, fieldName = '') => {
    if (!Array.isArray(rules)) return true
    
    for (const rule of rules) {
      // 必填验证
      if (rule.required && (!value || value.toString().trim() === '')) {
        const message = rule.message || `${fieldName}不能为空`
        if (fieldName) setValidationError(fieldName, message)
        return { valid: false, message }
      }
      
      // 长度验证
      if (value && rule.min && value.toString().length < rule.min) {
        const message = rule.message || `${fieldName}长度不能少于${rule.min}个字符`
        if (fieldName) setValidationError(fieldName, message)
        return { valid: false, message }
      }
      
      if (value && rule.max && value.toString().length > rule.max) {
        const message = rule.message || `${fieldName}长度不能超过${rule.max}个字符`
        if (fieldName) setValidationError(fieldName, message)
        return { valid: false, message }
      }
      
      // 正则验证
      if (value && rule.pattern && !rule.pattern.test(value)) {
        const message = rule.message || `${fieldName}格式不正确`
        if (fieldName) setValidationError(fieldName, message)
        return { valid: false, message }
      }
      
      // 自定义验证函数
      if (rule.validator && typeof rule.validator === 'function') {
        const result = rule.validator(value)
        if (result !== true) {
          const message = typeof result === 'string' ? result : (rule.message || `${fieldName}验证失败`)
          if (fieldName) setValidationError(fieldName, message)
          return { valid: false, message }
        }
      }
    }
    
    // 验证通过，清除错误
    if (fieldName) clearValidationErrors(fieldName)
    return { valid: true }
  }
  
  // 验证整个表单
  const validateForm = (formData, rules) => {
    isValidating.value = true
    clearValidationErrors()
    
    const errors = {}
    let isValid = true
    
    for (const [field, fieldRules] of Object.entries(rules)) {
      const value = formData[field]
      const result = validateField(value, fieldRules, field)
      
      if (!result.valid) {
        errors[field] = result.message
        isValid = false
      }
    }
    
    validationErrors.value = errors
    isValidating.value = false
    
    return {
      valid: isValid,
      errors
    }
  }
  
  // 异步验证表单
  const validateFormAsync = async (formRef) => {
    if (!formRef) return false
    
    try {
      isValidating.value = true
      await formRef.validate()
      clearValidationErrors()
      return true
    } catch (error) {
      console.error('表单验证失败:', error)
      return false
    } finally {
      isValidating.value = false
    }
  }
  
  // 重置表单验证状态
  const resetValidation = (formRef = null) => {
    clearValidationErrors()
    isValidating.value = false
    
    if (formRef && formRef.resetFields) {
      formRef.resetFields()
    }
  }
  
  // 获取预定义的验证规则
  const getValidationRules = (ruleType) => {
    return VALIDATION_RULES[ruleType] || []
  }
  
  // 创建自定义验证规则
  const createValidationRule = (options = {}) => {
    const {
      required = false,
      min = null,
      max = null,
      pattern = null,
      validator = null,
      message = '',
      trigger = 'blur'
    } = options
    
    const rule = { trigger }
    
    if (required) {
      rule.required = true
      rule.message = message || '此字段为必填项'
    }
    
    if (min !== null) {
      rule.min = min
      rule.message = message || `长度不能少于${min}个字符`
    }
    
    if (max !== null) {
      rule.max = max
      rule.message = message || `长度不能超过${max}个字符`
    }
    
    if (pattern) {
      rule.pattern = pattern
      rule.message = message || '格式不正确'
    }
    
    if (validator) {
      rule.validator = validator
      rule.message = message || '验证失败'
    }
    
    return rule
  }
  
  // 常用验证器
  const validators = {
    // 邮箱验证
    email: (value) => {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailPattern.test(value) || '请输入有效的邮箱地址'
    },
    
    // 手机号验证
    phone: (value) => {
      const phonePattern = /^1[3-9]\d{9}$/
      return phonePattern.test(value) || '请输入有效的手机号码'
    },
    
    // URL验证
    url: (value) => {
      try {
        new URL(value)
        return true
      } catch {
        return '请输入有效的URL地址'
      }
    },
    
    // 数字验证
    number: (value) => {
      return !isNaN(Number(value)) || '请输入有效的数字'
    },
    
    // 正整数验证
    positiveInteger: (value) => {
      const num = Number(value)
      return (Number.isInteger(num) && num > 0) || '请输入正整数'
    }
  }
  
  return {
    // 状态
    validationErrors,
    isValidating,
    
    // 方法
    validateField,
    validateForm,
    validateFormAsync,
    clearValidationErrors,
    setValidationError,
    resetValidation,
    
    // 规则相关
    getValidationRules,
    createValidationRule,
    
    // 常用验证器
    validators,
    
    // 常量
    VALIDATION_RULES
  }
}
