import { STATUS_MAPPINGS, STATUS_TEXT, TAG_TYPES } from '@/utils/constants'

/**
 * 状态映射 Composable
 * 提供统一的状态、难度、分数等映射功能
 */
export function useStatusMapping() {
  
  // 获取状态标签类型
  const getStatusTagType = (status) => {
    return TAG_TYPES[status] || ''
  }
  
  // 获取状态文本
  const getStatusText = (status) => {
    return STATUS_TEXT[status] || status
  }
  
  // 获取难度标签类型
  const getDifficultyTagType = (difficulty) => {
    return TAG_TYPES[difficulty] || ''
  }
  
  // 获取难度文本
  const getDifficultyText = (difficulty) => {
    return STATUS_TEXT[difficulty] || difficulty
  }
  
  // 获取分数标签类型
  const getScoreTagType = (score) => {
    return TAG_TYPES[score] || ''
  }
  
  // 获取分数文本
  const getScoreText = (score) => {
    return `${score}分`
  }
  
  // 检查状态是否有效
  const isValidStatus = (status, type = 'status') => {
    switch (type) {
      case 'exam':
        return Object.values(STATUS_MAPPINGS.EXAM_STATUS).includes(status)
      case 'project':
        return Object.values(STATUS_MAPPINGS.PROJECT_STATUS).includes(status)
      case 'difficulty':
        return Object.values(STATUS_MAPPINGS.DIFFICULTY).includes(status)
      case 'score':
        return Object.values(STATUS_MAPPINGS.SCORE_LEVELS).includes(score)
      default:
        return false
    }
  }
  
  // 获取所有可用状态
  const getAvailableStatuses = (type = 'status') => {
    switch (type) {
      case 'exam':
        return Object.entries(STATUS_MAPPINGS.EXAM_STATUS).map(([key, value]) => ({
          value,
          label: STATUS_TEXT[value] || value,
          type: TAG_TYPES[value] || ''
        }))
      case 'project':
        return Object.entries(STATUS_MAPPINGS.PROJECT_STATUS).map(([key, value]) => ({
          value,
          label: STATUS_TEXT[value] || value,
          type: TAG_TYPES[value] || ''
        }))
      case 'difficulty':
        return Object.entries(STATUS_MAPPINGS.DIFFICULTY).map(([key, value]) => ({
          value,
          label: STATUS_TEXT[value] || value,
          type: TAG_TYPES[value] || ''
        }))
      case 'score':
        return Object.entries(STATUS_MAPPINGS.SCORE_LEVELS).map(([key, value]) => ({
          value,
          label: `${value}分`,
          type: TAG_TYPES[value] || ''
        }))
      default:
        return []
    }
  }
  
  // 格式化状态显示
  const formatStatus = (status, type = 'status') => {
    return {
      value: status,
      label: getStatusText(status),
      type: getStatusTagType(status),
      valid: isValidStatus(status, type)
    }
  }
  
  return {
    // 基础映射函数
    getStatusTagType,
    getStatusText,
    getDifficultyTagType,
    getDifficultyText,
    getScoreTagType,
    getScoreText,
    
    // 验证函数
    isValidStatus,
    
    // 获取选项函数
    getAvailableStatuses,
    
    // 格式化函数
    formatStatus,
    
    // 常量
    STATUS_MAPPINGS,
    STATUS_TEXT,
    TAG_TYPES
  }
}
