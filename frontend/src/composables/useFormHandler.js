/**
 * 统一表单处理 Composable
 * 提供表单状态管理、验证、提交等统一逻辑
 */

import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useLoading } from './useLoading'
import { useErrorHandler } from './useErrorHandler'
import { useFormValidation } from './useFormValidation'

/**
 * 表单处理配置选项
 * @typedef {Object} FormHandlerOptions
 * @property {Object} initialData - 初始表单数据
 * @property {Object} validationRules - 验证规则
 * @property {Function} submitHandler - 提交处理函数
 * @property {Function} loadHandler - 数据加载函数（编辑模式）
 * @property {string} successMessage - 成功消息
 * @property {string} errorMessage - 错误消息
 * @property {string} redirectPath - 成功后重定向路径
 * @property {boolean} resetAfterSubmit - 提交后是否重置表单
 * @property {boolean} autoLoad - 是否自动加载数据
 */

/**
 * 使用表单处理器
 * @param {FormHandlerOptions} options - 配置选项
 * @returns {Object} 表单处理相关的状态和方法
 */
export function useFormHandler(options = {}) {
  const {
    initialData = {},
    validationRules = {},
    submitHandler,
    loadHandler,
    successMessage = '操作成功',
    errorMessage = '操作失败',
    redirectPath = null,
    resetAfterSubmit = false,
    autoLoad = false
  } = options

  const router = useRouter()
  const formRef = ref()
  
  // 使用其他 composables
  const { loading: submitting, execute } = useLoading()
  const { loading: loadingData, execute: executeLoad } = useLoading()
  const { handleApiError, handleAsyncError } = useErrorHandler()
  const { validateFormAsync, getValidationRules } = useFormValidation()

  // 表单状态
  const formData = reactive({ ...initialData })
  const originalData = ref({ ...initialData })
  const isEdit = ref(false)
  const isDirty = ref(false)

  // 计算属性
  const hasChanges = computed(() => {
    return JSON.stringify(formData) !== JSON.stringify(originalData.value)
  })

  const canSubmit = computed(() => {
    return !submitting.value && hasChanges.value
  })

  /**
   * 重置表单到初始状态
   */
  const resetForm = () => {
    Object.keys(formData).forEach(key => {
      delete formData[key]
    })
    Object.assign(formData, { ...initialData })
    originalData.value = { ...initialData }
    isDirty.value = false
    
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  /**
   * 设置表单数据
   */
  const setFormData = (data) => {
    Object.keys(formData).forEach(key => {
      delete formData[key]
    })
    Object.assign(formData, data)
    originalData.value = { ...data }
    isEdit.value = true
    isDirty.value = false
  }

  /**
   * 更新单个字段
   */
  const updateField = (field, value) => {
    formData[field] = value
    isDirty.value = true
  }

  /**
   * 批量更新字段
   */
  const updateFields = (updates) => {
    Object.assign(formData, updates)
    isDirty.value = true
  }

  /**
   * 验证表单
   */
  const validateForm = async () => {
    if (!formRef.value) {
      console.warn('表单引用未找到')
      return false
    }

    try {
      return await validateFormAsync(formRef.value)
    } catch (error) {
      console.error('表单验证失败:', error)
      return false
    }
  }

  /**
   * 提交表单
   */
  const submitForm = async () => {
    if (!submitHandler) {
      console.error('未提供提交处理函数')
      return false
    }

    // 验证表单
    const isValid = await validateForm()
    if (!isValid) {
      ElMessage.error('请检查表单输入')
      return false
    }

    return await execute(async () => {
      try {
        const result = await submitHandler(formData, isEdit.value)
        
        ElMessage.success(successMessage)
        
        if (resetAfterSubmit) {
          resetForm()
        } else {
          // 更新原始数据，标记为未修改
          originalData.value = { ...formData }
          isDirty.value = false
        }

        // 重定向
        if (redirectPath) {
          if (typeof redirectPath === 'function') {
            const path = redirectPath(result, formData)
            if (path) router.push(path)
          } else {
            router.push(redirectPath)
          }
        }

        return result
      } catch (error) {
        handleApiError(error, { defaultMessage: errorMessage })
        throw error
      }
    })
  }

  /**
   * 加载数据（编辑模式）
   */
  const loadData = async (id) => {
    if (!loadHandler) {
      console.warn('未提供数据加载函数')
      return null
    }

    return await executeLoad(async () => {
      try {
        const data = await loadHandler(id)
        setFormData(data)
        return data
      } catch (error) {
        handleApiError(error, { defaultMessage: '加载数据失败' })
        throw error
      }
    })
  }

  /**
   * 取消编辑
   */
  const cancelEdit = () => {
    if (hasChanges.value) {
      return new Promise((resolve) => {
        ElMessageBox.confirm(
          '有未保存的更改，确定要取消吗？',
          '确认取消',
          {
            confirmButtonText: '确定',
            cancelButtonText: '继续编辑',
            type: 'warning'
          }
        ).then(() => {
          resetForm()
          resolve(true)
        }).catch(() => {
          resolve(false)
        })
      })
    } else {
      resetForm()
      return Promise.resolve(true)
    }
  }

  /**
   * 返回上一页
   */
  const goBack = async () => {
    const canLeave = await cancelEdit()
    if (canLeave) {
      router.back()
    }
  }

  /**
   * 获取字段验证规则
   */
  const getFieldRules = (fieldName) => {
    return validationRules[fieldName] || getValidationRules(fieldName)
  }

  /**
   * 标记字段为脏数据
   */
  const markFieldDirty = (fieldName) => {
    isDirty.value = true
  }

  /**
   * 清除字段验证错误
   */
  const clearFieldError = (fieldName) => {
    if (formRef.value) {
      formRef.value.clearValidate(fieldName)
    }
  }

  // 自动加载数据
  if (autoLoad && loadHandler) {
    loadData()
  }

  return {
    // 状态
    formRef,
    formData,
    originalData,
    isEdit,
    isDirty,
    submitting,
    loadingData,
    hasChanges,
    canSubmit,

    // 方法
    resetForm,
    setFormData,
    updateField,
    updateFields,
    validateForm,
    submitForm,
    loadData,
    cancelEdit,
    goBack,
    getFieldRules,
    markFieldDirty,
    clearFieldError
  }
}

/**
 * 创建表单处理配置的辅助函数
 */
export function createFormConfig(config = {}) {
  return {
    initialData: {},
    validationRules: {},
    submitHandler: null,
    loadHandler: null,
    successMessage: '操作成功',
    errorMessage: '操作失败',
    redirectPath: null,
    resetAfterSubmit: false,
    autoLoad: false,
    ...config
  }
}
