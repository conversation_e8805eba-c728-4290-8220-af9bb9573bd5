import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { validateAndConvertId, isValidId, validateRouteParams } from '@/utils/validation'

/**
 * 参数验证 Composable
 * 提供统一的参数验证和错误处理
 */
export function useParameterValidation() {
  const validationErrors = ref([])
  const isValidating = ref(false)

  /**
   * 验证单个ID参数
   * @param {any} id - 要验证的ID
   * @param {string} paramName - 参数名称
   * @returns {number|null} 验证后的ID或null
   */
  const validateId = (id, paramName = 'ID') => {
    if (!isValidId(id, paramName)) {
      const error = `${paramName}无效: ${id}`
      validationErrors.value.push(error)
      ElMessage.error(error)
      return null
    }
    return validateAndConvertId(id, paramName)
  }

  /**
   * 验证路由参数
   * @param {Object} routeParams - 路由参数对象
   * @param {Array} requiredParams - 必需的参数列表
   * @returns {boolean} 验证是否通过
   */
  const validateRoute = (routeParams, requiredParams = []) => {
    isValidating.value = true
    validationErrors.value = []

    const validation = validateRouteParams(routeParams, requiredParams)
    
    if (!validation.valid) {
      validationErrors.value = validation.errors
      ElMessage.error(`参数验证失败: ${validation.errors.join(', ')}`)
      isValidating.value = false
      return false
    }

    isValidating.value = false
    return true
  }

  /**
   * 验证API调用参数
   * @param {Object} params - API参数
   * @param {Object} rules - 验证规则
   * @returns {Object|null} 验证后的参数或null
   */
  const validateApiParams = (params, rules = {}) => {
    isValidating.value = true
    validationErrors.value = []

    try {
      // 基本的null/undefined检查
      for (const [key, value] of Object.entries(params)) {
        if (rules[key] && rules[key].required && (value === null || value === undefined || value === '')) {
          throw new Error(`参数 ${key} 不能为空`)
        }

        // ID类型参数特殊处理
        if (key.toLowerCase().includes('id') && value !== null && value !== undefined) {
          if (!isValidId(value, key)) {
            throw new Error(`参数 ${key} 必须是有效的ID`)
          }
        }
      }

      isValidating.value = false
      return params
    } catch (error) {
      validationErrors.value.push(error.message)
      ElMessage.error(error.message)
      isValidating.value = false
      return null
    }
  }

  /**
   * 清除验证错误
   */
  const clearErrors = () => {
    validationErrors.value = []
  }

  /**
   * 安全的API调用包装器
   * @param {Function} apiCall - API调用函数
   * @param {Array} args - 参数数组
   * @param {Object} options - 选项
   * @returns {Promise} API调用结果
   */
  const safeApiCall = async (apiCall, args = [], options = {}) => {
    const { validateIds = true, showErrors = true } = options

    try {
      // 验证ID类型的参数
      if (validateIds) {
        for (let i = 0; i < args.length; i++) {
          const arg = args[i]
          if (typeof arg === 'string' || typeof arg === 'number') {
            // 假设前几个参数是ID
            if (i < 2 && !isValidId(arg, `参数${i + 1}`)) {
              throw new Error(`第${i + 1}个参数不是有效的ID: ${arg}`)
            }
          }
        }
      }

      return await apiCall(...args)
    } catch (error) {
      if (showErrors) {
        console.error('❌ API调用失败:', error)
        ElMessage.error(error.message || 'API调用失败')
      }
      throw error
    }
  }

  /**
   * 检查参数是否为空值
   * @param {any} value - 要检查的值
   * @returns {boolean} 是否为空值
   */
  const isEmptyValue = (value) => {
    return value === null || 
           value === undefined || 
           value === '' || 
           value === 'null' || 
           value === 'undefined' ||
           (typeof value === 'number' && isNaN(value))
  }

  /**
   * 过滤掉空值参数
   * @param {Object} params - 参数对象
   * @returns {Object} 过滤后的参数对象
   */
  const filterEmptyParams = (params) => {
    const filtered = {}
    for (const [key, value] of Object.entries(params)) {
      if (!isEmptyValue(value)) {
        filtered[key] = value
      }
    }
    return filtered
  }

  return {
    // 状态
    validationErrors,
    isValidating,

    // 方法
    validateId,
    validateRoute,
    validateApiParams,
    clearErrors,
    safeApiCall,
    isEmptyValue,
    filterEmptyParams
  }
}
