/**
 * 页面初始化 Composable
 * 提供统一的页面初始化逻辑，包括项目加载、数据获取、错误处理等
 */

import { ref, onMounted } from 'vue'
import { useProject } from './useProject'
import { useLoading } from './useLoading'
import { useErrorHandler } from './useErrorHandler'
import { ElMessage } from 'element-plus'

/**
 * 页面初始化配置选项
 * @typedef {Object} PageInitOptions
 * @property {boolean} requireProject - 是否需要加载项目信息
 * @property {Array<Function>} criticalTasks - 关键任务列表（阻塞页面显示）
 * @property {Array<Function>} backgroundTasks - 后台任务列表（不阻塞页面显示）
 * @property {string} errorMessage - 初始化失败时的错误消息
 * @property {boolean} showLoadingOnMount - 是否在挂载时显示加载状态
 */

/**
 * 使用页面初始化
 * @param {PageInitOptions} options - 配置选项
 * @returns {Object} 初始化相关的状态和方法
 */
export function usePageInit(options = {}) {
  const {
    requireProject = true,
    criticalTasks = [],
    backgroundTasks = [],
    errorMessage = '页面初始化失败',
    showLoadingOnMount = true
  } = options

  // 使用其他 composables
  const { project, projectId, fetchProject } = useProject()
  const { loading, execute } = useLoading()
  const { handleAsyncError } = useErrorHandler()

  // 初始化状态
  const initialized = ref(false)
  const initError = ref(null)

  /**
   * 执行关键任务
   * 这些任务必须成功完成才能继续
   */
  const executeCriticalTasks = async () => {
    const tasks = []
    
    // 添加项目加载任务（如果需要）
    if (requireProject) {
      tasks.push(fetchProject)
    }
    
    // 添加用户定义的关键任务
    tasks.push(...criticalTasks)

    // 并行执行所有关键任务
    const results = await Promise.allSettled(tasks.map(task => 
      typeof task === 'function' ? task() : task
    ))

    // 检查是否有任务失败
    const failures = results.filter(result => result.status === 'rejected')
    if (failures.length > 0) {
      const error = new Error(`关键任务失败: ${failures.map(f => f.reason?.message || f.reason).join(', ')}`)
      error.failures = failures
      throw error
    }

    return results.map(result => result.value)
  }

  /**
   * 执行后台任务
   * 这些任务失败不会影响页面正常显示
   */
  const executeBackgroundTasks = async () => {
    if (backgroundTasks.length === 0) return

    try {
      const results = await Promise.allSettled(backgroundTasks.map(task => 
        typeof task === 'function' ? task() : task
      ))

      // 记录失败的后台任务，但不抛出错误
      const failures = results.filter(result => result.status === 'rejected')
      if (failures.length > 0) {
        console.warn('⚠️ 后台任务失败:', failures.map(f => f.reason?.message || f.reason))
      }

      return results
    } catch (error) {
      console.warn('⚠️ 后台任务执行失败:', error)
    }
  }

  /**
   * 初始化页面
   */
  const initializePage = async () => {
    try {
      console.log('🚀 开始初始化页面...')
      
      // 执行关键任务
      await executeCriticalTasks()
      
      // 标记初始化完成
      initialized.value = true
      initError.value = null
      
      console.log('✅ 页面关键数据初始化完成')
      
      // 异步执行后台任务
      executeBackgroundTasks().then(() => {
        console.log('✅ 后台任务执行完成')
      })
      
    } catch (error) {
      console.error('❌ 页面初始化失败:', error)
      initError.value = error
      ElMessage.error(errorMessage)
      throw error
    }
  }

  /**
   * 重新初始化页面
   */
  const reinitialize = async () => {
    initialized.value = false
    initError.value = null
    await initializePage()
  }

  /**
   * 添加关键任务
   */
  const addCriticalTask = (task) => {
    criticalTasks.push(task)
  }

  /**
   * 添加后台任务
   */
  const addBackgroundTask = (task) => {
    backgroundTasks.push(task)
  }

  // 自动初始化（如果启用）
  if (showLoadingOnMount) {
    onMounted(async () => {
      await execute(initializePage)
    })
  }

  return {
    // 状态
    initialized,
    initError,
    loading,
    project,
    projectId,
    
    // 方法
    initializePage,
    reinitialize,
    addCriticalTask,
    addBackgroundTask,
    executeCriticalTasks,
    executeBackgroundTasks
  }
}

/**
 * 创建页面初始化配置的辅助函数
 */
export function createPageInitConfig(config = {}) {
  return {
    requireProject: true,
    criticalTasks: [],
    backgroundTasks: [],
    errorMessage: '页面初始化失败',
    showLoadingOnMount: true,
    ...config
  }
}
