import { ref } from 'vue'

/**
 * 加载状态管理 Composable
 * 提供统一的加载状态管理和错误处理
 */
export function useLoading() {
  const loading = ref(false)
  const error = ref(null)
  const data = ref(null)
  
  // 执行异步操作
  const execute = async (asyncFn, options = {}) => {
    const {
      showLoading = true,
      resetError = true,
      resetData = false
    } = options
    
    if (showLoading) loading.value = true
    if (resetError) error.value = null
    if (resetData) data.value = null
    
    try {
      const result = await asyncFn()
      data.value = result
      return result
    } catch (err) {
      error.value = err
      throw err
    } finally {
      if (showLoading) loading.value = false
    }
  }
  
  // 重置所有状态
  const reset = () => {
    loading.value = false
    error.value = null
    data.value = null
  }
  
  // 设置加载状态
  const setLoading = (value) => {
    loading.value = value
  }
  
  // 设置错误状态
  const setError = (err) => {
    error.value = err
    loading.value = false
  }
  
  // 设置数据
  const setData = (value) => {
    data.value = value
    error.value = null
  }
  
  return {
    loading,
    error,
    data,
    execute,
    reset,
    setLoading,
    setError,
    setData
  }
}

/**
 * 多个加载状态管理 Composable
 * 用于管理多个独立的加载状态
 */
export function useMultipleLoading() {
  const loadingStates = ref({})
  
  // 创建加载状态
  const createLoading = (key) => {
    if (!loadingStates.value[key]) {
      loadingStates.value[key] = {
        loading: false,
        error: null,
        data: null
      }
    }
    return loadingStates.value[key]
  }
  
  // 设置加载状态
  const setLoading = (key, value) => {
    const state = createLoading(key)
    state.loading = value
  }
  
  // 设置错误状态
  const setError = (key, error) => {
    const state = createLoading(key)
    state.error = error
    state.loading = false
  }
  
  // 设置数据
  const setData = (key, data) => {
    const state = createLoading(key)
    state.data = data
    state.error = null
  }
  
  // 执行异步操作
  const execute = async (key, asyncFn, options = {}) => {
    const state = createLoading(key)
    const { resetError = true, resetData = false } = options
    
    state.loading = true
    if (resetError) state.error = null
    if (resetData) state.data = null
    
    try {
      const result = await asyncFn()
      state.data = result
      return result
    } catch (err) {
      state.error = err
      throw err
    } finally {
      state.loading = false
    }
  }
  
  // 获取状态
  const getState = (key) => {
    return loadingStates.value[key] || { loading: false, error: null, data: null }
  }
  
  // 重置状态
  const reset = (key) => {
    if (key) {
      delete loadingStates.value[key]
    } else {
      loadingStates.value = {}
    }
  }
  
  return {
    loadingStates,
    createLoading,
    setLoading,
    setError,
    setData,
    execute,
    getState,
    reset
  }
}
