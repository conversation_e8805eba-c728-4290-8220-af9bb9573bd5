import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 错误处理 Composable
 * 提供统一的错误处理和用户提示
 */
export function useErrorHandler() {
  
  // 处理API错误
  const handleApiError = (error, options = {}) => {
    const {
      showMessage = true,
      defaultMessage = '操作失败',
      logError = true
    } = options

    if (logError) {
      console.error('❌ API错误:', error)
    }

    let message = defaultMessage

    // 特殊处理参数验证错误
    if (error.validationErrors) {
      message = `参数验证失败: ${error.validationErrors.join(', ')}`
    }
    // 提取错误信息
    else if (error.response?.data?.message) {
      message = error.response.data.message
    } else if (error.response?.data?.error?.message) {
      message = error.response.data.error.message
    } else if (error.message) {
      message = error.message
    }

    // 特殊处理常见的参数错误
    if (message.includes('参数') && message.includes('无效')) {
      message = '页面参数错误，请返回重新操作'
    }

    if (showMessage) {
      ElMessage.error(message)
    }

    return {
      message,
      status: error.response?.status,
      data: error.response?.data,
      isValidationError: !!error.validationErrors
    }
  }
  
  // 处理异步操作错误
  const handleAsyncError = async (asyncFn, options = {}) => {
    const {
      errorMessage = '操作失败',
      showError = true,
      rethrow = false
    } = options
    
    try {
      return await asyncFn()
    } catch (error) {
      const errorInfo = handleApiError(error, {
        showMessage: showError,
        defaultMessage: errorMessage
      })
      
      if (rethrow) {
        throw error
      }
      
      return { error: errorInfo, success: false }
    }
  }
  
  // 确认对话框
  const confirmAction = async (message, title = '确认操作', options = {}) => {
    const {
      confirmButtonText = '确定',
      cancelButtonText = '取消',
      type = 'warning'
    } = options
    
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText,
        cancelButtonText,
        type
      })
      return true
    } catch (error) {
      if (error === 'cancel') {
        return false
      }
      throw error
    }
  }
  
  // 删除确认
  const confirmDelete = async (itemName, itemType = '项目') => {
    return confirmAction(
      `确定要删除${itemType}"${itemName}"吗？此操作不可恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      }
    )
  }
  
  // 网络错误处理
  const handleNetworkError = (error) => {
    if (!navigator.onLine) {
      ElMessage.error('网络连接已断开，请检查网络设置')
      return
    }
    
    if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
      ElMessage.error('网络请求失败，请检查网络连接')
      return
    }
    
    handleApiError(error)
  }
  
  // 表单验证错误处理
  const handleValidationError = (formRef, message = '请检查表单输入') => {
    if (formRef?.validate) {
      formRef.validate((valid) => {
        if (!valid) {
          ElMessage.error(message)
        }
      })
    } else {
      ElMessage.error(message)
    }
  }
  
  return {
    handleApiError,
    handleAsyncError,
    confirmAction,
    confirmDelete,
    handleNetworkError,
    handleValidationError
  }
}
