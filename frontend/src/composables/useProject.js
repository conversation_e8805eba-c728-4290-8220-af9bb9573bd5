import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { projectAPI } from '@/api'
import { validateAndConvertId, isValidId } from '@/utils/validation'

/**
 * 项目信息管理 Composable
 * 提供项目信息的获取、缓存和状态管理
 */
export function useProject() {
  const route = useRoute()
  
  // 状态
  const project = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 计算属性
  const projectId = computed(() => {
    const id = route.params.id || route.params.projectId

    // 使用验证工具函数确保ID有效
    const validId = validateAndConvertId(id, 'projectId')
    if (validId === null && id !== undefined) {
      console.warn('⚠️  项目ID无效:', id)
    }

    return validId
  })
  
  const projectName = computed(() => project.value?.name || '')
  const projectDescription = computed(() => project.value?.description || '')
  
  // 获取项目信息
  const fetchProject = async (id = null) => {
    const targetId = id || projectId.value

    // 验证项目ID
    if (!isValidId(targetId, 'projectId')) {
      const errorMsg = `项目ID无效: ${targetId}`
      error.value = errorMsg
      console.error('❌', errorMsg)
      ElMessage.error(errorMsg)
      return null
    }
    
    loading.value = true
    error.value = null
    
    try {
      console.log('🔄 开始获取项目信息...', targetId)
      const response = await projectAPI.getProject(targetId)
      project.value = response.data.data
      console.log('✅ 项目信息获取成功:', project.value.name)
      return project.value
    } catch (err) {
      console.error('❌ 获取项目信息失败:', err)
      error.value = err.response?.data?.message || err.message || '获取项目信息失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 重置状态
  const reset = () => {
    project.value = null
    loading.value = false
    error.value = null
  }
  
  // 生成默认考卷信息
  const generateDefaultExamInfo = () => {
    if (!project.value) return {}
    
    const currentDate = new Date().toISOString().split('T')[0]
    return {
      title: `${project.value.name}-${currentDate}`,
      description: project.value.description || ''
    }
  }
  
  // 生成默认AI提示词
  const generateDefaultPrompt = () => {
    if (!project.value) return ''
    
    const { name, description } = project.value
    let prompt = ''
    
    if (name && description) {
      prompt = `${name}，${description}`
    } else if (name) {
      prompt = name
    } else if (description) {
      prompt = description
    }
    
    return prompt.trim() || '请生成知识点体系'
  }
  
  return {
    // 状态
    project,
    loading,
    error,
    
    // 计算属性
    projectId,
    projectName,
    projectDescription,
    
    // 方法
    fetchProject,
    reset,
    generateDefaultExamInfo,
    generateDefaultPrompt
  }
}
