/**
 * 新考试类型示例
 * 
 * 这个文件展示了如何在重构后的架构中轻松添加新的考试类型
 * 包括支持图片选项的考试
 */

import { getQuizFactory } from '@/factories/quizFactory.js';
import { QUIZ_TYPES, OPTION_TYPES } from '@/config/quizConfig.js';

// 示例：添加一个新的考试类型 - 世界著名画作
const WORLD_PAINTINGS = 'world-paintings';

// 示例数据结构
const worldPaintingsData = [
  {
    fileName: 'mona_lisa.jpg',
    name: '蒙娜丽莎',
    artist: '达芬奇',
    country: '意大利',
    period: '文艺复兴',
    year: 1503
  },
  {
    fileName: 'starry_night.jpg',
    name: '星夜',
    artist: '梵高',
    country: '荷兰',
    period: '后印象派',
    year: 1889
  },
  {
    fileName: 'guernica.jpg',
    name: '格尔尼卡',
    artist: '毕加索',
    country: '西班牙',
    period: '立体主义',
    year: 1937
  }
  // ... 更多数据
];

/**
 * 示例1：传统文字选项考试
 * 问题：这幅画的作者是谁？
 * 选项：文字选项（艺术家名称）
 */
function registerTextOptionPaintingQuiz() {
  const quizFactory = getQuizFactory();
  
  quizFactory.registerQuiz(WORLD_PAINTINGS, {
    title: '世界著名画作',
    description: '看图识别世界著名画作的作者！',
    difficulty: 3,
    generatorType: 'simple',
    optionType: OPTION_TYPES.TEXT,
    optionConfig: {
      valueField: 'artist' // 选项显示艺术家名称
    },
    dataModule: () => Promise.resolve({ worldPaintings: worldPaintingsData }),
    dataField: 'worldPaintings'
  });
}

/**
 * 示例2：图片选项考试
 * 问题：哪幅画是梵高的作品？
 * 选项：图片选项（显示画作缩略图）
 */
function registerImageOptionPaintingQuiz() {
  const quizFactory = getQuizFactory();
  
  quizFactory.registerQuiz('world-paintings-image', {
    title: '世界著名画作（图片选项）',
    description: '从图片中选择正确的画作！',
    difficulty: 4,
    generatorType: 'simple',
    optionType: OPTION_TYPES.IMAGE,
    optionConfig: {
      imageField: 'fileName',
      nameField: 'name'
    },
    dataModule: () => Promise.resolve({ worldPaintings: worldPaintingsData }),
    dataField: 'worldPaintings'
  });
}



/**
 * 示例3：自定义题目生成器
 * 用于复杂的业务逻辑
 */
import { QuestionGenerator } from '@/generators/questionGenerators.js';

class PaintingQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super(config);
  }

  generateSingleQuestion(item, dataArray, optionCount) {
    // 自定义逻辑：按时代分组选择选项
    const options = new Set([item.artist]);
    const sameEra = dataArray.filter(other => 
      Math.abs(other.year - item.year) <= 50 && other.artist !== item.artist
    );
    
    // 优先从同时代艺术家中选择
    while (options.size < optionCount && options.size < sameEra.length + 1) {
      const randomIndex = Math.floor(Math.random() * sameEra.length);
      const randomItem = sameEra[randomIndex];
      if (randomItem) {
        options.add(randomItem.artist);
      }
    }
    
    // 如果同时代艺术家不够，从所有艺术家中补充
    if (options.size < optionCount) {
      const allArtists = [...new Set(dataArray.map(a => a.artist))];
      while (options.size < optionCount && options.size < allArtists.length) {
        const randomIndex = Math.floor(Math.random() * allArtists.length);
        const randomArtist = allArtists[randomIndex];
        if (randomArtist !== item.artist) {
          options.add(randomArtist);
        }
      }
    }

    return {
      image: this.getQuestionImage(item),
      answer: item.artist,
      options: Array.from(options),
      ...this.getExtraFields(item)
    };
  }

  getExtraFields(item) {
    return {
      ...super.getExtraFields(item),
      paintingName: item.name,
      artist: item.artist,
      period: item.period,
      year: item.year
    };
  }
}

/**
 * 注册自定义题目生成器
 */
function registerCustomPaintingQuiz() {
  const quizFactory = getQuizFactory();
  
  // 首先注册自定义生成器
  const { QuestionGeneratorFactory } = require('@/generators/questionGenerators.js');
  QuestionGeneratorFactory.create = (function(originalCreate) {
    return function(type, config) {
      if (type === 'custom-painting') {
        return new PaintingQuestionGenerator(config);
      }
      return originalCreate.call(this, type, config);
    };
  })(QuestionGeneratorFactory.create);
  
  // 然后注册考试
  quizFactory.registerQuiz('world-paintings-custom', {
    title: '世界著名画作（按时代分组）',
    description: '识别同时代艺术家的作品！',
    difficulty: 4,
    generatorType: 'custom-painting',
    dataModule: () => Promise.resolve({ worldPaintings: worldPaintingsData }),
    dataField: 'worldPaintings'
  });
}

/**
 * 示例4：动态数据加载
 * 从API获取数据而不是静态导入
 */
function registerDynamicPaintingQuiz() {
  const quizFactory = getQuizFactory();
  
  quizFactory.registerQuiz('world-paintings-dynamic', {
    title: '世界著名画作（动态数据）',
    description: '从服务器获取最新的画作数据！',
    difficulty: 3,
    generatorType: 'simple',
    optionType: OPTION_TYPES.TEXT,
    optionConfig: {
      valueField: 'artist'
    },
    // 动态数据加载
    dataModule: async () => {
      try {
        const response = await fetch('/api/paintings');
        const data = await response.json();
        return { worldPaintings: data };
      } catch (error) {
        console.error('Failed to load paintings data:', error);
        // 降级到静态数据
        return { worldPaintings: worldPaintingsData };
      }
    },
    dataField: 'worldPaintings'
  });
}

/**
 * 使用示例
 */
export function initializeNewQuizTypes() {
  // 注册所有示例考试类型
  registerTextOptionPaintingQuiz();
  registerImageOptionPaintingQuiz();
  registerCustomPaintingQuiz();
  registerDynamicPaintingQuiz();
  
  console.log('✅ 新考试类型注册完成');
}

/**
 * 在Vue组件中使用新考试类型的示例
 */
export const newQuizUsageExample = {
  // 在QuizList.vue中，新考试会自动出现在列表中
  // 不需要修改任何现有代码
  
  // 在Quiz.vue中，新考试会自动支持所有功能
  // 包括图片缩放、选项显示等
  
  // 添加新的特殊显示逻辑（如果需要）
  template: `
    <!-- 在Quiz.vue的模板中添加 -->
    <div v-if="isPaintingQuiz && currentQuestion && answerChecked" class="painting-info">
      <div class="painting-name">{{ currentQuestion.paintingName }}</div>
      <div class="painting-artist">{{ currentQuestion.artist }}</div>
      <div class="painting-period">{{ currentQuestion.period }}</div>
      <div class="painting-year">{{ currentQuestion.year }}</div>
    </div>
  `,
  
  computed: {
    isPaintingQuiz() {
      return this.quizType?.startsWith('world-paintings');
    }
  }
};

export default {
  initializeNewQuizTypes,
  newQuizUsageExample
}; 