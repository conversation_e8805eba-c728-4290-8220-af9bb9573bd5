const vehicleFiles = [
  {
    code: 'vehicle_1',
    name: '轿车',
    fileName: "vehicle_1.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_2',
    name: '公交车',
    fileName: "vehicle_2.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_3',
    name: '卡车',
    fileName: "vehicle_3.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_4',
    name: '摩托车',
    fileName: "vehicle_4.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_5',
    name: '自行车',
    fileName: "vehicle_5.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_6',
    name: '地铁',
    fileName: "vehicle_6.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_7',
    name: '火车',
    fileName: "vehicle_7.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_8',
    name: '高铁',
    fileName: "vehicle_8.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_9',
    name: '有轨电车',
    fileName: "vehicle_9.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_11',
    name: '救护车',
    fileName: "vehicle_11.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_12',
    name: '消防车',
    fileName: "vehicle_12.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_13',
    name: '警车',
    fileName: "vehicle_13.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_14',
    name: '挖掘机',
    fileName: "vehicle_14.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_15',
    name: '推土机',
    fileName: "vehicle_15.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_16',
    name: '拖拉机',
    fileName: "vehicle_16.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_17',
    name: '校车',
    fileName: "vehicle_17.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_18',
    name: '出租车',
    fileName: "vehicle_18.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_19',
    name: '电动车',
    fileName: "vehicle_19.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_20',
    name: '三轮车',
    fileName: "vehicle_20.jpg",
    category: '陆地'
  },
  {
    code: 'vehicle_21',
    name: '飞机',
    fileName: "vehicle_21.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_22',
    name: '直升机',
    fileName: "vehicle_22.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_23',
    name: '战斗机',
    fileName: "vehicle_23.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_24',
    name: '客机',
    fileName: "vehicle_24.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_26',
    name: '滑翔机',
    fileName: "vehicle_26.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_27',
    name: '热气球',
    fileName: "vehicle_27.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_28',
    name: '降落伞',
    fileName: "vehicle_28.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_29',
    name: '无人机',
    fileName: "vehicle_29.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_30',
    name: '火箭',
    fileName: "vehicle_30.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_32',
    name: '航天飞机',
    fileName: "vehicle_32.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_34',
    name: '水上飞机',
    fileName: "vehicle_34.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_35',
    name: '喷气式飞机',
    fileName: "vehicle_35.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_37',
    name: '运输机',
    fileName: "vehicle_37.jpg",
    category: '天空'
  },
  {
    code: 'vehicle_41',
    name: '轮船',
    fileName: "vehicle_41.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_42',
    name: '帆船',
    fileName: "vehicle_42.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_43',
    name: '快艇',
    fileName: "vehicle_43.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_44',
    name: '游艇',
    fileName: "vehicle_44.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_45',
    name: '潜水艇',
    fileName: "vehicle_45.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_46',
    name: '航空母舰',
    fileName: "vehicle_46.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_47',
    name: '货船',
    fileName: "vehicle_47.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_48',
    name: '渔船',
    fileName: "vehicle_48.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_49',
    name: '橡皮艇',
    fileName: "vehicle_49.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_50',
    name: '皮划艇',
    fileName: "vehicle_50.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_51',
    name: '独木舟',
    fileName: "vehicle_51.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_52',
    name: '冲浪板',
    fileName: "vehicle_52.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_54',
    name: '邮轮',
    fileName: "vehicle_54.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_55',
    name: '游轮',
    fileName: "vehicle_55.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_59',
    name: '摩托艇',
    fileName: "vehicle_59.jpg",
    category: '水里'
  },
  {
    code: 'vehicle_60',
    name: '竹筏',
    fileName: "vehicle_60.jpg",
    category: '水里'
  },
];

import imageConfig from '../config/imageConfig.js';

export const vehicles = vehicleFiles.map(vehicle => ({
  ...vehicle,
  imageUrl: imageConfig.getImageUrl('vehicles', vehicle.fileName)
}));