const vegetableFiles = [
  {
    code: 'vegetable_1',
    name: '胡萝卜',
    fileName: "vegetable_1.jpg"
  },
  {
    code: 'vegetable_2',
    name: '白萝卜',
    fileName: "vegetable_2.jpg"
  },
  {
    code: 'vegetable_3',
    name: '土豆',
    fileName: "vegetable_3.jpg"
  },
  {
    code: 'vegetable_4',
    name: '红薯',
    fileName: "vegetable_4.jpg"
  },
  {
    code: 'vegetable_5',
    name: '洋葱',
    fileName: "vegetable_5.jpg"
  },
  {
    code: 'vegetable_6',
    name: '大蒜',
    fileName: "vegetable_6.jpg"
  },
  {
    code: 'vegetable_7',
    name: '生姜',
    fileName: "vegetable_7.jpg"
  },
  {
    code: 'vegetable_8',
    name: '莲藕',
    fileName: "vegetable_8.jpg"
  },
  {
    code: 'vegetable_9',
    name: '山药',
    fileName: "vegetable_9.jpg"
  },
  {
    code: 'vegetable_10',
    name: '芋头',
    fileName: "vegetable_10.jpg"
  },
  {
    code: 'vegetable_12',
    name: '小白菜',
    fileName: "vegetable_12.jpg"
  },
  {
    code: 'vegetable_13',
    name: '菠菜',
    fileName: "vegetable_13.jpg"
  },
  {
    code: 'vegetable_14',
    name: '韭菜',
    fileName: "vegetable_14.jpg"
  },
  {
    code: 'vegetable_15',
    name: '芹菜',
    fileName: "vegetable_15.jpg"
  },
  {
    code: 'vegetable_16',
    name: '生菜',
    fileName: "vegetable_16.jpg"
  },
  {
    code: 'vegetable_17',
    name: '油麦菜',
    fileName: "vegetable_17.jpg"
  },
  {
    code: 'vegetable_18',
    name: '茼蒿',
    fileName: "vegetable_18.jpg"
  },
  {
    code: 'vegetable_19',
    name: '苋菜',
    fileName: "vegetable_19.jpg"
  },
  {
    code: 'vegetable_20',
    name: '空心菜',
    fileName: "vegetable_20.jpg"
  },
  {
    code: 'vegetable_21',
    name: '黄瓜',
    fileName: "vegetable_21.jpg"
  },
  {
    code: 'vegetable_22',
    name: '西红柿',
    fileName: "vegetable_22.jpg"
  },
  {
    code: 'vegetable_23',
    name: '茄子',
    fileName: "vegetable_23.jpg"
  },
  {
    code: 'vegetable_24',
    name: '青椒',
    fileName: "vegetable_24.jpg"
  },
  {
    code: 'vegetable_25',
    name: '红椒',
    fileName: "vegetable_25.jpg"
  },
  {
    code: 'vegetable_26',
    name: '南瓜',
    fileName: "vegetable_26.jpg"
  },
  {
    code: 'vegetable_27',
    name: '冬瓜',
    fileName: "vegetable_27.jpg"
  },
  {
    code: 'vegetable_28',
    name: '丝瓜',
    fileName: "vegetable_28.jpg"
  },
  {
    code: 'vegetable_29',
    name: '苦瓜',
    fileName: "vegetable_29.jpg"
  },
  {
    code: 'vegetable_30',
    name: '西葫芦',
    fileName: "vegetable_30.jpg"
  },
  {
    code: 'vegetable_31',
    name: '豆角',
    fileName: "vegetable_31.jpg"
  },
  {
    code: 'vegetable_32',
    name: '四季豆',
    fileName: "vegetable_32.jpg"
  },
  {
    code: 'vegetable_33',
    name: '豌豆',
    fileName: "vegetable_33.jpg"
  },
  {
    code: 'vegetable_34',
    name: '毛豆',
    fileName: "vegetable_34.jpg"
  },
  {
    code: 'vegetable_35',
    name: '蚕豆',
    fileName: "vegetable_35.jpg"
  },
  {
    code: 'vegetable_36',
    name: '豇豆',
    fileName: "vegetable_36.jpg"
  },
  {
    code: 'vegetable_37',
    name: '扁豆',
    fileName: "vegetable_37.jpg"
  },
  {
    code: 'vegetable_38',
    name: '绿豆芽',
    fileName: "vegetable_38.jpg"
  },
  {
    code: 'vegetable_39',
    name: '黄豆芽',
    fileName: "vegetable_39.jpg"
  },
  {
    code: 'vegetable_40',
    name: '豆苗',
    fileName: "vegetable_40.jpg"
  },
  {
    code: 'vegetable_41',
    name: '花菜',
    fileName: "vegetable_41.jpg"
  },
  {
    code: 'vegetable_42',
    name: '西兰花',
    fileName: "vegetable_42.jpg"
  },
  {
    code: 'vegetable_43',
    name: '芥蓝',
    fileName: "vegetable_43.jpg"
  },
  {
    code: 'vegetable_44',
    name: '菜花',
    fileName: "vegetable_44.jpg"
  },
  {
    code: 'vegetable_45',
    name: '紫甘蓝',
    fileName: "vegetable_45.jpg"
  },
  {
    code: 'vegetable_46',
    name: '卷心菜',
    fileName: "vegetable_46.jpg"
  },
  {
    code: 'vegetable_47',
    name: '萝卜缨',
    fileName: "vegetable_47.jpg"
  },
  {
    code: 'vegetable_48',
    name: '香菜',
    fileName: "vegetable_48.jpg"
  },
  {
    code: 'vegetable_49',
    name: '小葱',
    fileName: "vegetable_49.jpg"
  },
  {
    code: 'vegetable_50',
    name: '韭黄',
    fileName: "vegetable_50.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const vegetables = vegetableFiles.map(vegetable => ({
  ...vegetable,
  imageUrl: imageConfig.getImageUrl('vegetables', vegetable.fileName)
}));