const facilityFiles = [
  {
    code: 'facility_1',
    name: '公交站',
    fileName: "facility_1.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_2',
    name: '地铁站',
    fileName: "facility_2.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_3',
    name: '火车站',
    fileName: "facility_3.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_4',
    name: '机场',
    fileName: "facility_4.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_5',
    name: '公共厕所',
    fileName: "facility_5.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_6',
    name: '图书馆',
    fileName: "facility_6.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_7',
    name: '博物馆',
    fileName: "facility_7.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_8',
    name: '体育馆',
    fileName: "facility_8.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_9',
    name: '公园',
    fileName: "facility_9.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_10',
    name: '医院',
    fileName: "facility_10.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_11',
    name: '社区卫生站',
    fileName: "facility_11.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_12',
    name: '学校',
    fileName: "facility_12.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_13',
    name: '幼儿园',
    fileName: "facility_13.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_14',
    name: '消防站',
    fileName: "facility_14.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_15',
    name: '派出所',
    fileName: "facility_15.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_16',
    name: '邮局',
    fileName: "facility_16.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_17',
    name: '银行',
    fileName: "facility_17.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_18',
    name: '超市',
    fileName: "facility_18.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_19',
    name: '菜市场',
    fileName: "facility_19.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_20',
    name: '健身广场',
    fileName: "facility_20.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_21',
    name: '停车场',
    fileName: "facility_21.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_22',
    name: '加油站',
    fileName: "facility_22.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_23',
    name: '充电桩',
    fileName: "facility_23.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_24',
    name: '报刊亭',
    fileName: "facility_24.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_25',
    name: '电话亭',
    fileName: "facility_25.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_26',
    name: '垃圾站',
    fileName: "facility_26.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_27',
    name: '公共自行车点',
    fileName: "facility_27.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_28',
    name: '人行天桥',
    fileName: "facility_28.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_29',
    name: '地下通道',
    fileName: "facility_29.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_30',
    name: '交通信号灯',
    fileName: "facility_30.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_31',
    name: '公交专用道',
    fileName: "facility_31.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_32',
    name: '出租车停靠点',
    fileName: "facility_32.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_33',
    name: '轮渡码头',
    fileName: "facility_33.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_34',
    name: '高速公路服务区',
    fileName: "facility_34.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_35',
    name: '公共饮水处',
    fileName: "facility_35.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_36',
    name: '紧急避难所',
    fileName: "facility_36.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_37',
    name: '社区活动中心',
    fileName: "facility_37.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_38',
    name: '老年服务中心',
    fileName: "facility_38.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_39',
    name: '青少年宫',
    fileName: "facility_39.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_40',
    name: '科技馆',
    fileName: "facility_40.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_41',
    name: '美术馆',
    fileName: "facility_41.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_42',
    name: '文化宫',
    fileName: "facility_42.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_43',
    name: '音乐厅',
    fileName: "facility_43.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_44',
    name: '剧院',
    fileName: "facility_44.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_45',
    name: '电影院',
    fileName: "facility_45.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_46',
    name: '游泳馆',
    fileName: "facility_46.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_47',
    name: '篮球场',
    fileName: "facility_47.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_48',
    name: '足球场',
    fileName: "facility_48.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_49',
    name: '网球场',
    fileName: "facility_49.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_50',
    name: '羽毛球场',
    fileName: "facility_50.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_51',
    name: '乒乓球台',
    fileName: "facility_51.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_52',
    name: '健身路径',
    fileName: "facility_52.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_53',
    name: '公共WiFi热点',
    fileName: "facility_53.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_54',
    name: '信息亭',
    fileName: "facility_54.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_55',
    name: '自动售货机',
    fileName: "facility_55.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_56',
    name: '快递柜',
    fileName: "facility_56.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_57',
    name: '共享雨伞点',
    fileName: "facility_57.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_58',
    name: 'AED急救点',
    fileName: "facility_58.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_59',
    name: '献血屋',
    fileName: "facility_59.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_60',
    name: '疫苗接种点',
    fileName: "facility_60.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_61',
    name: '心理辅导站',
    fileName: "facility_61.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_62',
    name: '法律服务站',
    fileName: "facility_62.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_63',
    name: '就业服务中心',
    fileName: "facility_63.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_64',
    name: '社保服务中心',
    fileName: "facility_64.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_65',
    name: '婚姻登记处',
    fileName: "facility_65.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_66',
    name: '出入境大厅',
    fileName: "facility_66.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_67',
    name: '税务大厅',
    fileName: "facility_67.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_68',
    name: '工商所',
    fileName: "facility_68.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_69',
    name: '城管服务站',
    fileName: "facility_69.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_70',
    name: '环保监测站',
    fileName: "facility_70.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_71',
    name: '气象服务站',
    fileName: "facility_71.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_72',
    name: '地震监测站',
    fileName: "facility_72.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_73',
    name: '公共充电站',
    fileName: "facility_73.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_74',
    name: '电动汽车充电桩',
    fileName: "facility_74.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_75',
    name: '共享汽车点',
    fileName: "facility_75.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_76',
    name: '旅游咨询中心',
    fileName: "facility_76.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_77',
    name: '景区售票处',
    fileName: "facility_77.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_78',
    name: '公共停车场',
    fileName: "facility_78.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_79',
    name: '立体车库',
    fileName: "facility_79.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_80',
    name: '自行车停车场',
    fileName: "facility_80.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_81',
    name: '摩托车停车场',
    fileName: "facility_81.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_82',
    name: '公交枢纽站',
    fileName: "facility_82.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_83',
    name: '长途汽车站',
    fileName: "facility_83.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_84',
    name: '高铁站',
    fileName: "facility_84.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_85',
    name: '磁悬浮站',
    fileName: "facility_85.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_86',
    name: '轻轨站',
    fileName: "facility_86.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_87',
    name: '有轨电车站',
    fileName: "facility_87.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_88',
    name: 'BRT快速公交站',
    fileName: "facility_88.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_89',
    name: '水上巴士站',
    fileName: "facility_89.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_90',
    name: '直升机停机坪',
    fileName: "facility_90.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_91',
    name: '公共晾晒区',
    fileName: "facility_91.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_92',
    name: '社区食堂',
    fileName: "facility_92.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_93',
    name: '爱心驿站',
    fileName: "facility_93.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_94',
    name: '志愿者服务站',
    fileName: "facility_94.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_95',
    name: '无障碍设施',
    fileName: "facility_95.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_96',
    name: '盲道',
    fileName: "facility_96.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_97',
    name: '轮椅坡道',
    fileName: "facility_97.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_98',
    name: '母婴室',
    fileName: "facility_98.jpg",
    category: '公共设施'
  },
  {
    code: 'facility_99',
    name: '第三卫生间',
    fileName: "facility_99.jpg",
    category: '公共设施'
  },
];

import imageConfig from '../config/imageConfig.js';

export const publicFacilities = facilityFiles.map(item => ({
  ...item,
  imageUrl: imageConfig.getImageUrl('public-facilities', item.fileName)
}));