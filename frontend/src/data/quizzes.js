import { carLogos } from './car-logos.js';
import { countryFlags } from './country-flags.js';
import { animals } from './animals.js';
import { worldLandmarks } from './world-landmarks.js';
import { chinaLandmarks } from './china-landmarks.js';
import { ethnicities } from './ethnicities.js';
import { worldFlagsPlus } from './world-flags-plus.js';
import { cityRegions } from './city-regions.js';
import { japaneseActresses } from './japanese-actresses.js';
import { militaryEquipment } from './military-equipment.js';
import { vehicles } from './vehicles.js';
import { dogCatBreeds } from './dogCatBreeds.js';
import { houseplants } from './houseplants.js';
import { homeAppliances } from './home-appliances.js';
import { vegetables } from './vegetables.js';
import { fruits } from './fruits.js';
import { dishes } from './dishes.js';
import { sports } from './sports.js';
import { fish } from './fish.js';
import { worldFoods } from './worldFoods.js';
import { globalActresses } from './globalActresses.js';
import { globalActors } from './globalActors.js';
import { fairyTales } from './fairy_tales.js';
import { chineseFoods } from './chinese_foods.js';
import { celestialBodies } from './celestial_bodies.js';
import { clothes } from './clothes.js';
import { trafficSigns } from './trafficSigns.js';
import { livestock } from './livestock.js';
import { beautifulSceneries } from './beautiful_sceneries.js';

import { landmarkBuildings } from './landmarkBuildings.js';
import { generateCityImage } from '@/utils/textToImage.js';

// 难度等级常量
export const DIFFICULTY_LEVELS = {
  1: { stars: 1, options: 4, label: '1星（简单）' },
  2: { stars: 2, options: 4, label: '2星（简单）' },
  3: { stars: 3, options: 6, label: '3星（中等）' },
  4: { stars: 4, options: 6, label: '4星（中等）' },
  5: { stars: 5, options: 8, label: '5星（困难）' }
};



// Function to generate quiz questions with category support and customizable option count
const generateQuestions = (dataArray, hasCategory = false, optionCount = 4) => {
  if (!dataArray || dataArray.length === 0) return [];

  return dataArray.map(item => {
    const options = new Set([item.name]);
    
    if (hasCategory && (item.category || item.continent)) {
      // For items with categories, select options from the same category/continent
      const categoryKey = item.category || item.continent;
      const sameCategory = dataArray.filter(other => 
        (other.category === categoryKey || other.continent === categoryKey) && other.name !== item.name
      );
      
      // Add random incorrect answers from the same category
      while (options.size < optionCount && options.size < sameCategory.length + 1) {
        const randomIndex = Math.floor(Math.random() * sameCategory.length);
        const randomItem = sameCategory[randomIndex];
        if (randomItem && randomItem.name !== item.name) {
          options.add(randomItem.name);
        }
      }
      
      // If we don't have enough options from the same category, fill with any options
      if (options.size < optionCount) {
        const allNames = dataArray.map(d => d.name);
        while (options.size < optionCount && options.size < allNames.length) {
          const randomIndex = Math.floor(Math.random() * allNames.length);
          const randomName = allNames[randomIndex];
          if (randomName !== item.name) {
            options.add(randomName);
          }
        }
      }
    } else {
      // For items without categories, use the original logic
      const allNames = dataArray.map(d => d.name);
      while (options.size < optionCount && options.size < allNames.length) {
        const randomIndex = Math.floor(Math.random() * allNames.length);
        const randomName = allNames[randomIndex];
        if (randomName !== item.name) {
          options.add(randomName);
        }
      }
    }

    return {
      image: item.fileName,
      answer: item.name,
      options: Array.from(options).sort(() => Math.random() - 0.5), // Shuffle options
      category: item.category || item.continent || null // Include category/continent info if available
    };
  });
};

// Function to generate quiz questions from animals data (with category support)
const generateAnimalQuestions = () => {
  return generateQuestions(animals, true, DIFFICULTY_LEVELS[1].options);
};



// Function to generate car logo questions (without category)
const generateCarLogoQuestions = () => {
  return generateQuestions(carLogos, false, DIFFICULTY_LEVELS[2].options);
};

// Function to generate Japanese actress questions (without category, 4-star difficulty)
const generateJapaneseActressQuestions = () => {
  return generateQuestions(japaneseActresses, false, DIFFICULTY_LEVELS[4].options);
};

// Function to generate country flag questions (without category)
const generateCountryFlagQuestions = () => {
  return generateQuestions(countryFlags, false, DIFFICULTY_LEVELS[3].options);
};

// Function to generate world flags plus questions (with continent support, 8 options)
const generateWorldFlagsPlusQuestions = () => {
  return generateQuestions(worldFlagsPlus, true, DIFFICULTY_LEVELS[5].options);
};

// Function to generate world landmarks questions (without category)
const generateWorldLandmarkQuestions = () => {
  return generateQuestions(worldLandmarks, false, DIFFICULTY_LEVELS[3].options);
};

// Function to generate china landmarks questions (with category support)
const generateChinaLandmarkQuestions = () => {
  return generateQuestions(chinaLandmarks, true, DIFFICULTY_LEVELS[3].options);
};

// Function to generate ethnicity questions (with category support)
const generateEthnicityQuestions = () => {
  return generateQuestions(ethnicities, true, DIFFICULTY_LEVELS[5].options);
};


// Function to generate city region questions (with region support)
const generateCityRegionQuestions = () => {
  // 按地区分组省份
  const regionProvinces = {
    "华东": ["山东", "江苏", "浙江", "安徽", "江西", "福建"],
    "华南": ["广东", "广西", "湖南", "湖北", "海南"],
    "华北": ["河南", "河北", "山西", "辽宁", "黑龙江", "吉林"],
    "西部": ["四川", "云南", "新疆", "甘肃", "内蒙古", "陕西", "贵州", "青海", "西藏", "宁夏"]
  };
  
  return cityRegions.map((city, index) => {
    // 生成城市图片（使用Canvas）
    const cityImageDataUrl = generateCityImage(city, {
      width: 400,
      height: 300,
      backgroundColor: '#f8f9fa',
      primaryColor: '#2c3e50',
      secondaryColor: '#7f8c8d',
      accentColor: '#3498db'
    });
    
    // 获取该城市所在地区的所有省份作为选项
    const cityRegion = city.region;
    const regionProvincesList = regionProvinces[cityRegion] || [];
    const targetOptionCount = DIFFICULTY_LEVELS[4].options; // 4星难度 = 6个选项
    
    // 确保正确答案在选项中
    let options = [...regionProvincesList];
    if (!options.includes(city.province)) {
      options.push(city.province);
    }
    
    // 如果选项少于目标数量，从其他地区补充
    if (options.length < targetOptionCount) {
      const allProvinces = Object.values(regionProvinces).flat();
      const otherProvinces = allProvinces.filter(p => 
        !options.includes(p) && p !== city.province
      );
      
      while (options.length < targetOptionCount && otherProvinces.length > 0) {
        const randomIndex = Math.floor(Math.random() * otherProvinces.length);
        const randomProvince = otherProvinces.splice(randomIndex, 1)[0];
        options.push(randomProvince);
      }
    }
    
    // 如果选项超过目标数量，随机选择（确保包含正确答案）
    if (options.length > targetOptionCount) {
      const correctAnswer = city.province;
      const otherOptions = options.filter(opt => opt !== correctAnswer);
      const shuffledOthers = otherOptions.sort(() => Math.random() - 0.5);
      options = [correctAnswer, ...shuffledOthers.slice(0, targetOptionCount - 1)];
    }
    
    // 如果某个地区省份不足6个，则使用该地区所有省份（4个选项）
    if (regionProvincesList.length < targetOptionCount) {
      // 对于省份不足6个的地区，使用4个选项
      const minOptionCount = 4;
      if (options.length > minOptionCount) {
        const correctAnswer = city.province;
        const otherOptions = options.filter(opt => opt !== correctAnswer);
        const shuffledOthers = otherOptions.sort(() => Math.random() - 0.5);
        options = [correctAnswer, ...shuffledOthers.slice(0, minOptionCount - 1)];
      }
    }
    
    return {
      image: cityImageDataUrl, // 使用Canvas生成的图片
      question: `这个城市属于哪个省份？`,
      city: city.city,
      province: city.province,
      plate_code: city.plate_code,
      region: city.region,
      answer: city.province, // 答案是省份
      options: options.sort(() => Math.random() - 0.5), // 随机排序选项
      category: "地理知识"
    };
  });
};

// Function to generate military equipment questions (with category support)
const generateMilitaryEquipmentQuestions = () => {
  return generateQuestions(militaryEquipment, true, DIFFICULTY_LEVELS[5].options);
};

// Function to generate vehicle questions (with category support)
const generateVehicleQuestions = () => {
  return generateQuestions(vehicles, true, DIFFICULTY_LEVELS[2].options);
};



// Function to generate dog and cat breed questions (with category support, 2-star difficulty)
const generateDogCatBreedQuestions = () => {
  return generateQuestions(dogCatBreeds, true, DIFFICULTY_LEVELS[2].options);
};

// Function to generate houseplant questions (with category support, 3-star difficulty)
const generateHouseplantQuestions = () => {
  return generateQuestions(houseplants, true, DIFFICULTY_LEVELS[3].options);
};



// Function to generate home appliance questions (1-star difficulty, 4 options)
const generateHomeApplianceQuestions = () => {
  return generateQuestions(homeAppliances, false, DIFFICULTY_LEVELS[1].options);
};

// Function to generate vegetable questions (2-star difficulty, 4 options)
const generateVegetableQuestions = () => {
  return generateQuestions(vegetables, false, DIFFICULTY_LEVELS[2].options);
};

// Function to generate fruit questions (2-star difficulty, 4 options)
const generateFruitQuestions = () => {
  return generateQuestions(fruits, false, DIFFICULTY_LEVELS[2].options);
};

const generateDishQuestions = () => {
  return generateQuestions(dishes, false, DIFFICULTY_LEVELS[2].options);
};

const generateSportQuestions = () => {
  return generateQuestions(sports, false, DIFFICULTY_LEVELS[2].options);
};

const generateFishQuestions = () => {
  return generateQuestions(fish, false, DIFFICULTY_LEVELS[3].options);
};

// Function to generate world foods questions with country-based options (3-star difficulty)
const generateWorldFoodsQuestions = () => {
  if (!worldFoods || worldFoods.length === 0) return [];

  return worldFoods.map(food => {
    const options = new Set([food.country]);
    
    // 从其他国家中选择错误选项
    const otherCountries = [...new Set(worldFoods.map(f => f.country))].filter(country => country !== food.country);
    
    // 添加随机的其他国家作为错误选项
    while (options.size < DIFFICULTY_LEVELS[3].options && options.size < otherCountries.length + 1) {
      const randomIndex = Math.floor(Math.random() * otherCountries.length);
      const randomCountry = otherCountries[randomIndex];
      if (randomCountry !== food.country) {
        options.add(randomCountry);
      }
    }

    return {
      image: food.fileName,
      answer: food.country,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      foodName: food.name,
      difficulty: food.difficulty
    };
  });
};



const generateLandmarkBuildingQuestions = () => {
  if (!landmarkBuildings || landmarkBuildings.length === 0) return [];

  return landmarkBuildings.map(building => {
    const options = new Set([building.country]);
    
    // 从其他国家中选择错误选项
    const otherCountries = [...new Set(landmarkBuildings.map(b => b.country))].filter(country => country !== building.country);
    
    // 添加随机的其他国家作为错误选项
    while (options.size < DIFFICULTY_LEVELS[3].options && options.size < otherCountries.length + 1) {
      const randomIndex = Math.floor(Math.random() * otherCountries.length);
      const randomCountry = otherCountries[randomIndex];
      if (randomCountry !== building.country) {
        options.add(randomCountry);
      }
    }

    return {
      image: building.fileName,
      answer: building.country,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      buildingName: building.buildingName,
      buildingType: building.buildingType
    };
  });
};

// Function to generate global actress questions with country-based options (4-star difficulty)
const generateGlobalActressQuestions = () => {
  if (!globalActresses || globalActresses.length === 0) return [];

  return globalActresses.map(actress => {
    const options = new Set([actress.country]);
    
    // 按人种分类，从同一人种中选择错误选项
    const sameRaceActresses = globalActresses.filter(other => 
      other.race === actress.race && other.country !== actress.country
    );
    
    // 添加同一人种的其他国家作为错误选项
    while (options.size < DIFFICULTY_LEVELS[4].options && options.size < sameRaceActresses.length + 1) {
      const randomIndex = Math.floor(Math.random() * sameRaceActresses.length);
      const randomActress = sameRaceActresses[randomIndex];
      if (randomActress && randomActress.country !== actress.country) {
        options.add(randomActress.country);
      }
    }
    
    // 如果同一人种的国家不够，从其他人种补充
    if (options.size < DIFFICULTY_LEVELS[4].options) {
      const allCountries = [...new Set(globalActresses.map(a => a.country))];
      while (options.size < DIFFICULTY_LEVELS[4].options && options.size < allCountries.length) {
        const randomIndex = Math.floor(Math.random() * allCountries.length);
        const randomCountry = allCountries[randomIndex];
        if (randomCountry !== actress.country) {
          options.add(randomCountry);
        }
      }
    }

    return {
      image: actress.fileName,
      answer: actress.country,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      category: actress.race,
      actressName: actress.name,
      race: actress.race
    };
  });
};

// Function to generate global actor questions with country-based options (4-star difficulty)
const generateGlobalActorQuestions = () => {
  if (!globalActors || globalActors.length === 0) return [];

  return globalActors.map(actor => {
    const options = new Set([actor.country]);
    
    // 按人种分类，从同一人种中选择错误选项
    const sameRaceActors = globalActors.filter(other => 
      other.race === actor.race && other.country !== actor.country
    );
    
    // 添加同一人种的其他国家作为错误选项
    while (options.size < DIFFICULTY_LEVELS[4].options && options.size < sameRaceActors.length + 1) {
      const randomIndex = Math.floor(Math.random() * sameRaceActors.length);
      const randomActor = sameRaceActors[randomIndex];
      if (randomActor && randomActor.country !== actor.country) {
        options.add(randomActor.country);
      }
    }
    
    // 如果同一人种的国家不够，从其他人种补充
    if (options.size < DIFFICULTY_LEVELS[4].options) {
      const allCountries = [...new Set(globalActors.map(a => a.country))];
      while (options.size < DIFFICULTY_LEVELS[4].options && options.size < allCountries.length) {
        const randomIndex = Math.floor(Math.random() * allCountries.length);
        const randomCountry = allCountries[randomIndex];
        if (randomCountry !== actor.country) {
          options.add(randomCountry);
        }
      }
    }

    return {
      image: actor.fileName,
      answer: actor.country,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      category: actor.race,
      actorName: actor.name,
      race: actor.race
    };
  });
};

// Function to generate fairy tale questions (2-star difficulty)
const generateFairyTaleQuestions = () => {
  return generateQuestions(fairyTales, true, DIFFICULTY_LEVELS[2].options);
};

// Function to generate Chinese foods questions with city-based options (3-star difficulty)
const generateChineseFoodsQuestions = () => {
  if (!chineseFoods || chineseFoods.length === 0) return [];

  return chineseFoods.map(food => {
    const options = new Set([food.city]);
    
    // 从其他城市中选择错误选项
    const otherCities = [...new Set(chineseFoods.map(f => f.city))].filter(city => city !== food.city);
    
    // 添加随机的其他城市作为错误选项
    while (options.size < DIFFICULTY_LEVELS[3].options && options.size < otherCities.length + 1) {
      const randomIndex = Math.floor(Math.random() * otherCities.length);
      const randomCity = otherCities[randomIndex];
      if (randomCity !== food.city) {
        options.add(randomCity);
      }
    }

    return {
      image: food.fileName,
      answer: food.city,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      foodName: food.name,
      fame: food.fame
    };
  });
};

// Function to generate celestial bodies questions with name-based options (4-star difficulty)
const generateCelestialBodiesQuestions = () => {
  if (!celestialBodies || celestialBodies.length === 0) return [];

  return celestialBodies.map(body => {
    const options = new Set([body.name]);
    
    // 从其他天体名称中选择错误选项
    const otherNames = celestialBodies.map(b => b.name).filter(name => name !== body.name);
    
    // 添加随机的其他天体名称作为错误选项
    while (options.size < DIFFICULTY_LEVELS[4].options && options.size < otherNames.length + 1) {
      const randomIndex = Math.floor(Math.random() * otherNames.length);
      const randomName = otherNames[randomIndex];
      if (randomName !== body.name) {
        options.add(randomName);
      }
    }

    return {
      image: body.fileName,
      answer: body.name,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      celestialBodyType: body.type,
      fame: body.fame
    };
  });
};

// Function to generate clothes questions with name-based options (2-star difficulty)
const generateClothesQuestions = () => {
  if (!clothes || clothes.length === 0) return [];

  return clothes.map(item => {
    const options = new Set([item.name]);
    
    // 从其他服装名称中选择错误选项
    const otherNames = clothes.map(c => c.name).filter(name => name !== item.name);
    
    // 添加随机的其他服装名称作为错误选项
    while (options.size < DIFFICULTY_LEVELS[2].options && options.size < otherNames.length + 1) {
      const randomIndex = Math.floor(Math.random() * otherNames.length);
      const randomName = otherNames[randomIndex];
      if (randomName !== item.name) {
        options.add(randomName);
      }
    }

    return {
      image: item.fileName,
      answer: item.name,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      clothesCategory: item.category
    };
  });
};

// Function to generate traffic signs questions with category support (3-star difficulty)
const generateTrafficSignsQuestions = () => {
  return generateQuestions(trafficSigns, true, DIFFICULTY_LEVELS[3].options);
};

// Function to generate livestock questions (2-star difficulty)
const generateLivestockQuestions = () => {
  return generateQuestions(livestock, false, DIFFICULTY_LEVELS[2].options);
};

// Function to generate beautiful sceneries questions with country-based options (3-star difficulty)
const generateBeautifulSceneriesQuestions = () => {
  if (!beautifulSceneries || beautifulSceneries.length === 0) return [];

  return beautifulSceneries.map(scenery => {
    const options = new Set([scenery.country]);
    
    // 从其他国家中选择错误选项
    const otherCountries = [...new Set(beautifulSceneries.map(s => s.country))].filter(country => country !== scenery.country);
    
    // 添加随机的其他国家作为错误选项
    while (options.size < DIFFICULTY_LEVELS[3].options && options.size < otherCountries.length + 1) {
      const randomIndex = Math.floor(Math.random() * otherCountries.length);
      const randomCountry = otherCountries[randomIndex];
      if (randomCountry !== scenery.country) {
        options.add(randomCountry);
      }
    }

    return {
      image: scenery.fileName,
      answer: scenery.country,
      options: Array.from(options).sort(() => Math.random() - 0.5), // 随机排序选项
      sceneryName: scenery.name
    };
  });
};



export const quizzes = {
  'animals': {
    title: '动物世界',
    description: '看图识动物，测试你的动物知识！',
    difficulty: 1,
    questions: generateAnimalQuestions(),
  },
  'car-logos': {
    title: '车标达人挑战',
    description: '看看你认识多少个车标？',
    difficulty: 2,
    questions: generateCarLogoQuestions(),
  },
  'country-flags': {
    title: '国旗大全',
    description: '来挑战一下你认识多少国旗吧！',
    difficulty: 3,
    questions: generateCountryFlagQuestions(),
  },
  'world-landmarks': {
    title: '世界名胜古迹',
    description: '看图识别世界著名的名胜古迹，测试你的地理知识！',
    difficulty: 3,
    questions: generateWorldLandmarkQuestions(),
  },
  'china-landmarks': {
    title: '中国旅游景点',
    description: '探索祖国大好河山，测试你对中国著名旅游景点的了解！',
    difficulty: 3,
    questions: generateChinaLandmarkQuestions(),
  },

  'world-flags-plus': {
    title: '世界国旗Plus',
    description: '终极挑战！识别世界上所有195个国家的国旗！',
    difficulty: 5,
    questions: generateWorldFlagsPlusQuestions(),
  },
  'ethnicities': {
    title: '世界民族文化',
    description: '看传统服饰识民族，测试你对世界各民族文化的了解！',
    difficulty: 5,
    questions: generateEthnicityQuestions(),
  },
  'city-regions': {
    title: '城市归属省份',
    description: '看城市名猜省份，测试你的地理知识！选择车牌最靠后的城市',
    difficulty: 4,
    questions: generateCityRegionQuestions(),
  },
  'japanese-actresses': {
    title: '日本女优知识',
    description: '看图识别日本历史上最有名的女优，测试你的知识面！',
    difficulty: 4,
    hidden: true,
    questions: generateJapaneseActressQuestions(),
  },
  'military-equipment': {
    title: '军事装备大全',
    description: '看图识别世界各国著名的军事装备，测试你的军事知识！涵盖步枪、坦克、战斗机、舰船等20个类别。',
    difficulty: 5,
    questions: generateMilitaryEquipmentQuestions(),
  },
  'vehicles': {
    title: '交通工具大全',
    description: '看图识别各种交通工具，测试你对陆地、天空、水里交通工具的了解！',
    difficulty: 2,
    questions: generateVehicleQuestions(),
  },

  'dog-cat-breeds': {
    title: '猫狗品种大全',
    description: '看图识别猫狗品种！从金毛、哈士奇到英短、布偶猫，测试你对宠物品种的了解程度！',
    difficulty: 2,
    questions: generateDogCatBreedQuestions(),
  },
  'houseplants': {
    title: '家养植物品种',
    description: '看图识别家养植物品种！从观叶植物、观花植物到多肉植物，测试你对花鸟市场常见植物的了解！',
    difficulty: 3,
    questions: generateHouseplantQuestions(),
  },

  'home-appliances': {
    title: '家用电器大全',
    description: '看图识别常见家用电器！从厨房电器到个人护理用品，测试你对日常生活用品的了解！',
    difficulty: 1,
    questions: generateHomeApplianceQuestions(),
  },
  'vegetables': {
    title: '常见蔬菜',
    description: '看图识别常见蔬菜！从根茎类到叶菜类，从瓜果类到豆类，测试你对日常蔬菜的了解！',
    difficulty: 2,
    questions: generateVegetableQuestions(),
  },
  'fruits': {
    title: '常见水果',
    description: '看图识别常见水果！从苹果香蕉到热带水果，从浆果类到柑橘类，测试你对日常水果的了解！',
    difficulty: 2,
    questions: generateFruitQuestions(),
  },
  'dishes': {
    title: '家常菜大全',
    description: '看图识别常见家常菜！从红烧肉到宫保鸡丁，从麻婆豆腐到糖醋排骨，测试你对中式家常菜的了解！',
    difficulty: 2,
    questions: generateDishQuestions(),
  },
  'sports': {
    title: '体育运动',
    description: '看图识别各种体育运动！从足球篮球到游泳跑步，从武术太极到攀岩滑雪，测试你对体育运动的了解！',
    difficulty: 2,
    questions: generateSportQuestions(),
  },
  'fish': {
    title: '鱼类大全',
    description: '看图识别常见鱼类！从淡水鱼到海水鱼，从鲤鱼草鱼到带鱼鲈鱼，测试你对鱼类的了解！',
    difficulty: 3,
    questions: generateFishQuestions(),
  },
  'world-foods': {
    title: '世界各国著名食物',
    description: '看图识别世界各国的著名美食！从中国的北京烤鸭到意大利的披萨，从日本的寿司到法国的鹅肝，猜猜这些美食来自哪个国家！',
    difficulty: 3,
    questions: generateWorldFoodsQuestions(),
  },

  'global-actresses': {
    title: '世界各国代表女明星',
    description: '看图识别世界各国最具代表性的女明星，按黄种人、白种人、黑种人分类，猜猜她们来自哪个国家！',
    difficulty: 4,
    questions: generateGlobalActressQuestions(),
  },
  'global-actors': {
    title: '世界各国代表男明星',
    description: '看图识别世界各国最具代表性的男明星，按黄种人、白种人、黑种人分类，猜猜他们来自哪个国家！',
    difficulty: 4,
    questions: generateGlobalActorQuestions(),
  },
  'landmark_buildings': {
    title: '各国标志性建筑',
    description: '看图识别世界各国的标志性建筑！包含城市地标和乡村特色建筑，猜猜这些建筑来自哪个国家！',
    difficulty: 3,
    questions: generateLandmarkBuildingQuestions(),
  },
  'fairy_tales': {
    title: '童话故事',
    description: '看图识别经典童话故事和卡通片！包含中国、日本、美国和其他国家的著名童话故事，猜猜它们来自哪个国家！',
    difficulty: 2,
    questions: generateFairyTaleQuestions(),
  },
  'chinese_foods': {
    title: '中国著名食物',
    description: '看图识别中国各个城市最有名的食物！从北京烤鸭到重庆火锅，从兰州拉面到广州肠粉，猜猜这些美食来自哪个城市！',
    difficulty: 3,
    questions: generateChineseFoodsQuestions(),
  },
  'celestial_bodies': {
    title: '宇宙天体',
    description: '看图识别宇宙中的各种天体！从太阳系的行星到遥远的恒星，从卫星到彗星，猜猜这是哪个天体！',
    difficulty: 4,
    questions: generateCelestialBodiesQuestions(),
  },
  'clothes': {
    title: '服装类型',
    description: '看图识别各种服装类型！从衣服到裤子、袜子再到帽子，测试你对日常服装的了解！',
    difficulty: 2,
    questions: generateClothesQuestions(),
  },
  'traffic_signs': {
    title: '交通指示牌',
    description: '看图识别中国交通指示牌！从禁令标志到警告标志，从指示标志到指路标志，测试你对道路交通标志的了解！',
    difficulty: 3,
    questions: generateTrafficSignsQuestions(),
  },
  'livestock': {
    title: '家禽和家畜',
    description: '看图识别常见的家禽和家畜！从鸡鸭鹅到猪牛羊，从家养宠物到农场动物，测试你对农业养殖动物的了解！',
    difficulty: 2,
    questions: generateLivestockQuestions(),
  },
  'beautiful_sceneries': {
    title: '最美风景',
    description: '看图识别世界上最美的风景！从马尔代夫海滩到瑞士阿尔卑斯山，从挪威峡湾到新西兰米尔福德峡湾，猜猜这些绝美风景来自哪个国家！',
    difficulty: 3,
    questions: generateBeautifulSceneriesQuestions(),
  },
};