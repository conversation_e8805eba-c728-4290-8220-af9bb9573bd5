const chinaLandmarkFiles = [
  {
    code: 'china_landmark_1',
    name: '万里长城',
    location: '北京',
    fileName: "china_landmark_1.jpg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_2',
    name: '故宫',
    location: '北京',
    fileName: "china_landmark_2.jpeg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_3',
    name: '天坛',
    location: '北京',
    fileName: "china_landmark_3.png",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_4',
    name: '颐和园',
    location: '北京',
    fileName: "china_landmark_4.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_5',
    name: '兵马俑',
    location: '陕西西安',
    fileName: "china_landmark_5.jpg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_6',
    name: '布达拉宫',
    location: '西藏拉萨',
    fileName: "china_landmark_6.png",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_7',
    name: '西湖',
    location: '浙江杭州',
    fileName: "china_landmark_7.jpeg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_8',
    name: '九寨沟',
    location: '四川',
    fileName: "china_landmark_8.jpg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_9',
    name: '黄山',
    location: '安徽',
    fileName: "china_landmark_9.jpeg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_10',
    name: '张家界',
    location: '湖南',
    fileName: "china_landmark_10.jpg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_11',
    name: '泰山',
    location: '山东',
    fileName: "china_landmark_11.jpg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_12',
    name: '华山',
    location: '陕西',
    fileName: "china_landmark_12.jpeg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_13',
    name: '峨眉山',
    location: '四川',
    fileName: "china_landmark_13.jpg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_14',
    name: '五台山',
    location: '山西',
    fileName: "china_landmark_14.jpeg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_15',
    name: '普陀山',
    location: '浙江',
    fileName: "china_landmark_15.jpg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_16',
    name: '武夷山',
    location: '福建',
    fileName: "china_landmark_16.png",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_17',
    name: '庐山',
    location: '江西',
    fileName: "china_landmark_17.jpg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_18',
    name: '衡山',
    location: '湖南',
    fileName: "china_landmark_18.jpg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_19',
    name: '嵩山',
    location: '河南',
    fileName: "china_landmark_19.jpg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_20',
    name: '恒山',
    location: '山西',
    fileName: "china_landmark_20.jpeg",
    category: '山脉景观'
  },
  {
    code: 'china_landmark_21',
    name: '青海湖',
    location: '青海',
    fileName: "china_landmark_21.png",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_22',
    name: '洞庭湖',
    location: '湖南',
    fileName: "china_landmark_22.jpeg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_23',
    name: '鄱阳湖',
    location: '江西',
    fileName: "china_landmark_23.png",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_24',
    name: '太湖',
    location: '江苏',
    fileName: "china_landmark_24.jpeg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_25',
    name: '千岛湖',
    location: '浙江',
    fileName: "china_landmark_25.jpg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_26',
    name: '洱海',
    location: '云南',
    fileName: "china_landmark_26.jpeg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_27',
    name: '泸沽湖',
    location: '云南',
    fileName: "china_landmark_27.jpg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_28',
    name: '纳木错',
    location: '西藏',
    fileName: "china_landmark_28.jpg",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_29',
    name: '茶卡盐湖',
    location: '青海',
    fileName: "china_landmark_29.png",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_30',
    name: '黄龙',
    location: '四川',
    fileName: "china_landmark_30.png",
    category: '湖海水系'
  },
  {
    code: 'china_landmark_31',
    name: '圆明园',
    location: '北京',
    fileName: "china_landmark_31.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_32',
    name: '雍和宫',
    location: '北京',
    fileName: "china_landmark_32.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_33',
    name: '恭王府',
    location: '北京',
    fileName: "china_landmark_33.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_34',
    name: '避暑山庄',
    location: '河北承德',
    fileName: "china_landmark_34.png",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_35',
    name: '大昭寺',
    location: '西藏拉萨',
    fileName: "china_landmark_35.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_36',
    name: '拙政园',
    location: '江苏苏州',
    fileName: "china_landmark_36.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_37',
    name: '留园',
    location: '江苏苏州',
    fileName: "china_landmark_37.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_38',
    name: '网师园',
    location: '江苏苏州',
    fileName: "china_landmark_38.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_39',
    name: '豫园',
    location: '上海',
    fileName: "china_landmark_39.jpg",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_40',
    name: '岳麓书院',
    location: '湖南长沙',
    fileName: "china_landmark_40.png",
    category: '古建筑群'
  },
  {
    code: 'china_landmark_41',
    name: '八达岭长城',
    location: '北京',
    fileName: "china_landmark_41.jpg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_42',
    name: '慕田峪长城',
    location: '北京',
    fileName: "china_landmark_42.jpg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_43',
    name: '秦始皇陵',
    location: '陕西西安',
    fileName: "china_landmark_43.jpeg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_44',
    name: '大雁塔',
    location: '陕西西安',
    fileName: "china_landmark_44.jpg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_45',
    name: '明十三陵',
    location: '北京',
    fileName: "china_landmark_45.jpeg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_46',
    name: '丽江古城',
    location: '云南',
    fileName: "china_landmark_46.jpeg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_47',
    name: '平遥古城',
    location: '山西',
    fileName: "china_landmark_47.jpeg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_48',
    name: '凤凰古城',
    location: '湖南',
    fileName: "china_landmark_48.png",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_49',
    name: '乌镇',
    location: '浙江',
    fileName: "china_landmark_49.jpg",
    category: '历史遗迹'
  },
  {
    code: 'china_landmark_50',
    name: '周庄',
    location: '江苏',
    fileName: "china_landmark_50.jpg",
    category: '历史遗迹'
  },
];

import imageConfig from '../config/imageConfig.js';

export const chinaLandmarks = chinaLandmarkFiles.map(landmark => ({
  ...landmark,
  imageUrl: imageConfig.getImageUrl('chinaLandmarks', landmark.fileName)
}));

// 数据验证函数
function validateChinaLandmarkData() {
  const names = new Set();
  const filenames = new Set();
  const codes = new Set();

  for (const landmark of chinaLandmarkFiles) {
    if (!landmark.name || !landmark.fileName || !landmark.code || !landmark.category || !landmark.location) {
      console.error('缺少必需字段的数据项:', landmark);
    }

    if (names.has(landmark.name)) {
      console.error('重复的景点名称:', landmark.name);
    }
    names.add(landmark.name);

    if (filenames.has(landmark.fileName)) {
      console.error('重复的文件名:', landmark.fileName);
    }
    filenames.add(landmark.fileName);

    if (codes.has(landmark.code)) {
      console.error('重复的编码:', landmark.code);
    }
    codes.add(landmark.code);
  }

  console.log('中国旅游景点数据验证完成');
  console.log('总数量:', chinaLandmarkFiles.length);
  console.log('分类统计:', 
    chinaLandmarkFiles.reduce((acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + 1;
      return acc;
    }, {})
  );
}

// 运行验证
validateChinaLandmarkData();
