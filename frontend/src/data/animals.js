const animalFiles = [
  {
    code: 'animal_1',
    name: '狗',
    fileName: "animal_1.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_2',
    name: '猫',
    fileName: "animal_2.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_3',
    name: '大象',
    fileName: "animal_3.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_4',
    name: '狮子',
    fileName: "animal_4.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_5',
    name: '老虎',
    fileName: "animal_5.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_6',
    name: '熊猫',
    fileName: "animal_6.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_7',
    name: '兔子',
    fileName: "animal_7.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_8',
    name: '马',
    fileName: "animal_8.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_9',
    name: '牛',
    fileName: "animal_9.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_10',
    name: '羊',
    fileName: "animal_10.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_11',
    name: '猪',
    fileName: "animal_11.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_12',
    name: '猴子',
    fileName: "animal_12.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_13',
    name: '长颈鹿',
    fileName: "animal_13.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_14',
    name: '河马',
    fileName: "animal_14.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_15',
    name: '犀牛',
    fileName: "animal_15.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_16',
    name: '斑马',
    fileName: "animal_16.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_17',
    name: '袋鼠',
    fileName: "animal_17.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_18',
    name: '考拉',
    fileName: "animal_18.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_19',
    name: '狐狸',
    fileName: "animal_19.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_20',
    name: '狼',
    fileName: "animal_20.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_21',
    name: '熊',
    fileName: "animal_21.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_22',
    name: '鹿',
    fileName: "animal_22.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_23',
    name: '松鼠',
    fileName: "animal_23.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_24',
    name: '刺猬',
    fileName: "animal_24.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_25',
    name: '浣熊',
    fileName: "animal_25.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_26',
    name: '蝙蝠',
    fileName: "animal_26.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_27',
    name: '老鼠',
    fileName: "animal_27.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_28',
    name: '仓鼠',
    fileName: "animal_28.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_29',
    name: '豹子',
    fileName: "animal_29.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_30',
    name: '猎豹',
    fileName: "animal_30.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_31',
    name: '山羊',
    fileName: "animal_31.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_32',
    name: '骆驼',
    fileName: "animal_32.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_33',
    name: '羊驼',
    fileName: "animal_33.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_34',
    name: '驴',
    fileName: "animal_34.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_35',
    name: '北极熊',
    fileName: "animal_35.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_36',
    name: '小熊猫',
    fileName: "animal_36.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_37',
    name: '土拨鼠',
    fileName: "animal_37.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_38',
    name: '水獭',
    fileName: "animal_38.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_39',
    name: '貂',
    fileName: "animal_39.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_40',
    name: '獾',
    fileName: "animal_40.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_41',
    name: '野牛',
    fileName: "animal_41.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_42',
    name: '麋鹿',
    fileName: "animal_42.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_43',
    name: '驯鹿',
    fileName: "animal_43.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_44',
    name: '野猪',
    fileName: "animal_44.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_45',
    name: '花栗鼠',
    fileName: "animal_45.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_46',
    name: '田鼠',
    fileName: "animal_46.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_47',
    name: '豚鼠',
    fileName: "animal_47.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_48',
    name: '黄鼠狼',
    fileName: "animal_48.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_49',
    name: '臭鼬',
    fileName: "animal_49.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_50',
    name: '猞猁',
    fileName: "animal_50.jpg",
    category: '哺乳动物'
  },
  {
    code: 'animal_51',
    name: '鸡',
    fileName: "animal_51.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_52',
    name: '鸭',
    fileName: "animal_52.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_53',
    name: '鹅',
    fileName: "animal_53.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_54',
    name: '鸽子',
    fileName: "animal_54.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_55',
    name: '麻雀',
    fileName: "animal_55.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_56',
    name: '燕子',
    fileName: "animal_56.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_57',
    name: '乌鸦',
    fileName: "animal_57.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_58',
    name: '喜鹊',
    fileName: "animal_58.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_59',
    name: '老鹰',
    fileName: "animal_59.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_60',
    name: '猫头鹰',
    fileName: "animal_60.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_61',
    name: '孔雀',
    fileName: "animal_61.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_62',
    name: '火烈鸟',
    fileName: "animal_62.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_63',
    name: '鹦鹉',
    fileName: "animal_63.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_64',
    name: '啄木鸟',
    fileName: "animal_64.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_65',
    name: '企鹅',
    fileName: "animal_65.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_66',
    name: '天鹅',
    fileName: "animal_66.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_67',
    name: '鹤',
    fileName: "animal_67.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_68',
    name: '鹈鹕',
    fileName: "animal_68.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_69',
    name: '海鸥',
    fileName: "animal_69.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_70',
    name: '鸵鸟',
    fileName: "animal_70.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_73',
    name: '白鹭',
    fileName: "animal_73.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_77',
    name: '鸳鸯',
    fileName: "animal_77.webp",
    category: '鸟类'
  },
  {
    code: 'animal_78',
    name: '野鸭',
    fileName: "animal_78.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_79',
    name: '大雁',
    fileName: "animal_79.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_80',
    name: '画眉',
    fileName: "animal_80.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_81',
    name: '百灵',
    fileName: "animal_81.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_84',
    name: '黄鹂',
    fileName: "animal_84.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_85',
    name: '金丝雀',
    fileName: "animal_85.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_86',
    name: '八哥',
    fileName: "animal_86.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_87',
    name: '鹌鹑',
    fileName: "animal_87.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_88',
    name: '乌鸫',
    fileName: "animal_88.jpg",
    category: '鸟类'
  },
  {
    code: 'animal_92',
    name: '金鱼',
    fileName: "animal_92.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_93',
    name: '鲤鱼',
    fileName: "animal_93.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_94',
    name: '鲫鱼',
    fileName: "animal_94.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_95',
    name: '草鱼',
    fileName: "animal_95.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_98',
    name: '带鱼',
    fileName: "animal_98.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_99',
    name: '黄鱼',
    fileName: "animal_99.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_100',
    name: '鳗鱼',
    fileName: "animal_100.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_101',
    name: '三文鱼',
    fileName: "animal_101.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_102',
    name: '金枪鱼',
    fileName: "animal_102.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_103',
    name: '比目鱼',
    fileName: "animal_103.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_104',
    name: '海马',
    fileName: "animal_104.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_105',
    name: '鲨鱼',
    fileName: "animal_105.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_106',
    name: '鲸鱼',
    fileName: "animal_106.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_107',
    name: '海豚',
    fileName: "animal_107.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_108',
    name: '海豹',
    fileName: "animal_108.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_109',
    name: '海狮',
    fileName: "animal_109.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_110',
    name: '海象',
    fileName: "animal_110.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_111',
    name: '海龟',
    fileName: "animal_111.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_112',
    name: '海星',
    fileName: "animal_112.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_113',
    name: '章鱼',
    fileName: "animal_113.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_114',
    name: '鱿鱼',
    fileName: "animal_114.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_115',
    name: '乌贼',
    fileName: "animal_115.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_116',
    name: '螃蟹',
    fileName: "animal_116.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_117',
    name: '虾',
    fileName: "animal_117.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_118',
    name: '龙虾',
    fileName: "animal_118.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_119',
    name: '扇贝',
    fileName: "animal_119.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_120',
    name: '生蚝',
    fileName: "animal_120.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_121',
    name: '蛤蜊',
    fileName: "animal_121.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_122',
    name: '鲍鱼',
    fileName: "animal_122.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_123',
    name: '海螺',
    fileName: "animal_123.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_126',
    name: '水母',
    fileName: "animal_126.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_127',
    name: '海葵',
    fileName: "animal_127.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_128',
    name: '珊瑚',
    fileName: "animal_128.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_129',
    name: '海胆',
    fileName: "animal_129.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_130',
    name: '海参',
    fileName: "animal_130.jpg",
    category: '水生动物'
  },
  {
    code: 'animal_131',
    name: '蛇',
    fileName: "animal_131.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_132',
    name: '乌龟',
    fileName: "animal_132.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_133',
    name: '鳄鱼',
    fileName: "animal_133.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_134',
    name: '蜥蜴',
    fileName: "animal_134.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_135',
    name: '变色龙',
    fileName: "animal_135.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_136',
    name: '壁虎',
    fileName: "animal_136.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_137',
    name: '青蛙',
    fileName: "animal_137.jpg",
    category: '两栖动物'
  },
  {
    code: 'animal_138',
    name: '蟾蜍',
    fileName: "animal_138.jpg",
    category: '两栖动物'
  },
  {
    code: 'animal_140',
    name: '牛蛙',
    fileName: "animal_140.jpg",
    category: '两栖动物'
  },
  {
    code: 'animal_144',
    name: '娃娃鱼',
    fileName: "animal_144.jpg",
    category: '两栖动物'
  },
  {
    code: 'animal_146',
    name: '陆龟',
    fileName: "animal_146.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_147',
    name: '水龟',
    fileName: "animal_147.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_148',
    name: '巴西龟',
    fileName: "animal_148.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_149',
    name: '鳄龟',
    fileName: "animal_149.jpg",
    category: '爬行动物'
  },
  {
    code: 'animal_151',
    name: '蝴蝶',
    fileName: "animal_151.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_152',
    name: '蜜蜂',
    fileName: "animal_152.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_153',
    name: '蚂蚁',
    fileName: "animal_153.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_154',
    name: '蜘蛛',
    fileName: "animal_154.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_155',
    name: '蜗牛',
    fileName: "animal_155.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_156',
    name: '蝎子',
    fileName: "animal_156.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_157',
    name: '蜈蚣',
    fileName: "animal_157.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_158',
    name: '蜻蜓',
    fileName: "animal_158.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_159',
    name: '瓢虫',
    fileName: "animal_159.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_160',
    name: '蟋蟀',
    fileName: "animal_160.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_161',
    name: '蚱蜢',
    fileName: "animal_161.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_162',
    name: '螳螂',
    fileName: "animal_162.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_163',
    name: '蚕',
    fileName: "animal_163.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_164',
    name: '蚕蛾',
    fileName: "animal_164.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_165',
    name: '天蛾',
    fileName: "animal_165.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_167',
    name: '萤火虫',
    fileName: "animal_167.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_168',
    name: '金龟子',
    fileName: "animal_168.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_169',
    name: '甲虫',
    fileName: "animal_169.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_170',
    name: '独角仙',
    fileName: "animal_170.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_172',
    name: '天牛',
    fileName: "animal_172.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_173',
    name: '象鼻虫',
    fileName: "animal_173.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_174',
    name: '蚜虫',
    fileName: "animal_174.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_176',
    name: '白蚁',
    fileName: "animal_176.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_183',
    name: '椿象',
    fileName: "animal_183.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_184',
    name: '蝉',
    fileName: "animal_184.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_185',
    name: '知了',
    fileName: "animal_185.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_186',
    name: '蚊子',
    fileName: "animal_186.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_187',
    name: '苍蝇',
    fileName: "animal_187.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_188',
    name: '虻',
    fileName: "animal_188.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_191',
    name: '跳蚤',
    fileName: "animal_191.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_192',
    name: '虱子',
    fileName: "animal_192.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_193',
    name: '蜱',
    fileName: "animal_193.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_194',
    name: '螨虫',
    fileName: "animal_194.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_198',
    name: '蛞蝓',
    fileName: "animal_198.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_199',
    name: '蚯蚓',
    fileName: "animal_199.jpg",
    category: '昆虫'
  },
  {
    code: 'animal_200',
    name: '水蛭',
    fileName: "animal_200.jpg",
    category: '昆虫'
  },
];

import imageConfig from '../config/imageConfig.js';

export const animals = animalFiles.map(animal => ({
  ...animal,
  imageUrl: imageConfig.getImageUrl('animals', animal.fileName)
}));