const sportsStarsFiles = [
  {
    code: 'star_1',
    name: '梅西',
    englishName: '<PERSON>',
    sport: '足球',
    country: '阿根廷',
    fileName: "star_1.jpg"
  },
  {
    code: 'star_2',
    name: 'C罗',
    englishName: '<PERSON><PERSON><PERSON>',
    sport: '足球',
    country: '葡萄牙',
    fileName: "star_2.jpg"
  },
  {
    code: 'star_3',
    name: '内马尔',
    englishName: 'Neymar',
    sport: '足球',
    country: '巴西',
    fileName: "star_3.jpg"
  },
  {
    code: 'star_4',
    name: '姆巴佩',
    englishName: '<PERSON><PERSON><PERSON>',
    sport: '足球',
    country: '法国',
    fileName: "star_4.jpg"
  },
  {
    code: 'star_5',
    name: '哈兰德',
    englishName: 'Erling Haaland',
    sport: '足球',
    country: '挪威',
    fileName: "star_5.jpg"
  },
  {
    code: 'star_6',
    name: '贝利',
    englishName: 'Pele',
    sport: '足球',
    country: '巴西',
    fileName: "star_6.jpg"
  },
  {
    code: 'star_7',
    name: '马拉多纳',
    englishName: 'Diego Maradona',
    sport: '足球',
    country: '阿根廷',
    fileName: "star_7.jpg"
  },
  {
    code: 'star_8',
    name: '齐达内',
    englishName: 'Zinedine Zidane',
    sport: '足球',
    country: '法国',
    fileName: "star_8.jpg"
  },
  {
    code: 'star_9',
    name: '罗纳尔迪尼奥',
    englishName: 'Ronaldinho',
    sport: '足球',
    country: '巴西',
    fileName: "star_9.jpg"
  },
  {
    code: 'star_10',
    name: '卡卡',
    englishName: 'Kaka',
    sport: '足球',
    country: '巴西',
    fileName: "star_10.jpg"
  },
  {
    code: 'star_11',
    name: '贝克汉姆',
    englishName: 'David Beckham',
    sport: '足球',
    country: '英格兰',
    fileName: "star_11.jpg"
  },
  {
    code: 'star_12',
    name: '克鲁伊夫',
    englishName: 'Johan Cruyff',
    sport: '足球',
    country: '荷兰',
    fileName: "star_12.jpg"
  },
  {
    code: 'star_13',
    name: '普拉蒂尼',
    englishName: 'Michel Platini',
    sport: '足球',
    country: '法国',
    fileName: "star_13.jpg"
  },
  {
    code: 'star_14',
    name: '巴乔',
    englishName: 'Roberto Baggio',
    sport: '足球',
    country: '意大利',
    fileName: "star_14.jpg"
  },
  {
    code: 'star_15',
    name: '托蒂',
    englishName: 'Francesco Totti',
    sport: '足球',
    country: '意大利',
    fileName: "star_15.jpg"
  },
  {
    code: 'star_16',
    name: '德布劳内',
    englishName: 'Kevin De Bruyne',
    sport: '足球',
    country: '比利时',
    fileName: "star_16.jpg"
  },
  {
    code: 'star_17',
    name: '莫德里奇',
    englishName: 'Luka Modric',
    sport: '足球',
    country: '克罗地亚',
    fileName: "star_17.jpg"
  },
  {
    code: 'star_18',
    name: '拉莫斯',
    englishName: 'Sergio Ramos',
    sport: '足球',
    country: '西班牙',
    fileName: "star_18.jpg"
  },
  {
    code: 'star_19',
    name: '范迪克',
    englishName: 'Virgil van Dijk',
    sport: '足球',
    country: '荷兰',
    fileName: "star_19.jpg"
  },
  {
    code: 'star_20',
    name: '萨拉赫',
    englishName: 'Mohamed Salah',
    sport: '足球',
    country: '埃及',
    fileName: "star_20.jpg"
  },
  {
    code: 'star_21',
    name: '凯恩',
    englishName: 'Harry Kane',
    sport: '足球',
    country: '英格兰',
    fileName: "star_21.jpg"
  },
  {
    code: 'star_22',
    name: '本泽马',
    englishName: 'Karim Benzema',
    sport: '足球',
    country: '法国',
    fileName: "star_22.jpg"
  },
  {
    code: 'star_23',
    name: '格列兹曼',
    englishName: 'Antoine Griezmann',
    sport: '足球',
    country: '法国',
    fileName: "star_23.jpg"
  },
  {
    code: 'star_24',
    name: '苏亚雷斯',
    englishName: 'Luis Suarez',
    sport: '足球',
    country: '乌拉圭',
    fileName: "star_24.jpg"
  },
  {
    code: 'star_25',
    name: '莱万多夫斯基',
    englishName: 'Robert Lewandowski',
    sport: '足球',
    country: '波兰',
    fileName: "star_25.jpg"
  },
  {
    code: 'star_26',
    name: '迈克尔·乔丹',
    englishName: 'Michael Jordan',
    sport: '篮球',
    country: '美国',
    fileName: "star_26.jpg"
  },
  {
    code: 'star_27',
    name: '勒布朗·詹姆斯',
    englishName: 'LeBron James',
    sport: '篮球',
    country: '美国',
    fileName: "star_27.jpg"
  },
  {
    code: 'star_28',
    name: '科比·布莱恩特',
    englishName: 'Kobe Bryant',
    sport: '篮球',
    country: '美国',
    fileName: "star_28.jpg"
  },
  {
    code: 'star_29',
    name: '斯蒂芬·库里',
    englishName: 'Stephen Curry',
    sport: '篮球',
    country: '美国',
    fileName: "star_29.jpg"
  },
  {
    code: 'star_30',
    name: '凯文·杜兰特',
    englishName: 'Kevin Durant',
    sport: '篮球',
    country: '美国',
    fileName: "star_30.jpg"
  },
  {
    code: 'star_31',
    name: '沙奎尔·奥尼尔',
    englishName: 'Shaquille O\'Neal',
    sport: '篮球',
    country: '美国',
    fileName: "star_31.jpg"
  },
  {
    code: 'star_32',
    name: '魔术师约翰逊',
    englishName: 'Magic Johnson',
    sport: '篮球',
    country: '美国',
    fileName: "star_32.jpg"
  },
  {
    code: 'star_33',
    name: '拉里·伯德',
    englishName: 'Larry Bird',
    sport: '篮球',
    country: '美国',
    fileName: "star_33.jpg"
  },
  {
    code: 'star_34',
    name: '蒂姆·邓肯',
    englishName: 'Tim Duncan',
    sport: '篮球',
    country: '美国',
    fileName: "star_34.jpg"
  },
  {
    code: 'star_35',
    name: '哈基姆·奥拉朱旺',
    englishName: 'Hakeem Olajuwon',
    sport: '篮球',
    country: '美国',
    fileName: "star_35.jpg"
  },
  {
    code: 'star_36',
    name: '卡里姆·阿卜杜尔-贾巴尔',
    englishName: 'Kareem Abdul-Jabbar',
    sport: '篮球',
    country: '美国',
    fileName: "star_36.jpg"
  },
  {
    code: 'star_37',
    name: '威尔特·张伯伦',
    englishName: 'Wilt Chamberlain',
    sport: '篮球',
    country: '美国',
    fileName: "star_37.jpg"
  },
  {
    code: 'star_38',
    name: '比尔·拉塞尔',
    englishName: 'Bill Russell',
    sport: '篮球',
    country: '美国',
    fileName: "star_38.jpg"
  },
  {
    code: 'star_39',
    name: '姚明',
    englishName: 'Yao Ming',
    sport: '篮球',
    country: '中国',
    fileName: "star_39.jpg"
  },
  {
    code: 'star_40',
    name: '字母哥',
    englishName: 'Giannis Antetokounmpo',
    sport: '篮球',
    country: '希腊',
    fileName: "star_40.jpg"
  },
  {
    code: 'star_41',
    name: '詹姆斯·哈登',
    englishName: 'James Harden',
    sport: '篮球',
    country: '美国',
    fileName: "star_41.jpg"
  },
  {
    code: 'star_42',
    name: '拉塞尔·威斯布鲁克',
    englishName: 'Russell Westbrook',
    sport: '篮球',
    country: '美国',
    fileName: "star_42.jpg"
  },
  {
    code: 'star_43',
    name: '保罗·乔治',
    englishName: 'Paul George',
    sport: '篮球',
    country: '美国',
    fileName: "star_43.jpg"
  },
  {
    code: 'star_44',
    name: '安东尼·戴维斯',
    englishName: 'Anthony Davis',
    sport: '篮球',
    country: '美国',
    fileName: "star_44.jpg"
  },
  {
    code: 'star_45',
    name: '卢卡·东契奇',
    englishName: 'Luka Doncic',
    sport: '篮球',
    country: '斯洛文尼亚',
    fileName: "star_45.jpg"
  },
  {
    code: 'star_46',
    name: '费德勒',
    englishName: 'Roger Federer',
    sport: '网球',
    country: '瑞士',
    fileName: "star_46.jpg"
  },
  {
    code: 'star_47',
    name: '纳达尔',
    englishName: 'Rafael Nadal',
    sport: '网球',
    country: '西班牙',
    fileName: "star_47.jpg"
  },
  {
    code: 'star_48',
    name: '德约科维奇',
    englishName: 'Novak Djokovic',
    sport: '网球',
    country: '塞尔维亚',
    fileName: "star_48.jpg"
  },
  {
    code: 'star_49',
    name: '小威廉姆斯',
    englishName: 'Serena Williams',
    sport: '网球',
    country: '美国',
    fileName: "star_49.jpg"
  },
  {
    code: 'star_50',
    name: '大威廉姆斯',
    englishName: 'Venus Williams',
    sport: '网球',
    country: '美国',
    fileName: "star_50.jpg"
  },
  {
    code: 'star_51',
    name: '莎拉波娃',
    englishName: 'Maria Sharapova',
    sport: '网球',
    country: '俄罗斯',
    fileName: "star_51.jpg"
  },
  {
    code: 'star_52',
    name: '阿加西',
    englishName: 'Andre Agassi',
    sport: '网球',
    country: '美国',
    fileName: "star_52.jpg"
  },
  {
    code: 'star_53',
    name: '桑普拉斯',
    englishName: 'Pete Sampras',
    sport: '网球',
    country: '美国',
    fileName: "star_53.jpg"
  },
  {
    code: 'star_54',
    name: '格拉芙',
    englishName: 'Steffi Graf',
    sport: '网球',
    country: '德国',
    fileName: "star_54.jpg"
  },
  {
    code: 'star_55',
    name: '纳芙拉蒂洛娃',
    englishName: 'Martina Navratilova',
    sport: '网球',
    country: '美国',
    fileName: "star_55.jpg"
  },
  {
    code: 'star_56',
    name: '穆雷',
    englishName: 'Andy Murray',
    sport: '网球',
    country: '英国',
    fileName: "star_56.jpg"
  },
  {
    code: 'star_57',
    name: '瓦林卡',
    englishName: 'Stan Wawrinka',
    sport: '网球',
    country: '瑞士',
    fileName: "star_57.jpg"
  },
  {
    code: 'star_58',
    name: '西里奇',
    englishName: 'Marin Cilic',
    sport: '网球',
    country: '克罗地亚',
    fileName: "star_58.jpg"
  },
  {
    code: 'star_59',
    name: '哈勒普',
    englishName: 'Simona Halep',
    sport: '网球',
    country: '罗马尼亚',
    fileName: "star_59.jpg"
  },
  {
    code: 'star_60',
    name: '大坂直美',
    englishName: 'Naomi Osaka',
    sport: '网球',
    country: '日本',
    fileName: "star_60.jpg"
  },
  {
    code: 'star_61',
    name: '博尔特',
    englishName: 'Usain Bolt',
    sport: '田径',
    country: '牙买加',
    fileName: "star_61.jpg"
  },
  {
    code: 'star_62',
    name: '刘翔',
    englishName: 'Liu Xiang',
    sport: '田径',
    country: '中国',
    fileName: "star_62.jpg"
  },
  {
    code: 'star_63',
    name: '苏炳添',
    englishName: 'Su Bingtian',
    sport: '田径',
    country: '中国',
    fileName: "star_63.jpg"
  },
  {
    code: 'star_64',
    name: '卡尔·刘易斯',
    englishName: 'Carl Lewis',
    sport: '田径',
    country: '美国',
    fileName: "star_64.jpg"
  },
  {
    code: 'star_65',
    name: '杰西·欧文斯',
    englishName: 'Jesse Owens',
    sport: '田径',
    country: '美国',
    fileName: "star_65.jpg"
  },
  {
    code: 'star_66',
    name: '埃德·摩西',
    englishName: 'Edwin Moses',
    sport: '田径',
    country: '美国',
    fileName: "star_66.jpg"
  },
  {
    code: 'star_67',
    name: '迈克尔·约翰逊',
    englishName: 'Michael Johnson',
    sport: '田径',
    country: '美国',
    fileName: "star_67.jpg"
  },
  {
    code: 'star_68',
    name: '阿什顿·伊顿',
    englishName: 'Ashton Eaton',
    sport: '田径',
    country: '美国',
    fileName: "star_68.jpg"
  },
  {
    code: 'star_69',
    name: '波拉·拉德克利夫',
    englishName: 'Paula Radcliffe',
    sport: '田径',
    country: '英国',
    fileName: "star_69.jpg"
  },
  {
    code: 'star_70',
    name: '海勒·格布雷塞拉西',
    englishName: 'Haile Gebrselassie',
    sport: '田径',
    country: '埃塞俄比亚',
    fileName: "star_70.jpg"
  },
  {
    code: 'star_71',
    name: '谢震业',
    englishName: 'Xie Zhenye',
    sport: '田径',
    country: '中国',
    fileName: "star_71.jpg"
  },
  {
    code: 'star_72',
    name: '巩立姣',
    englishName: 'Gong Lijiao',
    sport: '田径',
    country: '中国',
    fileName: "star_72.jpg"
  },
  {
    code: 'star_73',
    name: '王嘉男',
    englishName: 'Wang Jianan',
    sport: '田径',
    country: '中国',
    fileName: "star_73.jpg"
  },
  {
    code: 'star_74',
    name: '吕会会',
    englishName: 'Lv Huihui',
    sport: '田径',
    country: '中国',
    fileName: "star_74.jpg"
  },
  {
    code: 'star_75',
    name: '韦永丽',
    englishName: 'Wei Yongli',
    sport: '田径',
    country: '中国',
    fileName: "star_75.jpg"
  },
  {
    code: 'star_76',
    name: '菲尔普斯',
    englishName: 'Michael Phelps',
    sport: '游泳',
    country: '美国',
    fileName: "star_76.jpg"
  },
  {
    code: 'star_77',
    name: '凯蒂·莱德基',
    englishName: 'Katie Ledecky',
    sport: '游泳',
    country: '美国',
    fileName: "star_77.jpg"
  },
  {
    code: 'star_78',
    name: '亚当·佩蒂',
    englishName: 'Adam Peaty',
    sport: '游泳',
    country: '英国',
    fileName: "star_78.jpg"
  },
  {
    code: 'star_79',
    name: '马克·施皮茨',
    englishName: 'Mark Spitz',
    sport: '游泳',
    country: '美国',
    fileName: "star_79.jpg"
  },
  {
    code: 'star_80',
    name: '孙杨',
    englishName: 'Sun Yang',
    sport: '游泳',
    country: '中国',
    fileName: "star_80.jpg"
  },
  {
    code: 'star_81',
    name: '叶诗文',
    englishName: 'Ye Shiwen',
    sport: '游泳',
    country: '中国',
    fileName: "star_81.jpg"
  },
  {
    code: 'star_82',
    name: '宁泽涛',
    englishName: 'Ning Zetao',
    sport: '游泳',
    country: '中国',
    fileName: "star_82.jpg"
  },
  {
    code: 'star_83',
    name: '傅园慧',
    englishName: 'Fu Yuanhui',
    sport: '游泳',
    country: '中国',
    fileName: "star_83.jpg"
  },
  {
    code: 'star_84',
    name: '张雨霏',
    englishName: 'Zhang Yufei',
    sport: '游泳',
    country: '中国',
    fileName: "star_84.jpg"
  },
  {
    code: 'star_85',
    name: '汪顺',
    englishName: 'Wang Shun',
    sport: '游泳',
    country: '中国',
    fileName: "star_85.jpg"
  },
  {
    code: 'star_86',
    name: '老虎伍兹',
    englishName: 'Tiger Woods',
    sport: '高尔夫',
    country: '美国',
    fileName: "star_86.jpg"
  },
  {
    code: 'star_87',
    name: '阿里',
    englishName: 'Muhammad Ali',
    sport: '拳击',
    country: '美国',
    fileName: "star_87.jpg"
  },
  {
    code: 'star_88',
    name: '泰森',
    englishName: 'Mike Tyson',
    sport: '拳击',
    country: '美国',
    fileName: "star_88.jpg"
  },
  {
    code: 'star_89',
    name: '汉密尔顿',
    englishName: 'Lewis Hamilton',
    sport: 'F1赛车',
    country: '英国',
    fileName: "star_89.jpg"
  },
  {
    code: 'star_90',
    name: '舒马赫',
    englishName: 'Michael Schumacher',
    sport: 'F1赛车',
    country: '德国',
    fileName: "star_90.jpg"
  },
  {
    code: 'star_91',
    name: '拜尔斯',
    englishName: 'Simone Biles',
    sport: '体操',
    country: '美国',
    fileName: "star_91.jpg"
  },
  {
    code: 'star_92',
    name: '李宁',
    englishName: 'Li Ning',
    sport: '体操',
    country: '中国',
    fileName: "star_92.jpg"
  },
  {
    code: 'star_93',
    name: '羽生结弦',
    englishName: 'Yuzuru Hanyu',
    sport: '花样滑冰',
    country: '日本',
    fileName: "star_93.jpg"
  },
  {
    code: 'star_94',
    name: '林丹',
    englishName: 'Lin Dan',
    sport: '羽毛球',
    country: '中国',
    fileName: "star_94.jpg"
  },
  {
    code: 'star_95',
    name: '李娜',
    englishName: 'Li Na',
    sport: '网球',
    country: '中国',
    fileName: "star_95.jpg"
  },
  {
    code: 'star_96',
    name: '丁俊晖',
    englishName: 'Ding Junhui',
    sport: '台球',
    country: '中国',
    fileName: "star_96.jpg"
  },
  {
    code: 'star_97',
    name: '奥沙利文',
    englishName: 'Ronnie O Sullivan',
    sport: '台球',
    country: '英国',
    fileName: "star_97.jpg"
  },
  {
    code: 'star_98',
    name: '梅威瑟',
    englishName: 'Floyd Mayweather',
    sport: '拳击',
    country: '美国',
    fileName: "star_98.jpg"
  },
  {
    code: 'star_99',
    name: '帕奎奥',
    englishName: 'Manny Pacquiao',
    sport: '拳击',
    country: '菲律宾',
    fileName: "star_99.jpg"
  },
  {
    code: 'star_100',
    name: '维斯塔潘',
    englishName: 'Max Verstappen',
    sport: 'F1赛车',
    country: '荷兰',
    fileName: "star_100.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const sportsStars = sportsStarsFiles.map(star => ({
  ...star,
  imageUrl: imageConfig.getImageUrl('sports_stars', star.fileName)
}));