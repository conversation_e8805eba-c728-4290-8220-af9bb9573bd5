const fishFiles = [
  {
    code: 'fish_1',
    name: '鲤鱼',
    fileName: "fish_1.jpg"
  },
  {
    code: 'fish_2',
    name: '草鱼',
    fileName: "fish_2.jpg"
  },
  {
    code: 'fish_3',
    name: '鲫鱼',
    fileName: "fish_3.jpg"
  },
  {
    code: 'fish_4',
    name: '带鱼',
    fileName: "fish_4.jpg"
  },
  {
    code: 'fish_5',
    name: '黄花鱼',
    fileName: "fish_5.jpg"
  },
  {
    code: 'fish_6',
    name: '鲈鱼',
    fileName: "fish_6.jpg"
  },
  {
    code: 'fish_7',
    name: '鲢鱼',
    fileName: "fish_7.jpg"
  },
  {
    code: 'fish_8',
    name: '青鱼',
    fileName: "fish_8.jpg"
  },
  {
    code: 'fish_9',
    name: '鳙鱼',
    fileName: "fish_9.jpg"
  },
  {
    code: 'fish_10',
    name: '鳗鱼',
    fileName: "fish_10.jpg"
  },
  {
    code: 'fish_11',
    name: '鲳鱼',
    fileName: "fish_11.jpg"
  },
  {
    code: 'fish_12',
    name: '石斑鱼',
    fileName: "fish_12.jpg"
  },
  {
    code: 'fish_13',
    name: '鳕鱼',
    fileName: "fish_13.jpg"
  },
  {
    code: 'fish_14',
    name: '三文鱼',
    fileName: "fish_14.jpg"
  },
  {
    code: 'fish_15',
    name: '金枪鱼',
    fileName: "fish_15.jpg"
  },
  {
    code: 'fish_16',
    name: '比目鱼',
    fileName: "fish_16.jpg"
  },
  {
    code: 'fish_17',
    name: '鲅鱼',
    fileName: "fish_17.jpg"
  },
  {
    code: 'fish_18',
    name: '黄鳝',
    fileName: "fish_18.jpg"
  },
  {
    code: 'fish_19',
    name: '泥鳅',
    fileName: "fish_19.jpg"
  },
  {
    code: 'fish_20',
    name: '鲶鱼',
    fileName: "fish_20.jpg"
  },
  {
    code: 'fish_21',
    name: '罗非鱼',
    fileName: "fish_21.jpg"
  },
  {
    code: 'fish_22',
    name: '桂鱼',
    fileName: "fish_22.jpg"
  },
  {
    code: 'fish_23',
    name: '武昌鱼',
    fileName: "fish_23.jpg"
  },
  {
    code: 'fish_24',
    name: '鳊鱼',
    fileName: "fish_24.jpg"
  },
  {
    code: 'fish_25',
    name: '鲷鱼',
    fileName: "fish_25.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const fish = fishFiles.map(fish => ({
  ...fish,
  imageUrl: imageConfig.getImageUrl('fish', fish.fileName)
}));