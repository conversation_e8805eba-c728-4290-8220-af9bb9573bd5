const occupationFiles = [
  {
    code: 'occupation_1',
    name: '消防员',
    fileName: "occupation_1.jpg"
  },
  {
    code: 'occupation_2',
    name: '教师',
    fileName: "occupation_2.jpg"
  },
  {
    code: 'occupation_3',
    name: '警察',
    fileName: "occupation_3.jpg"
  },
  {
    code: 'occupation_4',
    name: '医生',
    fileName: "occupation_4.jpg"
  },
  {
    code: 'occupation_5',
    name: '护士',
    fileName: "occupation_5.jpg"
  },
  {
    code: 'occupation_6',
    name: '厨师',
    fileName: "occupation_6.jpg"
  },
  {
    code: 'occupation_7',
    name: '司机',
    fileName: "occupation_7.jpg"
  },
  {
    code: 'occupation_8',
    name: '农民',
    fileName: "occupation_8.jpg"
  },
  {
    code: 'occupation_9',
    name: '工程师',
    fileName: "occupation_9.jpg"
  },
  {
    code: 'occupation_10',
    name: '律师',
    fileName: "occupation_10.jpg"
  },
  {
    code: 'occupation_11',
    name: '会计',
    fileName: "occupation_11.jpg"
  },
  {
    code: 'occupation_12',
    name: '销售员',
    fileName: "occupation_12.jpg"
  },
  {
    code: 'occupation_13',
    name: '服务员',
    fileName: "occupation_13.jpg"
  },
  {
    code: 'occupation_14',
    name: '建筑工人',
    fileName: "occupation_14.jpg"
  },
  {
    code: 'occupation_15',
    name: '清洁工',
    fileName: "occupation_15.jpg"
  },
  {
    code: 'occupation_16',
    name: '记者',
    fileName: "occupation_16.jpg"
  },
  {
    code: 'occupation_17',
    name: '艺术家',
    fileName: "occupation_17.jpg"
  },
  {
    code: 'occupation_18',
    name: '运动员',
    fileName: "occupation_18.jpg"
  },
  {
    code: 'occupation_19',
    name: '科学家',
    fileName: "occupation_19.jpg"
  },
  {
    code: 'occupation_20',
    name: '商人',
    fileName: "occupation_20.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const occupations = occupationFiles.map(occupation => ({
  ...occupation,
  imageUrl: imageConfig.getImageUrl('occupations', occupation.fileName)
}));