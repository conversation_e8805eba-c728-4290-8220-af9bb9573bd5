const countryFlagFiles = [
  {
    code: 'flag_1',
    name: '俄罗斯',
    fileName: "flag_1.jpg"
  },
  {
    code: 'flag_2',
    name: '加拿大',
    fileName: "flag_2.jpg"
  },
  {
    code: 'flag_3',
    name: '中国',
    fileName: "flag_3.jpeg"
  },
  {
    code: 'flag_4',
    name: '美国',
    fileName: "flag_4.jpg"
  },
  {
    code: 'flag_5',
    name: '巴西',
    fileName: "flag_5.jpg"
  },
  {
    code: 'flag_6',
    name: '澳大利亚',
    fileName: "flag_6.jpg"
  },
  {
    code: 'flag_7',
    name: '印度',
    fileName: "flag_7.jpg"
  },
  {
    code: 'flag_8',
    name: '阿根廷',
    fileName: "flag_8.jpg"
  },
  {
    code: 'flag_9',
    name: '哈萨克斯坦',
    fileName: "flag_9.jpg"
  },
  {
    code: 'flag_10',
    name: '阿尔及利亚',
    fileName: "flag_10.jpg"
  },
  {
    code: 'flag_11',
    name: '刚果民主共和国',
    fileName: "flag_11.jpg"
  },
  {
    code: 'flag_12',
    name: '沙特阿拉伯',
    fileName: "flag_12.jpg"
  },
  {
    code: 'flag_13',
    name: '墨西哥',
    fileName: "flag_13.jpg"
  },
  {
    code: 'flag_14',
    name: '印度尼西亚',
    fileName: "flag_14.jpg"
  },
  {
    code: 'flag_15',
    name: '苏丹',
    fileName: "flag_15.jpg"
  },
  {
    code: 'flag_16',
    name: '利比亚',
    fileName: "flag_16.jpg"
  },
  {
    code: 'flag_17',
    name: '伊朗',
    fileName: "flag_17.png"
  },
  {
    code: 'flag_18',
    name: '蒙古',
    fileName: "flag_18.jpg"
  },
  {
    code: 'flag_19',
    name: '秘鲁',
    fileName: "flag_19.jpg"
  },
  {
    code: 'flag_20',
    name: '乍得',
    fileName: "flag_20.jpg"
  },
  {
    code: 'flag_21',
    name: '尼日尔',
    fileName: "flag_21.jpg"
  },
  {
    code: 'flag_22',
    name: '安哥拉',
    fileName: "flag_22.png"
  },
  {
    code: 'flag_23',
    name: '马里',
    fileName: "flag_23.jpg"
  },
  {
    code: 'flag_24',
    name: '南非',
    fileName: "flag_24.jpg"
  },
  {
    code: 'flag_25',
    name: '哥伦比亚',
    fileName: "flag_25.jpg"
  },
  {
    code: 'flag_26',
    name: '埃塞俄比亚',
    fileName: "flag_26.jpg"
  },
  {
    code: 'flag_27',
    name: '玻利维亚',
    fileName: "flag_27.jpg"
  },
  {
    code: 'flag_28',
    name: '毛里塔尼亚',
    fileName: "flag_28.jpg"
  },
  {
    code: 'flag_29',
    name: '埃及',
    fileName: "flag_29.jpg"
  },
  {
    code: 'flag_30',
    name: '坦桑尼亚',
    fileName: "flag_30.jpg"
  },
  {
    code: 'flag_31',
    name: '尼日利亚',
    fileName: "flag_31.jpg"
  },
  {
    code: 'flag_32',
    name: '委内瑞拉',
    fileName: "flag_32.jpg"
  },
  {
    code: 'flag_33',
    name: '巴基斯坦',
    fileName: "flag_33.png"
  },
  {
    code: 'flag_34',
    name: '纳米比亚',
    fileName: "flag_34.jpg"
  },
  {
    code: 'flag_35',
    name: '莫桑比克',
    fileName: "flag_35.jpg"
  },
  {
    code: 'flag_36',
    name: '土耳其',
    fileName: "flag_36.jpg"
  },
  {
    code: 'flag_37',
    name: '智利',
    fileName: "flag_37.png"
  },
  {
    code: 'flag_38',
    name: '赞比亚',
    fileName: "flag_38.jpg"
  },
  {
    code: 'flag_39',
    name: '缅甸',
    fileName: "flag_39.jpg"
  },
  {
    code: 'flag_40',
    name: '阿富汗',
    fileName: "flag_40.jpg"
  },
  {
    code: 'flag_41',
    name: '索马里',
    fileName: "flag_41.jpg"
  },
  {
    code: 'flag_42',
    name: '中非',
    fileName: "flag_42.jpg"
  },
  {
    code: 'flag_43',
    name: '南苏丹',
    fileName: "flag_43.jpg"
  },
  {
    code: 'flag_44',
    name: '马达加斯加',
    fileName: "flag_44.jpg"
  },
  {
    code: 'flag_45',
    name: '乌克兰',
    fileName: "flag_45.jpg"
  },
  {
    code: 'flag_46',
    name: '肯尼亚',
    fileName: "flag_46.png"
  },
  {
    code: 'flag_47',
    name: '博茨瓦纳',
    fileName: "flag_47.jpg"
  },
  {
    code: 'flag_48',
    name: '法国',
    fileName: "flag_48.jpg"
  },
  {
    code: 'flag_49',
    name: '也门',
    fileName: "flag_49.jpg"
  },
  {
    code: 'flag_50',
    name: '泰国',
    fileName: "flag_50.jpg"
  },
  {
    code: 'flag_51',
    name: '西班牙',
    fileName: "flag_51.jpg"
  },
  {
    code: 'flag_52',
    name: '土库曼斯坦',
    fileName: "flag_52.jpg"
  },
  {
    code: 'flag_53',
    name: '喀麦隆',
    fileName: "flag_53.jpg"
  },
  {
    code: 'flag_54',
    name: '巴布亚新几内亚',
    fileName: "flag_54.GIF"
  },
  {
    code: 'flag_55',
    name: '摩洛哥',
    fileName: "flag_55.jpg"
  },
  {
    code: 'flag_56',
    name: '瑞典',
    fileName: "flag_56.jpg"
  },
  {
    code: 'flag_57',
    name: '乌兹别克斯坦',
    fileName: "flag_57.jpg"
  },
  {
    code: 'flag_58',
    name: '伊拉克',
    fileName: "flag_58.jpg"
  },
  {
    code: 'flag_59',
    name: '巴拉圭',
    fileName: "flag_59.jpg"
  },
  {
    code: 'flag_60',
    name: '津巴布韦',
    fileName: "flag_60.jpg"
  },
  {
    code: 'flag_61',
    name: '挪威',
    fileName: "flag_61.jpg"
  },
  {
    code: 'flag_62',
    name: '日本',
    fileName: "flag_62.jpg"
  },
  {
    code: 'flag_63',
    name: '德国',
    fileName: "flag_63.jpg"
  },
  {
    code: 'flag_64',
    name: '刚果共和国',
    fileName: "flag_64.jpg"
  },
  {
    code: 'flag_65',
    name: '芬兰',
    fileName: "flag_65.png"
  },
  {
    code: 'flag_66',
    name: '越南',
    fileName: "flag_66.jpg"
  },
  {
    code: 'flag_67',
    name: '马来西亚',
    fileName: "flag_67.jpg"
  },
  {
    code: 'flag_68',
    name: '科特迪瓦',
    fileName: "flag_68.png"
  },
  {
    code: 'flag_69',
    name: '波兰',
    fileName: "flag_69.jpg"
  },
  {
    code: 'flag_70',
    name: '阿曼',
    fileName: "flag_70.jpg"
  },
  {
    code: 'flag_71',
    name: '意大利',
    fileName: "flag_71.jpg"
  },
  {
    code: 'flag_72',
    name: '菲律宾',
    fileName: "flag_72.png"
  },
  {
    code: 'flag_73',
    name: '布基纳法索',
    fileName: "flag_73.gif"
  },
  {
    code: 'flag_74',
    name: '新西兰',
    fileName: "flag_74.jpg"
  },
  {
    code: 'flag_75',
    name: '加蓬',
    fileName: "flag_75.jpg"
  },
  {
    code: 'flag_76',
    name: '厄瓜多尔',
    fileName: "flag_76.jpg"
  },
  {
    code: 'flag_77',
    name: '几内亚',
    fileName: "flag_77.jpg"
  },
  {
    code: 'flag_78',
    name: '英国',
    fileName: "flag_78.jpg"
  },
  {
    code: 'flag_79',
    name: '乌干达',
    fileName: "flag_79.jpg"
  },
  {
    code: 'flag_80',
    name: '加纳',
    fileName: "flag_80.jpg"
  },
  {
    code: 'flag_81',
    name: '罗马尼亚',
    fileName: "flag_81.jpg"
  },
  {
    code: 'flag_82',
    name: '老挝',
    fileName: "flag_82.jpg"
  },
  {
    code: 'flag_83',
    name: '圭亚那',
    fileName: "flag_83.jpeg"
  },
  {
    code: 'flag_84',
    name: '白俄罗斯',
    fileName: "flag_84.jpg"
  },
  {
    code: 'flag_85',
    name: '吉尔吉斯斯坦',
    fileName: "flag_85.jpg"
  },
  {
    code: 'flag_86',
    name: '塞内加尔',
    fileName: "flag_86.jpg"
  },
  {
    code: 'flag_87',
    name: '叙利亚',
    fileName: "flag_87.jpg"
  },
  {
    code: 'flag_88',
    name: '柬埔寨',
    fileName: "flag_88.jpg"
  },
  {
    code: 'flag_89',
    name: '乌拉圭',
    fileName: "flag_89.gif"
  },
  {
    code: 'flag_90',
    name: '苏里南',
    fileName: "flag_90.jpg"
  },
  {
    code: 'flag_91',
    name: '突尼斯',
    fileName: "flag_91.jpg"
  },
  {
    code: 'flag_92',
    name: '尼泊尔',
    fileName: "flag_92.png"
  },
  {
    code: 'flag_93',
    name: '孟加拉国',
    fileName: "flag_93.jpg"
  },
  {
    code: 'flag_94',
    name: '塔吉克斯坦',
    fileName: "flag_94.jpg"
  },
  {
    code: 'flag_95',
    name: '希腊',
    fileName: "flag_95.jpg"
  },
  {
    code: 'flag_96',
    name: '尼加拉瓜',
    fileName: "flag_96.jpg"
  },
  {
    code: 'flag_97',
    name: '朝鲜',
    fileName: "flag_97.jpg"
  },
  {
    code: 'flag_98',
    name: '马拉维',
    fileName: "flag_98.jpg"
  },
  {
    code: 'flag_99',
    name: '贝宁',
    fileName: "flag_99.jpg"
  },
  {
    code: 'flag_100',
    name: '洪都拉斯',
    fileName: "flag_100.jpg"
  },
];


import imageConfig from '../config/imageConfig.js';

export const countryFlags = countryFlagFiles.map(flag => ({
  ...flag,
  imageUrl: imageConfig.getImageUrl('countryFlags', flag.fileName)
}));
