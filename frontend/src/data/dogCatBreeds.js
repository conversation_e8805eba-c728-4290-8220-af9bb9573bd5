const breedFiles = [
  {
    code: 'breed_1',
    name: '金毛寻回犬',
    fileName: "breed_1.jpg",
    category: '狗'
  },
  {
    code: 'breed_2',
    name: '拉布拉多犬',
    fileName: "breed_2.jpg",
    category: '狗'
  },
  {
    code: 'breed_3',
    name: '泰迪犬',
    fileName: "breed_3.jpg",
    category: '狗'
  },
  {
    code: 'breed_4',
    name: '哈士奇',
    fileName: "breed_4.jpg",
    category: '狗'
  },
  {
    code: 'breed_5',
    name: '萨摩耶',
    fileName: "breed_5.jpg",
    category: '狗'
  },
  {
    code: 'breed_6',
    name: '边境牧羊犬',
    fileName: "breed_6.jpg",
    category: '狗'
  },
  {
    code: 'breed_7',
    name: '德国牧羊犬',
    fileName: "breed_7.jpg",
    category: '狗'
  },
  {
    code: 'breed_8',
    name: '比熊犬',
    fileName: "breed_8.jpg",
    category: '狗'
  },
  {
    code: 'breed_9',
    name: '博美犬',
    fileName: "breed_9.jpg",
    category: '狗'
  },
  {
    code: 'breed_10',
    name: '柯基犬',
    fileName: "breed_10.jpg",
    category: '狗'
  },
  {
    code: 'breed_11',
    name: '阿拉斯加雪橇犬',
    fileName: "breed_11.jpg",
    category: '狗'
  },
  {
    code: 'breed_12',
    name: '松狮犬',
    fileName: "breed_12.jpg",
    category: '狗'
  },
  {
    code: 'breed_13',
    name: '斗牛犬',
    fileName: "breed_13.jpg",
    category: '狗'
  },
  {
    code: 'breed_14',
    name: '雪纳瑞',
    fileName: "breed_14.jpg",
    category: '狗'
  },
  {
    code: 'breed_15',
    name: '吉娃娃',
    fileName: "breed_15.jpg",
    category: '狗'
  },
  {
    code: 'breed_16',
    name: '巴哥犬',
    fileName: "breed_16.jpg",
    category: '狗'
  },
  {
    code: 'breed_17',
    name: '约克夏梗',
    fileName: "breed_17.jpg",
    category: '狗'
  },
  {
    code: 'breed_18',
    name: '马尔济斯犬',
    fileName: "breed_18.jpg",
    category: '狗'
  },
  {
    code: 'breed_19',
    name: '西施犬',
    fileName: "breed_19.jpg",
    category: '狗'
  },
  {
    code: 'breed_20',
    name: '大丹犬',
    fileName: "breed_20.jpg",
    category: '狗'
  },
  {
    code: 'breed_21',
    name: '中华田园犬',
    fileName: "breed_21.jpg",
    category: '狗'
  },
  {
    code: 'breed_22',
    name: '英国短毛猫',
    fileName: "breed_22.jpg",
    category: '猫'
  },
  {
    code: 'breed_23',
    name: '美国短毛猫',
    fileName: "breed_23.jpg",
    category: '猫'
  },
  {
    code: 'breed_24',
    name: '波斯猫',
    fileName: "breed_24.jpg",
    category: '猫'
  },
  {
    code: 'breed_25',
    name: '暹罗猫',
    fileName: "breed_25.jpg",
    category: '猫'
  },
  {
    code: 'breed_26',
    name: '苏格兰折耳猫',
    fileName: "breed_26.jpg",
    category: '猫'
  },
  {
    code: 'breed_27',
    name: '俄罗斯蓝猫',
    fileName: "breed_27.jpg",
    category: '猫'
  },
  {
    code: 'breed_28',
    name: '布偶猫',
    fileName: "breed_28.jpg",
    category: '猫'
  },
  {
    code: 'breed_29',
    name: '阿比西尼亚猫',
    fileName: "breed_29.jpg",
    category: '猫'
  },
  {
    code: 'breed_30',
    name: '孟加拉猫',
    fileName: "breed_30.jpg",
    category: '猫'
  },
  {
    code: 'breed_31',
    name: '缅因猫',
    fileName: "breed_31.jpg",
    category: '猫'
  },
  {
    code: 'breed_32',
    name: '土耳其安哥拉猫',
    fileName: "breed_32.jpg",
    category: '猫'
  },
  {
    code: 'breed_33',
    name: '挪威森林猫',
    fileName: "breed_33.jpg",
    category: '猫'
  },
  {
    code: 'breed_34',
    name: '西伯利亚森林猫',
    fileName: "breed_34.jpg",
    category: '猫'
  },
  {
    code: 'breed_35',
    name: '加菲猫',
    fileName: "breed_35.jpg",
    category: '猫'
  },
  {
    code: 'breed_36',
    name: '无毛猫',
    fileName: "breed_36.jpg",
    category: '猫'
  },
  {
    code: 'breed_37',
    name: '曼基康猫',
    fileName: "breed_37.jpg",
    category: '猫'
  },
  {
    code: 'breed_38',
    name: '银渐层',
    fileName: "breed_38.jpg",
    category: '猫'
  },
  {
    code: 'breed_39',
    name: '金渐层',
    fileName: "breed_39.jpg",
    category: '猫'
  },
  {
    code: 'breed_40',
    name: '蓝白猫',
    fileName: "breed_40.jpg",
    category: '猫'
  },
  {
    code: 'breed_41',
    name: '橘猫',
    fileName: "breed_41.jpg",
    category: '猫'
  },
  {
    code: 'breed_42',
    name: '中国狸花猫',
    fileName: "breed_42.jpg",
    category: '猫'
  },
  {
    code: 'breed_43',
    name: '中国三花猫',
    fileName: "breed_43.jpg",
    category: '猫'
  },
];

import imageConfig from '../config/imageConfig.js';

export const dogCatBreeds = breedFiles.map(item => ({
  ...item,
  imageUrl: imageConfig.getImageUrl('dog-cat-breeds', item.fileName)
}));