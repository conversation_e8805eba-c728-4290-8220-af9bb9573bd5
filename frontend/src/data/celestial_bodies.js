const celestialBodiesFiles = [
  {
    code: 'celestial_body_1',
    name: '太阳',
    type: '恒星',
    fame: 5,
    fileName: "celestial_body_1.jpg"
  },
  {
    code: 'celestial_body_2',
    name: '地球',
    type: '行星',
    fame: 5,
    fileName: "celestial_body_2.jpg"
  },
  {
    code: 'celestial_body_3',
    name: '月球',
    type: '卫星',
    fame: 5,
    fileName: "celestial_body_3.jpg"
  },
  {
    code: 'celestial_body_4',
    name: '火星',
    type: '行星',
    fame: 5,
    fileName: "celestial_body_4.jpg"
  },
  {
    code: 'celestial_body_5',
    name: '木星',
    type: '行星',
    fame: 4,
    fileName: "celestial_body_5.jpg"
  },
  {
    code: 'celestial_body_6',
    name: '土星',
    type: '行星',
    fame: 4,
    fileName: "celestial_body_6.jpg"
  },
  {
    code: 'celestial_body_7',
    name: '金星',
    type: '行星',
    fame: 4,
    fileName: "celestial_body_7.jpg"
  },
  {
    code: 'celestial_body_8',
    name: '水星',
    type: '行星',
    fame: 3,
    fileName: "celestial_body_8.jpg"
  },
  {
    code: 'celestial_body_9',
    name: '天王星',
    type: '行星',
    fame: 3,
    fileName: "celestial_body_9.jpg"
  },
  {
    code: 'celestial_body_10',
    name: '海王星',
    type: '行星',
    fame: 3,
    fileName: "celestial_body_10.jpg"
  },
  {
    code: 'celestial_body_11',
    name: '冥王星',
    type: '矮行星',
    fame: 4,
    fileName: "celestial_body_11.jpg"
  },
  {
    code: 'celestial_body_12',
    name: '木卫一',
    type: '卫星',
    fame: 3,
    fileName: "celestial_body_12.jpg"
  },
  {
    code: 'celestial_body_13',
    name: '木卫二',
    type: '卫星',
    fame: 3,
    fileName: "celestial_body_13.jpg"
  },
  {
    code: 'celestial_body_14',
    name: '土卫六',
    type: '卫星',
    fame: 3,
    fileName: "celestial_body_14.jpg"
  },
  {
    code: 'celestial_body_15',
    name: '谷神星',
    type: '矮行星',
    fame: 3,
    fileName: "celestial_body_15.jpg"
  },
  {
    code: 'celestial_body_16',
    name: '北极星',
    type: '恒星',
    fame: 4,
    fileName: "celestial_body_16.jpg"
  },
  {
    code: 'celestial_body_17',
    name: '天狼星',
    type: '恒星',
    fame: 3,
    fileName: "celestial_body_17.jpg"
  },
  {
    code: 'celestial_body_18',
    name: '参宿四',
    type: '恒星',
    fame: 3,
    fileName: "celestial_body_18.jpg"
  },
  {
    code: 'celestial_body_19',
    name: '哈雷彗星',
    type: '彗星',
    fame: 4,
    fileName: "celestial_body_19.jpg"
  },
  {
    code: 'celestial_body_20',
    name: '小行星带',
    type: '小行星群',
    fame: 3,
    fileName: "celestial_body_20.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const celestialBodies = celestialBodiesFiles.map(body => ({
  ...body,
  imageUrl: imageConfig.getImageUrl('celestial_bodies', body.fileName)
}));