// 课程数据
const coursesData = {
  kindergarten: {
    id: 'kindergarten',
    name: '幼儿园',
    description: '幼儿园阶段课程',
    subjects: {
      primary_prep: {
        id: 'primary-prep',
        name: '幼升小',
        description: '幼升小准备课程',
        courses: {
          'shanghai-primary-prep': {
            id: 'shanghai-primary-prep',
            name: '幼升小（上海）',
            description: '上海幼升小真题练习，按科目分类整理，包含数学、英语、语文、科学常识等内容',
            chapters: [
              {
                id: 'math',
                name: '数学',
                description: '上海幼升小数学真题，包含数的认识、简单运算、图形识别、逻辑推理等内容',
                htmlFile: '/courses/kindergarten/shanghai/math.html'
              },
              {
                id: 'english',
                name: '英语',
                description: '上海幼升小英语真题，包含字母认识、简单单词、基础对话、听力理解等内容',
                htmlFile: '/courses/kindergarten/shanghai/english.html'
              },
              {
                id: 'chinese',
                name: '语文',
                description: '上海幼升小语文真题，包含拼音、汉字认识、组词造句、阅读理解等内容',
                htmlFile: '/courses/kindergarten/shanghai/chinese.html'
              },
              {
                id: 'science',
                name: '科学常识',
                description: '上海幼升小科学常识真题，包含生活常识、自然现象、动植物知识、安全知识等内容',
                htmlFile: '/courses/kindergarten/shanghai/science.html'
              }
            ]
          }
        }
      }
    }
  },
  primary: {
    id: 'primary',
    name: '小学',
    description: '小学阶段课程',
    subjects: {
      math: {
        id: 'primary-math',
        name: '数学',
        description: '小学数学课程',
        courses: {
          'primary-to-junior': {
            id: 'primary-to-junior',
            name: '小升初数学（浙江教育版）',
            description: '小升初数学真题和知识点总结，按题型分类整理',
            chapters: [
              {
                id: 'primary-to-junior',
                name: '小升初数学专题',
                description: '包含计算、几何、应用题、数论、统计与概率等专题内容',
                htmlFile: '/courses/math/zhejiang/primary-to-junior.html'
              }
            ]
          }
        }
      }
    }
  },
  junior: {
    id: 'junior',
    name: '初中',
    description: '初中阶段课程',
    subjects: {
      math: {
        id: 'junior-math',
        name: '数学',
        description: '初中数学课程',
        courses: {
          'zhejiang-junior-math': {
            id: 'zhejiang-junior-math',
            name: '初中数学（浙江教育版）',
            description: '浙江教育版初中数学，包含所有知识点和中考真题',
            chapters: [
              { 
                id: 'grade7-1', 
                name: '初一上', 
                description: '七年级上学期数学，包含有理数、整式加减、一元一次方程、几何图形初步等内容',
                htmlFile: '/courses/math/zhejiang/grade7-1.html'
              },
              { 
                id: 'grade7-2', 
                name: '初一下', 
                description: '七年级下学期数学，包含相交线与平行线、实数、平面直角坐标系、二元一次方程组、不等式与不等式组、数据的收集整理与描述等内容',
                htmlFile: '/courses/math/zhejiang/grade7-2.html'
              },
              { 
                id: 'grade7-algebra-intensive', 
                name: '七年级数学强化专题', 
                description: '因式分解、整式的乘除和分式运算专项训练，包含中考真题和模拟题50道',
                htmlFile: '/courses/math/zhejiang/grade7-algebra-intensive.html'
              }
            ]
          }
        }
      },
      english: {
        id: 'junior-english',
        name: '英语',
        description: '初中英语课程',
        courses: {
          'zhejiang-junior-english': {
            id: 'zhejiang-junior-english',
            name: '初中英语（人教版）',
            description: '人教版初中英语语法，包含所有语法知识点和中考真题',
            chapters: [
              { 
                id: 'grade7-1', 
                name: '初一上', 
                description: '七年级上学期英语语法，包含be动词、人称代词、名词复数、一般现在时、疑问句等基础语法内容',
                htmlFile: '/courses/english/zhejiang/grade7-1.html'
              },
              { 
                id: 'grade7-2', 
                name: '初一下', 
                description: '七年级下学期英语语法，包含现在进行时、情态动词、祈使句、一般过去时等进阶语法内容',
                htmlFile: '/courses/english/zhejiang/grade7-2.html'
              }
            ]
          }
        }
      },
      science: {
        id: 'junior-science',
        name: '科学',
        description: '初中科学课程',
        courses: {
          'zhejiang-junior-science': {
            id: 'zhejiang-junior-science',
            name: '初中科学（浙江教育版）',
            description: '浙江教育版初中科学，包含物理、化学、生物、地理知识点和中考真题',
            chapters: [
              { 
                id: 'grade7-1', 
                name: '初一上', 
                description: '七年级上学期科学，包含声音、光现象、物质性质、生物特征、地球和地图等内容',
                htmlFile: '/courses/science/zhejiang/grade7-1.html'
              },
              { 
                id: 'grade7-2', 
                name: '初一下', 
                description: '七年级下学期科学，包含力学、光学、生物繁殖、地球内部结构等知识',
                htmlFile: '/courses/science/zhejiang/grade7-2.html'
              },
              { 
                id: 'grade7-intensive', 
                name: '七年级科学强化专题', 
                description: '物理和化学专项训练，包含浙江省期末模拟题和中考真题50道',
                htmlFile: '/courses/science/zhejiang/grade7-intensive.html'
              }
            ]
          }
        }
      }
    }
  }
}

// 获取所有学段
export const getAllGrades = () => {
  return Object.entries(coursesData).map(([key, value]) => ({
    id: key,
    name: value.name,
    description: value.description
  }))
}

// 获取指定学段的所有科目
export const getSubjectsByGrade = (gradeId) => {
  const grade = coursesData[gradeId]
  if (!grade || !grade.subjects) return []
  return Object.entries(grade.subjects).map(([key, value]) => ({
    id: key,
    name: value.name,
    description: value.description
  }))
}

// 获取指定学段和科目的所有课程
export const getCoursesBySubject = (gradeId, subjectId) => {
  const grade = coursesData[gradeId]
  if (!grade || !grade.subjects || !grade.subjects[subjectId] || !grade.subjects[subjectId].courses) return []
  return Object.values(grade.subjects[subjectId].courses)
}

// 获取课程数据（通过gradeId, subjectId, courseId）
export const getCourseData = (gradeId, subjectId, courseId) => {
  const grade = coursesData[gradeId]
  if (!grade || !grade.subjects[subjectId] || !grade.subjects[subjectId].courses[courseId]) {
    return null
  }
  return grade.subjects[subjectId].courses[courseId]
}

// 通过courseId查找课程数据（在所有学段和科目中搜索）
export const getCourseById = (courseId) => {
  for (const gradeKey in coursesData) {
    const grade = coursesData[gradeKey]
    for (const subjectKey in grade.subjects) {
      const subject = grade.subjects[subjectKey]
      if (subject.courses && subject.courses[courseId]) {
        return subject.courses[courseId]
      }
    }
  }
  return null
}

// 获取章节数据（现在返回HTML文件路径）
export const getChapterData = (gradeId, subjectId, courseId, chapterId) => {
  const course = getCourseData(gradeId, subjectId, courseId)
  if (!course) return null
  
  const chapter = course.chapters.find(ch => ch.id === chapterId)
  return chapter || null
}

// 获取章节课时数据（这里简化处理，实际可以根据知识点数量计算）
export const getChapterLessons = (chapterId) => {
  // 简化处理，返回固定数量的课时
  const lessonCounts = {
    'grade7-1': Array(8).fill(null).map((_, i) => ({ id: i + 1, name: `第${i + 1}课时` })),
    'grade7-2': Array(6).fill(null).map((_, i) => ({ id: i + 1, name: `第${i + 1}课时` })),
    'grade7-intensive': Array(2).fill(null).map((_, i) => ({ id: i + 1, name: `专题${i + 1}` })),
    'primary-to-junior': Array(5).fill(null).map((_, i) => ({ id: i + 1, name: `专题${i + 1}` })),
    'math': Array(10).fill(null).map((_, i) => ({ id: i + 1, name: `题目${i + 1}` })),
    'english': Array(10).fill(null).map((_, i) => ({ id: i + 1, name: `题目${i + 1}` })),
    'chinese': Array(10).fill(null).map((_, i) => ({ id: i + 1, name: `题目${i + 1}` })),
    'science': Array(10).fill(null).map((_, i) => ({ id: i + 1, name: `题目${i + 1}` }))
  }
  return lessonCounts[chapterId] || []
}

// 获取所有课程列表（兼容旧版本）
export const getAllCourses = () => {
  const allCourses = []
  Object.values(coursesData).forEach(grade => {
    Object.values(grade.subjects).forEach(subject => {
      allCourses.push(...Object.values(subject.courses))
    })
  })
  return allCourses
}