const houseplantFiles = [
  {
    code: 'houseplant_1',
    name: '绿萝',
    fileName: "houseplant_1.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_2',
    name: '发财树',
    fileName: "houseplant_2.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_3',
    name: '幸福树',
    fileName: "houseplant_3.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_4',
    name: '平安树',
    fileName: "houseplant_4.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_5',
    name: '橡皮树',
    fileName: "houseplant_5.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_6',
    name: '虎皮兰',
    fileName: "houseplant_6.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_7',
    name: '吊兰',
    fileName: "houseplant_7.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_8',
    name: '文竹',
    fileName: "houseplant_8.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_9',
    name: '常春藤',
    fileName: "houseplant_9.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_10',
    name: '君子兰',
    fileName: "houseplant_10.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_11',
    name: '龟背竹',
    fileName: "houseplant_11.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_12',
    name: '琴叶榕',
    fileName: "houseplant_12.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_13',
    name: '散尾葵',
    fileName: "houseplant_13.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_14',
    name: '富贵竹',
    fileName: "houseplant_14.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_15',
    name: '万年青',
    fileName: "houseplant_15.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_16',
    name: '铁树',
    fileName: "houseplant_16.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_17',
    name: '金钱树',
    fileName: "houseplant_17.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_18',
    name: '巴西木',
    fileName: "houseplant_18.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_19',
    name: '滴水观音',
    fileName: "houseplant_19.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_20',
    name: '鸭脚木',
    fileName: "houseplant_20.jpg",
    category: '观叶植物'
  },
  {
    code: 'houseplant_21',
    name: '月季',
    fileName: "houseplant_21.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_22',
    name: '玫瑰',
    fileName: "houseplant_22.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_23',
    name: '茉莉',
    fileName: "houseplant_23.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_24',
    name: '栀子花',
    fileName: "houseplant_24.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_25',
    name: '桂花',
    fileName: "houseplant_25.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_26',
    name: '茶花',
    fileName: "houseplant_26.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_27',
    name: '杜鹃',
    fileName: "houseplant_27.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_28',
    name: '蝴蝶兰',
    fileName: "houseplant_28.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_29',
    name: '长寿花',
    fileName: "houseplant_29.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_30',
    name: '仙客来',
    fileName: "houseplant_30.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_31',
    name: '一品红',
    fileName: "houseplant_31.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_32',
    name: '海棠',
    fileName: "houseplant_32.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_33',
    name: '三角梅',
    fileName: "houseplant_33.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_34',
    name: '天竺葵',
    fileName: "houseplant_34.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_35',
    name: '四季海棠',
    fileName: "houseplant_35.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_36',
    name: '康乃馨',
    fileName: "houseplant_36.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_37',
    name: '满天星',
    fileName: "houseplant_37.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_38',
    name: '百合',
    fileName: "houseplant_38.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_39',
    name: '郁金香',
    fileName: "houseplant_39.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_40',
    name: '水仙',
    fileName: "houseplant_40.jpg",
    category: '观花植物'
  },
  {
    code: 'houseplant_41',
    name: '景天',
    fileName: "houseplant_41.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_42',
    name: '莲花掌',
    fileName: "houseplant_42.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_43',
    name: '石莲花',
    fileName: "houseplant_43.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_45',
    name: '十二卷',
    fileName: "houseplant_45.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_46',
    name: '生石花',
    fileName: "houseplant_46.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_47',
    name: '虎刺梅',
    fileName: "houseplant_47.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_48',
    name: '仙人掌',
    fileName: "houseplant_48.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_49',
    name: '昙花',
    fileName: "houseplant_49.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_50',
    name: '蟹爪兰',
    fileName: "houseplant_50.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_51',
    name: '仙人球',
    fileName: "houseplant_51.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_52',
    name: '金手指',
    fileName: "houseplant_52.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_53',
    name: '芦荟',
    fileName: "houseplant_53.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_54',
    name: '虎尾兰',
    fileName: "houseplant_54.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_55',
    name: '佛珠',
    fileName: "houseplant_55.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_56',
    name: '爱之蔓',
    fileName: "houseplant_56.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_57',
    name: '玉树',
    fileName: "houseplant_57.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_58',
    name: '燕子掌',
    fileName: "houseplant_58.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_59',
    name: '令箭荷花',
    fileName: "houseplant_59.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_60',
    name: '麒麟掌',
    fileName: "houseplant_60.jpg",
    category: '多肉植物'
  },
  {
    code: 'houseplant_61',
    name: '富贵竹',
    fileName: "houseplant_61.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_62',
    name: '绿萝',
    fileName: "houseplant_62.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_63',
    name: '吊兰',
    fileName: "houseplant_63.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_64',
    name: '常春藤',
    fileName: "houseplant_64.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_65',
    name: '铜钱草',
    fileName: "houseplant_65.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_66',
    name: '水仙',
    fileName: "houseplant_66.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_67',
    name: '风信子',
    fileName: "houseplant_67.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_68',
    name: '薄荷',
    fileName: "houseplant_68.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_69',
    name: '豆瓣绿',
    fileName: "houseplant_69.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_70',
    name: '白掌',
    fileName: "houseplant_70.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_71',
    name: '红掌',
    fileName: "houseplant_71.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_72',
    name: '龟背竹',
    fileName: "houseplant_72.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_73',
    name: '合果芋',
    fileName: "houseplant_73.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_74',
    name: '花叶万年青',
    fileName: "houseplant_74.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_75',
    name: '银皇后',
    fileName: "houseplant_75.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_76',
    name: '马蹄莲',
    fileName: "houseplant_76.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_77',
    name: '睡莲',
    fileName: "houseplant_77.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_78',
    name: '荷花',
    fileName: "houseplant_78.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_79',
    name: '网纹草',
    fileName: "houseplant_79.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_80',
    name: '袖珍椰子',
    fileName: "houseplant_80.jpg",
    category: '水培植物'
  },
  {
    code: 'houseplant_81',
    name: '薄荷',
    fileName: "houseplant_81.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_82',
    name: '罗勒',
    fileName: "houseplant_82.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_83',
    name: '迷迭香',
    fileName: "houseplant_83.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_84',
    name: '百里香',
    fileName: "houseplant_84.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_85',
    name: '薰衣草',
    fileName: "houseplant_85.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_86',
    name: '柠檬草',
    fileName: "houseplant_86.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_87',
    name: '九层塔',
    fileName: "houseplant_87.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_88',
    name: '紫苏',
    fileName: "houseplant_88.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_89',
    name: '香菜',
    fileName: "houseplant_89.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_90',
    name: '茉莉',
    fileName: "houseplant_90.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_91',
    name: '栀子花',
    fileName: "houseplant_91.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_92',
    name: '桂花',
    fileName: "houseplant_92.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_93',
    name: '夜来香',
    fileName: "houseplant_93.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_94',
    name: '含笑',
    fileName: "houseplant_94.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_95',
    name: '白兰',
    fileName: "houseplant_95.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_96',
    name: '米兰',
    fileName: "houseplant_96.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_97',
    name: '玫瑰',
    fileName: "houseplant_97.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_98',
    name: '香叶天竺葵',
    fileName: "houseplant_98.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_99',
    name: '柠檬薄荷',
    fileName: "houseplant_99.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_100',
    name: '留兰香',
    fileName: "houseplant_100.jpg",
    category: '香草植物'
  },
  {
    code: 'houseplant_101',
    name: '波士顿蕨',
    fileName: "houseplant_101.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_102',
    name: '鸟巢蕨',
    fileName: "houseplant_102.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_103',
    name: '铁线蕨',
    fileName: "houseplant_103.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_104',
    name: '肾蕨',
    fileName: "houseplant_104.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_105',
    name: '鹿角蕨',
    fileName: "houseplant_105.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_106',
    name: '卷柏',
    fileName: "houseplant_106.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_107',
    name: '石松',
    fileName: "houseplant_107.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_108',
    name: '凤尾蕨',
    fileName: "houseplant_108.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_109',
    name: '翠云草',
    fileName: "houseplant_109.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_110',
    name: '万年松',
    fileName: "houseplant_110.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_111',
    name: '孔雀蕨',
    fileName: "houseplant_111.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_112',
    name: '镰刀蕨',
    fileName: "houseplant_112.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_113',
    name: '石韦',
    fileName: "houseplant_113.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_114',
    name: '瓦韦',
    fileName: "houseplant_114.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_115',
    name: '扇蕨',
    fileName: "houseplant_115.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_116',
    name: '蜈蚣草',
    fileName: "houseplant_116.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_117',
    name: '贯众',
    fileName: "houseplant_117.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_118',
    name: '狗脊',
    fileName: "houseplant_118.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_119',
    name: '金毛狗',
    fileName: "houseplant_119.jpg",
    category: '蕨类植物'
  },
  {
    code: 'houseplant_120',
    name: '桫椤',
    fileName: "houseplant_120.jpg",
    category: '蕨类植物'
  },
];

import imageConfig from '../config/imageConfig.js';

export const houseplants = houseplantFiles.map(item => ({
  ...item,
  imageUrl: imageConfig.getImageUrl('houseplants', item.fileName)
}));