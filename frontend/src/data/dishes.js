const dishFiles = [
  {
    code: 'dish_1',
    name: '红烧肉',
    fileName: "dish_1.jpg"
  },
  {
    code: 'dish_2',
    name: '宫保鸡丁',
    fileName: "dish_2.jpg"
  },
  {
    code: 'dish_3',
    name: '麻婆豆腐',
    fileName: "dish_3.jpg"
  },
  {
    code: 'dish_4',
    name: '糖醋排骨',
    fileName: "dish_4.jpg"
  },
  {
    code: 'dish_5',
    name: '鱼香肉丝',
    fileName: "dish_5.jpg"
  },
  {
    code: 'dish_6',
    name: '回锅肉',
    fileName: "dish_6.jpg"
  },
  {
    code: 'dish_7',
    name: '青椒肉丝',
    fileName: "dish_7.jpg"
  },
  {
    code: 'dish_8',
    name: '西红柿炒鸡蛋',
    fileName: "dish_8.jpg"
  },
  {
    code: 'dish_9',
    name: '土豆丝',
    fileName: "dish_9.jpg"
  },
  {
    code: 'dish_10',
    name: '蒜蓉西兰花',
    fileName: "dish_10.jpg"
  },
  {
    code: 'dish_11',
    name: '红烧茄子',
    fileName: "dish_11.jpg"
  },
  {
    code: 'dish_12',
    name: '可乐鸡翅',
    fileName: "dish_12.jpg"
  },
  {
    code: 'dish_13',
    name: '水煮鱼',
    fileName: "dish_13.jpg"
  },
  {
    code: 'dish_14',
    name: '酸菜鱼',
    fileName: "dish_14.jpg"
  },
  {
    code: 'dish_15',
    name: '白切鸡',
    fileName: "dish_15.jpg"
  },
  {
    code: 'dish_16',
    name: '蒸蛋羹',
    fileName: "dish_16.jpg"
  },
  {
    code: 'dish_17',
    name: '炒河粉',
    fileName: "dish_17.jpg"
  },
  {
    code: 'dish_18',
    name: '酱爆茄子',
    fileName: "dish_18.jpg"
  },
  {
    code: 'dish_19',
    name: '清蒸鲈鱼',
    fileName: "dish_19.jpg"
  },
  {
    code: 'dish_20',
    name: '小炒肉',
    fileName: "dish_20.jpg"
  },
  {
    code: 'dish_21',
    name: '红烧狮子头',
    fileName: "dish_21.jpg"
  },
  {
    code: 'dish_22',
    name: '口水鸡',
    fileName: "dish_22.jpg"
  },
  {
    code: 'dish_23',
    name: '蚂蚁上树',
    fileName: "dish_23.jpg"
  },
  {
    code: 'dish_24',
    name: '干煸四季豆',
    fileName: "dish_24.jpg"
  },
  {
    code: 'dish_25',
    name: '白灼菜心',
    fileName: "dish_25.jpg"
  },
  {
    code: 'dish_26',
    name: '蒜泥白肉',
    fileName: "dish_26.jpg"
  },
  {
    code: 'dish_27',
    name: '糖醋里脊',
    fileName: "dish_27.jpg"
  },
  {
    code: 'dish_28',
    name: '干锅花菜',
    fileName: "dish_28.jpg"
  },
  {
    code: 'dish_29',
    name: '红烧带鱼',
    fileName: "dish_29.jpg"
  },
  {
    code: 'dish_30',
    name: '凉拌黄瓜',
    fileName: "dish_30.jpg"
  },
  {
    code: 'dish_31',
    name: '韭菜炒蛋',
    fileName: "dish_31.jpg"
  },
  {
    code: 'dish_32',
    name: '芹菜炒肉',
    fileName: "dish_32.jpg"
  },
  {
    code: 'dish_33',
    name: '豆角焖面',
    fileName: "dish_33.jpg"
  },
  {
    code: 'dish_34',
    name: '蒜苗炒肉',
    fileName: "dish_34.jpg"
  },
  {
    code: 'dish_35',
    name: '冬瓜排骨汤',
    fileName: "dish_35.jpg"
  },
  {
    code: 'dish_36',
    name: '番茄牛腩',
    fileName: "dish_36.jpg"
  },
  {
    code: 'dish_37',
    name: '辣椒炒肉',
    fileName: "dish_37.jpg"
  },
  {
    code: 'dish_38',
    name: '蒜蓉粉丝蒸扇贝',
    fileName: "dish_38.jpg"
  },
  {
    code: 'dish_39',
    name: '红烧冬瓜',
    fileName: "dish_39.jpg"
  },
  {
    code: 'dish_40',
    name: '酸辣土豆丝',
    fileName: "dish_40.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const dishes = dishFiles.map(dish => ({
  ...dish,
  imageUrl: imageConfig.getImageUrl('dishes', dish.fileName)
}));