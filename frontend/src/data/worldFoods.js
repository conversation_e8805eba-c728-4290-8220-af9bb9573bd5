const worldFoodsFiles = [
  {
    code: 'food_1',
    name: '北京烤鸭',
    country: '中国',
    difficulty: 3,
    fileName: "food_1.jpg"
  },
  {
    code: 'food_2',
    name: '麻婆豆腐',
    country: '中国',
    difficulty: 3,
    fileName: "food_2.jpg"
  },
  {
    code: 'food_3',
    name: '小笼包',
    country: '中国',
    difficulty: 3,
    fileName: "food_3.jpg"
  },
  {
    code: 'food_4',
    name: '意大利面',
    country: '意大利',
    difficulty: 2,
    fileName: "food_4.jpg"
  },
  {
    code: 'food_5',
    name: '披萨',
    country: '意大利',
    difficulty: 1,
    fileName: "food_5.jpg"
  },
  {
    code: 'food_6',
    name: '提拉米苏',
    country: '意大利',
    difficulty: 2,
    fileName: "food_6.jpg"
  },
  {
    code: 'food_7',
    name: '法式鹅肝',
    country: '法国',
    difficulty: 3,
    fileName: "food_7.jpg"
  },
  {
    code: 'food_8',
    name: '马卡龙',
    country: '法国',
    difficulty: 2,
    fileName: "food_8.jpg"
  },
  {
    code: 'food_9',
    name: '可颂面包',
    country: '法国',
    difficulty: 2,
    fileName: "food_9.jpg"
  },
  {
    code: 'food_10',
    name: '寿司',
    country: '日本',
    difficulty: 1,
    fileName: "food_10.jpg"
  },
  {
    code: 'food_11',
    name: '拉面',
    country: '日本',
    difficulty: 2,
    fileName: "food_11.jpg"
  },
  {
    code: 'food_12',
    name: '天妇罗',
    country: '日本',
    difficulty: 2,
    fileName: "food_12.jpg"
  },
  {
    code: 'food_13',
    name: '泡菜',
    country: '韩国',
    difficulty: 2,
    fileName: "food_13.jpg"
  },
  {
    code: 'food_14',
    name: '韩式烤肉',
    country: '韩国',
    difficulty: 2,
    fileName: "food_14.jpg"
  },
  {
    code: 'food_15',
    name: '汉堡包',
    country: '美国',
    difficulty: 1,
    fileName: "food_15.jpg"
  },
  {
    code: 'food_16',
    name: '热狗',
    country: '美国',
    difficulty: 1,
    fileName: "food_16.jpg"
  },
  {
    code: 'food_17',
    name: '炸鱼薯条',
    country: '英国',
    difficulty: 2,
    fileName: "food_17.jpg"
  },
  {
    code: 'food_18',
    name: '英式下午茶',
    country: '英国',
    difficulty: 2,
    fileName: "food_18.jpg"
  },
  {
    code: 'food_19',
    name: '德式香肠',
    country: '德国',
    difficulty: 2,
    fileName: "food_19.jpg"
  },
  {
    code: 'food_20',
    name: '椒盐卷饼',
    country: '德国',
    difficulty: 3,
    fileName: "food_20.jpg"
  },
  {
    code: 'food_21',
    name: '海鲜饭',
    country: '西班牙',
    difficulty: 2,
    fileName: "food_21.jpg"
  },
  {
    code: 'food_22',
    name: '火腿',
    country: '西班牙',
    difficulty: 2,
    fileName: "food_22.jpg"
  },
  {
    code: 'food_23',
    name: '玉米饼',
    country: '墨西哥',
    difficulty: 2,
    fileName: "food_23.jpg"
  },
  {
    code: 'food_24',
    name: '墨西哥卷饼',
    country: '墨西哥',
    difficulty: 2,
    fileName: "food_24.jpg"
  },
  {
    code: 'food_25',
    name: '咖喱',
    country: '印度',
    difficulty: 2,
    fileName: "food_25.jpg"
  },
  {
    code: 'food_26',
    name: '印度飞饼',
    country: '印度',
    difficulty: 2,
    fileName: "food_26.jpg"
  },
  {
    code: 'food_27',
    name: '冬阴功汤',
    country: '泰国',
    difficulty: 2,
    fileName: "food_27.jpg"
  },
  {
    code: 'food_28',
    name: '泰式炒河粉',
    country: '泰国',
    difficulty: 2,
    fileName: "food_28.jpg"
  },
  {
    code: 'food_29',
    name: '越南河粉',
    country: '越南',
    difficulty: 2,
    fileName: "food_29.jpg"
  },
  {
    code: 'food_30',
    name: '春卷',
    country: '越南',
    difficulty: 2,
    fileName: "food_30.jpg"
  },
  {
    code: 'food_31',
    name: '土耳其烤肉',
    country: '土耳其',
    difficulty: 2,
    fileName: "food_31.jpg"
  },
  {
    code: 'food_32',
    name: '土耳其软糖',
    country: '土耳其',
    difficulty: 3,
    fileName: "food_32.jpg"
  },
  {
    code: 'food_33',
    name: '希腊沙拉',
    country: '希腊',
    difficulty: 2,
    fileName: "food_33.jpg"
  },
  {
    code: 'food_34',
    name: '慕萨卡',
    country: '希腊',
    difficulty: 3,
    fileName: "food_34.jpg"
  },
  {
    code: 'food_35',
    name: '罗宋汤',
    country: '俄罗斯',
    difficulty: 2,
    fileName: "food_35.jpg"
  },
  {
    code: 'food_36',
    name: '鱼子酱',
    country: '俄罗斯',
    difficulty: 2,
    fileName: "food_36.jpg"
  },
  {
    code: 'food_37',
    name: '巴西烤肉',
    country: '巴西',
    difficulty: 2,
    fileName: "food_37.jpg"
  },
  {
    code: 'food_38',
    name: '阿萨伊果碗',
    country: '巴西',
    difficulty: 3,
    fileName: "food_38.jpg"
  },
  {
    code: 'food_39',
    name: '阿根廷牛排',
    country: '阿根廷',
    difficulty: 2,
    fileName: "food_39.jpg"
  },
  {
    code: 'food_40',
    name: '酸橘汁腌鱼',
    country: '秘鲁',
    difficulty: 3,
    fileName: "food_40.jpg"
  },
  {
    code: 'food_41',
    name: '智利海鲈鱼',
    country: '智利',
    difficulty: 3,
    fileName: "food_41.jpg"
  },
  {
    code: 'food_42',
    name: '枫糖浆',
    country: '加拿大',
    difficulty: 2,
    fileName: "food_42.jpg"
  },
  {
    code: 'food_43',
    name: '袋鼠肉',
    country: '澳大利亚',
    difficulty: 3,
    fileName: "food_43.jpg"
  },
  {
    code: 'food_44',
    name: '奇异果',
    country: '新西兰',
    difficulty: 2,
    fileName: "food_44.jpg"
  },
  {
    code: 'food_45',
    name: '法拉费',
    country: '埃及',
    difficulty: 3,
    fileName: "food_45.jpg"
  },
  {
    code: 'food_46',
    name: '塔吉锅',
    country: '摩洛哥',
    difficulty: 3,
    fileName: "food_46.jpg"
  },
  {
    code: 'food_47',
    name: '南非干肉条',
    country: '南非',
    difficulty: 3,
    fileName: "food_47.jpg"
  },
  {
    code: 'food_48',
    name: '鹰嘴豆泥',
    country: '以色列',
    difficulty: 3,
    fileName: "food_48.jpg"
  },
  {
    code: 'food_49',
    name: '黎巴嫩烤肉',
    country: '黎巴嫩',
    difficulty: 3,
    fileName: "food_49.jpg"
  },
  {
    code: 'food_50',
    name: '波斯米饭',
    country: '伊朗',
    difficulty: 3,
    fileName: "food_50.jpg"
  },
  {
    code: 'food_51',
    name: '阿富汗抓饭',
    country: '阿富汗',
    difficulty: 3,
    fileName: "food_51.jpg"
  },
  {
    code: 'food_52',
    name: '巴基斯坦烤饼',
    country: '巴基斯坦',
    difficulty: 3,
    fileName: "food_52.jpg"
  },
  {
    code: 'food_53',
    name: '孟加拉鱼咖喱',
    country: '孟加拉国',
    difficulty: 3,
    fileName: "food_53.jpg"
  },
  {
    code: 'food_54',
    name: '斯里兰卡咖喱',
    country: '斯里兰卡',
    difficulty: 3,
    fileName: "food_54.jpg"
  },
  {
    code: 'food_55',
    name: '缅甸茶叶沙拉',
    country: '缅甸',
    difficulty: 3,
    fileName: "food_55.jpg"
  },
  {
    code: 'food_56',
    name: '椰浆饭',
    country: '马来西亚',
    difficulty: 2,
    fileName: "food_56.jpg"
  },
  {
    code: 'food_57',
    name: '海南鸡饭',
    country: '新加坡',
    difficulty: 2,
    fileName: "food_57.jpg"
  },
  {
    code: 'food_58',
    name: '印尼炒饭',
    country: '印度尼西亚',
    difficulty: 2,
    fileName: "food_58.jpg"
  },
  {
    code: 'food_59',
    name: '菲律宾烤乳猪',
    country: '菲律宾',
    difficulty: 3,
    fileName: "food_59.jpg"
  },
  {
    code: 'food_60',
    name: '柬埔寨鱼汤面',
    country: '柬埔寨',
    difficulty: 3,
    fileName: "food_60.jpg"
  },
  {
    code: 'food_61',
    name: '老挝青木瓜沙拉',
    country: '老挝',
    difficulty: 3,
    fileName: "food_61.jpg"
  },
  {
    code: 'food_62',
    name: '蒙古烤羊肉',
    country: '蒙古',
    difficulty: 3,
    fileName: "food_62.jpg"
  },
  {
    code: 'food_63',
    name: '尼泊尔饺子',
    country: '尼泊尔',
    difficulty: 3,
    fileName: "food_63.jpg"
  },
  {
    code: 'food_64',
    name: '不丹辣椒奶酪',
    country: '不丹',
    difficulty: 3,
    fileName: "food_64.jpg"
  },
  {
    code: 'food_65',
    name: '马尔代夫鱼咖喱',
    country: '马尔代夫',
    difficulty: 3,
    fileName: "food_65.jpg"
  },
  {
    code: 'food_66',
    name: '挪威三文鱼',
    country: '挪威',
    difficulty: 2,
    fileName: "food_66.jpg"
  },
  {
    code: 'food_67',
    name: '瑞典肉丸',
    country: '瑞典',
    difficulty: 2,
    fileName: "food_67.jpg"
  },
  {
    code: 'food_68',
    name: '丹麦酥',
    country: '丹麦',
    difficulty: 2,
    fileName: "food_68.jpg"
  },
  {
    code: 'food_69',
    name: '芬兰驯鹿肉',
    country: '芬兰',
    difficulty: 3,
    fileName: "food_69.jpg"
  },
  {
    code: 'food_70',
    name: '冰岛发酵鲨鱼肉',
    country: '冰岛',
    difficulty: 3,
    fileName: "food_70.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const worldFoods = worldFoodsFiles.map(food => ({
  ...food,
  imageUrl: imageConfig.getImageUrl('world_foods', food.fileName)
}));