const landmarkBuildingFiles = [
  {
    code: 'city_1',
    country: '中国',
    buildingName: '天安门',
    buildingType: 'city',
    fileName: "city_1.jpg"
  },
  {
    code: 'rural_1',
    country: '中国',
    buildingName: '中国农村建筑',
    buildingType: 'rural',
    fileName: "rural_1.jpg"
  },
  {
    code: 'city_2',
    country: '日本',
    buildingName: '东京塔',
    buildingType: 'city',
    fileName: "city_2.jpg"
  },
  {
    code: 'rural_2',
    country: '日本',
    buildingName: '日式茅草屋',
    buildingType: 'rural',
    fileName: "rural_2.jpg"
  },
  {
    code: 'city_3',
    country: '韩国',
    buildingName: '首尔塔',
    buildingType: 'city',
    fileName: "city_3.jpg"
  },
  {
    code: 'rural_3',
    country: '韩国',
    buildingName: '韩式传统民居',
    buildingType: 'rural',
    fileName: "rural_3.jpg"
  },
  {
    code: 'city_4',
    country: '印度',
    buildingName: '泰姬陵',
    buildingType: 'city',
    fileName: "city_4.jpg"
  },
  {
    code: 'rural_4',
    country: '印度',
    buildingName: '印度村庄房屋',
    buildingType: 'rural',
    fileName: "rural_4.jpg"
  },
  {
    code: 'city_5',
    country: '泰国',
    buildingName: '大皇宫',
    buildingType: 'city',
    fileName: "city_5.jpg"
  },
  {
    code: 'rural_5',
    country: '泰国',
    buildingName: '泰式高脚屋',
    buildingType: 'rural',
    fileName: "rural_5.jpg"
  },
  {
    code: 'city_6',
    country: '新加坡',
    buildingName: '鱼尾狮',
    buildingType: 'city',
    fileName: "city_6.jpg"
  },
  {
    code: 'rural_6',
    country: '新加坡',
    buildingName: '新加坡传统店屋',
    buildingType: 'rural',
    fileName: "rural_6.jpg"
  },
  {
    code: 'city_7',
    country: '马来西亚',
    buildingName: '双子塔',
    buildingType: 'city',
    fileName: "city_7.jpg"
  },
  {
    code: 'rural_7',
    country: '马来西亚',
    buildingName: '马来高脚屋',
    buildingType: 'rural',
    fileName: "rural_7.jpg"
  },
  {
    code: 'city_8',
    country: '印尼',
    buildingName: '独立纪念塔',
    buildingType: 'city',
    fileName: "city_8.jpg"
  },
  {
    code: 'rural_8',
    country: '印尼',
    buildingName: '印尼传统房屋',
    buildingType: 'rural',
    fileName: "rural_8.jpg"
  },
  {
    code: 'city_9',
    country: '菲律宾',
    buildingName: '马尼拉大教堂',
    buildingType: 'city',
    fileName: "city_9.jpg"
  },
  {
    code: 'rural_9',
    country: '菲律宾',
    buildingName: '菲律宾高脚屋',
    buildingType: 'rural',
    fileName: "rural_9.jpg"
  },
  {
    code: 'city_10',
    country: '越南',
    buildingName: '胡志明陵墓',
    buildingType: 'city',
    fileName: "city_10.jpg"
  },
  {
    code: 'rural_10',
    country: '越南',
    buildingName: '越南传统民居',
    buildingType: 'rural',
    fileName: "rural_10.jpg"
  },
  {
    code: 'city_11',
    country: '缅甸',
    buildingName: '仰光大金塔',
    buildingType: 'city',
    fileName: "city_11.jpg"
  },
  {
    code: 'rural_11',
    country: '缅甸',
    buildingName: '缅甸传统房屋',
    buildingType: 'rural',
    fileName: "rural_11.jpg"
  },
  {
    code: 'city_12',
    country: '柬埔寨',
    buildingName: '吴哥窟',
    buildingType: 'city',
    fileName: "city_12.jpg"
  },
  {
    code: 'rural_12',
    country: '柬埔寨',
    buildingName: '柬埔寨高脚屋',
    buildingType: 'rural',
    fileName: "rural_12.jpg"
  },
  {
    code: 'city_13',
    country: '老挝',
    buildingName: '塔銮',
    buildingType: 'city',
    fileName: "city_13.jpg"
  },
  {
    code: 'rural_13',
    country: '老挝',
    buildingName: '老挝传统民居',
    buildingType: 'rural',
    fileName: "rural_13.jpg"
  },
  {
    code: 'city_14',
    country: '蒙古',
    buildingName: '成吉思汗雕像',
    buildingType: 'city',
    fileName: "city_14.jpg"
  },
  {
    code: 'rural_14',
    country: '蒙古',
    buildingName: '蒙古包',
    buildingType: 'rural',
    fileName: "rural_14.jpg"
  },
  {
    code: 'city_15',
    country: '哈萨克斯坦',
    buildingName: '巴伊杰列克塔',
    buildingType: 'city',
    fileName: "city_15.jpg"
  },
  {
    code: 'rural_15',
    country: '哈萨克斯坦',
    buildingName: '哈萨克传统房屋',
    buildingType: 'rural',
    fileName: "rural_15.jpg"
  },
  {
    code: 'city_16',
    country: '法国',
    buildingName: '埃菲尔铁塔',
    buildingType: 'city',
    fileName: "city_16.jpg"
  },
  {
    code: 'rural_16',
    country: '法国',
    buildingName: '法式乡村别墅',
    buildingType: 'rural',
    fileName: "rural_16.jpg"
  },
  {
    code: 'city_17',
    country: '英国',
    buildingName: '大本钟',
    buildingType: 'city',
    fileName: "city_17.jpg"
  },
  {
    code: 'rural_17',
    country: '英国',
    buildingName: '英式乡村小屋',
    buildingType: 'rural',
    fileName: "rural_17.jpg"
  },
  {
    code: 'city_18',
    country: '德国',
    buildingName: '新天鹅堡',
    buildingType: 'city',
    fileName: "city_18.jpg"
  },
  {
    code: 'rural_18',
    country: '德国',
    buildingName: '德式农舍',
    buildingType: 'rural',
    fileName: "rural_18.jpg"
  },
  {
    code: 'city_19',
    country: '意大利',
    buildingName: '比萨斜塔',
    buildingType: 'city',
    fileName: "city_19.jpg"
  },
  {
    code: 'rural_19',
    country: '意大利',
    buildingName: '托斯卡纳农庄',
    buildingType: 'rural',
    fileName: "rural_19.jpg"
  },
  {
    code: 'city_20',
    country: '俄罗斯',
    buildingName: '红场',
    buildingType: 'city',
    fileName: "city_20.jpg"
  },
  {
    code: 'rural_20',
    country: '俄罗斯',
    buildingName: '俄式木屋',
    buildingType: 'rural',
    fileName: "rural_20.jpg"
  },
  {
    code: 'city_21',
    country: '西班牙',
    buildingName: '圣家堂',
    buildingType: 'city',
    fileName: "city_21.jpg"
  },
  {
    code: 'rural_21',
    country: '西班牙',
    buildingName: '西班牙乡村房屋',
    buildingType: 'rural',
    fileName: "rural_21.jpg"
  },
  {
    code: 'city_22',
    country: '荷兰',
    buildingName: '阿姆斯特丹王宫',
    buildingType: 'city',
    fileName: "city_22.jpg"
  },
  {
    code: 'rural_22',
    country: '荷兰',
    buildingName: '荷兰风车房',
    buildingType: 'rural',
    fileName: "rural_22.jpg"
  },
  {
    code: 'city_23',
    country: '瑞士',
    buildingName: '马特洪峰',
    buildingType: 'city',
    fileName: "city_23.jpg"
  },
  {
    code: 'rural_23',
    country: '瑞士',
    buildingName: '瑞士木屋',
    buildingType: 'rural',
    fileName: "rural_23.jpg"
  },
  {
    code: 'rural_24',
    country: '奥地利',
    buildingName: '奥地利农舍',
    buildingType: 'rural',
    fileName: "rural_24.jpg"
  },
  {
    code: 'city_25',
    country: '比利时',
    buildingName: '原子球塔',
    buildingType: 'city',
    fileName: "city_25.jpg"
  },
  {
    code: 'rural_25',
    country: '比利时',
    buildingName: '比利时乡村房屋',
    buildingType: 'rural',
    fileName: "rural_25.jpg"
  },
  {
    code: 'rural_26',
    country: '瑞典',
    buildingName: '瑞典红房子',
    buildingType: 'rural',
    fileName: "rural_26.jpg"
  },
  {
    code: 'city_27',
    country: '挪威',
    buildingName: '奥斯陆歌剧院',
    buildingType: 'city',
    fileName: "city_27.jpg"
  },
  {
    code: 'rural_27',
    country: '挪威',
    buildingName: '挪威木屋',
    buildingType: 'rural',
    fileName: "rural_27.jpg"
  },
  {
    code: 'city_28',
    country: '丹麦',
    buildingName: '小美人鱼雕像',
    buildingType: 'city',
    fileName: "city_28.jpg"
  },
  {
    code: 'rural_28',
    country: '丹麦',
    buildingName: '丹麦农舍',
    buildingType: 'rural',
    fileName: "rural_28.jpg"
  },
  {
    code: 'city_29',
    country: '芬兰',
    buildingName: '赫尔辛基大教堂',
    buildingType: 'city',
    fileName: "city_29.jpg"
  },
  {
    code: 'rural_29',
    country: '芬兰',
    buildingName: '芬兰木屋',
    buildingType: 'rural',
    fileName: "rural_29.jpg"
  },
  {
    code: 'city_30',
    country: '波兰',
    buildingName: '华沙文化宫',
    buildingType: 'city',
    fileName: "city_30.jpg"
  },
  {
    code: 'rural_30',
    country: '波兰',
    buildingName: '波兰乡村房屋',
    buildingType: 'rural',
    fileName: "rural_30.jpg"
  },
  {
    code: 'city_31',
    country: '捷克',
    buildingName: '布拉格城堡',
    buildingType: 'city',
    fileName: "city_31.jpg"
  },
  {
    code: 'rural_31',
    country: '捷克',
    buildingName: '捷克乡村房屋',
    buildingType: 'rural',
    fileName: "rural_31.jpg"
  },
  {
    code: 'city_32',
    country: '匈牙利',
    buildingName: '布达佩斯国会大厦',
    buildingType: 'city',
    fileName: "city_32.jpg"
  },
  {
    code: 'rural_32',
    country: '匈牙利',
    buildingName: '匈牙利农舍',
    buildingType: 'rural',
    fileName: "rural_32.jpg"
  },
  {
    code: 'city_33',
    country: '希腊',
    buildingName: '帕特农神庙',
    buildingType: 'city',
    fileName: "city_33.jpg"
  },
  {
    code: 'rural_33',
    country: '希腊',
    buildingName: '希腊岛屿房屋',
    buildingType: 'rural',
    fileName: "rural_33.jpg"
  },
  {
    code: 'city_34',
    country: '美国',
    buildingName: '自由女神像',
    buildingType: 'city',
    fileName: "city_34.jpg"
  },
  {
    code: 'rural_34',
    country: '美国',
    buildingName: '美式农场房屋',
    buildingType: 'rural',
    fileName: "rural_34.jpg"
  },
  {
    code: 'city_35',
    country: '加拿大',
    buildingName: 'CN塔',
    buildingType: 'city',
    fileName: "city_35.jpg"
  },
  {
    code: 'rural_35',
    country: '加拿大',
    buildingName: '加拿大木屋',
    buildingType: 'rural',
    fileName: "rural_35.jpg"
  },
  {
    code: 'city_36',
    country: '巴西',
    buildingName: '基督像',
    buildingType: 'city',
    fileName: "city_36.jpg"
  },
  {
    code: 'rural_36',
    country: '巴西',
    buildingName: '巴西乡村房屋',
    buildingType: 'rural',
    fileName: "rural_36.jpg"
  },
  {
    code: 'city_37',
    country: '阿根廷',
    buildingName: '方尖碑',
    buildingType: 'city',
    fileName: "city_37.jpg"
  },
  {
    code: 'rural_37',
    country: '阿根廷',
    buildingName: '潘帕斯草原房屋',
    buildingType: 'rural',
    fileName: "rural_37.jpg"
  },
  {
    code: 'city_38',
    country: '墨西哥',
    buildingName: '奇琴伊察',
    buildingType: 'city',
    fileName: "city_38.jpg"
  },
  {
    code: 'rural_38',
    country: '墨西哥',
    buildingName: '墨西哥乡村房屋',
    buildingType: 'rural',
    fileName: "rural_38.jpg"
  },
  {
    code: 'city_39',
    country: '智利',
    buildingName: '圣地亚哥大教堂',
    buildingType: 'city',
    fileName: "city_39.jpg"
  },
  {
    code: 'rural_39',
    country: '智利',
    buildingName: '智利乡村房屋',
    buildingType: 'rural',
    fileName: "rural_39.jpg"
  },
  {
    code: 'city_40',
    country: '秘鲁',
    buildingName: '马丘比丘',
    buildingType: 'city',
    fileName: "city_40.jpg"
  },
  {
    code: 'rural_40',
    country: '秘鲁',
    buildingName: '秘鲁传统房屋',
    buildingType: 'rural',
    fileName: "rural_40.jpg"
  },
  {
    code: 'city_41',
    country: '哥伦比亚',
    buildingName: '波哥大大教堂',
    buildingType: 'city',
    fileName: "city_41.jpg"
  },
  {
    code: 'rural_41',
    country: '哥伦比亚',
    buildingName: '哥伦比亚乡村房屋',
    buildingType: 'rural',
    fileName: "rural_41.jpg"
  },
  {
    code: 'city_42',
    country: '埃及',
    buildingName: '金字塔',
    buildingType: 'city',
    fileName: "city_42.jpg"
  },
  {
    code: 'rural_42',
    country: '埃及',
    buildingName: '尼罗河村庄',
    buildingType: 'rural',
    fileName: "rural_42.jpg"
  },
  {
    code: 'city_43',
    country: '南非',
    buildingName: '桌山',
    buildingType: 'city',
    fileName: "city_43.jpg"
  },
  {
    code: 'rural_43',
    country: '南非',
    buildingName: '南非传统圆屋',
    buildingType: 'rural',
    fileName: "rural_43.jpg"
  },
  {
    code: 'city_44',
    country: '肯尼亚',
    buildingName: '内罗毕国家公园',
    buildingType: 'city',
    fileName: "city_44.jpg"
  },
  {
    code: 'rural_44',
    country: '肯尼亚',
    buildingName: '肯尼亚传统房屋',
    buildingType: 'rural',
    fileName: "rural_44.jpg"
  },
  {
    code: 'city_45',
    country: '摩洛哥',
    buildingName: '哈桑二世清真寺',
    buildingType: 'city',
    fileName: "city_45.jpg"
  },
  {
    code: 'rural_45',
    country: '摩洛哥',
    buildingName: '摩洛哥传统房屋',
    buildingType: 'rural',
    fileName: "rural_45.jpg"
  },
  {
    code: 'city_46',
    country: '尼日利亚',
    buildingName: '拉各斯国家剧院',
    buildingType: 'city',
    fileName: "city_46.jpg"
  },
  {
    code: 'rural_46',
    country: '尼日利亚',
    buildingName: '尼日利亚传统房屋',
    buildingType: 'rural',
    fileName: "rural_46.jpg"
  },
  {
    code: 'city_47',
    country: '澳大利亚',
    buildingName: '悉尼歌剧院',
    buildingType: 'city',
    fileName: "city_47.jpg"
  },
  {
    code: 'rural_47',
    country: '澳大利亚',
    buildingName: '澳洲农场房屋',
    buildingType: 'rural',
    fileName: "rural_47.jpg"
  },
  {
    code: 'city_48',
    country: '新西兰',
    buildingName: '天空塔',
    buildingType: 'city',
    fileName: "city_48.jpg"
  },
  {
    code: 'rural_48',
    country: '新西兰',
    buildingName: '新西兰牧场小屋',
    buildingType: 'rural',
    fileName: "rural_48.jpg"
  },
  {
    code: 'city_49',
    country: '阿联酋',
    buildingName: '哈利法塔',
    buildingType: 'city',
    fileName: "city_49.jpg"
  },
  {
    code: 'rural_49',
    country: '阿联酋',
    buildingName: '阿拉伯传统房屋',
    buildingType: 'rural',
    fileName: "rural_49.jpg"
  },
  {
    code: 'city_50',
    country: '土耳其',
    buildingName: '圣索菲亚大教堂',
    buildingType: 'city',
    fileName: "city_50.jpg"
  },
  {
    code: 'rural_50',
    country: '土耳其',
    buildingName: '土耳其乡村房屋',
    buildingType: 'rural',
    fileName: "rural_50.jpg"
  },
  {
    code: 'city_51',
    country: '沙特阿拉伯',
    buildingName: '麦加大清真寺',
    buildingType: 'city',
    fileName: "city_51.jpg"
  },
  {
    code: 'rural_51',
    country: '沙特阿拉伯',
    buildingName: '沙特传统房屋',
    buildingType: 'rural',
    fileName: "rural_51.jpg"
  },
  {
    code: 'city_52',
    country: '伊朗',
    buildingName: '伊斯法罕清真寺',
    buildingType: 'city',
    fileName: "city_52.jpg"
  },
  {
    code: 'rural_52',
    country: '伊朗',
    buildingName: '伊朗传统房屋',
    buildingType: 'rural',
    fileName: "rural_52.jpg"
  },
  {
    code: 'city_53',
    country: '以色列',
    buildingName: '哭墙',
    buildingType: 'city',
    fileName: "city_53.jpg"
  },
  {
    code: 'rural_53',
    country: '以色列',
    buildingName: '以色列传统房屋',
    buildingType: 'rural',
    fileName: "rural_53.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const landmarkBuildings = landmarkBuildingFiles.map(building => ({
  ...building,
  imageUrl: imageConfig.getImageUrl('landmark_buildings', building.fileName)
}));