const dangerousSportFiles = [
  {
    code: 'sport_1',
    name: '自由潜水',
    fileName: "sport_1.jpg"
  },
  {
    code: 'sport_2',
    name: '翼装飞行',
    fileName: "sport_2.jpg"
  },
  {
    code: 'sport_4',
    name: '跳伞',
    fileName: "sport_4.jpg"
  },
  {
    code: 'sport_5',
    name: '冲浪',
    fileName: "sport_5.jpg"
  },
  {
    code: 'sport_7',
    name: '滑板',
    fileName: "sport_7.jpg"
  },
  {
    code: 'sport_9',
    name: '蹦极',
    fileName: "sport_9.jpg"
  },
  {
    code: 'sport_10',
    name: '拳击',
    fileName: "sport_10.jpg"
  },
  {
    code: 'sport_11',
    name: '综合格斗',
    fileName: "sport_11.jpg"
  },
  {
    code: 'sport_12',
    name: '橄榄球',
    fileName: "sport_12.jpg"
  },
  {
    code: 'sport_13',
    name: '冰球',
    fileName: "sport_13.jpg"
  },
  {
    code: 'sport_14',
    name: '赛车',
    fileName: "sport_14.jpg"
  },
  {
    code: 'sport_15',
    name: '极限跳伞',
    fileName: "sport_15.jpg"
  },
  {
    code: 'sport_16',
    name: '洞穴探险',
    fileName: "sport_16.jpg"
  },
  {
    code: 'sport_17',
    name: '高山滑雪',
    fileName: "sport_17.jpg"
  },
  {
    code: 'sport_18',
    name: '冲浪大浪',
    fileName: "sport_18.jpg"
  },
  {
    code: 'sport_19',
    name: '攀冰',
    fileName: "sport_19.jpg"
  },
  {
    code: 'sport_20',
    name: '滑翔伞',
    fileName: "sport_20.jpg"
  },
  {
    code: 'sport_21',
    name: '极限摩托',
    fileName: "sport_21.jpg"
  },
  {
    code: 'sport_22',
    name: '街头滑板',
    fileName: "sport_22.jpg"
  },
  {
    code: 'sport_24',
    name: '高空走钢丝',
    fileName: "sport_24.jpg"
  },
  {
    code: 'sport_25',
    name: '极限攀岩',
    fileName: "sport_25.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const dangerousSports = dangerousSportFiles.map(sport => ({
  ...sport,
  imageUrl: imageConfig.getImageUrl('dangerous_sports', sport.fileName)
}));