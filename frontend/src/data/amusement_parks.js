const amusementParkFiles = [
  {
    code: 'park_1',
    name: '迪士尼乐园 奥兰多',
    fileName: "park_1.jpg"
  },
  {
    code: 'park_2',
    name: '环球影城 奥兰多',
    fileName: "park_2.jpg"
  },
  {
    code: 'park_3',
    name: '迪士尼乐园 巴黎',
    fileName: "park_3.jpg"
  },
  {
    code: 'park_4',
    name: '环球影城 好莱坞',
    fileName: "park_4.jpg"
  },
  {
    code: 'park_5',
    name: '迪士尼乐园 东京',
    fileName: "park_5.jpg"
  },
  {
    code: 'park_6',
    name: '迪士尼乐园 香港',
    fileName: "park_6.jpg"
  },
  {
    code: 'park_7',
    name: '迪士尼乐园 上海',
    fileName: "park_7.jpg"
  },
  {
    code: 'park_8',
    name: '环球影城 大阪',
    fileName: "park_8.jpg"
  },
  {
    code: 'park_9',
    name: '环球影城 新加坡',
    fileName: "park_9.jpg"
  },
  {
    code: 'park_10',
    name: '迪士尼乐园 洛杉矶',
    fileName: "park_10.jpg"
  },
  {
    code: 'park_11',
    name: '乐高乐园 温莎',
    fileName: "park_11.jpg"
  },
  {
    code: 'park_12',
    name: '乐高乐园 加利福尼亚',
    fileName: "park_12.jpg"
  },
  {
    code: 'park_13',
    name: '乐高乐园 佛罗里达',
    fileName: "park_13.jpg"
  },
  {
    code: 'park_14',
    name: '乐高乐园 德国',
    fileName: "park_14.jpg"
  },
  {
    code: 'park_15',
    name: '乐高乐园 丹麦',
    fileName: "park_15.jpg"
  },
  {
    code: 'park_16',
    name: '六旗魔术山',
    fileName: "park_16.jpg"
  },
  {
    code: 'park_17',
    name: '六旗大冒险',
    fileName: "park_17.jpg"
  },
  {
    code: 'park_18',
    name: '布希花园 坦帕',
    fileName: "park_18.jpg"
  },
  {
    code: 'park_19',
    name: '布希花园 威廉斯堡',
    fileName: "park_19.jpg"
  },
  {
    code: 'park_20',
    name: '海洋世界 奥兰多',
    fileName: "park_20.jpg"
  }
];

import imageConfig from '../config/imageConfig.js';

export const amusementParks = amusementParkFiles.map(park => ({
  ...park,
  imageUrl: imageConfig.getImageUrl('amusement_parks', park.fileName)
}));