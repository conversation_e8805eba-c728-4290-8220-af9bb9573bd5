const worldLandmarkFiles = [
  {
    code: 'landmark_1',
    name: '长城',
    country: '中国',
    fileName: "landmark_1.webp"
  },
  {
    code: 'landmark_2',
    name: '泰姬陵',
    country: '印度',
    fileName: "landmark_2.png"
  },
  {
    code: 'landmark_3',
    name: '埃菲尔铁塔',
    country: '法国',
    fileName: "landmark_3.png"
  },
  {
    code: 'landmark_4',
    name: '自由女神像',
    country: '美国',
    fileName: "landmark_4.jpeg"
  },
  {
    code: 'landmark_5',
    name: '比萨斜塔',
    country: '意大利',
    fileName: "landmark_5.jpg"
  },
  {
    code: 'landmark_6',
    name: '悉尼歌剧院',
    country: '澳大利亚',
    fileName: "landmark_6.jpeg"
  },
  {
    code: 'landmark_7',
    name: '马丘比丘',
    country: '秘鲁',
    fileName: "landmark_7.png"
  },
  {
    code: 'landmark_8',
    name: '基督像',
    country: '巴西',
    fileName: "landmark_8.jpg"
  },
  {
    code: 'landmark_9',
    name: '斗兽场',
    country: '意大利',
    fileName: "landmark_9.png"
  },
  {
    code: 'landmark_10',
    name: '巨石阵',
    country: '英国',
    fileName: "landmark_10.png"
  },
  {
    code: 'landmark_11',
    name: '金字塔',
    country: '埃及',
    fileName: "landmark_11.jpeg"
  },
  {
    code: 'landmark_12',
    name: '圣家堂',
    country: '西班牙',
    fileName: "landmark_12.jpg"
  },
  {
    code: 'landmark_13',
    name: '新天鹅堡',
    country: '德国',
    fileName: "landmark_13.png"
  },
  {
    code: 'landmark_14',
    name: '佩特拉古城',
    country: '约旦',
    fileName: "landmark_14.png"
  },
  {
    code: 'landmark_15',
    name: '安哥拉瀑布',
    country: '委内瑞拉',
    fileName: "landmark_15.jpg"
  },
  {
    code: 'landmark_16',
    name: '富士山',
    country: '日本',
    fileName: "landmark_16.jpg"
  },
  {
    code: 'landmark_17',
    name: '尼亚加拉瀑布',
    country: '美国/加拿大',
    fileName: "landmark_17.jpeg"
  },
  {
    code: 'landmark_18',
    name: '巴黎圣母院',
    country: '法国',
    fileName: "landmark_18.jpg"
  },
  {
    code: 'landmark_19',
    name: '大本钟',
    country: '英国',
    fileName: "landmark_19.webp"
  },
  {
    code: 'landmark_20',
    name: '卢浮宫',
    country: '法国',
    fileName: "landmark_20.png"
  },
  {
    code: 'landmark_21',
    name: '凯旋门',
    country: '法国',
    fileName: "landmark_21.webp"
  },
  {
    code: 'landmark_22',
    name: '圣彼得大教堂',
    country: '梵蒂冈',
    fileName: "landmark_22.jpeg"
  },
  {
    code: 'landmark_23',
    name: '克里姆林宫',
    country: '俄国',
    fileName: "landmark_23.png"
  },
  {
    code: 'landmark_24',
    name: '红场',
    country: '俄国',
    fileName: "landmark_24.jpg"
  },
  {
    code: 'landmark_25',
    name: '帝国大厦',
    country: '美国',
    fileName: "landmark_25.jpg"
  },
  {
    code: 'landmark_26',
    name: '金门大桥',
    country: '美国',
    fileName: "landmark_26.png"
  },
  {
    code: 'landmark_27',
    name: '天坛',
    country: '中国',
    fileName: "landmark_27.jpg"
  },
  {
    code: 'landmark_28',
    name: '紫禁城',
    country: '中国',
    fileName: "landmark_28.jpeg"
  },
  {
    code: 'landmark_29',
    name: '兵马俑',
    country: '中国',
    fileName: "landmark_29.jpg"
  },
  {
    code: 'landmark_30',
    name: '乐山大佛',
    country: '中国',
    fileName: "landmark_30.jpg"
  },
  {
    code: 'landmark_31',
    name: '都江堰',
    country: '中国',
    fileName: "landmark_31.png"
  },
  {
    code: 'landmark_32',
    name: '吴哥窟',
    country: '柬埔寨',
    fileName: "landmark_32.jpg"
  },
  {
    code: 'landmark_33',
    name: '婆罗浮屠',
    country: '印度尼西亚',
    fileName: "landmark_33.png"
  },
  {
    code: 'landmark_34',
    name: '复活节岛石像',
    country: '智利',
    fileName: "landmark_34.jpg"
  },
  {
    code: 'landmark_35',
    name: '白宫',
    country: '美国',
    fileName: "landmark_35.jpeg"
  },
  {
    code: 'landmark_36',
    name: '国会大厦',
    country: '美国',
    fileName: "landmark_36.jpg"
  },
  {
    code: 'landmark_37',
    name: '华盛顿纪念碑',
    country: '美国',
    fileName: "landmark_37.jpg"
  },
  {
    code: 'landmark_38',
    name: '林肯纪念堂',
    country: '美国',
    fileName: "landmark_38.jpg"
  },
  {
    code: 'landmark_39',
    name: '鲁什莫尔山',
    country: '美国',
    fileName: "landmark_39.png"
  },
  {
    code: 'landmark_40',
    name: '大峡谷',
    country: '美国',
    fileName: "landmark_40.png"
  },
  {
    code: 'landmark_41',
    name: '黄石公园',
    country: '美国',
    fileName: "landmark_41.png"
  },
  {
    code: 'landmark_42',
    name: '优胜美地',
    country: '美国',
    fileName: "landmark_42.png"
  },
  {
    code: 'landmark_43',
    name: '阿尔罕布拉宫',
    country: '西班牙',
    fileName: "landmark_43.png"
  },
  {
    code: 'landmark_44',
    name: '圣地亚哥大教堂',
    country: '西班牙',
    fileName: "landmark_44.png"
  },
  {
    code: 'landmark_45',
    name: '科隆大教堂',
    country: '德国',
    fileName: "landmark_45.jpeg"
  },
  {
    code: 'landmark_46',
    name: '勃兰登堡门',
    country: '德国',
    fileName: "landmark_46.jpg"
  },
  {
    code: 'landmark_47',
    name: '维也纳歌剧院',
    country: '奥地利',
    fileName: "landmark_47.jpg"
  },
  {
    code: 'landmark_48',
    name: '美泉宫',
    country: '奥地利',
    fileName: "landmark_48.jpg"
  },
  {
    code: 'landmark_49',
    name: '萨尔茨堡老城',
    country: '奥地利',
    fileName: "landmark_49.jpg"
  },
  {
    code: 'landmark_50',
    name: '哈尔施塔特',
    country: '奥地利',
    fileName: "landmark_50.jpg"
  },
  {
    code: 'landmark_51',
    name: '布拉格城堡',
    country: '捷克',
    fileName: "landmark_51.jpg"
  },
  {
    code: 'landmark_52',
    name: '查理大桥',
    country: '捷克',
    fileName: "landmark_52.png"
  },
  {
    code: 'landmark_53',
    name: '温莎城堡',
    country: '英国',
    fileName: "landmark_53.png"
  },
  {
    code: 'landmark_54',
    name: '爱丁堡城堡',
    country: '英国',
    fileName: "landmark_54.png"
  },
  {
    code: 'landmark_55',
    name: '伦敦塔桥',
    country: '英国',
    fileName: "landmark_55.jpg"
  },
  {
    code: 'landmark_56',
    name: '威斯敏斯特宫',
    country: '英国',
    fileName: "landmark_56.jpg"
  },
  {
    code: 'landmark_57',
    name: '阿克罗波利斯',
    country: '希腊',
    fileName: "landmark_57.jpg"
  },
  {
    code: 'landmark_58',
    name: '帕特农神庙',
    country: '希腊',
    fileName: "landmark_58.jpg"
  },
  {
    code: 'landmark_59',
    name: '圣托里尼',
    country: '希腊',
    fileName: "landmark_59.jpg"
  },
  {
    code: 'landmark_60',
    name: '米克诺斯岛',
    country: '希腊',
    fileName: "landmark_60.png"
  },
  {
    code: 'landmark_61',
    name: '圣索菲亚大教堂',
    country: '土耳其',
    fileName: "landmark_61.jpg"
  },
  {
    code: 'landmark_62',
    name: '蓝色清真寺',
    country: '土耳其',
    fileName: "landmark_62.jpg"
  },
  {
    code: 'landmark_63',
    name: '卡帕多奇亚',
    country: '土耳其',
    fileName: "landmark_63.png"
  },
  {
    code: 'landmark_64',
    name: '圣瓦西里大教堂',
    country: '俄国',
    fileName: "landmark_64.png"
  },
  {
    code: 'landmark_65',
    name: '冬宫',
    country: '俄国',
    fileName: "landmark_65.jpg"
  },
  {
    code: 'landmark_66',
    name: '贝加尔湖',
    country: '俄国',
    fileName: "landmark_66.jpeg"
  },
  {
    code: 'landmark_67',
    name: '阿尔卑斯山',
    country: '瑞士',
    fileName: "landmark_67.jpg"
  },
  {
    code: 'landmark_68',
    name: '马特洪峰',
    country: '瑞士',
    fileName: "landmark_68.jpg"
  },
  {
    code: 'landmark_69',
    name: '少女峰',
    country: '瑞士',
    fileName: "landmark_69.png"
  },
  {
    code: 'landmark_70',
    name: '滑雪场',
    country: '瑞士',
    fileName: "landmark_70.jpg"
  },
  {
    code: 'landmark_71',
    name: '峡湾',
    country: '挪威',
    fileName: "landmark_71.png"
  },
  {
    code: 'landmark_72',
    name: '布道石',
    country: '挪威',
    fileName: "landmark_72.jpg"
  },
  {
    code: 'landmark_73',
    name: '极光',
    country: '挪威',
    fileName: "landmark_73.png"
  },
  {
    code: 'landmark_74',
    name: '蓝湖温泉',
    country: '冰岛',
    fileName: "landmark_74.png"
  },
  {
    code: 'landmark_75',
    name: '瓦特纳冰川',
    country: '冰岛',
    fileName: "landmark_75.png"
  },
  {
    code: 'landmark_76',
    name: '基里巴斯环礁',
    country: '基里巴斯',
    fileName: "landmark_76.jpg"
  },
  {
    code: 'landmark_77',
    name: '乌鲁鲁',
    country: '澳大利亚',
    fileName: "landmark_77.png"
  },
  {
    code: 'landmark_78',
    name: '十二门徒石',
    country: '澳大利亚',
    fileName: "landmark_78.png"
  },
  {
    code: 'landmark_79',
    name: '大堡礁',
    country: '澳大利亚',
    fileName: "landmark_79.jpg"
  },
  {
    code: 'landmark_80',
    name: '米尔福德峡湾',
    country: '新西兰',
    fileName: "landmark_80.png"
  },
  {
    code: 'landmark_81',
    name: '霍比屯',
    country: '新西兰',
    fileName: "landmark_81.jpg"
  },
  {
    code: 'landmark_82',
    name: '库克山',
    country: '新西兰',
    fileName: "landmark_82.png"
  },
  {
    code: 'landmark_83',
    name: '维多利亚瀑布',
    country: '赞比亚/津巴布韦',
    fileName: "landmark_83.png"
  },
  {
    code: 'landmark_84',
    name: '乞力马扎罗山',
    country: '坦桑尼亚',
    fileName: "landmark_84.png"
  },
  {
    code: 'landmark_85',
    name: '塞伦盖蒂',
    country: '坦桑尼亚',
    fileName: "landmark_85.png"
  },
  {
    code: 'landmark_86',
    name: '撒哈拉沙漠',
    country: '非洲',
    fileName: "landmark_86.png"
  },
  {
    code: 'landmark_87',
    name: '桌山',
    country: '南非',
    fileName: "landmark_87.png"
  },
  {
    code: 'landmark_88',
    name: '好望角',
    country: '南非',
    fileName: "landmark_88.jpg"
  },
  {
    code: 'landmark_89',
    name: '埃尔多拉多',
    country: '哥伦比亚',
    fileName: "landmark_89.jpg"
  },
  {
    code: 'landmark_90',
    name: '伊瓜苏瀑布',
    country: '巴西/阿根廷',
    fileName: "landmark_90.png"
  },
  {
    code: 'landmark_91',
    name: '面包山',
    country: '巴西',
    fileName: "landmark_91.jpg"
  },
  {
    code: 'landmark_92',
    name: '阿塔卡马沙漠',
    country: '智利',
    fileName: "landmark_92.jpg"
  },
  {
    code: 'landmark_93',
    name: '加拉帕戈斯群岛',
    country: '厄瓜多尔',
    fileName: "landmark_93.png"
  },
  {
    code: 'landmark_94',
    name: '安第斯山脉',
    country: '南美洲',
    fileName: "landmark_94.jpg"
  },
  {
    code: 'landmark_95',
    name: '托雷德尔潘恩',
    country: '智利',
    fileName: "landmark_95.jpg"
  },
  {
    code: 'landmark_96',
    name: '摩洛哥古城',
    country: '摩洛哥',
    fileName: "landmark_96.png"
  },
  {
    code: 'landmark_97',
    name: '瓦地伦',
    country: '约旦',
    fileName: "landmark_97.jpg"
  },
  {
    code: 'landmark_98',
    name: '死海',
    country: '约旦/以色列',
    fileName: "landmark_98.png"
  },
  {
    code: 'landmark_99',
    name: '迪拜塔',
    country: '阿联酋',
    fileName: "landmark_99.jpeg"
  },
  {
    code: 'landmark_100',
    name: '棕榈岛',
    country: '阿联酋',
    fileName: "landmark_100.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const worldLandmarks = worldLandmarkFiles.map(landmark => ({
  ...landmark,
  name: `【${landmark.country}】${landmark.name}`, // 组合国家和名称
  imageUrl: imageConfig.getImageUrl('worldLandmarks', landmark.fileName)
}));
