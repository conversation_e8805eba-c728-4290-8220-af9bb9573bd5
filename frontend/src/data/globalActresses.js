const actressFiles = [
  {
    code: 'actress_1',
    name: '章子怡',
    country: '中国',
    race: '黄种人',
    fileName: "actress_1.jpg"
  },
  {
    code: 'actress_2',
    name: '巩俐',
    country: '中国',
    race: '黄种人',
    fileName: "actress_2.jpg"
  },
  {
    code: 'actress_3',
    name: '范冰冰',
    country: '中国',
    race: '黄种人',
    fileName: "actress_3.jpg"
  },
  {
    code: 'actress_4',
    name: '周迅',
    country: '中国',
    race: '黄种人',
    fileName: "actress_4.jpg"
  },
  {
    code: 'actress_5',
    name: '石原里美',
    country: '日本',
    race: '黄种人',
    fileName: "actress_5.jpg"
  },
  {
    code: 'actress_6',
    name: '新垣结衣',
    country: '日本',
    race: '黄种人',
    fileName: "actress_6.jpg"
  },
  {
    code: 'actress_7',
    name: '长泽雅美',
    country: '日本',
    race: '黄种人',
    fileName: "actress_7.jpg"
  },
  {
    code: 'actress_8',
    name: '深田恭子',
    country: '日本',
    race: '黄种人',
    fileName: "actress_8.jpg"
  },
  {
    code: 'actress_9',
    name: '全智贤',
    country: '韩国',
    race: '黄种人',
    fileName: "actress_9.jpg"
  },
  {
    code: 'actress_10',
    name: '宋慧乔',
    country: '韩国',
    race: '黄种人',
    fileName: "actress_10.jpg"
  },
  {
    code: 'actress_11',
    name: '金泰希',
    country: '韩国',
    race: '黄种人',
    fileName: "actress_11.jpg"
  },
  {
    code: 'actress_12',
    name: '孙艺珍',
    country: '韩国',
    race: '黄种人',
    fileName: "actress_12.jpg"
  },
  {
    code: 'actress_13',
    name: '平采娜·乐维瑟派布恩',
    country: '泰国',
    race: '黄种人',
    fileName: "actress_13.jpg"
  },
  {
    code: 'actress_14',
    name: '乌拉萨雅·斯帕邦德',
    country: '泰国',
    race: '黄种人',
    fileName: "actress_14.jpg"
  },
  {
    code: 'actress_15',
    name: '吴青芸',
    country: '越南',
    race: '黄种人',
    fileName: "actress_15.jpg"
  },
  {
    code: 'actress_16',
    name: '安妮·柯蒂斯',
    country: '菲律宾',
    race: '黄种人',
    fileName: "actress_16.jpg"
  },
  {
    code: 'actress_17',
    name: '玛丽安·里维拉',
    country: '菲律宾',
    race: '黄种人',
    fileName: "actress_17.jpg"
  },
  {
    code: 'actress_18',
    name: '阿格妮丝·莫妮卡',
    country: '印度尼西亚',
    race: '黄种人',
    fileName: "actress_18.jpg"
  },
  {
    code: 'actress_19',
    name: '拉伊莎·安德里亚娜',
    country: '印度尼西亚',
    race: '黄种人',
    fileName: "actress_19.jpg"
  },
  {
    code: 'actress_20',
    name: '杨紫琼',
    country: '马来西亚',
    race: '黄种人',
    fileName: "actress_20.jpg"
  },
  {
    code: 'actress_21',
    name: '梁静茹',
    country: '马来西亚',
    race: '黄种人',
    fileName: "actress_21.jpg"
  },
  {
    code: 'actress_22',
    name: '范文芳',
    country: '新加坡',
    race: '黄种人',
    fileName: "actress_22.jpg"
  },
  {
    code: 'actress_23',
    name: '郑惠玉',
    country: '新加坡',
    race: '黄种人',
    fileName: "actress_23.jpg"
  },
  {
    code: 'actress_24',
    name: '艾西瓦娅·雷',
    country: '印度',
    race: '黄种人',
    fileName: "actress_24.jpg"
  },
  {
    code: 'actress_25',
    name: '普里扬卡·乔普拉',
    country: '印度',
    race: '黄种人',
    fileName: "actress_25.jpg"
  },
  {
    code: 'actress_26',
    name: '卡特莉娜·卡芙',
    country: '印度',
    race: '黄种人',
    fileName: "actress_26.jpg"
  },
  {
    code: 'actress_27',
    name: '迪皮卡·帕度柯妮',
    country: '印度',
    race: '黄种人',
    fileName: "actress_27.jpg"
  },
  {
    code: 'actress_28',
    name: '詹妮弗·劳伦斯',
    country: '美国',
    race: '白种人',
    fileName: "actress_28.jpg"
  },
  {
    code: 'actress_29',
    name: '斯嘉丽·约翰逊',
    country: '美国',
    race: '白种人',
    fileName: "actress_29.jpg"
  },
  {
    code: 'actress_30',
    name: '安吉丽娜·朱莉',
    country: '美国',
    race: '白种人',
    fileName: "actress_30.jpg"
  },
  {
    code: 'actress_31',
    name: '梅丽尔·斯特里普',
    country: '美国',
    race: '白种人',
    fileName: "actress_31.jpg"
  },
  {
    code: 'actress_32',
    name: '艾玛·斯通',
    country: '美国',
    race: '白种人',
    fileName: "actress_32.jpg"
  },
  {
    code: 'actress_33',
    name: '艾玛·沃特森',
    country: '英国',
    race: '白种人',
    fileName: "actress_33.jpg"
  },
  {
    code: 'actress_34',
    name: '凯特·温斯莱特',
    country: '英国',
    race: '白种人',
    fileName: "actress_34.jpg"
  },
  {
    code: 'actress_35',
    name: '海伦·米伦',
    country: '英国',
    race: '白种人',
    fileName: "actress_35.jpg"
  },
  {
    code: 'actress_36',
    name: '朱迪·丹奇',
    country: '英国',
    race: '白种人',
    fileName: "actress_36.jpg"
  },
  {
    code: 'actress_37',
    name: '玛丽昂·歌迪亚',
    country: '法国',
    race: '白种人',
    fileName: "actress_37.jpg"
  },
  {
    code: 'actress_38',
    name: '苏菲·玛索',
    country: '法国',
    race: '白种人',
    fileName: "actress_38.jpg"
  },
  {
    code: 'actress_39',
    name: '伊莎贝尔·于佩尔',
    country: '法国',
    race: '白种人',
    fileName: "actress_39.jpg"
  },
  {
    code: 'actress_40',
    name: '朱丽叶·比诺什',
    country: '法国',
    race: '白种人',
    fileName: "actress_40.jpg"
  },
  {
    code: 'actress_41',
    name: '黛安·克鲁格',
    country: '德国',
    race: '白种人',
    fileName: "actress_41.jpg"
  },
  {
    code: 'actress_42',
    name: '弗兰卡·波滕特',
    country: '德国',
    race: '白种人',
    fileName: "actress_42.jpg"
  },
  {
    code: 'actress_43',
    name: '妮娜·霍斯',
    country: '德国',
    race: '白种人',
    fileName: "actress_43.jpg"
  },
  {
    code: 'actress_44',
    name: '莫妮卡·贝鲁奇',
    country: '意大利',
    race: '白种人',
    fileName: "actress_44.jpg"
  },
  {
    code: 'actress_45',
    name: '索菲亚·罗兰',
    country: '意大利',
    race: '白种人',
    fileName: "actress_45.jpg"
  },
  {
    code: 'actress_46',
    name: '伊莎贝拉·罗西里尼',
    country: '意大利',
    race: '白种人',
    fileName: "actress_46.jpg"
  },
  {
    code: 'actress_47',
    name: '佩内洛普·克鲁兹',
    country: '西班牙',
    race: '白种人',
    fileName: "actress_47.jpg"
  },
  {
    code: 'actress_48',
    name: '艾尔莎·帕塔奇',
    country: '西班牙',
    race: '白种人',
    fileName: "actress_48.jpg"
  },
  {
    code: 'actress_49',
    name: '米拉·乔沃维奇',
    country: '俄罗斯',
    race: '白种人',
    fileName: "actress_49.jpg"
  },
  {
    code: 'actress_50',
    name: '娜塔莉·沃佳诺娃',
    country: '俄罗斯',
    race: '白种人',
    fileName: "actress_50.jpg"
  },
  {
    code: 'actress_51',
    name: '伊琳娜·沙伊克',
    country: '俄罗斯',
    race: '白种人',
    fileName: "actress_51.jpg"
  },
  {
    code: 'actress_52',
    name: '妮可·基德曼',
    country: '澳大利亚',
    race: '白种人',
    fileName: "actress_52.jpg"
  },
  {
    code: 'actress_53',
    name: '凯特·布兰切特',
    country: '澳大利亚',
    race: '白种人',
    fileName: "actress_53.jpg"
  },
  {
    code: 'actress_54',
    name: '娜奥米·沃茨',
    country: '澳大利亚',
    race: '白种人',
    fileName: "actress_54.jpg"
  },
  {
    code: 'actress_55',
    name: '玛格特·罗比',
    country: '澳大利亚',
    race: '白种人',
    fileName: "actress_55.jpg"
  },
  {
    code: 'actress_56',
    name: '瑞秋·麦克亚当斯',
    country: '加拿大',
    race: '白种人',
    fileName: "actress_56.jpg"
  },
  {
    code: 'actress_57',
    name: '艾伦·佩吉',
    country: '加拿大',
    race: '白种人',
    fileName: "actress_57.jpg"
  },
  {
    code: 'actress_58',
    name: '桑德拉·欧',
    country: '加拿大',
    race: '白种人',
    fileName: "actress_58.jpg"
  },
  {
    code: 'actress_59',
    name: '艾丽西亚·维坎德',
    country: '瑞典',
    race: '白种人',
    fileName: "actress_59.jpg"
  },
  {
    code: 'actress_60',
    name: '诺米·拉佩斯',
    country: '瑞典',
    race: '白种人',
    fileName: "actress_60.jpg"
  },
  {
    code: 'actress_62',
    name: '康妮·尼尔森',
    country: '丹麦',
    race: '白种人',
    fileName: "actress_62.jpg"
  },
  {
    code: 'actress_63',
    name: '卡里斯·范·侯登',
    country: '荷兰',
    race: '白种人',
    fileName: "actress_63.jpg"
  },
  {
    code: 'actress_64',
    name: '法米克·詹森',
    country: '荷兰',
    race: '白种人',
    fileName: "actress_64.jpg"
  },
  {
    code: 'actress_65',
    name: '雷妮·泽尔维格',
    country: '挪威',
    race: '白种人',
    fileName: "actress_65.jpg"
  },
  {
    code: 'actress_66',
    name: '阿克塞尔·亨尼',
    country: '挪威',
    race: '白种人',
    fileName: "actress_66.jpg"
  },
  {
    code: 'actress_67',
    name: '伊莎贝拉·斯科鲁普科',
    country: '波兰',
    race: '白种人',
    fileName: "actress_67.jpg"
  },
  {
    code: 'actress_68',
    name: '阿格涅什卡·格罗乔斯卡',
    country: '波兰',
    race: '白种人',
    fileName: "actress_68.jpg"
  },
  {
    code: 'actress_69',
    name: '伊娃·格林',
    country: '捷克',
    race: '白种人',
    fileName: "actress_69.jpg"
  },
  {
    code: 'actress_70',
    name: '维罗妮卡·泽曼诺娃',
    country: '捷克',
    race: '白种人',
    fileName: "actress_70.jpg"
  },
  {
    code: 'actress_71',
    name: '芭芭拉·帕尔文',
    country: '匈牙利',
    race: '白种人',
    fileName: "actress_71.jpg"
  },
  {
    code: 'actress_72',
    name: '维卡·克里克斯',
    country: '匈牙利',
    race: '白种人',
    fileName: "actress_72.jpg"
  },
  {
    code: 'actress_73',
    name: '亚历山德拉·玛丽亚·拉拉',
    country: '罗马尼亚',
    race: '白种人',
    fileName: "actress_73.jpg"
  },
  {
    code: 'actress_74',
    name: '莫妮卡·比尔拉德努',
    country: '罗马尼亚',
    race: '白种人',
    fileName: "actress_74.jpg"
  },
  {
    code: 'actress_75',
    name: '伊莲妮·帕帕斯',
    country: '希腊',
    race: '白种人',
    fileName: "actress_75.jpg"
  },
  {
    code: 'actress_76',
    name: '玛丽亚·纳夫普利奥图',
    country: '希腊',
    race: '白种人',
    fileName: "actress_76.jpg"
  },
  {
    code: 'actress_77',
    name: '劳拉·伯恩',
    country: '芬兰',
    race: '白种人',
    fileName: "actress_77.jpg"
  },
  {
    code: 'actress_79',
    name: '克里斯蒂安·贝格',
    country: '奥地利',
    race: '白种人',
    fileName: "actress_79.jpg"
  },
  {
    code: 'actress_80',
    name: '苏珊娜·沃尔夫',
    country: '奥地利',
    race: '白种人',
    fileName: "actress_80.jpg"
  },
  {
    code: 'actress_81',
    name: '哈莉·贝瑞',
    country: '美国',
    race: '黑种人',
    fileName: "actress_81.jpg"
  },
  {
    code: 'actress_82',
    name: '维奥拉·戴维斯',
    country: '美国',
    race: '黑种人',
    fileName: "actress_82.jpg"
  },
  {
    code: 'actress_83',
    name: '奥普拉·温弗瑞',
    country: '美国',
    race: '黑种人',
    fileName: "actress_83.jpg"
  },
  {
    code: 'actress_84',
    name: '塔拉吉·P·汉森',
    country: '美国',
    race: '黑种人',
    fileName: "actress_84.jpg"
  },
  {
    code: 'actress_85',
    name: '露皮塔·尼永奥',
    country: '美国',
    race: '黑种人',
    fileName: "actress_85.jpg"
  },
  {
    code: 'actress_86',
    name: '娜奥米·哈里斯',
    country: '英国',
    race: '黑种人',
    fileName: "actress_86.jpg"
  },
  {
    code: 'actress_87',
    name: '坦迪·牛顿',
    country: '英国',
    race: '黑种人',
    fileName: "actress_87.jpg"
  },
  {
    code: 'actress_88',
    name: '列蒂希娅·赖特',
    country: '英国',
    race: '黑种人',
    fileName: "actress_88.jpg"
  },
  {
    code: 'actress_92',
    name: '特里·费托',
    country: '南非',
    race: '黑种人',
    fileName: "actress_92.jpg"
  },
  {
    code: 'actress_97',
    name: '露皮塔·尼永奥',
    country: '肯尼亚',
    race: '黑种人',
    fileName: "actress_97.jpg"
  },
  {
    code: 'actress_100',
    name: '妮娅·朗',
    country: '加纳',
    race: '黑种人',
    fileName: "actress_100.jpg"
  },
  {
    code: 'actress_104',
    name: '娜奥米·坎贝尔',
    country: '牙买加',
    race: '黑种人',
    fileName: "actress_104.jpg"
  },
  {
    code: 'actress_108',
    name: '伊娃·门德斯',
    country: '古巴',
    race: '黑种人',
    fileName: "actress_108.jpg"
  },
  {
    code: 'actress_110',
    name: '米歇尔·罗德里格斯',
    country: '多米尼加',
    race: '黑种人',
    fileName: "actress_110.jpg"
  },
  {
    code: 'actress_111',
    name: '加奈儿·梦奈',
    country: '海地',
    race: '黑种人',
    fileName: "actress_111.jpg"
  },
  {
    code: 'actress_113',
    name: '妮基·米娜',
    country: '特立尼达和多巴哥',
    race: '黑种人',
    fileName: "actress_113.jpg"
  },
  {
    code: 'actress_114',
    name: '海蒂·拉玛',
    country: '特立尼达和多巴哥',
    race: '黑种人',
    fileName: "actress_114.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const globalActresses = actressFiles.map(item => ({
  ...item,
  imageUrl: imageConfig.getImageUrl('global-actresses', item.fileName)
}));