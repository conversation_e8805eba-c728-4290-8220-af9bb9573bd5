const fairyTaleFiles = [
  {
    code: 'fairy_tale_1',
    name: '哪吒闹海',
    fileName: "fairy_tale_1.jpg"
  },
  {
    code: 'fairy_tale_2',
    name: '大闹天宫',
    fileName: "fairy_tale_2.jpg"
  },
  {
    code: 'fairy_tale_3',
    name: '葫芦兄弟',
    fileName: "fairy_tale_3.jpg"
  },
  {
    code: 'fairy_tale_4',
    name: '黑猫警长',
    fileName: "fairy_tale_4.jpg"
  },
  {
    code: 'fairy_tale_5',
    name: '舒克和贝塔',
    fileName: "fairy_tale_5.jpg"
  },
  {
    code: 'fairy_tale_6',
    name: '喜羊羊与灰太狼',
    fileName: "fairy_tale_6.jpg"
  },
  {
    code: 'fairy_tale_7',
    name: '熊出没',
    fileName: "fairy_tale_7.jpg"
  },
  {
    code: 'fairy_tale_8',
    name: '大头儿子小头爸爸',
    fileName: "fairy_tale_8.jpg"
  },
  {
    code: 'fairy_tale_13',
    name: '哆啦A梦',
    fileName: "fairy_tale_13.jpg"
  },
  {
    code: 'fairy_tale_14',
    name: '名侦探柯南',
    fileName: "fairy_tale_14.jpg"
  },
  {
    code: 'fairy_tale_15',
    name: '樱桃小丸子',
    fileName: "fairy_tale_15.jpg"
  },
  {
    code: 'fairy_tale_16',
    name: '蜡笔小新',
    fileName: "fairy_tale_16.jpg"
  },
  {
    code: 'fairy_tale_17',
    name: '白雪公主',
    fileName: "fairy_tale_17.jpg"
  },
  {
    code: 'fairy_tale_18',
    name: '灰姑娘',
    fileName: "fairy_tale_18.jpg"
  },
  {
    code: 'fairy_tale_19',
    name: '小红帽',
    fileName: "fairy_tale_19.jpg"
  },
  {
    code: 'fairy_tale_20',
    name: '睡美人',
    fileName: "fairy_tale_20.jpg"
  },
  {
    code: 'fairy_tale_21',
    name: '狮子王',
    fileName: "fairy_tale_21.jpg"
  },
  {
    code: 'fairy_tale_22',
    name: '冰雪奇缘',
    fileName: "fairy_tale_22.jpg"
  },
  {
    code: 'fairy_tale_23',
    name: '玩具总动员',
    fileName: "fairy_tale_23.jpg"
  },
  {
    code: 'fairy_tale_25',
    name: '小美人鱼',
    fileName: "fairy_tale_25.jpg"
  },
  {
    code: 'fairy_tale_26',
    name: '美女与野兽',
    fileName: "fairy_tale_26.jpg"
  },
  {
    code: 'fairy_tale_28',
    name: '阿拉丁',
    fileName: "fairy_tale_28.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const fairyTales = fairyTaleFiles.map(tale => ({
  ...tale,
  imageUrl: imageConfig.getImageUrl('fairy_tales', tale.fileName)
}));
