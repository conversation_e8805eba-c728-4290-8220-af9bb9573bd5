const carLogoFiles = [
  {
    code: 'logo_1',
    name: '奥迪',
    fileName: "logo_1.jpeg"
  },
  {
    code: 'logo_2',
    name: '宝马',
    fileName: "logo_2.jpg"
  },
  {
    code: 'logo_3',
    name: '奔驰',
    fileName: "logo_3.jpg"
  },
  {
    code: 'logo_4',
    name: '大众',
    fileName: "logo_4.jpg"
  },
  {
    code: 'logo_5',
    name: '丰田',
    fileName: "logo_5.jpg"
  },
  {
    code: 'logo_6',
    name: '本田',
    fileName: "logo_6.jpg"
  },
  {
    code: 'logo_7',
    name: '福特',
    fileName: "logo_7.png"
  },
  {
    code: 'logo_8',
    name: '雪佛兰',
    fileName: "logo_8.jpg"
  },
  {
    code: 'logo_9',
    name: '日产',
    fileName: "logo_9.jpg"
  },
  {
    code: 'logo_10',
    name: '现代',
    fileName: "logo_10.jpg"
  },
  {
    code: 'logo_11',
    name: '起亚',
    fileName: "logo_11.jpeg"
  },
  {
    code: 'logo_12',
    name: '雷克萨斯',
    fileName: "logo_12.png"
  },
  {
    code: 'logo_13',
    name: '保时捷',
    fileName: "logo_13.webp"
  },
  {
    code: 'logo_14',
    name: '法拉利',
    fileName: "logo_14.jpeg"
  },
  {
    code: 'logo_15',
    name: '兰博基尼',
    fileName: "logo_15.jpg"
  },
  {
    code: 'logo_16',
    name: '沃尔沃',
    fileName: "logo_16.jpg"
  },
  {
    code: 'logo_17',
    name: '捷豹',
    fileName: "logo_17.jpeg"
  },
  {
    code: 'logo_18',
    name: '路虎',
    fileName: "logo_18.png"
  },
  {
    code: 'logo_19',
    name: '特斯拉',
    fileName: "logo_19.jpg"
  },
  {
    code: 'logo_20',
    name: '比亚迪',
    fileName: "logo_20.png"
  },
  {
    code: 'logo_21',
    name: '吉利',
    fileName: "logo_21.jpeg"
  },
  {
    code: 'logo_22',
    name: '长安',
    fileName: "logo_22.jpg"
  },
  {
    code: 'logo_23',
    name: '哈弗',
    fileName: "logo_23.jpg"
  },
  {
    code: 'logo_24',
    name: '蔚来',
    fileName: "logo_24.jpg"
  },
  {
    code: 'logo_25',
    name: '小鹏',
    fileName: "logo_25.jpg"
  },
  {
    code: 'logo_26',
    name: '理想',
    fileName: "logo_26.jpeg"
  },
  {
    code: 'logo_27',
    name: '马自达',
    fileName: "logo_27.jpg"
  },
  {
    code: 'logo_28',
    name: '斯巴鲁',
    fileName: "logo_28.jpg"
  },
  {
    code: 'logo_29',
    name: '三菱',
    fileName: "logo_29.jpg"
  },
  {
    code: 'logo_30',
    name: '铃木',
    fileName: "logo_30.jpg"
  },
  {
    code: 'logo_31',
    name: '标致',
    fileName: "logo_31.jpg"
  },
  {
    code: 'logo_32',
    name: '雪铁龙',
    fileName: "logo_32.jpg"
  },
  {
    code: 'logo_33',
    name: '雷诺',
    fileName: "logo_33.jpg"
  },
  {
    code: 'logo_34',
    name: '菲亚特',
    fileName: "logo_34.jpeg"
  },
  {
    code: 'logo_35',
    name: '阿尔法·罗密欧',
    fileName: "logo_35.png"
  },
  {
    code: 'logo_36',
    name: '玛莎拉蒂',
    fileName: "logo_36.jpg"
  },
  {
    code: 'logo_37',
    name: '宾利',
    fileName: "logo_37.jpg"
  },
  {
    code: 'logo_38',
    name: '劳斯莱斯',
    fileName: "logo_38.jpg"
  },
  {
    code: 'logo_39',
    name: '阿斯顿·马丁',
    fileName: "logo_39.jpg"
  },
  {
    code: 'logo_40',
    name: '迈凯伦',
    fileName: "logo_40.jpeg"
  },
  {
    code: 'logo_41',
    name: '布加迪',
    fileName: "logo_41.jpg"
  },
  {
    code: 'logo_42',
    name: '科尼赛克',
    fileName: "logo_42.jpg"
  },
  {
    code: 'logo_43',
    name: '帕加尼',
    fileName: "logo_43.jpg"
  },
  {
    code: 'logo_44',
    name: '红旗',
    fileName: "logo_44.png"
  },
  {
    code: 'logo_45',
    name: '领克',
    fileName: "logo_45.jpg"
  },
  {
    code: 'logo_46',
    name: 'WEY',
    fileName: "logo_46.jpg"
  },
  {
    code: 'logo_47',
    name: '广汽传祺',
    fileName: "logo_47.jpeg"
  },
  {
    code: 'logo_48',
    name: '荣威',
    fileName: "logo_48.jpeg"
  },
  {
    code: 'logo_49',
    name: '名爵',
    fileName: "logo_49.jpeg"
  },
  {
    code: 'logo_50',
    name: '斯柯达',
    fileName: "logo_50.jpeg"
  },
  {
    code: 'logo_51',
    name: '西雅特',
    fileName: "logo_51.jpeg"
  },
  {
    code: 'logo_52',
    name: 'DS',
    fileName: "logo_52.jpg"
  },
  {
    code: 'logo_53',
    name: '讴歌',
    fileName: "logo_53.jpeg"
  },
  {
    code: 'logo_54',
    name: '英菲尼迪',
    fileName: "logo_54.jpg"
  },
  {
    code: 'logo_55',
    name: '创维',
    fileName: "logo_55.png"
  },
  {
    code: 'logo_56',
    name: '凯迪拉克',
    fileName: "logo_56.jpg"
  },
  {
    code: 'logo_57',
    name: '林肯',
    fileName: "logo_57.jpeg"
  },
  {
    code: 'logo_58',
    name: 'Jeep',
    fileName: "logo_58.jpg"
  },
  {
    code: 'logo_59',
    name: '道奇',
    fileName: "logo_59.png"
  },
  {
    code: 'logo_60',
    name: '克莱斯勒',
    fileName: "logo_60.jpg"
  },
];


import imageConfig from '../config/imageConfig.js';

export const carLogos = carLogoFiles.map(logo => ({
  ...logo,
  imageUrl: imageConfig.getImageUrl('carLogos', logo.fileName)
}));
