const hairstyleFiles = [
  {
    code: 'hairstyle_1',
    name: '长直发',
    fileName: "hairstyle_1.jpg"
  },
  {
    code: 'hairstyle_2',
    name: '波浪卷发',
    fileName: "hairstyle_2.jpg"
  },
  {
    code: 'hairstyle_3',
    name: '波波头',
    fileName: "hairstyle_3.jpg"
  },
  {
    code: 'hairstyle_4',
    name: '齐肩发',
    fileName: "hairstyle_4.jpg"
  },
  {
    code: 'hairstyle_5',
    name: '短发',
    fileName: "hairstyle_5.jpg"
  },
  {
    code: 'hairstyle_6',
    name: '马尾辫',
    fileName: "hairstyle_6.jpg"
  },
  {
    code: 'hairstyle_7',
    name: '丸子头',
    fileName: "hairstyle_7.jpg"
  },
  {
    code: 'hairstyle_8',
    name: '麻花辫',
    fileName: "hairstyle_8.jpg"
  },
  {
    code: 'hairstyle_10',
    name: '空气刘海',
    fileName: "hairstyle_10.jpg"
  },
  {
    code: 'hairstyle_11',
    name: '法式刘海',
    fileName: "hairstyle_11.jpg"
  },
  {
    code: 'hairstyle_12',
    name: '齐刘海',
    fileName: "hairstyle_12.jpg"
  },
  {
    code: 'hairstyle_13',
    name: '斜刘海',
    fileName: "hairstyle_13.jpg"
  },
  {
    code: 'hairstyle_14',
    name: '大波浪',
    fileName: "hairstyle_14.jpg"
  },
  {
    code: 'hairstyle_15',
    name: '小卷发',
    fileName: "hairstyle_15.jpg"
  },
  {
    code: 'hairstyle_16',
    name: '玉米须烫',
    fileName: "hairstyle_16.jpg"
  },
  {
    code: 'hairstyle_17',
    name: '爆炸头',
    fileName: "hairstyle_17.jpg"
  },
  {
    code: 'hairstyle_18',
    name: '脏辫',
    fileName: "hairstyle_18.jpg"
  },
  {
    code: 'hairstyle_19',
    name: '波西米亚卷发',
    fileName: "hairstyle_19.jpg"
  },
  {
    code: 'hairstyle_20',
    name: '层次剪发',
    fileName: "hairstyle_20.jpg"
  },
];

import imageConfig from '../config/imageConfig.js';

export const hairstyles = hairstyleFiles.map(hairstyle => ({
  ...hairstyle,
  imageUrl: imageConfig.getImageUrl('hairstyles', hairstyle.fileName)
}));