// 麻将胡牌挑战题目数据
// 基于经典麻将听牌题目，使用agari算法库计算正确答案
// 所有牌型已按从小到大排序，方便用户阅读
// 已排除只有1个答案的题目，确保题目质量

export const mahjongWinningQuestions = [
  {
    "id": 1,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七"
    ],
    "answers": [
      "一",
      "七",
      "四"
    ]
  },
  {
    "id": 2,
    "tiles": [
      "二",
      "三",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八",
      "八",
      "九",
      "九"
    ],
    "answers": [
      "九",
      "五",
      "八",
      "六"
    ]
  },
  {
    "id": 3,
    "tiles": [
      "一",
      "一",
      "一",
      "二",
      "三",
      "四",
      "五",
      "六",
      "七",
      "八",
      "九",
      "九",
      "九"
    ],
    "answers": [
      "一",
      "七",
      "三",
      "九",
      "二",
      "五",
      "八",
      "六",
      "四"
    ]
  },
  {
    "id": 4,
    "tiles": [
      "一",
      "二",
      "三",
      "三",
      "三",
      "四",
      "五",
      "六",
      "七",
      "八",
      "八",
      "八",
      "九"
    ],
    "answers": [
      "三",
      "八"
    ]
  },
  {
    "id": 5,
    "tiles": [
      "二",
      "二",
      "二",
      "三",
      "四",
      "五",
      "五",
      "五",
      "六",
      "七",
      "八",
      "九",
      "九"
    ],
    "answers": [
      "九",
      "二",
      "五"
    ]
  },
  {
    "id": 6,
    "tiles": [
      "一",
      "一",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "一",
      "七",
      "四"
    ]
  },
  {
    "id": 7,
    "tiles": [
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "二",
      "五"
    ]
  },
  {
    "id": 8,
    "tiles": [
      "二",
      "三",
      "四",
      "四",
      "四",
      "五",
      "六",
      "七",
      "八",
      "八",
      "八",
      "九",
      "九"
    ],
    "answers": [
      "一",
      "九",
      "四"
    ]
  },
  {
    "id": 9,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "四",
      "五",
      "六",
      "七",
      "八",
      "九",
      "九",
      "九"
    ],
    "answers": [
      "一",
      "三",
      "二"
    ]
  },
  {
    "id": 10,
    "tiles": [
      "一",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "三",
      "九",
      "六"
    ]
  },
  {
    "id": 11,
    "tiles": [
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "七",
      "八",
      "九",
      "九"
    ],
    "answers": [
      "九",
      "二",
      "五"
    ]
  },
  {
    "id": 12,
    "tiles": [
      "一",
      "二",
      "三",
      "四",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "一",
      "七",
      "五",
      "六",
      "四"
    ]
  },
  {
    "id": 13,
    "tiles": [
      "二",
      "二",
      "三",
      "三",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "三",
      "二"
    ]
  },
  {
    "id": 14,
    "tiles": [
      "一",
      "一",
      "二",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "七",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "三",
      "六"
    ]
  },
  {
    "id": 15,
    "tiles": [
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八",
      "九",
      "九"
    ],
    "answers": [
      "一",
      "七",
      "四"
    ]
  },
  {
    "id": 16,
    "tiles": [
      "一",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八"
    ],
    "answers": [
      "一",
      "七",
      "四"
    ]
  },
  {
    "id": 17,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "一",
      "二"
    ]
  },
  {
    "id": 18,
    "tiles": [
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "六",
      "七",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "一",
      "七",
      "四"
    ]
  },
  {
    "id": 19,
    "tiles": [
      "一",
      "一",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "七",
      "八",
      "八"
    ],
    "answers": [
      "一",
      "八"
    ]
  },
  {
    "id": 20,
    "tiles": [
      "一",
      "二",
      "三",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "三",
      "九",
      "六"
    ]
  },
  {
    "id": 21,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "六",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "一",
      "七",
      "四"
    ]
  },
  {
    "id": 22,
    "tiles": [
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "三",
      "九",
      "六"
    ]
  },
  {
    "id": 23,
    "tiles": [
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "二",
      "五",
      "八"
    ]
  },
  {
    "id": 24,
    "tiles": [
      "一",
      "一",
      "二",
      "三",
      "四",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "三",
      "九",
      "六"
    ]
  },
  {
    "id": 25,
    "tiles": [
      "一",
      "二",
      "三",
      "四",
      "五",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "五",
      "八"
    ]
  },
  {
    "id": 26,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "五",
      "六",
      "七",
      "八",
      "九",
      "九"
    ],
    "answers": [
      "三",
      "九",
      "六"
    ]
  },
  {
    "id": 27,
    "tiles": [
      "二",
      "二",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "七",
      "七",
      "八",
      "八",
      "九"
    ],
    "answers": [
      "三",
      "九",
      "六"
    ]
  },
  {
    "id": 28,
    "tiles": [
      "一",
      "二",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "一",
      "七",
      "四"
    ]
  },
  {
    "id": 29,
    "tiles": [
      "二",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八",
      "八",
      "九"
    ],
    "answers": [
      "二",
      "五",
      "八"
    ]
  },
  {
    "id": 30,
    "tiles": [
      "一",
      "一",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "六",
      "六",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "一",
      "六"
    ]
  },
  {
    "id": 31,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "五",
      "八"
    ]
  },
  {
    "id": 32,
    "tiles": [
      "二",
      "二",
      "三",
      "三",
      "四",
      "五",
      "五",
      "六",
      "七",
      "七",
      "八",
      "八",
      "九"
    ],
    "answers": [
      "一",
      "四"
    ]
  },
  {
    "id": 33,
    "tiles": [
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八"
    ],
    "answers": [
      "二",
      "五",
      "八"
    ]
  },
  {
    "id": 34,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "一",
      "二"
    ]
  },
  {
    "id": 35,
    "tiles": [
      "一",
      "二",
      "三",
      "四",
      "四",
      "四",
      "五",
      "六",
      "六",
      "七",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "九",
      "五",
      "八",
      "六"
    ]
  },
  {
    "id": 36,
    "tiles": [
      "一",
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "一",
      "二",
      "五",
      "四"
    ]
  },
  {
    "id": 37,
    "tiles": [
      "一",
      "二",
      "三",
      "三",
      "三",
      "四",
      "五",
      "五",
      "六",
      "六",
      "七",
      "八",
      "九"
    ],
    "answers": [
      "七",
      "四"
    ]
  },
  {
    "id": 38,
    "tiles": [
      "一",
      "二",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "五",
      "六",
      "七",
      "八"
    ],
    "answers": [
      "一",
      "三",
      "九",
      "六",
      "四"
    ]
  },
  {
    "id": 39,
    "tiles": [
      "一",
      "一",
      "二",
      "三",
      "三",
      "四",
      "四",
      "五",
      "五",
      "六",
      "六",
      "六",
      "七"
    ],
    "answers": [
      "一",
      "六"
    ]
  }
];

// 中文数字映射，供其他模块使用
export const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九'];

// 中文数字到繁体万字的映射
export const chineseToWan = {
  '一': '萬',
  '二': '萬',
  '三': '萬',
  '四': '萬',
  '五': '萬',
  '六': '萬',
  '七': '萬',
  '八': '萬',
  '九': '萬'
};

// 获取随机题目的函数
export function getRandomQuestions(count = 10) {
  if (count >= mahjongWinningQuestions.length) {
    return [...mahjongWinningQuestions];
  }
  
  const shuffled = [...mahjongWinningQuestions].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}
