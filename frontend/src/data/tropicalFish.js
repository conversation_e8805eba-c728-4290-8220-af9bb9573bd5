const tropicalFishFiles = [
  {
    code: 'tropical_fish_1',
    name: '孔雀鱼',
    fileName: "tropical_fish_1.jpg"
  },
  {
    code: 'tropical_fish_2',
    name: '红绿灯鱼',
    fileName: "tropical_fish_2.jpg"
  },
  {
    code: 'tropical_fish_3',
    name: '斑马鱼',
    fileName: "tropical_fish_3.jpg"
  },
  {
    code: 'tropical_fish_4',
    name: '天使鱼',
    fileName: "tropical_fish_4.jpg"
  },
  {
    code: 'tropical_fish_5',
    name: '七彩神仙鱼',
    fileName: "tropical_fish_5.jpg"
  },
  {
    code: 'tropical_fish_6',
    name: '红剑鱼',
    fileName: "tropical_fish_6.jpg"
  },
  {
    code: 'tropical_fish_7',
    name: '黑玛丽鱼',
    fileName: "tropical_fish_7.jpg"
  },
  {
    code: 'tropical_fish_8',
    name: '月光鱼',
    fileName: "tropical_fish_8.jpg"
  },
  {
    code: 'tropical_fish_9',
    name: '珍珠鱼',
    fileName: "tropical_fish_9.jpg"
  },
  {
    code: 'tropical_fish_10',
    name: '蓝曼龙',
    fileName: "tropical_fish_10.jpg"
  },
  {
    code: 'tropical_fish_11',
    name: '金丝鱼',
    fileName: "tropical_fish_11.jpg"
  },
  {
    code: 'tropical_fish_12',
    name: '红鼻剪刀',
    fileName: "tropical_fish_12.jpg"
  },
  {
    code: 'tropical_fish_13',
    name: '霓虹灯鱼',
    fileName: "tropical_fish_13.jpg"
  },
  {
    code: 'tropical_fish_14',
    name: '银屏灯鱼',
    fileName: "tropical_fish_14.jpg"
  },
  {
    code: 'tropical_fish_15',
    name: '红尾玻璃',
    fileName: "tropical_fish_15.jpg"
  },
  {
    code: 'tropical_fish_16',
    name: '蓝眼灯鱼',
    fileName: "tropical_fish_16.jpg"
  },
  {
    code: 'tropical_fish_18',
    name: '红肚鱼',
    fileName: "tropical_fish_18.jpg"
  },
  {
    code: 'tropical_fish_19',
    name: '蓝肚鱼',
    fileName: "tropical_fish_19.jpg"
  },
  {
    code: 'tropical_fish_20',
    name: '红箭鱼',
    fileName: "tropical_fish_20.jpg"
  },
  {
    code: 'tropical_fish_21',
    name: '黑裙鱼',
    fileName: "tropical_fish_21.jpg"
  },
  {
    code: 'tropical_fish_22',
    name: '白招财鱼',
    fileName: "tropical_fish_22.jpg"
  }
];

import imageConfig from '../config/imageConfig.js';

export const tropicalFish = tropicalFishFiles.map(tropicalFish => ({
  ...tropicalFish,
  imageUrl: imageConfig.getImageUrl('tropical-fish', tropicalFish.fileName)
}));