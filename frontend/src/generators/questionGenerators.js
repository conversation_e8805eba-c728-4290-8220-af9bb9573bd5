import { OptionStrategyFactory, shuffleArray } from '@/strategies/optionStrategies.js';
import { DIFFICULTY_LEVELS, OPTION_TYPES } from '@/config/quizConfig.js';

/**
 * 题目生成器抽象基类
 */
export class QuestionGenerator {
  constructor(config = {}) {
    this.config = config;
    this.optionStrategy = OptionStrategyFactory.create(
      config.optionType || OPTION_TYPES.TEXT,
      config.optionConfig || {}
    );
  }

  /**
   * 生成题目
   * @param {Array} dataArray - 数据数组
   * @param {number} difficulty - 难度等级
   * @returns {Array} 题目数组
   */
  generateQuestions(dataArray, difficulty = 1) {
    if (!dataArray || dataArray.length === 0) return [];

    const optionCount = DIFFICULTY_LEVELS[difficulty]?.options || 4;
    const questions = [];

    for (const item of dataArray) {
      try {
        const question = this.generateSingleQuestion(item, dataArray, optionCount);
        if (question) {
          questions.push(question);
        }
      } catch (error) {
        console.warn(`生成题目失败:`, error, item);
      }
    }

    return this.config.shuffleQuestions ? shuffleArray(questions) : questions;
  }

  /**
   * 生成单个题目
   * @param {Object} item - 数据项
   * @param {Array} dataArray - 数据数组
   * @param {number} optionCount - 选项数量
   * @returns {Object} 题目对象
   */
  generateSingleQuestion(item, dataArray, optionCount) {
    const options = this.optionStrategy.generateOptions(item, dataArray, optionCount);
    const answer = this.optionStrategy.getCorrectAnswer(item);

    return {
      image: this.getQuestionImage(item),
      answer: answer,
      options: this.config.shuffleOptions ? shuffleArray(options) : options,
      ...this.getExtraFields(item)
    };
  }

  /**
   * 获取题目图片
   * @param {Object} item - 数据项
   * @returns {string} 图片路径或URL
   */
  getQuestionImage(item) {
    return item.fileName || item.image;
  }

  /**
   * 获取额外字段
   * @param {Object} item - 数据项
   * @returns {Object} 额外字段对象
   */
  getExtraFields(item) {
    return {
      category: item.category || item.continent || null
    };
  }
}

/**
 * 简单题目生成器
 */
export class SimpleQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { valueField: 'name' },
      ...config
    });
  }
}

/**
 * 基于分类的题目生成器
 */
export class CategoryBasedQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { 
        valueField: 'name',
        useCategory: true,
        categoryField: config.categoryField || 'category'
      },
      ...config
    });
  }
}

/**
 * 世界美食题目生成器
 */
export class WorldFoodsQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { valueField: 'country' },
      ...config
    });
  }

  getExtraFields(item) {
    return {
      ...super.getExtraFields(item),
      foodName: item.name,
      difficulty: item.difficulty
    };
  }
}

/**
 * 地标建筑题目生成器
 */
export class LandmarkBuildingQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { valueField: 'country' },
      ...config
    });
  }

  getExtraFields(item) {
    return {
      ...super.getExtraFields(item),
      buildingName: item.buildingName,
      buildingType: item.buildingType
    };
  }
}

/**
 * 全球明星题目生成器
 */
export class GlobalCelebrityQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { valueField: 'country' },
      ...config
    });
  }

  generateSingleQuestion(item, dataArray, optionCount) {
    // 按人种分类选择选项
    const options = new Set([item.country]);
    const sameRace = dataArray.filter(other => 
      other.race === item.race && other.country !== item.country
    );
    
    // 优先从同一人种选择
    while (options.size < optionCount && options.size < sameRace.length + 1) {
      const randomIndex = Math.floor(Math.random() * sameRace.length);
      const randomItem = sameRace[randomIndex];
      if (randomItem) {
        options.add(randomItem.country);
      }
    }
    
    // 如果不够，从所有国家补充
    if (options.size < optionCount) {
      const allCountries = [...new Set(dataArray.map(a => a.country))];
      while (options.size < optionCount && options.size < allCountries.length) {
        const randomIndex = Math.floor(Math.random() * allCountries.length);
        const randomCountry = allCountries[randomIndex];
        if (randomCountry !== item.country) {
          options.add(randomCountry);
        }
      }
    }

    return {
      image: this.getQuestionImage(item),
      answer: item.country,
      options: this.config.shuffleOptions ? shuffleArray(Array.from(options)) : Array.from(options),
      ...this.getExtraFields(item)
    };
  }

  getExtraFields(item) {
    return {
      ...super.getExtraFields(item),
      actressName: item.name,
      actorName: item.name,
      race: item.race
    };
  }
}

/**
 * 中国美食题目生成器
 */
export class ChineseFoodsQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { valueField: 'city' },
      ...config
    });
  }

  getExtraFields(item) {
    return {
      ...super.getExtraFields(item),
      foodName: item.name,
      fame: item.fame
    };
  }
}

/**
 * 宇宙天体题目生成器
 */
export class CelestialBodiesQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { valueField: 'name' },
      ...config
    });
  }

  getExtraFields(item) {
    return {
      ...super.getExtraFields(item),
      celestialBodyType: item.type,
      fame: item.fame
    };
  }
}

/**
 * 城市地区题目生成器
 */
export class CityRegionQuestionGenerator extends QuestionGenerator {
  constructor(config = {}) {
    super({
      optionType: OPTION_TYPES.TEXT,
      optionConfig: { valueField: 'province' },
      ...config
    });
  }

  generateQuestions(dataArray, difficulty = 4) {
    const { generateCityImage } = require('@/utils/textToImage.js');
    
    // 地区省份映射
    const regionProvinces = {
      "华东": ["山东", "江苏", "浙江", "安徽", "江西", "福建"],
      "华南": ["广东", "广西", "湖南", "湖北", "海南"],
      "华北": ["河南", "河北", "山西", "辽宁", "黑龙江", "吉林"],
      "西部": ["四川", "云南", "新疆", "甘肃", "内蒙古", "陕西", "贵州", "青海", "西藏", "宁夏"]
    };

    const optionCount = DIFFICULTY_LEVELS[difficulty]?.options || 6;
    const questions = [];

    for (const city of dataArray) {
      try {
        // 生成城市图片
        const cityImageDataUrl = generateCityImage(city, {
          width: 400,
          height: 300,
          backgroundColor: '#f8f9fa',
          primaryColor: '#2c3e50',
          secondaryColor: '#7f8c8d',
          accentColor: '#3498db'
        });

        // 获取该城市所在地区的省份作为选项
        const regionProvincesList = regionProvinces[city.region] || [];
        let options = [...regionProvincesList];
        
        if (!options.includes(city.province)) {
          options.push(city.province);
        }

        // 调整选项数量
        if (options.length > optionCount) {
          const correctAnswer = city.province;
          const otherOptions = options.filter(opt => opt !== correctAnswer);
          const shuffledOthers = shuffleArray(otherOptions);
          options = [correctAnswer, ...shuffledOthers.slice(0, optionCount - 1)];
        }

        const question = {
          image: cityImageDataUrl,
          question: `这个城市属于哪个省份？`,
          answer: city.province,
          options: this.config.shuffleOptions ? shuffleArray(options) : options,
          city: city.city,
          province: city.province,
          plate_code: city.plate_code,
          region: city.region,
          category: "地理知识"
        };

        questions.push(question);
      } catch (error) {
        console.warn(`生成城市题目失败:`, error, city);
      }
    }

    return this.config.shuffleQuestions ? shuffleArray(questions) : questions;
  }
}

/**
 * 题目生成器工厂
 */
export class QuestionGeneratorFactory {
  static create(type, config = {}) {
    switch (type) {
      case 'simple':
        return new SimpleQuestionGenerator(config);
      case 'category':
        return new CategoryBasedQuestionGenerator(config);
      case 'world-foods':
        return new WorldFoodsQuestionGenerator(config);
      case 'landmark-buildings':
        return new LandmarkBuildingQuestionGenerator(config);
      case 'global-celebrity':
        return new GlobalCelebrityQuestionGenerator(config);
      case 'chinese-foods':
        return new ChineseFoodsQuestionGenerator(config);
      case 'celestial-bodies':
        return new CelestialBodiesQuestionGenerator(config);
      case 'city-regions':
        return new CityRegionQuestionGenerator(config);
      default:
        return new SimpleQuestionGenerator(config);
    }
  }
} 