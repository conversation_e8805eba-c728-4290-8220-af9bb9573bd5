// 在最开始立即初始化 import.meta polyfill
console.log('🚀 应用开始加载...')

// 立即检查并初始化 import.meta polyfill
const initializeImportMetaPolyfill = () => {
  const needsPolyfill = (() => {
    try {
      new Function('return import.meta');
      return false;
    } catch (error) {
      return true;
    }
  })();

  if (needsPolyfill) {
    console.log('⚠️ 检测到需要 import.meta polyfill，立即初始化...');
    
    const isFileProtocol = location.protocol === 'file:';
    const baseUrl = isFileProtocol ? './' : '/';
    
    const importMeta = {
      url: window.location.href,
      env: {
        MODE: 'development',
        DEV: true,
        PROD: false,
        BASE_URL: baseUrl,
        VITE_WEBVIEW_BUILD: 'true',
        SSR: false,
        LEGACY: true
      },
      resolve: (path) => {
        if (path.startsWith('./') || path.startsWith('../')) {
          return new URL(path, window.location.href).href;
        }
        if (path.startsWith('/')) {
          return new URL(path, window.location.origin).href;
        }
        return path;
      }
    };

    // 存储到全局变量
    window.__vite_import_meta__ = importMeta;
    window.__import_meta_polyfill__ = importMeta;

    // 拦截 eval 和 Function
    const originalEval = window.eval;
    window.eval = function(code) {
      if (typeof code === 'string' && code.includes('import.meta')) {
        console.debug('🔧 eval中检测到import.meta使用，应用polyfill');
        code = code.replace(/import\.meta/g, 'window.__vite_import_meta__');
      }
      return originalEval.call(this, code);
    };

    const originalFunction = window.Function;
    window.Function = function(...args) {
      if (args.length > 0) {
        let code = args[args.length - 1];
        if (typeof code === 'string' && code.includes('import.meta')) {
          console.debug('🔧 Function构造函数中检测到import.meta使用，应用polyfill');
          code = code.replace(/import\.meta/g, 'window.__vite_import_meta__');
          args[args.length - 1] = code;
        }
      }
      return originalFunction.apply(this, args);
    };

    console.log('✅ import.meta polyfill 立即初始化完成');
  } else {
    console.log('✅ 环境原生支持 import.meta');
  }
};

// 立即执行polyfill初始化
initializeImportMetaPolyfill();

// 导入WebView兼容性工具 - 在所有其他导入之前
import { 
  initWebViewEnvironment, 
  isAndroid, 
  needsLegacySupport,
  getCompatibilityReport 
} from '@/utils/androidCompatibility'

// 导入polyfill测试工具
import { 
  testImportMetaPolyfill,
  getImportMeta 
} from '@/utils/importMetaPolyfill'

// 全局错误捕获
window.addEventListener('error', (event) => {
  console.error('❌ 全局错误:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error
  })
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ 未处理的Promise拒绝:', event.reason)
})

// 检查必要的API支持
console.log('📱 设备信息:', {
  userAgent: navigator.userAgent,
  viewport: {
    width: window.innerWidth,
    height: window.innerHeight
  },
  isAndroid: isAndroid(),
  needsLegacy: needsLegacySupport()
})

// 立即测试import.meta polyfill状态
console.log('�� 检查import.meta polyfill状态...')
try {
  const importMeta = getImportMeta()
  console.log('📋 当前import.meta对象:', importMeta)
} catch (error) {
  console.warn('⚠️ import.meta访问异常:', error.message)
}

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
// 引入全局评分标签样式
import './styles/score-tags.css'
// 引入全局Dialog统一样式
import './styles/dialog-common.css'

// 导入全局组件
import {
  PageHeader,
  LoadingWrapper,
  StatusTag,
  TableSkeleton,
  CardListSkeleton
} from '@/components'

// 应用初始化函数
async function initApp() {
  try {
    console.log('🔧 开始初始化应用...')
    
    // 首先进行WebView环境初始化
    const compatibilityReport = await initWebViewEnvironment()
    console.log('📊 兼容性报告:', compatibilityReport)
    
    // 验证import.meta polyfill是否正常工作
    if (compatibilityReport.needsImportMetaPolyfill) {
      setTimeout(() => {
        const testResult = testImportMetaPolyfill()
        console.log('🧪 import.meta polyfill测试结果:', testResult)
        
        if (!testResult.canAccess || !testResult.envAccess) {
          console.error('❌ import.meta polyfill未正常工作，应用可能无法启动')
        } else {
          console.log('✅ import.meta polyfill工作正常')
        }
      }, 300)
    }
    
    const app = createApp(App)

    // 注册所有图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }

    // 注册全局组件
    app.component('PageHeader', PageHeader)
    app.component('LoadingWrapper', LoadingWrapper)
    app.component('StatusTag', StatusTag)
    app.component('TableSkeleton', TableSkeleton)
    app.component('CardListSkeleton', CardListSkeleton)

    app.use(createPinia())
    app.use(ElementPlus)
    app.use(router)

    // 语音合成预热（需要用户交互才能激活）
    const initSpeechSynthesis = () => {
      if ('speechSynthesis' in window) {
        console.log('🔊 语音合成API可用，等待用户交互激活')

        // 等待语音列表加载
        const loadVoices = () => {
          const voices = speechSynthesis.getVoices()
          if (voices.length > 0) {
            console.log('🔊 可用语音数量:', voices.length)
            // 显示可用的中文语音
            const chineseVoices = voices.filter(voice =>
              voice.lang.includes('zh') || voice.lang.includes('Chinese')
            )
            console.log('🇨🇳 可用中文语音:', chineseVoices.map(v => v.name))
          } else {
            // 如果语音列表还没加载，等待一下再试
            setTimeout(loadVoices, 100)
          }
        }

        // 监听语音列表变化
        speechSynthesis.onvoiceschanged = loadVoices
        loadVoices()

        // 添加全局点击事件来激活语音合成（仅一次）
        let speechActivated = false
        const activateSpeech = () => {
          if (!speechActivated && 'speechSynthesis' in window) {
            speechActivated = true
            // 播放一个无声的语音来激活API
            const utterance = new SpeechSynthesisUtterance(' ')
            utterance.volume = 0.01
            utterance.rate = 10
            speechSynthesis.speak(utterance)
            console.log('🔊 语音合成已通过用户交互激活')
            document.removeEventListener('click', activateSpeech)
          }
        }
        document.addEventListener('click', activateSpeech, { once: true })
      }
    }

    // 开发环境下启用移动端测试工具
    if (getImportMeta().env.DEV) {
      try {
        const { initMobileTestingInDev } = await import('./utils/mobileTestHelper.js')
        initMobileTestingInDev()
      } catch (e) {
        console.warn('移动端测试工具加载失败:', e)
      }
    }

    // 应用挂载
    console.log('🔧 开始挂载Vue应用...')
    app.mount('#app')
    console.log('✅ Vue应用挂载成功')
    
    // 初始化语音合成
    initSpeechSynthesis()
    
    // 最终验证
    setTimeout(() => {
      if (window.__vite_import_meta__) {
        console.log('🎉 应用启动完成，import.meta polyfill工作正常')
      } else if (compatibilityReport.needsImportMetaPolyfill) {
        console.warn('⚠️ 应用已启动，但import.meta polyfill可能存在问题')
      }
    }, 1000)
    
    return app
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
    throw error
  }
}

// 启动应用
initApp().catch(error => {
  console.error('❌ 应用启动失败:', error)
  
  // 获取兼容性信息用于错误页面
  const compatInfo = {
    isAndroid: isAndroid(),
    needsLegacy: needsLegacySupport(),
    hasImportMetaPolyfill: !!window.__vite_import_meta__,
    userAgent: navigator.userAgent
  }
  
  // 创建一个简单的错误页面
  document.body.innerHTML = `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h1 style="color: red;">应用启动失败</h1>
      <p>错误信息: ${error.message}</p>
      <p>用户代理: ${navigator.userAgent}</p>
      <p>设备类型: ${compatInfo.isAndroid ? '安卓设备' : '非安卓设备'}</p>
      <p>需要Legacy支持: ${compatInfo.needsLegacy ? '是' : '否'}</p>
      <p>import.meta polyfill: ${compatInfo.hasImportMetaPolyfill ? '已加载' : '未加载'}</p>
      <p>协议: ${location.protocol}</p>
      <p>请尝试刷新页面或联系技术支持</p>
      
      <div style="margin: 20px 0;">
        <button onclick="location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; margin-right: 10px;">
          刷新页面
        </button>
        <button onclick="testImportMeta()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px;">
          测试import.meta
        </button>
      </div>
      
      <div id="test-results" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 4px;"></div>
    </div>
    
    <script>
      function testImportMeta() {
        const results = document.getElementById('test-results');
        results.innerHTML = '<h3>import.meta 测试结果:</h3>';
        
        const tests = [
          {
            name: '全局polyfill对象',
            test: () => !!window.__vite_import_meta__,
            value: () => window.__vite_import_meta__ ? JSON.stringify(window.__vite_import_meta__, null, 2) : 'undefined'
          },
          {
            name: 'eval测试',
            test: () => {
              try {
                return eval('window.__vite_import_meta__ && window.__vite_import_meta__.env.MODE') === 'development';
              } catch(e) {
                return false;
              }
            },
            value: () => {
              try {
                return eval('window.__vite_import_meta__ && window.__vite_import_meta__.env.MODE');
              } catch(e) {
                return e.message;
              }
            }
          }
        ];
        
        tests.forEach(test => {
          const passed = test.test();
          const value = test.value();
          results.innerHTML += \`
            <div style="margin: 10px 0; padding: 8px; border-radius: 4px; background: \${passed ? '#d4edda' : '#f8d7da'};">
              <strong>\${test.name}:</strong> \${passed ? '✅ 通过' : '❌ 失败'}<br>
              <small>值: \${value}</small>
            </div>
          \`;
        });
      }
    </script>
  `
})
