<template>
  <div class="quiz-result-container">
    <h1 class="result-title">考试结束!</h1>
        <p class="score">你的得分: {{ percentageScore }} 分</p>
    <button @click="restartQuiz" class="restart-button">再试一次</button>
    <button @click="backToList" class="back-button">返回列表</button>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { quizExamRecordsApi, EXAM_CATEGORIES, createExamRecordData } from '@/api/quizExamRecords';

const props = defineProps({
  score: Number,
  total: Number,
  quizType: String,
  examStartTime: Date
});

const emit = defineEmits(['restart']);

const router = useRouter();

const percentageScore = computed(() => {
  if (props.total === 0) {
    return 0;
  }
  return Math.floor((props.score / props.total) * 100);
});

const restartQuiz = () => {
  // 发射重试事件给父组件
  emit('restart');
};

const backToList = () => {
  router.push('/quiz-list');
};

// 保存考试记录
const saveExamRecord = async () => {
  try {
    // 计算真实的考试用时
    const durationSeconds = props.examStartTime 
      ? Math.floor((Date.now() - props.examStartTime.getTime()) / 1000)
      : 0; // 如果没有开始时间，默认为0
    
    const incorrectCount = props.total - props.score;
    
    // 根据quizType确定考试标题
    const getQuizTitle = (type) => {
      const titleMap = {
        'animals': '动物知识问答',
        'car-logos': '车标达人挑战',
        'country-flags': '国旗知识问答',
        'world-landmarks': '世界名胜古迹',
        'china-landmarks': '中国旅游景点',
        'world-flags-plus': '世界国旗Plus',
        'ethnicities': '世界民族文化',
        'city-regions': '城市属于哪个省份',
        'japanese-actresses': '日本女优知识',
        'military-equipment': '军事装备知识',
        'vehicles': '交通工具识别',
        'dog-cat-breeds': '猫狗品种识别',
        'houseplants': '家养植物品种问答',
        'home-appliances': '家用电器识别',
        'vegetables': '常见蔬菜识别',
        'fruits': '常见水果识别',
        'global-actresses': '世界各国最具代表的女明星',
        'global-actors': '世界各国最具代表的男明星',
        'world-foods': '世界各国著名食物',
        'plants': '植物知识识别', // 保留原有的plants映射
        'celestial_bodies': '宇宙天体',
        'livestock': '家禽和家畜识别',
        'beautiful_sceneries': '最美风景',
        'sports-stars': '体育明星识别',
        'general': '综合知识考试'
      };
      return titleMap[type] || '趣味考试';
    };
    
    const examData = createExamRecordData({
      category: EXAM_CATEGORIES.FUN_QUIZ,
      subcategory: null,
      title: getQuizTitle(props.quizType),
      totalQuestions: props.total,
      correctAnswers: props.score,
      durationSeconds: durationSeconds,
      details: {
        examType: 'fun_quiz',
        quizType: props.quizType,
        incorrectAnswers: incorrectCount
      }
    });
    
    await quizExamRecordsApi.addExamRecord(examData);
    console.log('✅ 趣味考试记录已保存');
  } catch (error) {
    console.error('❌ 保存趣味考试记录失败:', error);
    // 不显示错误消息，避免影响用户体验
  }
};

// 组件挂载时自动保存记录
onMounted(() => {
  saveExamRecord();
});
</script>

<style scoped>
.quiz-result-container {
  text-align: center;
  padding: 40px 20px;
}

.result-title {
  font-size: 2rem;
  margin-bottom: 20px;
}

.score {
  font-size: 1.5rem;
  margin-bottom: 30px;
}

.restart-button, .back-button {
  padding: 10px 20px;
  font-size: 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin: 0 10px;
}

.restart-button {
  background-color: #4caf50;
  color: white;
}

.back-button {
  background-color: #f0f0f0;
}
</style>