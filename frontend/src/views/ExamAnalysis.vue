<template>
  <div class="exam-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" link size="large">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1>考卷分析</h1>
          <p v-if="project">{{ project.name }}</p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：上传和分析 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>考卷上传</span>
              </div>
            </template>
            
            <!-- 文件上传 -->
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              drag
              multiple
              :auto-upload="false"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              accept="image/*"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将考卷图片拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持 jpg/png 格式图片，可上传多张考卷
                </div>
              </template>
            </el-upload>

            <!-- 分析按钮 -->
            <div class="analysis-actions">
              <el-button
                type="primary"
                size="large"
                :loading="analyzing"
                :disabled="fileList.length === 0"
                @click="analyzeExam"
              >
                <el-icon><MagicStick /></el-icon>
                {{ analyzing ? '分析中...' : '开始分析' }}
              </el-button>
              <el-button
                @click="clearFiles"
                :disabled="analyzing"
              >
                清空文件
              </el-button>
              <!-- 调试信息 -->
              <div class="debug-info" style="margin-left: 16px; color: #909399; font-size: 12px;">
                文件数量: {{ fileList.length }}
              </div>
            </div>

            <!-- 进度显示 -->
            <div v-if="analyzing" class="analysis-progress">
              <el-progress
                :percentage="Math.round(progress)"
                :status="progressStatus"
                :stroke-width="8"
                :format="(percentage) => `${percentage}%`"
              />
              <p class="progress-text">{{ progressText }}</p>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：分析结果 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>分析结果</span>
                <div class="header-actions">
                  <el-button
                    v-if="analysisResult"
                    link
                    @click="showConfigDialog = true"
                  >
                    查看完整配置
                  </el-button>
                  <el-button
                    link
                    :loading="clearingScores"
                    @click="clearAllScores"
                    style="color: #f56c6c;"
                  >
                    清空所有评分
                  </el-button>
                </div>
              </div>
            </template>

            <div v-if="!analysisResult && !analyzing" class="no-result">
              <el-empty description="暂无分析结果，请先上传考卷并进行分析" />
            </div>

            <div v-if="analysisResult" class="result-content">
              <div class="result-summary">
                <el-alert
                  :title="analysisResult.message"
                  type="success"
                  :closable="false"
                  show-icon
                />
              </div>

              <div class="scores-list">
                <h3>知识点掌握情况</h3>
                <div class="score-item" v-for="score in analysisResult.scores" :key="score.uid">
                  <div class="score-info">
                    <div class="score-name">{{ score.name }}</div>
                    <KnowledgePathDisplay
                      v-if="score.uid"
                      mode="single"
                      :project-id="projectId"
                      :uid="score.uid"
                      :path="score.path"
                      :max-length="40"
                      class="score-path"
                    />
                    <div v-else-if="score.path" class="score-path">{{ score.path }}</div>
                    <div class="score-description" v-if="score.description">
                      {{ score.description }}
                    </div>
                  </div>
                  <div class="score-value">
                    <el-rate
                      v-model="score.score"
                      :max="5"
                      disabled
                      show-score
                      text-color="#ff9900"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 完整配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="更新后的知识点配置"
      width="80%"
      :close-on-click-modal="false"
    >
      <LightweightJsonEditor
        v-if="analysisResult"
        :model-value="JSON.stringify(analysisResult.updatedConfiguration, null, 2)"
        :readonly="true"
        :height="500"
        :show-toolbar="false"
      />
      <template #footer>
        <el-button @click="showConfigDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowLeft, 
  UploadFilled, 
  MagicStick 
} from '@element-plus/icons-vue'
import { projectAPI } from '@/api'
import LightweightJsonEditor from '@/components/LightweightJsonEditor.vue'
import KnowledgePathDisplay from '@/components/knowledge/KnowledgePathDisplay.vue'
import { clearCacheAfterScoreUpdate } from '@/services/cacheService'
import { executeWithLoading } from '@/utils/apiUtils'
import { useErrorHandler } from '@/composables/useErrorHandler'

const router = useRouter()
const route = useRoute()

// 使用错误处理工具
const { handleAsyncError } = useErrorHandler()

// 响应式数据
const projectId = computed(() => parseInt(route.params.id))
const project = ref(null)
const fileList = ref([])
const analyzing = ref(false)
const progress = ref(0)
const progressStatus = ref('')
const progressText = ref('')
const analysisResult = ref(null)
const showConfigDialog = ref(false)
const uploadRef = ref()
const clearingScores = ref(false)
const loading = ref(false)

// 获取项目信息
const fetchProject = async () => {
  const response = await projectAPI.getProject(projectId.value)
  project.value = response.data.data
}

// 文件处理
const handleFileChange = (file, newFileList) => {
  console.log('📁 文件变化:', file.name, '新文件列表长度:', newFileList.length)
  fileList.value = newFileList
  console.log('📁 当前fileList长度:', fileList.value.length)
}

const handleFileRemove = (file, newFileList) => {
  console.log('🗑️ 移除文件:', file.name, '剩余文件:', newFileList.length)
  fileList.value = newFileList
  console.log('📁 当前fileList长度:', fileList.value.length)
}

const clearFiles = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
  analysisResult.value = null
}

// 考卷分析
const analyzeExam = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先上传考卷图片')
    return
  }

  try {
    analyzing.value = true
    progress.value = 0
    progressStatus.value = ''
    progressText.value = '准备上传文件...'

    // 创建FormData
    const formData = new FormData()
    fileList.value.forEach(file => {
      formData.append('files', file.raw)
    })

    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (progress.value < 90) {
        progress.value += Math.random() * 10
        if (progress.value < 30) {
          progressText.value = '上传考卷图片...'
        } else if (progress.value < 60) {
          progressText.value = 'AI正在分析考卷...'
        } else {
          progressText.value = '处理分析结果...'
        }
      }
    }, 1000)

    console.log('📊 开始考卷分析...')

    // 调用分析API
    const response = await fetch(`/api/projects/${projectId.value}/exam-analysis/analyze`, {
      method: 'POST',
      body: formData
    })

    clearInterval(progressInterval)

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error?.message || '分析失败')
    }

    const result = await response.json()

    if (result.success) {
      progress.value = 100
      progressStatus.value = 'success'
      progressText.value = '分析完成！'

      analysisResult.value = result.data

      console.log('✅ 考卷分析成功:', result.data)
      ElMessage.success('考卷分析完成！')

      // 显示结果摘要
      setTimeout(() => {
        ElMessageBox.alert(
          `成功分析了 ${fileList.value.length} 张考卷，更新了 ${result.data.scores.length} 个知识点的评分。`,
          '分析完成',
          {
            confirmButtonText: '查看详情',
            type: 'success'
          }
        )
      }, 1000)

    } else {
      throw new Error(result.error?.message || '分析失败')
    }

  } catch (error) {
    console.error('❌ 考卷分析失败:', error)
    progress.value = 0
    progressStatus.value = 'exception'
    progressText.value = '分析失败'
    ElMessage.error('考卷分析失败: ' + error.message)
  } finally {
    analyzing.value = false
  }
}

// 清空所有评分
const clearAllScores = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有知识点的评分吗？此操作不可撤销。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await executeWithLoading(async () => {
      console.log('🧹 开始清空所有评分...')

      const response = await fetch(`/api/projects/${projectId.value}/exam-analysis/clear-scores`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error?.message || '清空评分失败')
      }

      const result = await response.json()

      if (result.success) {
        console.log('✅ 成功清空所有评分')
        ElMessage.success('成功清空所有知识点评分')

        // 清空相关缓存
        await clearCacheAfterScoreUpdate(projectId.value)

        // 清空当前分析结果
        analysisResult.value = null
      } else {
        throw new Error(result.error?.message || '清空评分失败')
      }
    }, {
      loadingRef: clearingScores,
      errorMessage: '清空评分失败'
    })

  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 清空评分失败:', error)
    }
  }
}

// 返回上一页
const goBack = () => {
  router.push('/')
}

// 初始化页面数据
const initPageData = async () => {
  await executeWithLoading(async () => {
    await fetchProject()
  }, {
    loadingRef: loading,
    errorMessage: '页面初始化失败，请刷新重试'
  })
}

onMounted(() => {
  initPageData()
})
</script>

<style scoped>
.exam-analysis {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 4px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.main-content {
  margin-top: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.upload-demo {
  margin-bottom: 20px;
}

.analysis-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.analysis-progress {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.progress-text {
  margin-top: 8px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.no-result {
  padding: 40px 0;
}

.result-content {
  max-height: 600px;
  overflow-y: auto;
}

.result-summary {
  margin-bottom: 20px;
}

.scores-list h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.score-item:last-child {
  border-bottom: none;
}

.score-info {
  flex: 1;
}

.score-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.score-path {
  font-size: 12px;
  color: #909399;
  font-style: italic;
  margin-bottom: 4px;
}

.score-description {
  font-size: 12px;
  color: #606266;
}

.score-value {
  margin-left: 16px;
}
</style>
