<template>
  <div class="mobile-study">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">
        {{ isSwitchingProject ? '正在切换项目...' : '正在加载学习内容...' }}
      </div>
    </div>

    <!-- 项目信息 -->
    <div v-else-if="project" class="project-header">
      <!-- 项目切换器 -->
      <div class="project-switcher" v-if="!studySession.isActive">
        <label class="switcher-label">当前项目:</label>
        <el-select
          v-model="selectedProjectId"
          placeholder="切换项目"
          @change="switchProject"
          :loading="projectsLoading"
          class="project-select"
        >
          <el-option
            v-for="proj in allProjects"
            :key="proj.id"
            :label="proj.name"
            :value="proj.id">
          </el-option>
        </el-select>
      </div>

      <h1 class="project-title">{{ project.name }}</h1>
      <p class="project-desc" v-if="project.description">{{ project.description }}</p>
      <div class="study-progress" v-if="studySession.isActive">
        <div class="progress-text">
          第 {{ studySession.currentIndex + 1 }} 张 / 共 {{ studySession.cards.length }} 张
        </div>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: progressPercentage + '%' }"
          ></div>
        </div>
      </div>
      <div class="mobile-tip" v-if="!studySession.isActive">
        <span>📱 专为手机优化的学习体验</span>
      </div>
    </div>

    <!-- 学习配置 -->
    <div v-if="!studySession.isActive && flashcards.length > 0" class="study-config">
      <h2>开始学习</h2>
      
      <div class="config-item">
        <label>学习策略</label>
        <select v-model="studyConfig.strategy" class="mobile-select">
          <option value="random">随机</option>
          <option value="error_priority">错误率高优先</option>
          <option value="never_studied">未学过优先</option>
          <option value="high_difficulty">高难度优先</option>
          <option value="low_difficulty">简单难度优先</option>
          <option value="sequential">顺序学习</option>
        </select>
      </div>

      <div class="config-item">
        <label>学习数量</label>
        <input 
          type="number" 
          v-model="studyConfig.count" 
          :min="1" 
          :max="flashcards.length"
          class="mobile-input"
        />
        <span class="count-tip">（最多 {{ flashcards.length }} 张）</span>
      </div>

      <button @click="startStudy" class="start-button">
        开始学习 ({{ studyConfig.count }} 张卡片)
      </button>
    </div>

    <!-- 学习卡片 -->
    <div v-if="studySession.isActive && currentCard" class="study-card">
      <!-- 卡片内容 -->
      <div class="card-container" :class="`difficulty-${currentCard.difficulty}`">
        <!-- 正面 -->
        <div v-if="!showBack" class="card-face front">
          <div class="face-header">
            <span class="face-title">正面</span>
          </div>
          <div class="face-content">
            <MobileRichTextViewer :content="currentCard.frontContent" />
          </div>

          <!-- 有背面内容时显示查看答案按钮 -->
          <button v-if="hasBackContent" @click="showBack = true" class="show-answer-btn">
            查看答案
          </button>

          <!-- 没有背面内容时直接显示学习反馈按钮 -->
          <div v-else class="feedback-buttons">
            <button @click="submitResult(true)" class="feedback-btn correct">
              ✓ 答对了
            </button>
            <button @click="submitResult(false)" class="feedback-btn wrong">
              ✗ 答错了
            </button>
            <button @click="skipCard" class="feedback-btn skip">
              → 跳过
            </button>
          </div>
        </div>

        <!-- 背面 -->
        <div v-else-if="hasBackContent" class="card-face back">
          <div class="face-header">
            <span class="face-title">背面</span>
            <div class="header-actions">
              <button @click="showBack = false" class="back-btn">← 返回正面</button>
              <button @click="speakContent('back')" class="speak-btn">🔊</button>
            </div>
          </div>
          <div class="face-content">
            <MobileRichTextViewer :content="currentCard.backContent" />
          </div>

          <!-- 学习反馈 -->
          <div class="feedback-buttons">
            <button @click="submitResult(true)" class="feedback-btn correct">
              ✓ 答对了
            </button>
            <button @click="submitResult(false)" class="feedback-btn wrong">
              ✗ 答错了
            </button>
            <button @click="skipCard" class="feedback-btn skip">
              → 跳过
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习完成 -->
    <div v-if="studyCompleted" class="study-complete">
      <div class="complete-icon">🎉</div>
      <h2>学习完成！</h2>
      <div class="study-stats">
        <div class="stat-item">
          <span class="stat-number">{{ studySession.cards.length }}</span>
          <span class="stat-label">张卡片</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ correctCount }}</span>
          <span class="stat-label">答对</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ wrongCount }}</span>
          <span class="stat-label">答错</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ skipCount }}</span>
          <span class="stat-label">跳过</span>
        </div>
      </div>
      <button @click="restartStudy" class="restart-btn">
        再次学习
      </button>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <div class="error-icon">❌</div>
      <h2>加载失败</h2>
      <p>{{ error }}</p>
      <button @click="retry" class="retry-btn">重试</button>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && flashcards.length === 0 && !error" class="empty-container">
      <div class="empty-icon">📚</div>
      <h2>暂无卡片</h2>
      <p>该项目还没有创建任何学习卡片</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { projectAPI } from '@/api'
import { flashcardAPI } from '@/api/flashcard'
import { quizExamRecordsApi, EXAM_CATEGORIES, createExamRecordData } from '@/api/quizExamRecords'
import { isSpeechSynthesisSupported, speakHtmlContent } from '@/utils/speech'
import { executeWithLoading, executeParallel } from '@/utils/apiUtils'
import MobileRichTextViewer from '@/components/MobileRichTextViewer.vue'

const route = useRoute()
const router = useRouter()

// 响应式状态
const loading = ref(false)
const projectsLoading = ref(false)
const error = ref('')
const allProjects = ref([])
const project = ref(null)
const flashcards = ref([])
const selectedProjectId = ref(null)
const isSwitchingProject = ref(false)

// 学习状态
const studySession = reactive({
  isActive: false,
  cards: [],
  currentIndex: 0,
  results: [],
  startTime: null
})

const showBack = ref(false)
const studyCompleted = ref(false)

// 学习配置
const studyConfig = reactive({
  count: 20,
  strategy: 'random' // random, sequential, error_priority, never_studied, high_difficulty, low_difficulty
})

// 计算属性
const currentCard = computed(() => {
  if (studySession.isActive && studySession.currentIndex < studySession.cards.length) {
    return studySession.cards[studySession.currentIndex]
  }
  return null
})

const progressPercentage = computed(() => {
  if (studySession.cards.length === 0) return 0
  return Math.round((studySession.currentIndex / studySession.cards.length) * 100)
})

const correctCount = computed(() => {
  return studySession.results.filter(r => r === 'correct').length
})

const wrongCount = computed(() => {
  return studySession.results.filter(r => r === 'wrong').length
})

const skipCount = computed(() => {
  return studySession.results.filter(r => r === 'skip').length
})

// 判断当前卡片是否有背面内容
const hasBackContent = computed(() => {
  return currentCard.value?.backContent &&
         currentCard.value.backContent.trim() !== '' &&
         currentCard.value.backContent !== '<p></p>' &&
         currentCard.value.backContent !== '<p><br></p>'
})

// 方法
const loadAllProjects = async () => {
  const response = await projectAPI.getProjects()
  allProjects.value = response.data.data || []
  console.log('✅ 项目列表加载完成:', allProjects.value.length, '个项目')
}

const loadData = async () => {
  const projectId = route.params.projectId
  console.log('🔄 移动端开始加载数据，项目ID:', projectId)

  // 设置当前选中的项目ID
  selectedProjectId.value = parseInt(projectId)

  await executeWithLoading(async () => {
    // 并行加载项目信息、卡片数据和项目列表
    const [projectResponse, flashcardsResponse] = await Promise.all([
      projectAPI.getProject(projectId),
      flashcardAPI.getFlashcards(projectId),
      executeWithLoading(loadAllProjects, {
        loadingRef: projectsLoading,
        showMessage: false
      }) // 异步加载项目列表，不阻塞主要数据加载
    ])

    console.log('📦 项目API响应:', projectResponse)
    console.log('📦 卡片API响应:', flashcardsResponse)

    project.value = projectResponse.data
    flashcards.value = Array.isArray(flashcardsResponse.data) ? flashcardsResponse.data : []

    console.log('✅ 项目数据加载完成:', {
      project: project.value,
      projectId: project.value?.id,
      projectName: project.value?.name,
      flashcardsCount: flashcards.value.length
    })

    // 设置默认学习数量
    studyConfig.count = Math.min(20, flashcards.value.length)
  }, {
    loadingRef: loading,
    errorRef: error,
    errorMessage: '加载失败，请检查网络连接或项目是否存在',
    finally: () => {
      isSwitchingProject.value = false
    }
  })
}

const startStudy = () => {
  const studyCards = getStudyCards()
  
  if (studyCards.length === 0) {
    alert('没有找到符合条件的卡片')
    return
  }
  
  studySession.cards = studyCards
  studySession.currentIndex = 0
  studySession.isActive = true
  studySession.results = []
  studySession.startTime = new Date()
  studyCompleted.value = false
  showBack.value = false
}

const getStudyCards = () => {
  let cards = [...flashcards.value]
  const maxCount = Math.min(studyConfig.count, cards.length)

  switch (studyConfig.strategy) {
    case 'random':
      // 随机打乱
      cards = shuffleArray(cards)
      break

    case 'sequential':
      // 按创建时间顺序
      cards.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
      break

    case 'error_priority':
      // 错误率高优先：按正确率升序排列（正确率低的在前面）
      cards.sort((a, b) => {
        const aRate = a.accuracyRate || 0
        const bRate = b.accuracyRate || 0
        return aRate - bRate
      })
      break

    case 'never_studied':
      // 未学过优先：学习次数为0的在前面，然后按学习次数升序
      cards.sort((a, b) => {
        const aCount = a.totalStudyCount || 0
        const bCount = b.totalStudyCount || 0
        if (aCount === 0 && bCount === 0) return 0
        if (aCount === 0) return -1
        if (bCount === 0) return 1
        return aCount - bCount
      })
      break

    case 'high_difficulty':
      // 高难度优先：按难度降序排列（难度高的在前面）
      cards.sort((a, b) => (b.difficulty || 1) - (a.difficulty || 1))
      break

    case 'low_difficulty':
      // 简单难度优先：按难度升序排列（难度低的在前面）
      cards.sort((a, b) => (a.difficulty || 1) - (b.difficulty || 1))
      break

    default:
      // 默认随机
      cards = shuffleArray(cards)
      break
  }

  return cards.slice(0, maxCount)
}

const shuffleArray = (array) => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

const submitResult = (isCorrect) => {
  console.log('🔄 移动端开始提交学习结果:', isCorrect)

  // 记录结果到本地会话
  studySession.results.push(isCorrect ? 'correct' : 'wrong')

  // 详细检查当前状态
  console.log('📋 当前卡片信息:', {
    currentCard: currentCard.value,
    id: currentCard.value?.id,
    title: currentCard.value?.title,
    project: project.value,
    projectId: project.value?.id,
    projectName: project.value?.name
  })

  // 验证必要数据
  if (!currentCard.value) {
    console.error('❌ 当前卡片为空，无法保存学习记录')
    nextCard()
    return
  }

  if (!currentCard.value.id) {
    console.error('❌ 当前卡片ID为空，无法保存学习记录')
    nextCard()
    return
  }

  // 获取项目ID，优先使用project.value.id，备用route.params.projectId
  const projectId = project.value?.id || route.params.projectId

  if (!projectId) {
    console.error('❌ 项目ID为空，无法保存学习记录', {
      projectValue: project.value,
      routeParams: route.params
    })
    nextCard()
    return
  }

  if (isCorrect === undefined || isCorrect === null) {
    console.error('❌ isCorrect参数无效，无法保存学习记录')
    nextCard()
    return
  }

  // 保存当前卡片信息，避免在nextCard()后丢失
  const currentCardData = {
    flashcardId: currentCard.value.id,
    projectId: projectId, // 使用上面获取的projectId
    isCorrect: isCorrect
  }

  console.log('📝 移动端准备保存的数据:', currentCardData)
  console.log('🔍 数据验证:', {
    flashcardId: currentCardData.flashcardId,
    flashcardIdType: typeof currentCardData.flashcardId,
    projectId: currentCardData.projectId,
    projectIdType: typeof currentCardData.projectId,
    isCorrect: currentCardData.isCorrect,
    isCorrectType: typeof currentCardData.isCorrect
  })

  // 立即跳转到下一张卡片
  nextCard()

  // 异步提交到服务器，不阻塞用户操作
  console.log('🚀 移动端开始调用API保存学习记录...')
  flashcardAPI.recordStudyResult(currentCardData)
    .then((response) => {
      console.log('✅ 移动端学习记录保存成功:', response)
    })
    .catch((error) => {
      console.error('❌ 移动端保存学习记录失败:', error)
      console.error('❌ 错误详情:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config
      })
      // 静默失败，不影响用户体验
    })
}

const skipCard = () => {
  studySession.results.push('skip')
  nextCard()
}

const nextCard = () => {
  showBack.value = false
  studySession.currentIndex++
  
  if (studySession.currentIndex >= studySession.cards.length) {
    // 学习完成
    studySession.isActive = false
    studyCompleted.value = true
    
    // 保存卡片考试汇总记录到quiz_exam_records表
    saveFlashcardExamRecord()
  }
}

const restartStudy = () => {
  studySession.isActive = false
  studyCompleted.value = false
  showBack.value = false
}

// 保存卡片考试汇总记录
const saveFlashcardExamRecord = async () => {
  if (!studySession.startTime || studySession.results.length === 0) return
  
  try {
    const endTime = new Date()
    const durationSeconds = Math.floor((endTime - studySession.startTime) / 1000)
    const totalQuestions = studySession.results.length
    const correctAnswers = correctCount.value
    const incorrectAnswers = wrongCount.value
    
    const examData = createExamRecordData({
      category: EXAM_CATEGORIES.FLASHCARD,
      subcategory: null,
      title: `${project.value?.name || '项目'}卡片学习`,
      totalQuestions: totalQuestions,
      correctAnswers: correctAnswers,
      durationSeconds: durationSeconds,
      details: {
        examType: 'flashcard',
        projectId: project.value?.id,
        projectName: project.value?.name,
        strategy: studyConfig.strategy,
        incorrectAnswers: incorrectAnswers,
        skipCount: skipCount.value,
        studiedCards: studySession.cards.length
      }
    })
    
    await quizExamRecordsApi.addExamRecord(examData)
    console.log('✅ 卡片考试汇总记录已保存')
  } catch (error) {
    console.error('❌ 保存卡片考试汇总记录失败:', error)
    // 静默失败，不影响用户体验
  }
}

const speakContent = async (side) => {
  if (!isSpeechSynthesisSupported()) {
    alert('您的浏览器不支持语音功能')
    return
  }
  
  try {
    const content = side === 'front' ? currentCard.value.frontContent : currentCard.value.backContent
    await speakHtmlContent(content)
  } catch (error) {
    console.error('朗读失败:', error)
  }
}

const switchProject = async () => {
  if (!selectedProjectId.value || selectedProjectId.value === project.value?.id) {
    return
  }

  console.log('🔄 切换项目:', selectedProjectId.value)

  // 设置切换状态
  isSwitchingProject.value = true

  // 重置学习状态
  studySession.isActive = false
  studyCompleted.value = false
  showBack.value = false
  studySession.cards = []
  studySession.currentIndex = 0
  studySession.results = []

  try {
    // 使用路由跳转到新项目
    await router.push(`/mobile/study/${selectedProjectId.value}`)
  } catch (err) {
    console.error('❌ 项目切换失败:', err)
    error.value = '项目切换失败，请重试'
    loading.value = false
    isSwitchingProject.value = false
  }
}

const retry = () => {
  loadData()
}

// 监听路由参数变化
watch(() => route.params.projectId, async (newProjectId, oldProjectId) => {
  if (newProjectId && newProjectId !== oldProjectId) {
    console.log('🔄 路由参数变化，重新加载数据:', { oldProjectId, newProjectId })

    await executeWithLoading(async () => {
      // 重置所有状态
      studySession.isActive = false
      studyCompleted.value = false
      showBack.value = false
      studySession.cards = []
      studySession.currentIndex = 0
      studySession.results = []
      error.value = ''

      // 重新加载数据
      await loadData()
    }, {
      loadingRef: loading,
      errorMessage: '重新加载数据失败'
    })
  }
}, { immediate: false })

onMounted(async () => {
  const projectId = route.params.projectId;
  if (projectId) {
    await loadData();
  } else {
    // No project ID in URL, fetch all and redirect
    await executeWithLoading(async () => {
      const response = await projectAPI.getProjects();
      allProjects.value = response.data.data || [];
      if (allProjects.value.length > 0) {
        const firstProjectId = allProjects.value[0].id;
        router.replace(`/mobile/study/${firstProjectId}`);
        // The watch on route params will trigger loadData
      } else {
        // No projects exist
        project.value = null;
        flashcards.value = [];
        error.value = '您还没有创建任何项目。';
      }
    }, {
      loadingRef: loading,
      errorMessage: '加载项目列表失败，请重试'
    })
  }
});
</script>

<style scoped>
.mobile-study {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  opacity: 0.9;
}

/* 项目头部 */
.project-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 项目切换器 */
.project-switcher {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  gap: 8px;
}

.switcher-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

.project-select {
  flex: 1;
  max-width: 200px;
  padding: 8px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.project-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.project-select:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.project-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.project-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.study-progress {
  margin-top: 16px;
}

.progress-text {
  font-size: 14px;
  color: #374151;
  margin-bottom: 8px;
  font-weight: 600;
}

.progress-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.mobile-tip {
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  font-size: 12px;
  color: #3b82f6;
  text-align: center;
}

/* 学习配置 */
.study-config {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden; /* 防止内容溢出 */
}

.study-config h2 {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 20px 0;
  text-align: center;
}

.config-item {
  margin-bottom: 20px;
}

.config-item label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.mobile-select,
.mobile-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  transition: border-color 0.2s ease;
  box-sizing: border-box; /* 确保padding和border包含在width内 */
}

.mobile-select:focus,
.mobile-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.count-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  display: block;
}

.start-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s ease;
  margin-top: 8px;
}

.start-button:active {
  transform: scale(0.98);
}

/* 学习卡片 */
.study-card {
  margin-bottom: 20px;
}

.card-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 3px solid #e5e7eb;
}

/* 难度配色 */
.card-container.difficulty-1 { border-color: #4CAF50; }
.card-container.difficulty-2 { border-color: #2196F3; }
.card-container.difficulty-3 { border-color: #9C27B0; }
.card-container.difficulty-4 { border-color: #FF9800; }
.card-container.difficulty-5 { border-color: #F44336; }

.card-face {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px); /* 适应屏幕高度，留出空间给其他元素 */
  min-height: 400px; /* 最小高度确保内容可见 */
  max-height: 600px; /* 最大高度防止过高 */
}

.face-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.face-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-btn {
  background: #6b7280;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.back-btn:active {
  background: #4b5563;
}

.speak-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.speak-btn:active {
  background: rgba(0, 0, 0, 0.1);
}

.face-content {
  flex: 1;
  padding: 24px 20px;
  font-size: 16px;
  line-height: 1.6;
  color: #1f2937;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 防止水平溢出 */
  word-wrap: break-word; /* 长单词换行 */
}

.show-answer-btn {
  margin: 0 20px 20px 20px;
  padding: 14px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.show-answer-btn:active {
  background: #1d4ed8;
}

/* 反馈按钮 */
.feedback-buttons {
  display: flex;
  gap: 8px;
  padding: 20px;
  flex-wrap: wrap;
  flex-shrink: 0; /* 防止按钮区域被压缩 */
}

.feedback-btn {
  flex: 1;
  min-width: 100px;
  padding: 14px 8px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.feedback-btn.correct {
  background: #10b981;
  color: white;
}

.feedback-btn.wrong {
  background: #ef4444;
  color: white;
}

.feedback-btn.skip {
  background: #6b7280;
  color: white;
}

.feedback-btn:active {
  transform: scale(0.95);
}

/* 学习完成 */
.study-complete {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.complete-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.study-complete h2 {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 24px 0;
}

.study-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24px;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.restart-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.restart-btn:active {
  transform: scale(0.98);
}

/* 错误和空状态 */
.error-container,
.empty-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 40px 24px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.error-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-container h2,
.empty-container h2 {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.error-container p,
.empty-container p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.retry-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-btn:active {
  background: #1d4ed8;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .mobile-study {
    padding: 12px;
  }

  .study-config {
    padding: 20px;
  }

  .mobile-select,
  .mobile-input {
    padding: 10px 14px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .card-face {
    height: calc(100vh - 180px); /* 小屏幕适应 */
    min-height: 350px;
    max-height: 500px;
  }

  .face-content {
    padding: 20px 16px;
    font-size: 15px;
  }

  .feedback-buttons {
    flex-direction: column;
  }

  .feedback-btn {
    flex: none;
    width: 100%;
  }

  .study-stats {
    flex-wrap: wrap;
    gap: 12px;
  }

  .stat-item {
    flex: 1;
    min-width: 60px;
  }
}

/* 更小屏幕的额外优化 */
@media (max-width: 360px) {
  .study-config {
    padding: 16px;
  }

  .mobile-select,
  .mobile-input {
    padding: 8px 12px;
  }

  .card-face {
    height: calc(100vh - 160px); /* 超小屏幕适应 */
    min-height: 320px;
    max-height: 450px;
  }

  .face-content {
    padding: 16px 12px;
    font-size: 14px;
  }

  .card-container {
    border-width: 2px;
  }
}
</style>
