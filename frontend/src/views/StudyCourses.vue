<template>
  <div class="study-courses">
    <PageHeader title="学习课程" />
    
    
    <div class="courses-container">
      <div class="course-category" v-for="category in courseCategories" :key="category.id">
        <div class="category-header">
          <h2>{{ category.name }}</h2>
          <p class="category-description">{{ category.description }}</p>
        </div>
        
        <div class="course-grid">
          <div 
            class="course-card" 
            v-for="course in category.courses" 
            :key="course.id"
            @click="goToCourse(course)"
          >
            <div class="course-header">
              <div class="course-icon">
                <el-icon><component :is="getCourseIcon(course)" /></el-icon>
              </div>
              <div class="course-title">
                <h3>{{ course.name }}</h3>
                <div class="course-stats">
                  <span class="chapter-count">{{ course.chapters.length }} 章节</span>
                </div>
              </div>
            </div>
            <p class="course-description">{{ course.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Reading, 
  DataAnalysis,
  ChatDotRound, 
  EditPen,
  Tools,
  School,
  Document
} from '@element-plus/icons-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { getAllGrades, getSubjectsByGrade, getCoursesBySubject } from '@/data/courses.js'

const router = useRouter()

// 课程分类数据
const courseCategories = computed(() => {
  const categories = []
  const grades = getAllGrades()
  
  grades.forEach(grade => {
    const subjects = getSubjectsByGrade(grade.id)
    const allCourses = []
    
    // 收集该学段下所有科目的课程
    subjects.forEach(subject => {
      const courses = getCoursesBySubject(grade.id, subject.id)
      allCourses.push(...courses)
    })
    
    if (allCourses.length > 0) {
      categories.push({
        id: grade.id,
        name: grade.name,
        description: grade.description,
        courses: allCourses
      })
    }
  })
  
  return categories
})

// 根据课程内容获取相应图标
const getCourseIcon = (course) => {
  const courseName = course.name.toLowerCase()
  const description = course.description.toLowerCase()
  
  // 根据课程名称和描述判断学科类型
  if (courseName.includes('数学') || description.includes('数学') || 
      courseName.includes('算术') || description.includes('计算')) {
    return DataAnalysis
  } else if (courseName.includes('英语') || description.includes('英语') || 
             courseName.includes('english') || description.includes('字母')) {
    return ChatDotRound
  } else if (courseName.includes('语文') || description.includes('语文') || 
             description.includes('拼音') || description.includes('汉字')) {
    return EditPen
  } else if (courseName.includes('科学') || description.includes('科学') || 
             description.includes('常识') || description.includes('自然')) {
    return Tools
  } else if (courseName.includes('幼升小') || description.includes('幼升小')) {
    return School
  } else {
    return Reading
  }
}

const goToCourse = (course) => {
  router.push(`/courses/${course.id}`)
}
</script>

<style scoped>
.study-courses {
  max-width: 1200px;
  margin: 0 auto;
}

.courses-container {
  margin-top: 20px;
}

.course-category {
  margin-bottom: 32px;
}

.category-header {
  margin-bottom: 16px;
}

.category-header h2 {
  color: #303133;
  font-size: 24px;
  margin: 0 0 8px 0;
}

.category-description {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.course-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  min-height: 120px;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.course-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.course-icon {
  width: 42px;
  height: 42px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.course-icon .el-icon {
  font-size: 20px;
  color: white;
}

.course-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.course-card h3 {
  color: #303133;
  font-size: 18px;
  margin: 0 0 6px 0;
  line-height: 1.3;
  font-weight: 600;
}

.course-stats {
  display: flex;
  align-items: center;
}

.chapter-count {
  color: #909399;
  font-size: 12px;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.course-description {
  color: #606266;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 为不同学科设置不同的图标背景色 */
.course-card:nth-child(4n+1) .course-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 数学 - 蓝紫 */
}

.course-card:nth-child(4n+2) .course-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); /* 英语 - 粉红 */
}

.course-card:nth-child(4n+3) .course-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); /* 语文 - 青蓝 */
}

.course-card:nth-child(4n+4) .course-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); /* 科学 - 绿青 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-grid {
    grid-template-columns: 1fr;
  }
  
  .course-card {
    min-height: 100px;
    padding: 16px;
  }
  
  .course-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }
  
  .course-icon .el-icon {
    font-size: 20px;
  }
  
  .course-card h3 {
    font-size: 16px;
  }
}
</style>