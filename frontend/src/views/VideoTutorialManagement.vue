<template>
  <div class="video-tutorial-management">
    <!-- 页面头部 -->
    <PageHeader
      title="视频教程管理"
      subtitle="管理所有项目的视频教程"
      :show-back-button="false"
    >
      <template #actions>
        <el-button @click="refreshList" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </template>
    </PageHeader>

    <!-- 主要内容区域 -->
    <div class="content-area" v-loading="loading">
      <!-- 统计信息 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ tutorialList.length }}</div>
              <div class="stat-label">总教程数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ uniqueProjects }}</div>
              <div class="stat-label">涉及项目</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ uniqueKnowledgePoints }}</div>
              <div class="stat-label">知识点数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ recentTutorials }}</div>
              <div class="stat-label">本周新增</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索和筛选 -->
      <el-card class="filter-card">
        <div class="filter-container">
          <div class="filter-left">
            <el-select
              v-model="selectedProjectId"
              placeholder="选择项目"
              clearable
              style="width: 200px"
              @change="handleProjectChange"
            >
              <el-option
                v-for="project in projectOptions"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
            
            <el-input
              v-model="searchKeyword"
              placeholder="搜索知识点名称或路径..."
              clearable
              style="width: 300px"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <div class="filter-actions">
            <el-button @click="clearFilters">清空筛选</el-button>
            <el-button type="danger" @click="batchDelete" :disabled="selectedTutorials.length === 0">
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedTutorials.length }})
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 教程列表 -->
      <el-card class="tutorial-list-card">
        <template #header>
          <div class="card-header">
            <span>教程列表</span>
            <span class="tutorial-count">{{ filteredTutorials.length }} 个教程</span>
          </div>
        </template>

        <div v-if="filteredTutorials.length === 0" class="empty-state">
          <el-empty description="暂无视频教程" />
        </div>

        <el-table
          v-else
          :data="filteredTutorials"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="项目" width="150">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ getProjectName(row.projectId) }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="知识点" min-width="200">
            <template #default="{ row }">
              <div class="knowledge-point-cell">
                <div class="point-name">{{ row.knowledgePointName }}</div>
                <KnowledgePathDisplay
                  mode="single"
                  :project-id="row.projectId"
                  :knowledge-point-name="row.knowledgePointName"
                  :max-length="30"
                  class="point-path"
                />
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="描述" min-width="250" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.knowledgePointDescription || '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="创建时间" width="180" align="center">
            <template #default="{ row }">
              {{ formatDate(row.createdAt, "datetime") }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="380" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewTutorial(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button size="small" @click="regenerateTutorial(row)">
                <el-icon><Refresh /></el-icon>
                重新生成
              </el-button>
              <el-button size="small" @click="copyTutorialContent(row)">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
              <el-button type="danger" size="small" @click="deleteTutorial(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container" v-if="filteredTutorials.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredTutorials.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 教程详情对话框 -->
    <VideoTutorialDialog
      v-model="showTutorialDialog"
      :project-id="selectedTutorial?.projectId"
      :knowledge-point="selectedTutorial"
      :project-name="selectedTutorial?.projectName"
      :project-description="selectedTutorial?.projectDescription"
      @saved="onTutorialSaved"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, Search, Delete, View, DocumentCopy 
} from '@element-plus/icons-vue'
import { videoTutorialAPI, projectAPI } from '@/api'
import { useLoading } from '@/composables/useLoading'
import { useErrorHandler } from '@/composables/useErrorHandler'
import PageHeader from '@/components/common/PageHeader.vue'
import VideoTutorialDialog from '@/components/VideoTutorialDialog.vue'
import KnowledgePathDisplay from '@/components/knowledge/KnowledgePathDisplay.vue'

// 使用 Composables
const { loading, execute } = useLoading()
const { confirmDelete } = useErrorHandler()
import { formatDate } from '@/utils/environment'

// 响应式数据
const tutorialList = ref([])
const projectList = ref([])
const selectedProjectId = ref('')
const searchKeyword = ref('')
const showTutorialDialog = ref(false)
const selectedTutorial = ref(null)
const selectedTutorials = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const projectOptions = computed(() => {
  const uniqueProjects = new Map()
  tutorialList.value.forEach(tutorial => {
    if (!uniqueProjects.has(tutorial.projectId)) {
      const project = projectList.value.find(p => p.id === tutorial.projectId)
      if (project) {
        uniqueProjects.set(tutorial.projectId, project)
      }
    }
  })
  return Array.from(uniqueProjects.values())
})

const filteredTutorials = computed(() => {
  let filtered = tutorialList.value

  // 按项目筛选
  if (selectedProjectId.value) {
    filtered = filtered.filter(tutorial => tutorial.projectId === selectedProjectId.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(tutorial => 
      tutorial.knowledgePointName.toLowerCase().includes(keyword) ||
      tutorial.knowledgePointPath.toLowerCase().includes(keyword) ||
      (tutorial.knowledgePointDescription && tutorial.knowledgePointDescription.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

const uniqueProjects = computed(() => {
  const projectIds = new Set(tutorialList.value.map(t => t.projectId))
  return projectIds.size
})

const uniqueKnowledgePoints = computed(() => {
  const uniqueNames = new Set(tutorialList.value.map(t => t.knowledgePointName))
  return uniqueNames.size
})

const recentTutorials = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  
  return tutorialList.value.filter(tutorial => 
    new Date(tutorial.createdAt) > oneWeekAgo
  ).length
})

// 方法
const loadAllTutorials = async () => {
  await execute(async () => {
    console.log('🔄 开始加载所有视频教程...')
    
    // 先加载项目列表
    const projectResponse = await projectAPI.getProjects()
    projectList.value = projectResponse.data.data || []
    
    // 加载所有项目的视频教程
    const allTutorials = []
    for (const project of projectList.value) {
      try {
        const response = await videoTutorialAPI.getVideoTutorials(project.id)
        const tutorials = response.data.data || []
        allTutorials.push(...tutorials)
      } catch (error) {
        console.warn(`加载项目 ${project.name} 的视频教程失败:`, error)
      }
    }
    
    tutorialList.value = allTutorials.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    console.log(`✅ 视频教程列表加载成功，共 ${tutorialList.value.length} 个教程`)
  })
}

const refreshList = () => {
  loadAllTutorials()
}

const handleProjectChange = () => {
  currentPage.value = 1
}

const handleSearch = () => {
  currentPage.value = 1
}

const clearFilters = () => {
  selectedProjectId.value = ''
  searchKeyword.value = ''
  currentPage.value = 1
}

const handleSelectionChange = (selection) => {
  selectedTutorials.value = selection
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const getProjectName = (projectId) => {
  const project = projectList.value.find(p => p.id === projectId)
  return project ? project.name : `项目${projectId}`
}

const viewTutorial = (tutorial) => {
  console.log('查看教程:', tutorial)

  // 查找对应的项目信息
  const project = projectList.value.find(p => p.id === tutorial.projectId)

  // 创建一个简单的对象，避免响应式问题
  selectedTutorial.value = {
    name: tutorial.knowledgePointName,
    fullPath: tutorial.knowledgePointPath,
    description: tutorial.knowledgePointDescription || '',
    existingTutorial: {
      id: tutorial.id,
      tutorialContent: tutorial.tutorialContent,
      knowledgePointName: tutorial.knowledgePointName,
      knowledgePointPath: tutorial.knowledgePointPath,
      knowledgePointDescription: tutorial.knowledgePointDescription,
      projectId: tutorial.projectId,
      createdAt: tutorial.createdAt,
      updatedAt: tutorial.updatedAt
    },
    projectId: tutorial.projectId,
    projectName: project?.name || '未知项目',
    projectDescription: project?.description || '暂无描述'
  }

  showTutorialDialog.value = true
}

const copyTutorialContent = async (tutorial) => {
  try {
    await navigator.clipboard.writeText(tutorial.tutorialContent)
    ElMessage.success('教程内容已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  }
}

const regenerateTutorial = async (tutorial) => {
  try {
    const confirmed = await ElMessageBox.confirm(
      `确定要重新生成"${tutorial.knowledgePointName}"的视频教程吗？这将覆盖现有内容。`,
      '重新生成确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (confirmed) {
      // 先删除现有教程
      await deleteTutorialById(tutorial.id, false)

      // 查找对应的项目信息
      const project = projectList.value.find(p => p.id === tutorial.projectId)

      // 重新生成教程
      selectedTutorial.value = {
        name: tutorial.knowledgePointName,
        fullPath: tutorial.knowledgePointPath,
        description: tutorial.knowledgePointDescription,
        projectId: tutorial.projectId,
        projectName: project?.name || '未知项目',
        projectDescription: project?.description || '暂无描述'
      }
      showTutorialDialog.value = true
      ElMessage.success('开始重新生成视频教程')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新生成教程失败:', error)
      ElMessage.error('重新生成教程失败')
    }
  }
}

const deleteTutorial = async (tutorial) => {
  try {
    const confirmed = await confirmDelete(
      `视频教程"${tutorial.knowledgePointName}"`,
      '删除后无法恢复，请谨慎操作！'
    )

    if (confirmed) {
      await deleteTutorialById(tutorial.id, true)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除教程失败:', error)
      ElMessage.error('删除教程失败')
    }
  }
}

const deleteTutorialById = async (tutorialId, showMessage = true) => {
  await execute(async () => {
    console.log('🗑️ 开始删除视频教程:', tutorialId)
    await videoTutorialAPI.deleteTutorial(tutorialId)

    // 从列表中移除
    const index = tutorialList.value.findIndex(t => t.id === tutorialId)
    if (index > -1) {
      tutorialList.value.splice(index, 1)
    }

    if (showMessage) {
      ElMessage.success('视频教程删除成功')
    }
    console.log('✅ 视频教程删除成功')
  })
}

const batchDelete = async () => {
  if (selectedTutorials.value.length === 0) {
    ElMessage.warning('请先选择要删除的教程')
    return
  }

  try {
    const confirmed = await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedTutorials.value.length} 个视频教程吗？删除后无法恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (confirmed) {
      await execute(async () => {
        console.log('🗑️ 开始批量删除视频教程:', selectedTutorials.value.length)

        const deletePromises = selectedTutorials.value.map(tutorial =>
          videoTutorialAPI.deleteTutorial(tutorial.id)
        )

        await Promise.all(deletePromises)

        // 从列表中移除已删除的教程
        const deletedIds = new Set(selectedTutorials.value.map(t => t.id))
        tutorialList.value = tutorialList.value.filter(t => !deletedIds.has(t.id))

        selectedTutorials.value = []
        ElMessage.success(`成功删除 ${deletePromises.length} 个视频教程`)
        console.log('✅ 批量删除视频教程成功')
      })
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除教程失败:', error)
      ElMessage.error('批量删除教程失败')
    }
  }
}

const onTutorialSaved = () => {
  loadAllTutorials()
}

// 初始化页面数据 - 立即开始加载
onMounted(() => {
  // 立即开始加载所有教程
  loadAllTutorials()
})
</script>

<style scoped>
.video-tutorial-management {
  max-width: 1400px;
  margin: 0 auto;
}

.content-area {
  min-height: 600px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 8px;
}

.stat-label {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 12px;
}

.tutorial-list-card {
  min-height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tutorial-count {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.knowledge-point-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.point-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.point-path {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
