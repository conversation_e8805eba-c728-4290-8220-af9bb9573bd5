<template>
  <div class="flashcard-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" link size="large">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1>{{ project?.name || '项目' }} - 卡片管理</h1>
          <p class="subtitle">管理项目中的学习卡片</p>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createNewCard" :icon="Plus">
          创建卡片
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="control-panel">
      <!-- 统计信息卡片 -->
      <div class="stats-cards" v-if="projectId">
        <!-- 统计信息加载状态 -->
        <template v-if="statsLoading">
          <div class="stat-card loading">
            <div class="stat-number skeleton"></div>
            <div class="stat-label">总卡片数</div>
          </div>
          <div class="stat-card loading">
            <div class="stat-number skeleton"></div>
            <div class="stat-label">今日学习</div>
          </div>
          <div class="stat-card loading">
            <div class="stat-number skeleton"></div>
            <div class="stat-label">待复习</div>
          </div>
          <div class="stat-card loading">
            <div class="stat-number skeleton"></div>
            <div class="stat-label">正确率</div>
          </div>
        </template>

        <!-- 统计信息实际内容 -->
        <template v-else>
          <div class="stat-card">
            <div class="stat-number">{{ stats?.totalCards || 0 }}</div>
            <div class="stat-label">总卡片数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats?.todayStudyRecords || 0 }}</div>
            <div class="stat-label">今日学习</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats?.needReviewCards || 0 }}</div>
            <div class="stat-label">待复习</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ stats?.accuracyRate || 0 }}%</div>
            <div class="stat-label">正确率</div>
          </div>
        </template>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-panel" v-if="projectId">
      <div class="filter-left">
        <el-select
          v-model="difficultyFilter"
          placeholder="难度筛选"
          @change="fetchFlashcards"
          style="width: 150px"
        >
          <el-option label="全部难度" :value="null" />
          <el-option label="简单" :value="1" />
          <el-option label="容易" :value="2" />
          <el-option label="中等" :value="3" />
          <el-option label="困难" :value="4" />
          <el-option label="极难" :value="5" />
        </el-select>

        <el-select
          v-model="studyStatusFilter"
          placeholder="学习状态"
          @change="fetchFlashcards"
          style="width: 150px"
        >
          <el-option label="全部状态" :value="null" />
          <el-option label="从未学习" value="never" />
          <el-option label="需要复习" value="review" />
        </el-select>
      </div>
      
      <div class="filter-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索卡片标题或知识点"
          @input="onSearch"
          style="width: 300px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 卡片列表 -->
    <div class="flashcard-list" v-if="projectId">
      <!-- 加载状态 - 使用骨架屏 -->
      <CardListSkeleton
        v-if="loading"
        :card-count="6"
        :show-header="true"
        :show-search="false"
      />

      <!-- 实际内容 -->
      <template v-else>
        <div class="list-header">
          <span>共 {{ filteredFlashcards.length }} 张卡片</span>
        </div>

        <div class="cards-grid">
          <div
            v-for="card in filteredFlashcards"
            :key="card.id"
            class="flashcard-item"
            :class="`difficulty-${card.difficulty}`"
            @click="startStudyCard(card)"
          >
            <div class="card-header">
              <div class="card-title">
                {{ card.title }}
                <span v-if="accuracyRates[card.id] !== undefined" class="accuracy-rate">
                  {{ accuracyRates[card.id] }}%
                </span>
              </div>
              <div class="card-actions">
                <el-dropdown @command="(command) => handleCardAction(command, card)">
                  <el-button size="small" :icon="More" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="reset-stats">重置正确率</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <div class="card-content">
              <div class="knowledge-path">
                <el-icon><Document /></el-icon>
                <KnowledgeLeafPathDisplay
                  v-if="card.knowledgePointId"
                  mode="single"
                  :project-id="projectId"
                  :uid="card.knowledgePointId"
                  :max-length="40"
                  class="path-display"
                />
                <span v-else-if="card.knowledgePointPath" class="path-text">
                  {{ card.knowledgePointPath }}
                </span>
                <span v-else class="path-text">
                  {{ card.knowledgePointName || '暂无分类' }}
                </span>
              </div>
              <div class="difficulty-badge" :class="`difficulty-${card.difficulty}`">
                {{ getDifficultyText(card.difficulty) }}
              </div>
            </div>

            <div class="card-preview">
              <RichTextViewer :content="card.frontContent || '暂无内容'" />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredFlashcards.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无卡片数据">
            <el-button type="primary" @click="createNewCard">创建第一张卡片</el-button>
          </el-empty>
        </div>
      </template>
    </div>

    <!-- 创建/编辑卡片对话框 -->
    <FlashcardDialog
      v-model="showCreateDialog"
      :project-id="projectId"
      :flashcard="editingFlashcard"
      @success="onFlashcardSaved"
    />



    <!-- 学习卡片对话框 -->
    <StudyDialog
      v-model="showStudyDialog"
      :flashcard="studyingFlashcard"
      @study-complete="onStudyComplete"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, More, Document, ArrowLeft, Share } from '@element-plus/icons-vue'
import FlashcardDialog from '@/components/flashcard/FlashcardDialog.vue'
import StudyDialog from '@/components/flashcard/StudyDialog.vue'
import RichTextViewer from '@/components/RichTextViewer.vue'
import KnowledgeLeafPathDisplay from '@/components/knowledge/KnowledgeLeafPathDisplay.vue'
import { useProject } from '@/composables'
import { flashcardAPI } from '@/api/flashcard'
import { formatDate } from '@/utils/environment'
import { generateAccessibleUrl, getIPDetectionGuide } from '@/utils/networkUtils'
import { 
  confirmDelete, 
  confirmReset, 
  showSuccess, 
  showError,
  createSafeRouteParam,
  difficultyUtils,
  arrayUtils,
  filterUtils
} from '@/utils'
import { useLoading } from '@/composables/useLoading'
import { useErrorHandler } from '@/composables/useErrorHandler'
import { executeWithLoading, executeParallel } from '@/utils/apiUtils'

const route = useRoute()
const router = useRouter()
const { project, projectId, fetchProject } = useProject()

// 使用工具类
const loading = ref(false)
const error = ref(null)
const statsLoading = ref(false)

// 响应式数据
const flashcards = ref([])
const stats = ref(null)
const accuracyRates = ref({})

// 筛选和搜索
const difficultyFilter = ref(null)
const studyStatusFilter = ref(null)
const searchKeyword = ref('')

// 对话框状态
const showCreateDialog = ref(false)
const showStudyDialog = ref(false)
const editingFlashcard = ref(null)
const studyingFlashcard = ref(null)

// 使用新工具类的计算属性
const filteredFlashcards = computed(() => {
  let result = arrayUtils.safeArray(flashcards.value)
  
  // 搜索筛选
  if (searchKeyword.value) {
    result = filterUtils.textSearch(result, searchKeyword.value, ['title', 'knowledgePointName'])
  }
  
  // 难度筛选
  if (difficultyFilter.value !== null) {
    result = filterUtils.multiFilter(result, { difficulty: difficultyFilter.value })
  }
  
  return result
})

// 使用新工具类的生命周期
onMounted(async () => {
  await fetchProject()
  
  if (projectId.value) {
    // 优先加载卡片列表
    await executeWithLoading(loadFlashcards, {
      loadingRef: loading,
      errorMessage: '获取卡片列表失败'
    })
    
    // 异步加载统计信息
    executeParallel([
      () => loadStatsAsync(),
      () => loadAccuracyRates()
    ])
  }
})

// 监听项目变化 - 优化版本
watch(projectId, async (newProjectId) => {
  if (newProjectId) {
    await executeWithLoading(async () => {
      // 优先加载卡片列表
      await loadFlashcards()

      // 异步加载统计信息
      executeParallel([
        () => loadStatsAsync(),
        () => loadAccuracyRates()
      ])
    }, {
      loadingRef: loading,
      errorMessage: '项目切换加载失败'
    })
  }
})

// 方法
const goBack = () => {
  router.push(`/projects/${projectId.value}`)
}

// 加载卡片数据（用于初始化）- 优化版本
const loadFlashcards = async () => {
  try {
    const response = await flashcardAPI.getFlashcards(projectId.value)
    console.log('=== 卡片API调试信息 ===')
    console.log('项目ID:', projectId.value)
    console.log('卡片数量:', response.data?.length || 0)
    console.log('========================')

    // 确保赋值为空数组而不是其他值
    flashcards.value = Array.isArray(response.data) ? response.data : []

    console.log('flashcards.value 赋值后长度:', flashcards.value.length)
  } catch (error) {
    console.error('获取卡片失败:', error)
    ElMessage.error('获取卡片列表失败')
    // 确保错误时也设置为空数组
    flashcards.value = []
  }
}

// 刷新卡片数据（用于手动刷新）
const fetchFlashcards = async () => {
  if (!projectId.value) return

  await executeWithLoading(loadFlashcards, {
    loadingRef: loading,
    errorMessage: '刷新卡片数据失败'
  })
}

// 加载统计信息的异步版本
const loadStatsAsync = async () => {
  const response = await flashcardAPI.getStats(projectId.value)
  stats.value = response.data || {}
}

// 加载正确率
const loadAccuracyRates = async () => {
  const response = await flashcardAPI.getAccuracyRates(projectId.value)
  accuracyRates.value = response.data || {}
}

// 加载统计信息 (带loading状态)
const loadStats = async () => {
  await executeWithLoading(loadStatsAsync, {
    loadingRef: statsLoading,
    errorMessage: '加载统计信息失败'
  })
}

const onProjectChange = () => {
  // 重置筛选条件
  difficultyFilter.value = null
  studyStatusFilter.value = null
  searchKeyword.value = ''
}

const onSearch = () => {
  // 搜索是通过计算属性实现的，这里可以添加防抖逻辑
}

const createNewCard = () => {
  editingFlashcard.value = null // 清空编辑数据，确保是创建模式
  showCreateDialog.value = true
}

// 使用新工具类
const { getDifficultyText } = difficultyUtils

// 直接开始学习指定卡片
const startStudyCard = (card) => {
  studyingFlashcard.value = card
  showStudyDialog.value = true
}

const handleCardAction = async (command, card) => {
  switch (command) {
    case 'edit':
      editingFlashcard.value = card
      showCreateDialog.value = true
      break
    case 'reset-stats':
      await resetCardStats(card)
      break
    case 'delete':
      await deleteCard(card)
      break
  }
}

const resetCardStats = async (card) => {
  try {
    await confirmReset(card.title, '正确率')
    
    await executeWithLoading(() => flashcardAPI.resetFlashcardStats(card.id, projectId.value), {
      loadingRef: loading,
      errorMessage: '重置正确率失败'
    })
    showSuccess('重置', '正确率')
    
    // 并行刷新数据
    await executeParallel([
      () => loadFlashcards(),
      () => loadStats()
    ])
  } catch (error) {
    if (error !== 'cancel') {
      showError('重置', error)
    }
  }
}

const deleteCard = async (card) => {
  try {
    await confirmDelete(card.title, '卡片')
    
    await executeWithLoading(() => flashcardAPI.deleteFlashcard(card.id, projectId.value), {
      loadingRef: loading,
      errorMessage: '删除卡片失败'
    })
    showSuccess('删除', '卡片')
    
    // 并行刷新数据
    await executeParallel([
      () => loadFlashcards(),
      () => loadStats()
    ])
  } catch (error) {
    if (error !== 'cancel') {
      showError('删除', error)
    }
  }
}

const onFlashcardSaved = async (eventData = {}) => {
  const { continueAfterSubmit = false } = eventData

  // 如果不是"创建并继续"模式，则关闭对话框
  if (!continueAfterSubmit) {
    showCreateDialog.value = false
    editingFlashcard.value = null
  }

  // 并行刷新数据
  await executeParallel([
    () => loadFlashcards(),
    () => loadStats()
  ])
}

const onStudyComplete = () => {
  showStudyDialog.value = false
  studyingFlashcard.value = null

  // 刷新卡片列表、统计数据和正确率
  loadFlashcards()
  loadStats()
  loadAccuracyRates()
}
</script>

<style scoped>
.flashcard-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* 控制面板 */
.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-cards {
  display: flex;
  gap: 16px;
}

.stat-card {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  min-width: 80px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

/* 筛选面板 */
.filter-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-left {
  display: flex;
  gap: 12px;
}

/* 卡片列表 */
.flashcard-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  font-size: 14px;
  color: #6b7280;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 20px;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 卡片项 */
.flashcard-item {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
  aspect-ratio: 4/5;
  display: flex;
  flex-direction: column;
  max-width: 300px;
  height: auto;
}

.flashcard-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 难度边框颜色 */
.flashcard-item.difficulty-1 { border-color: #4CAF50; }
.flashcard-item.difficulty-2 { border-color: #2196F3; }
.flashcard-item.difficulty-3 { border-color: #9C27B0; }
.flashcard-item.difficulty-4 { border-color: #FF9800; }
.flashcard-item.difficulty-5 { border-color: #F44336; }

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
  margin-right: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.accuracy-rate {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  min-width: 35px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3);
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.knowledge-path {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
  flex: 1;
  min-width: 0; /* 允许内容收缩 */
}

.knowledge-path .path-display {
  flex: 1;
  min-width: 0;
}

.knowledge-path .path-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.difficulty-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.difficulty-badge.difficulty-1 { background-color: #E8F5E8; color: #4CAF50; }
.difficulty-badge.difficulty-2 { background-color: #E3F2FD; color: #2196F3; }
.difficulty-badge.difficulty-3 { background-color: #F3E5F5; color: #9C27B0; }
.difficulty-badge.difficulty-4 { background-color: #FFF3E0; color: #FF9800; }
.difficulty-badge.difficulty-5 { background-color: #FFEBEE; color: #F44336; }

.card-preview {
  flex: 1;
  overflow: hidden;
  position: relative;
  min-height: 120px;
}

.card-preview :deep(.rich-text-content) {
  font-size: 14px;
  line-height: 1.4;
  color: #6b7280;
}

.card-preview :deep(.rich-text-content img) {
  max-width: 100%;
  max-height: 180px;
  object-fit: contain;
  border-radius: 4px;
  display: block;
  margin: 0 auto;
}

.card-preview :deep(.rich-text-content p) {
  margin: 0 0 8px 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.card-preview :deep(.rich-text-content h1),
.card-preview :deep(.rich-text-content h2),
.card-preview :deep(.rich-text-content h3) {
  font-size: 14px;
  margin: 0 0 4px 0;
  font-weight: 600;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 统计信息骨架屏样式 */
.stat-card.loading {
  opacity: 0.7;
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  height: 24px;
  width: 60px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
