<template>
  <div class="exam-generation">
    <!-- 页面头部 -->
    <PageHeader
      title="考卷生成"
      :subtitle="projectName"
      @back="goBack"
    >
      <template #alert>
        <el-alert
          v-if="route.query.regenerate === 'true'"
          title="重新生成模式"
          description="已加载原考卷配置，您可以修改配置后重新生成考卷"
          type="info"
          :closable="false"
          show-icon
          style="margin-top: 10px;"
        />
      </template>
      <template #actions>
        <el-button @click="viewExamList" type="info">
          <el-icon><Document /></el-icon>
          查看考卷列表
        </el-button>
      </template>
    </PageHeader>

    <!-- 主要内容 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧：考卷配置 -->
        <el-col :span="12">
          <ExamConfigForm
            v-model="examForm"
            :generating="generating"
            :selected-knowledge-points="selectedKnowledgePoints"
            :is-regenerate="route.query.regenerate === 'true'"
            @generate="generateExam"
          />
        </el-col>

        <!-- 右侧：知识点选择 -->
        <el-col :span="12">
          <KnowledgePointSelector
            :project-id="projectId"
            :knowledge-data="knowledgeData"
            :selected-knowledge-points="selectedKnowledgePoints"
            :score-filters="examForm.scoreFilters"
            @update:selected-knowledge-points="selectedKnowledgePoints = $event"
            @go-to-knowledge="goToKnowledgeManagement"
          />
        </el-col>
      </el-row>


    </div>


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document
} from '@element-plus/icons-vue'
import { knowledgeAPI, examAPI } from '@/api'
import PageHeader from '@/components/common/PageHeader.vue'
import ExamConfigForm from '@/components/exam/ExamConfigForm.vue'
import KnowledgePointSelector from '@/components/exam/KnowledgePointSelector.vue'

import { useProject } from '@/composables/useProject'
import { useErrorHandler } from '@/composables/useErrorHandler'
import { marked } from 'marked'
import markedKatex from 'marked-katex-extension'
import 'katex/dist/katex.min.css'
import { formatDate } from '@/utils/environment'
import { processSvgInMarkdown, containsSvg, preprocessMathDelimiters } from '@/utils/svgToImage'


const router = useRouter()
const route = useRoute()

// 使用 Composables
const { project, projectId, projectName, fetchProject, generateDefaultExamInfo } = useProject()
const { handleApiError, handleAsyncError } = useErrorHandler()

// 状态管理
const knowledgeData = ref([])
const selectedKnowledgePoints = ref([])
const generating = ref(false)
const generationResult = ref(null)
const showPreviewDialog = ref(false)

// 表单数据
const examForm = reactive({
  title: '',
  description: '',
  targetLevel: '',
  difficulty: 'medium',
  scoreFilters: [],
  additionalRequirements: ''
})

// 状态管理
const processingSvg = ref(false)
const processedMarkdownContent = ref('')



// 计算属性
const renderedMarkdown = computed(() => {
  if (!generationResult.value?.examContent) return ''

  let content = processedMarkdownContent.value || generationResult.value.examContent

  try {
    // 预处理数学公式分隔符
    content = preprocessMathDelimiters(content)

    // 配置marked选项
    marked.setOptions({
      breaks: true,
      gfm: true
    })

    // 使用KaTeX扩展，添加支持单美元符号的配置
    marked.use(markedKatex({
      throwOnError: false,
      strict: false,
      output: 'html',
      nonStandard: true
    }))

    return marked.parse(content)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return `<pre>${content}</pre>`
  }
})

// 页面初始化
onMounted(async () => {
  await loadProject()
  await loadKnowledgeData()

  // 检查是否是重新生成模式
  if (route.query.regenerate === 'true') {
    await nextTick()
    await loadExamDataFromQuery()
  }
})

// 从查询参数加载考卷数据
const loadExamDataFromQuery = async () => {
  try {
    // 填充表单数据，如果查询参数为空则保持当前值（可能是默认值）
    if (route.query.title) {
      examForm.title = route.query.title
    }
    if (route.query.description) {
      examForm.description = route.query.description
    }
    examForm.targetLevel = route.query.targetLevel || ''
    examForm.difficulty = route.query.difficulty || 'medium'
    examForm.additionalRequirements = route.query.additionalRequirements || ''

    // 解析知识点数据
    if (route.query.knowledgePoints) {
      try {
        const knowledgePoints = JSON.parse(route.query.knowledgePoints)
        // 如果是旧格式（字符串数组），需要转换为新格式
        if (Array.isArray(knowledgePoints) && knowledgePoints.length > 0) {
          if (typeof knowledgePoints[0] === 'string') {
            // 旧格式：字符串数组，需要等知识点数据加载后再转换
            setTimeout(() => {
              const convertedPoints = knowledgePoints.map(name => {
                const found = flatKnowledgeList.value.find(item => item.name === name)
                return found ? {
                  name: found.name,
                  fullPath: found.fullPath,
                  description: found.description || '',
                  score: found.score
                } : { name, fullPath: name, description: '', score: 3 }
              })
              selectedKnowledgePoints.value = convertedPoints
            }, 100)
          } else {
            // 新格式：对象数组
            selectedKnowledgePoints.value = knowledgePoints
          }
        }
        console.log('已加载知识点选择:', knowledgePoints)
      } catch (error) {
        console.error('解析知识点数据失败:', error)
      }
    }

    ElMessage.success('已加载原考卷配置，可以修改后重新生成')
  } catch (error) {
    console.error('加载考卷数据失败:', error)
    ElMessage.error('加载考卷数据失败')
  }
}

// 加载项目信息
const loadProject = async () => {
  await handleAsyncError(async () => {
    await fetchProject()
    setDefaultExamInfo()
  }, { errorMessage: '加载项目信息失败' })
}

// 设置默认的考卷标题和描述
const setDefaultExamInfo = () => {
  if (!project.value) return

  const defaultInfo = generateDefaultExamInfo()

  // 只在表单字段为空时设置默认值
  if (!examForm.title.trim()) {
    examForm.title = defaultInfo.title
  }

  if (!examForm.description.trim()) {
    examForm.description = defaultInfo.description
  }
}

// 加载知识点数据
const loadKnowledgeData = async () => {
  await handleAsyncError(async () => {
    const response = await knowledgeAPI.getKnowledgeConfiguration(projectId.value)
    if (response.data.success && response.data.data?.configuration) {
      knowledgeData.value = response.data.data.configuration
    }
  }, { errorMessage: '加载知识点数据失败', showError: false })
}

// 页面导航
const goBack = () => {
  router.back()
}

const viewExamList = () => {
  router.push(`/projects/${projectId.value}/exams`)
}

const goToKnowledgeManagement = () => {
  router.push(`/projects/${projectId.value}/knowledge`)
}

// 全选知识点
const selectAll = () => {
  // 选择当前筛选结果中的所有知识点（包含完整信息）
  const allKnowledgePoints = flatKnowledgeList.value.map(item => ({
    name: item.name,
    fullPath: item.fullPath,
    description: item.description || '',
    score: item.score
  }))
  selectedKnowledgePoints.value = [...allKnowledgePoints]

  if (allKnowledgePoints.length > 0) {
    ElMessage.success(`已选择 ${allKnowledgePoints.length} 个知识点`)
  } else {
    ElMessage.warning('当前筛选条件下没有可选择的知识点')
  }
}

// 清空选择
const clearSelection = () => {
  selectedKnowledgePoints.value = []
}

// 知识点选择变化
const onKnowledgeSelectionChange = (selected) => {
  selectedKnowledgePoints.value = selected
}

// 生成考卷
const generateExam = async () => {
  if (selectedKnowledgePoints.value.length === 0) {
    ElMessage.error('请至少选择一个知识点')
    return
  }

  generating.value = true

  try {
    const request = {
      title: examForm.title,
      description: examForm.description,
      targetLevel: examForm.targetLevel,
      difficulty: examForm.difficulty,
      knowledgePoints: selectedKnowledgePoints.value,
      scoreFilters: examForm.scoreFilters,
      additionalRequirements: examForm.additionalRequirements
    }

    console.log('🎯 开始生成考卷:', request)

    const response = await examAPI.generateExamPaper(projectId.value, request)
    const result = response.data.data

    if (result.success) {
      ElMessage.success('考卷生成成功！正在跳转到考卷管理页面...')

      // 延迟一下让用户看到成功消息，然后跳转到考卷管理页面
      setTimeout(() => {
        router.push(`/projects/${projectId.value}/exams`)
      }, 1000)
    } else {
      ElMessage.error(result.errorMessage || '考卷生成失败')
    }
  } catch (error) {
    console.error('生成考卷失败:', error)
    ElMessage.error('生成考卷失败: ' + (error.response?.data?.message || error.message))
  } finally {
    generating.value = false
  }
}

// 预览考卷
const previewExam = () => {
  showPreviewDialog.value = true
}

// 打印考卷
const printExam = async () => {
  if (!generationResult.value?.examContent) {
    ElMessage.error('没有可打印的考卷内容')
    return
  }

  // 如果正在处理SVG，等待处理完成
  if (processingSvg.value) {
    ElMessage.info('正在处理图形内容，请稍候...')
    return
  }

  // 确保SVG已经处理完成
  if (containsSvg(generationResult.value.examContent) && !processedMarkdownContent.value) {
    try {
      await processSvgContent()
    } catch (error) {
      console.error('打印前SVG处理失败:', error)
    }
  }

  // 创建打印窗口
  const printWindow = window.open('', '_blank')
  const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${examForm.title}</title>
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css">
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .exam-header { text-align: center; margin-bottom: 30px; }
        .exam-info { margin-bottom: 20px; }
        img { max-width: 100%; height: auto; display: block; margin: 10px auto; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
          img { page-break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <div class="exam-header">
        <h1>${examForm.title}</h1>
        <div class="exam-info">
          <p>目标等级：${examForm.targetLevel} | 难度：${getDifficultyText(examForm.difficulty)}</p>
        </div>
      </div>
      ${renderedMarkdown.value}
    </body>
    </html>
  `

  printWindow.document.write(printContent)
  printWindow.document.close()
  printWindow.focus()
  printWindow.print()
}

// 处理SVG内容
const processSvgContent = async () => {
  if (!generationResult.value?.examContent) return

  // 检查是否包含SVG
  if (!containsSvg(generationResult.value.examContent)) {
    processedMarkdownContent.value = ''
    return
  }

  try {
    processingSvg.value = true
    console.log('🎨 检测到SVG内容，开始转换为图片...')

    const processedContent = await processSvgInMarkdown(generationResult.value.examContent)
    processedMarkdownContent.value = processedContent

    console.log('✅ SVG转换完成')
    ElMessage.success('SVG图形已转换为图片')
  } catch (error) {
    console.error('❌ SVG处理失败:', error)
    ElMessage.warning('SVG图形转换失败，将显示原始内容')
    processedMarkdownContent.value = ''
  } finally {
    processingSvg.value = false
  }
}



// 获取难度文本
const getDifficultyText = (difficulty) => {
  const difficultyMap = {
    easy: '简单',
    medium: '中等',
    hard: '困难',
    competition: '竞赛',
    middle_school_exam: '中考',
    high_school_exam: '高考'
  }
  return difficultyMap[difficulty] || difficulty
}


</script>

<style scoped>
.exam-generation {
  padding: 20px;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.main-content {
  max-width: 1400px;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3) {
  color: #303133;
  margin-top: 20px;
  margin-bottom: 10px;
}

.markdown-content :deep(h1) {
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 10px;
}

.markdown-content :deep(p) {
  margin-bottom: 10px;
  line-height: 1.6;
}

.markdown-content :deep(ol),
.markdown-content :deep(ul) {
  margin-bottom: 15px;
  padding-left: 20px;
}

.markdown-content :deep(li) {
  margin-bottom: 5px;
}

.knowledge-item :deep(.el-checkbox) {
  width: 100%;
  align-items: flex-start;
}

.knowledge-item :deep(.el-checkbox__input) {
  margin-top: 2px;
}

.knowledge-item :deep(.el-checkbox__label) {
  width: 100%;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 8px;
}

.knowledge-item .el-tag {
  margin-left: 0;
}
</style>
