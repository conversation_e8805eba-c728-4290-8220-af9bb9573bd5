<template>
  <div class="mobile-mental-arithmetic-exam">
    <div class="project-header">
      <h1 class="project-title">数学口算考试</h1>
      <div class="mobile-tip">
        <span>🧠 挑战你的心算极限！</span>
      </div>
    </div>

    <div v-if="!examStarted" class="difficulty-selector-wrapper">
      <difficulty-selector @start-exam="startExam"></difficulty-selector>
    </div>
    <div v-if="examStarted && !examFinished" class="question-display-wrapper">
      <question-display
        :difficulty="selectedDifficulty"
        @exam-finished="showResult"
      ></question-display>
    </div>
    <div v-if="examFinished" class="exam-result-wrapper">
      <exam-result :result="examResult" @restart="restartExam"></exam-result>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import DifficultySelector from '@/components/mental_arithmetic_exam/DifficultySelector.vue';
import QuestionDisplay from '@/components/mental_arithmetic_exam/QuestionDisplay.vue';
import ExamResult from '@/components/mental_arithmetic_exam/ExamResult.vue';

const examStarted = ref(false);
const examFinished = ref(false);
const selectedDifficulty = ref(null);
const examResult = ref(null);
const examStartTime = ref(null);

const startExam = (difficulty) => {
  selectedDifficulty.value = difficulty;
  examStarted.value = true;
  examFinished.value = false;
  examStartTime.value = new Date();
};

const showResult = (result) => {
  const examEndTime = new Date();
  const durationSeconds = Math.floor((examEndTime - examStartTime.value) / 1000);
  
  examResult.value = {
    ...result,
    difficulty: selectedDifficulty.value,
    durationSeconds: durationSeconds
  };
  examFinished.value = true;
};

const restartExam = () => {
  examStarted.value = false;
  examFinished.value = false;
  selectedDifficulty.value = null;
  examResult.value = null;
  examStartTime.value = null;
};
</script>

<style scoped>
.mobile-mental-arithmetic-exam {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.project-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.project-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.mobile-tip {
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  font-size: 12px;
  color: #3b82f6;
  text-align: center;
}

.difficulty-selector-wrapper,
.question-display-wrapper,
.exam-result-wrapper {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
</style>