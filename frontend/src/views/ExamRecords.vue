<template>
  <div class="exam-records">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" link size="large">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1>考试记录</h1>
          <p v-if="examPaper">{{ examPaper.title }}</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="showAddDialog = true" type="primary">
          <el-icon><Plus /></el-icon>
          添加考试记录
        </el-button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="statistics-cards" v-if="statistics">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
          <div class="stat-label">考试次数</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ formatScore(statistics.maxScore) }}</div>
          <div class="stat-label">最高分</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ formatScore(statistics.avgScore) }}</div>
          <div class="stat-label">平均分</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ formatPercent(statistics.passRate) }}</div>
          <div class="stat-label">及格率</div>
        </div>
      </el-card>
    </div>

    <!-- 考试记录列表 -->
    <el-card class="records-card">
      <template #header>
        <div class="card-header">
          <span>考试记录</span>
          <el-button @click="refreshRecords" size="small">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <!-- 加载状态 - 使用表格骨架屏 -->
      <TableSkeleton
        v-if="loading"
        :rows="5"
        :column-widths="['120px', '100px', '200px', '200px']"
      />

      <!-- 空状态 -->
      <div v-else-if="examRecords.length === 0" class="empty-container">
        <el-empty description="暂无考试记录">
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加第一条记录
          </el-button>
        </el-empty>
      </div>

      <!-- 考试记录表格 -->
      <el-table v-else :data="examRecords" stripe>
        <el-table-column prop="examDate" label="考试日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.examDate,"date") }}
          </template>
        </el-table-column>
        <el-table-column prop="score" label="成绩" width="100">
          <template #default="{ row }">
            <el-tag :type="getScoreTagType(row.score)" size="large">
              {{ formatScore(row.score) }}分
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.remarks || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editRecord(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteRecord(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingRecord ? '编辑考试记录' : '添加考试记录'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="recordFormRef"
        :model="recordForm"
        :rules="recordFormState.rules"
        label-width="100px"
      >
        <el-form-item label="考试日期" prop="examDate">
          <el-date-picker
            v-model="recordForm.examDate"
            type="date"
            placeholder="选择考试日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="考试成绩" prop="score">
          <el-input-number
            v-model="recordForm.score"
            :min="0"
            :max="100"
            :precision="1"
            placeholder="请输入成绩"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="recordForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="submitRecord" :loading="recordFormState.submitting">
          {{ editingRecord ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Plus, Refresh, Edit, Delete
} from '@element-plus/icons-vue'
import { examAPI, examRecordAPI } from '@/api'
import { formatDate, formatDateTime } from '@/utils/environment'
import { useParameterValidation } from '@/composables/useParameterValidation'
import { scoreUtils } from '@/utils/dataUtils'
import { createFormState, safeSubmit } from '@/utils/formUtils'
import { executeWithLoading, executeParallel } from '@/utils/apiUtils'
import { VALIDATION_RULES } from '@/utils/constants'

const router = useRouter()
const route = useRoute()

const projectId = computed(() => {
  const id = route.params.projectId
  if (!isValidId(id, 'projectId')) {
    console.error('❌ 无效的项目ID:', id)
    return null
  }
  return validateAndConvertId(id, 'projectId')
})

const examId = computed(() => {
  const id = route.params.examId
  if (!isValidId(id, 'examId')) {
    console.error('❌ 无效的考试ID:', id)
    return null
  }
  return validateAndConvertId(id, 'examId')
})

const examPaper = ref(null)
const examRecords = ref([])
const statistics = ref(null)
const loading = ref(false)
const showAddDialog = ref(false)
const editingRecord = ref(null)

// 表单状态管理
const recordFormState = createFormState({
  examDate: '',
  score: null,
  remarks: ''
}, {
  examDate: VALIDATION_RULES.EXAM_DATE,
  score: VALIDATION_RULES.EXAM_SCORE,
  remarks: VALIDATION_RULES.OPTIONAL_TEXT
})

const { formData: recordForm, formRef: recordFormRef, submitting } = recordFormState

// 页面初始化 - 立即显示加载状态
onMounted(async () => {
  // 验证路由参数
  if (!projectId.value || !examId.value) {
    ElMessage.error('页面参数无效，请检查URL')
    router.back()
    return
  }

  // 使用工具类并行加载所有数据
  await executeWithLoading(async () => {
    await executeParallel([
      loadExamPaper,
      loadExamRecords,
      loadStatistics
    ])
  }, {
    loadingRef: loading,
    errorMessage: '页面数据加载失败'
  })
})

// 加载考卷信息
const loadExamPaper = async () => {
  const response = await examAPI.getExamPaper(projectId.value, examId.value)
  examPaper.value = response.data.data
}

// 加载考试记录
const loadExamRecords = async () => {
  const response = await examRecordAPI.getExamRecords(projectId.value, examId.value)
  examRecords.value = response.data.data || []
}

// 加载统计信息
const loadStatistics = async () => {
  const response = await examRecordAPI.getExamStatistics(projectId.value, examId.value)
  statistics.value = response.data.data
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 刷新记录
const refreshRecords = async () => {
  await executeWithLoading(async () => {
    await executeParallel([
      loadExamRecords,
      loadStatistics
    ])
  }, {
    loadingRef: loading,
    errorMessage: '刷新数据失败'
  })
}

// 编辑记录
const editRecord = (record) => {
  editingRecord.value = record
  recordFormState.setFormData({
    examDate: record.examDate,
    score: record.score,
    remarks: record.remarks || ''
  })
  showAddDialog.value = true
}

// 提交记录
const submitRecord = async () => {
  const apiCall = editingRecord.value 
    ? () => examRecordAPI.updateExamRecord(
        projectId.value,
        examId.value,
        editingRecord.value.id,
        recordFormState.getFormData()
      )
    : () => examRecordAPI.addExamRecord(projectId.value, examId.value, recordFormState.getFormData())

  const result = await safeSubmit(apiCall, recordFormState, {
    onSuccess: async () => {
      showAddDialog.value = false
      await refreshRecords()
    }
  })

  if (result) {
    ElMessage.success(editingRecord.value ? '考试记录更新成功' : '考试记录添加成功')
  }
}

// 删除记录
const deleteRecord = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条考试记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await examRecordAPI.deleteExamRecord(projectId.value, examId.value, record.id)
    ElMessage.success('考试记录删除成功')
    await refreshRecords()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除记录失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  editingRecord.value = null
  recordFormState.resetForm()
}

// 使用工具类中的函数
const formatScore = scoreUtils.formatScore
const formatPercent = scoreUtils.formatPercent
const getScoreTagType = scoreUtils.getScoreTagType
</script>

<style scoped>
.exam-records {
  padding: 20px;
  min-height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.records-card {
  margin-top: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.records-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 40px;
}

.records-card .el-table {
  flex: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
