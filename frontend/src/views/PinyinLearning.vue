<template>
  <div class="learning-app full-screen">
    <div class="header">
      <!-- 模式切换按钮 -->
      <div class="mode-switcher">
        <button 
          class="mode-btn" 
          :class="{ active: currentMode === 'pinyin' }"
          @click="switchMode('pinyin')"
        >
          汉语拼音
        </button>
        <button 
          class="mode-btn" 
          :class="{ active: currentMode === 'alphabet' }"
          @click="switchMode('alphabet')"
        >
          英语字母
        </button>
      </div>
      
      <h1>{{ currentMode === 'pinyin' ? '汉语拼音' : '英语字母' }}</h1>
      <p>{{ currentMode === 'pinyin' ? '点击拼音听发音' : '点击字母听发音' }}</p>
      <div class="exam-controls">
        <button class="exam-btn" @click="startExam" :disabled="isExamMode">
          开始考试
        </button>
        <button v-if="isExamMode" class="exam-btn secondary" @click="exitExam">
          退出考试
        </button>
      </div>
    </div>

    <!-- 考试模式 -->
    <div v-if="isExamMode" class="exam-container">
      <div class="exam-header">
        <h3>{{ currentMode === 'pinyin' ? '拼音听力考试' : '英语字母听力考试' }}</h3>
        <div class="exam-progress">
          第 {{ currentQuestionIndex + 1 }} / {{ totalQuestions }} 题
        </div>
      </div>

      <div v-if="!examFinished" class="question-section">
        <div class="audio-controls">
          <button class="play-btn" @click="playCurrentQuestion">
            🔊 重新播放
          </button>
          <p class="instruction">{{ currentMode === 'pinyin' ? '请选择你听到的拼音和声调：' : '请选择你听到的字母：' }}</p>
        </div>

        <div class="options-grid">
          <button
            v-for="(option, index) in currentOptions"
            :key="index"
            class="option-btn"
            @click="selectAnswer(index)"
            :disabled="answered"
            :class="{ 
              correct: answered && index === correctAnswerIndex,
              wrong: answered && index === selectedAnswerIndex && index !== correctAnswerIndex,
              selected: index === selectedAnswerIndex
            }"
          >
            <span class="option-content">
              <span v-if="currentMode === 'pinyin'" class="option-tone-mark">{{ tones[option.tone - 1].symbol }}</span>
              <span class="option-text">{{ formatDisplayText(option) }}</span>
            </span>
          </button>
        </div>

        <div v-if="answered" class="answer-feedback">
          <p v-if="selectedAnswerIndex === correctAnswerIndex" class="correct-msg">
            ✅ 回答正确！
          </p>
          <p v-else class="wrong-msg">
            ❌ 回答错误，正确答案是：{{ formatCorrectAnswerText() }}
          </p>
          <button class="next-btn" @click="nextQuestion">
            {{ currentQuestionIndex < totalQuestions - 1 ? '下一题' : '查看结果' }}
          </button>
        </div>
      </div>

      <!-- 考试结果 -->
      <div v-if="examFinished" class="exam-result">
        <h3>考试完成！</h3>
        <div class="score-display">
          <div class="score-circle">{{ examScore }}</div>
          <p>分</p>
        </div>
        <div class="result-details">
          <p>正确题数：{{ correctCount }} / {{ totalQuestions }}</p>
          <p>错误题数：{{ totalQuestions - correctCount }}</p>
          <p>正确率：{{ examScore }}%</p>
        </div>
        <div class="result-actions">
          <button class="exam-btn" @click="restartExam">重新考试</button>
          <button class="exam-btn secondary" @click="exitExam">退出考试</button>
        </div>
      </div>
    </div>

    <!-- 学习模式 -->
    <div v-else class="learning-container">
      <!-- 拼音学习 -->
      <div v-if="currentMode === 'pinyin'" class="pinyin-container">
        <!-- 拼音分类显示 -->
        <div 
          v-for="category in categories" 
          :key="category"
          class="pinyin-section"
        >
          <h2>{{ category }}</h2>
          <div class="pinyin-grid">
            <div 
              v-for="pinyin in getPinyinByCategory(category)" 
              :key="pinyin"
              class="pinyin-item"
              @click="playPinyinSequence(pinyin, category)"
            >
              <span class="pinyin-with-tone">
                <span class="tone-mark">{{ getCurrentToneSymbol(pinyin) }}</span>
                <span class="pinyin-text">{{ formatPinyinDisplay(pinyin, category) }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 英语字母学习 -->
      <div v-if="currentMode === 'alphabet'" class="alphabet-container">
        <div class="alphabet-section">
          <h2>英语字母 (A-Z)</h2>
          <div class="alphabet-grid">
            <div 
              v-for="letter in alphabetLetters" 
              :key="letter"
              class="alphabet-item"
              @click="playLetter(letter)"
            >
              <span class="letter-display">
                <span class="letter-upper">{{ letter.toUpperCase() }}</span>
                <span class="letter-lower">{{ letter.toLowerCase() }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { quizExamRecordsApi, EXAM_CATEGORIES, createExamRecordData } from '@/api/quizExamRecords'

// 当前模式：拼音或字母
const currentMode = ref('pinyin')

// 英语字母数组
const alphabetLetters = 'abcdefghijklmnopqrstuvwxyz'.split('')

// 四个声调
const tones = [
  { name: '阴平', symbol: '—' },
  { name: '阳平', symbol: '/' },
  { name: '上声', symbol: '∨' },
  { name: '去声', symbol: '\\' }
]

// 记录每个拼音当前的声调状态（用于显示和轮换）
const pinyinToneState = reactive({})

// 考试相关状态
const isExamMode = ref(false)
const examFinished = ref(false)
const currentQuestionIndex = ref(0)
const examQuestions = ref([])
const currentQuestion = ref(null)
const currentOptions = ref([])
const correctAnswerIndex = ref(0)
const selectedAnswerIndex = ref(-1)
const answered = ref(false)
const correctCount = ref(0)
const totalQuestions = computed(() => currentMode.value === 'pinyin' ? 20 : 10)
const examScore = computed(() => Math.round((correctCount.value / totalQuestions.value) * 100))
const examStartTime = ref(null)

// 根据实际音频文件整理的拼音
const pinyinData = [
  // 单韵母
  { pinyin: 'a', category: '单韵母' },
  { pinyin: 'o', category: '单韵母' },
  { pinyin: 'e', category: '单韵母' },
  { pinyin: 'i', category: '单韵母' },
  { pinyin: 'u', category: '单韵母' },
  { pinyin: 'v', category: '单韵母' },
  
  // 复韵母
  { pinyin: 'ai', category: '复韵母' },
  { pinyin: 'ei', category: '复韵母' },
  { pinyin: 'ui', category: '复韵母' },
  { pinyin: 'ao', category: '复韵母' },
  { pinyin: 'ou', category: '复韵母' },
  { pinyin: 'iu', category: '复韵母' },
  { pinyin: 'ie', category: '复韵母' },
  { pinyin: 've', category: '复韵母' },
  
  // 鼻韵母
  { pinyin: 'an', category: '鼻韵母' },
  { pinyin: 'en', category: '鼻韵母' },
  { pinyin: 'in', category: '鼻韵母' },
  { pinyin: 'un', category: '鼻韵母' },
  { pinyin: 'vn', category: '鼻韵母' },
  { pinyin: 'ang', category: '鼻韵母' },
  { pinyin: 'eng', category: '鼻韵母' },
  { pinyin: 'ing', category: '鼻韵母' },
  { pinyin: 'ong', category: '鼻韵母' },
  
  // 特殊韵母
  { pinyin: 'er', category: '特殊韵母' },
  
  // 声母
  { pinyin: 'bo', category: '声母' },
  { pinyin: 'po', category: '声母' },
  { pinyin: 'mo', category: '声母' },
  { pinyin: 'fo', category: '声母' },
  { pinyin: 'de', category: '声母' },
  { pinyin: 'te', category: '声母' },
  { pinyin: 'ne', category: '声母' },
  { pinyin: 'le', category: '声母' },
  { pinyin: 'ge', category: '声母' },
  { pinyin: 'ke', category: '声母' },
  { pinyin: 'he', category: '声母' },
  { pinyin: 'ji', category: '声母' },
  { pinyin: 'qi', category: '声母' },
  { pinyin: 'xi', category: '声母' },
  { pinyin: 'zi', category: '声母' },
  { pinyin: 'ci', category: '声母' },
  { pinyin: 'si', category: '声母' },
  { pinyin: 'zhi', category: '声母' },
  { pinyin: 'chi', category: '声母' },
  { pinyin: 'shi', category: '声母' },
  { pinyin: 'ri', category: '声母' },
  
  // 整体认读音节
  { pinyin: 'yi', category: '整体认读音节' },
  { pinyin: 'wu', category: '整体认读音节' },
  { pinyin: 'yu', category: '整体认读音节' },
  { pinyin: 'ye', category: '整体认读音节' },
  { pinyin: 'yue', category: '整体认读音节' },
  { pinyin: 'yuan', category: '整体认读音节' },
  { pinyin: 'yin', category: '整体认读音节' },
  { pinyin: 'yun', category: '整体认读音节' },
  { pinyin: 'ying', category: '整体认读音节' }
]

// 拼音分类
const categories = [
  '单韵母', '复韵母', '鼻韵母', '特殊韵母', '声母', '整体认读音节'
]

// 模式切换
const switchMode = (mode) => {
  if (isExamMode.value) return // 考试中不允许切换模式
  currentMode.value = mode
}

// 根据分类获取拼音列表
const getPinyinByCategory = (category) => {
  return pinyinData.filter(item => item.category === category).map(item => item.pinyin)
}

// 声母显示转换映射
const initialDisplayMap = {
  'bo': 'b', 'po': 'p', 'mo': 'm', 'fo': 'f',
  'de': 'd', 'te': 't', 'ne': 'n', 'le': 'l',
  'ge': 'g', 'ke': 'k', 'he': 'h',
  'ji': 'j', 'qi': 'q', 'xi': 'x',
  'zi': 'z', 'ci': 'c', 'si': 's',
  'zhi': 'zh', 'chi': 'ch', 'shi': 'sh', 'ri': 'r'
}

// 将拼音中的v转换为ü显示，声母转换为单字母显示
const formatPinyinDisplay = (pinyin, category) => {
  // 如果是声母，使用映射转换
  if (category === '声母' && initialDisplayMap[pinyin]) {
    return initialDisplayMap[pinyin]
  }
  // 其他情况将v转换为ü
  return pinyin.replace(/v/g, 'ü')
}

// 获取当前拼音显示的声调符号
const getCurrentToneSymbol = (pinyin) => {
  const currentTone = pinyinToneState[pinyin] || 1
  return tones[currentTone - 1].symbol
}

// 播放英语字母
const playLetter = (letter) => {
  try {
    const audio = new Audio(`https://kpe-system.oss-cn-shanghai.aliyuncs.com/sounds/26word/${letter.toLowerCase()}.mp3`)
    audio.play().catch(error => {
      console.error('播放失败:', error)
      ElMessage.warning(`暂无字母 ${letter.toUpperCase()} 的音频文件`)
    })
  } catch (error) {
    console.error('音频加载失败:', error)
    ElMessage.warning(`暂无字母 ${letter.toUpperCase()} 的音频文件`)
  }
}

// 播放拼音序列（轮流播放四个声调）
const playPinyinSequence = async (pinyin, category) => {
  // 初始化或获取当前声调
  if (!pinyinToneState[pinyin]) {
    pinyinToneState[pinyin] = 1
  }

  const currentTone = pinyinToneState[pinyin]
  
  // 播放当前声调
  playPinyin(pinyin, currentTone)
  
  // 延迟一段时间后切换到下一个声调
  setTimeout(() => {
    pinyinToneState[pinyin] = currentTone === 4 ? 1 : currentTone + 1
  }, 500) // 0.5秒后切换声调显示
}

// 播放单个拼音音频
const playPinyin = (pinyin, tone) => {
  try {
    const audio = new Audio(`https://kpe-system.oss-cn-shanghai.aliyuncs.com/sounds/pinyin/${pinyin}${tone}.mp3`)
    audio.play().catch(error => {
      console.error('播放失败:', error)
      ElMessage.warning(`暂无 ${pinyin}${tone} 的音频文件`)
    })
  } catch (error) {
    console.error('音频加载失败:', error)
    ElMessage.warning(`暂无 ${pinyin}${tone} 的音频文件`)
  }
}

// 获取所有可考试的拼音（包含所有分类）
const getExamPinyins = () => {
  return pinyinData.map(item => item.pinyin)
}

// 生成拼音考试题目
const generatePinyinExamQuestions = () => {
  const examPinyins = getExamPinyins()
  const questions = []
  
  for (let i = 0; i < 20; i++) {
    const randomPinyin = examPinyins[Math.floor(Math.random() * examPinyins.length)]
    const randomTone = Math.floor(Math.random() * 4) + 1
    
    // 找到对应的分类
    const item = pinyinData.find(data => data.pinyin === randomPinyin)
    const category = item ? item.category : ''
    
    questions.push({
      type: 'pinyin',
      pinyin: randomPinyin,
      tone: randomTone,
      category: category
    })
  }
  
  return questions
}

// 生成英语字母考试题目
const generateAlphabetExamQuestions = () => {
  const questions = []
  
  for (let i = 0; i < 10; i++) {
    const randomLetter = alphabetLetters[Math.floor(Math.random() * alphabetLetters.length)]
    
    questions.push({
      type: 'alphabet',
      letter: randomLetter
    })
  }
  
  return questions
}

// 生成拼音选项（3个错误 + 1个正确）
const generatePinyinOptions = (correctPinyin, correctTone) => {
  const examPinyins = getExamPinyins()
  
  // 找到正确答案的分类
  const correctItem = pinyinData.find(item => item.pinyin === correctPinyin)
  const correctCategory = correctItem ? correctItem.category : ''
  
  const options = [{ pinyin: correctPinyin, tone: correctTone, category: correctCategory }]
  
  // 生成3个错误选项
  while (options.length < 4) {
    const randomPinyin = examPinyins[Math.floor(Math.random() * examPinyins.length)]
    const randomTone = Math.floor(Math.random() * 4) + 1
    
    // 找到随机选项的分类
    const randomItem = pinyinData.find(item => item.pinyin === randomPinyin)
    const randomCategory = randomItem ? randomItem.category : ''
    
    // 确保不重复
    const optionStr = `${randomPinyin}-${randomTone}`
    const correctStr = `${correctPinyin}-${correctTone}`
    if (optionStr !== correctStr && !options.some(opt => `${opt.pinyin}-${opt.tone}` === optionStr)) {
      options.push({ pinyin: randomPinyin, tone: randomTone, category: randomCategory })
    }
  }
  
  // 随机打乱选项顺序
  for (let i = options.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [options[i], options[j]] = [options[j], options[i]]
  }
  
  return options
}

// 生成字母选项（3个错误 + 1个正确）
const generateAlphabetOptions = (correctLetter) => {
  const options = [{ letter: correctLetter }]
  
  // 生成3个错误选项
  while (options.length < 4) {
    const randomLetter = alphabetLetters[Math.floor(Math.random() * alphabetLetters.length)]
    
    if (randomLetter !== correctLetter && !options.some(opt => opt.letter === randomLetter)) {
      options.push({ letter: randomLetter })
    }
  }
  
  // 随机打乱选项顺序
  for (let i = options.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [options[i], options[j]] = [options[j], options[i]]
  }
  
  return options
}

// 格式化显示文本
const formatDisplayText = (option) => {
  if (currentMode.value === 'alphabet') {
    return option.letter.toUpperCase()
  } else {
    return formatPinyinDisplay(option.pinyin, option.category || '')
  }
}

// 格式化正确答案文本
const formatCorrectAnswerText = () => {
  if (currentMode.value === 'alphabet') {
    return currentQuestion.value.letter.toUpperCase()
  } else {
    return formatPinyinDisplay(currentQuestion.value.pinyin, currentQuestion.value.category || '') + tones[currentQuestion.value.tone - 1].symbol
  }
}

// 开始考试
const startExam = () => {
  isExamMode.value = true
  examFinished.value = false
  currentQuestionIndex.value = 0
  correctCount.value = 0
  examStartTime.value = new Date()
  
  if (currentMode.value === 'pinyin') {
    examQuestions.value = generatePinyinExamQuestions()
  } else {
    examQuestions.value = generateAlphabetExamQuestions()
  }
  
  loadQuestion()
}

// 加载当前题目
const loadQuestion = () => {
  if (currentQuestionIndex.value >= examQuestions.value.length) {
    examFinished.value = true
    saveExamRecord() // 考试结束时保存记录
    return
  }
  
  currentQuestion.value = examQuestions.value[currentQuestionIndex.value]
  
  if (currentMode.value === 'pinyin') {
    currentOptions.value = generatePinyinOptions(currentQuestion.value.pinyin, currentQuestion.value.tone)
    correctAnswerIndex.value = currentOptions.value.findIndex(
      opt => opt.pinyin === currentQuestion.value.pinyin && opt.tone === currentQuestion.value.tone
    )
  } else {
    currentOptions.value = generateAlphabetOptions(currentQuestion.value.letter)
    correctAnswerIndex.value = currentOptions.value.findIndex(
      opt => opt.letter === currentQuestion.value.letter
    )
  }
  
  selectedAnswerIndex.value = -1
  answered.value = false
  
  // 自动播放2次
  setTimeout(() => {
    playCurrentQuestion()
    setTimeout(() => {
      playCurrentQuestion()
    }, 1500) // 第二次播放间隔1.5秒
  }, 500) // 加载后0.5秒开始播放
}

// 播放当前题目音频
const playCurrentQuestion = () => {
  if (currentQuestion.value) {
    if (currentMode.value === 'pinyin') {
      playPinyin(currentQuestion.value.pinyin, currentQuestion.value.tone)
    } else {
      playLetter(currentQuestion.value.letter)
    }
  }
}

// 选择答案
const selectAnswer = (index) => {
  if (answered.value) return
  
  selectedAnswerIndex.value = index
  answered.value = true
  
  if (index === correctAnswerIndex.value) {
    correctCount.value++
  }
}

// 下一题
const nextQuestion = () => {
  currentQuestionIndex.value++
  loadQuestion()
}

// 重新考试
const restartExam = () => {
  startExam()
}

// 退出考试
const exitExam = () => {
  isExamMode.value = false
  examFinished.value = false
}

// 保存考试记录
const saveExamRecord = async () => {
  if (!examFinished.value || !examStartTime.value) return
  
  try {
    const examEndTime = new Date()
    const durationSeconds = Math.floor((examEndTime - examStartTime.value) / 1000)
    const incorrectCount = totalQuestions.value - correctCount.value
    
    const examData = createExamRecordData({
      category: EXAM_CATEGORIES.PINYIN,
      subcategory: null,
      title: currentMode.value === 'pinyin' ? '汉语拼音练习' : '英语字母练习',
      totalQuestions: totalQuestions.value,
      correctAnswers: correctCount.value,
      durationSeconds: durationSeconds,
      details: {
        examType: 'pinyin_alphabet',
        pinyinType: currentMode.value,
        incorrectAnswers: incorrectCount
      }
    })
    
    await quizExamRecordsApi.addExamRecord(examData)
    console.log('✅ 拼音考试记录已保存')
  } catch (error) {
    console.error('❌ 保存拼音考试记录失败:', error)
    // 不显示错误消息，避免影响用户体验
  }
}
</script>

<style scoped>
.learning-app {
  padding: 16px;
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 24px;
  color: #333;
  width: 100%;
  max-width: 800px;
}

.mode-switcher {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 16px;
}

.mode-btn {
  padding: 8px 16px;
  border: 2px solid #409EFF;
  background: white;
  color: #409EFF;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: #409EFF;
  color: white;
}

.mode-btn:hover {
  background: #409EFF;
  color: white;
}

.header h1 {
  font-size: 28px;
  margin: 0 0 8px 0;
  color: #409EFF;
}

.header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.exam-controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

.exam-btn {
  padding: 10px 20px;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.exam-btn:hover:not(:disabled) {
  background: #337ab7;
}

.exam-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.exam-btn.secondary {
  background: #909399;
}

.exam-btn.secondary:hover {
  background: #767a82;
}

/* 考试相关样式 */
.exam-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.exam-header {
  text-align: center;
  margin-bottom: 30px;
}

.exam-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
}

.exam-progress {
  color: #666;
  font-size: 16px;
}

.question-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.audio-controls {
  text-align: center;
  margin-bottom: 24px;
}

.play-btn {
  background: #67c23a;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 18px;
  cursor: pointer;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.play-btn:hover {
  background: #5daf34;
}

.instruction {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.option-btn {
  background: #f0f8ff;
  border: 2px solid #e6f2ff;
  padding: 16px;
  border-radius: 8px;
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.option-tone-mark {
  display: block;
  font-size: 18px;
  line-height: 1;
  margin-bottom: 6px;
  height: 18px;
  font-weight: bold;
}

.option-text {
  display: block;
  font-size: 20px;
  line-height: 1;
  font-weight: bold;
}

.option-btn:hover:not(:disabled) {
  background: #e6f2ff;
  transform: scale(1.02);
}

.option-btn:disabled {
  cursor: not-allowed;
}

.option-btn.correct {
  background: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.option-btn.wrong {
  background: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.option-btn.selected {
  transform: scale(0.98);
}

.answer-feedback {
  text-align: center;
}

.correct-msg {
  color: #67c23a;
  font-weight: bold;
  margin-bottom: 16px;
}

.wrong-msg {
  color: #f56c6c;
  font-weight: bold;
  margin-bottom: 16px;
}

.next-btn {
  background: #409EFF;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.next-btn:hover {
  background: #337ab7;
}

/* 考试结果样式 */
.exam-result {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.exam-result h3 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 24px;
}

.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(45deg, #409EFF, #67c23a);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
}

.score-display p {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.result-details {
  margin-bottom: 24px;
}

.result-details p {
  margin: 8px 0;
  color: #666;
  font-size: 16px;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 学习容器 */
.learning-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.pinyin-container {
  width: 100%;
}

.pinyin-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.pinyin-section h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  color: #333;
  text-align: center;
}

.pinyin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.pinyin-item {
  background: #f0f8ff;
  border: 2px solid #e6f2ff;
  border-radius: 8px;
  padding: 16px 8px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pinyin-item:hover {
  background: #409EFF;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.pinyin-item:active {
  transform: scale(0.95);
}

.pinyin-with-tone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.pinyin-text {
  display: block;
  line-height: 1;
}

.tone-mark {
  display: block;
  font-size: 16px;
  line-height: 1;
  margin-bottom: 5px;
  height: 16px;
  font-weight: bold;
}

/* 英语字母样式 */
.alphabet-container {
  width: 100%;
}

.alphabet-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.alphabet-section h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  color: #333;
  text-align: center;
}

.alphabet-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.alphabet-item {
  background: #f0fff0;
  border: 2px solid #e8f5e8;
  border-radius: 8px;
  padding: 16px 8px;
  text-align: center;
  font-weight: bold;
  color: #67c23a;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alphabet-item:hover {
  background: #67c23a;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.alphabet-item:active {
  transform: scale(0.95);
}

.letter-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.letter-upper {
  font-size: 24px;
  line-height: 1;
  margin-bottom: 4px;
}

.letter-lower {
  font-size: 18px;
  line-height: 1;
  opacity: 0.7;
}

/* 隐藏导航菜单，全屏显示 */
.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #f8f9fa;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .learning-app {
    padding: 12px;
    justify-content: flex-start;
  }
  
  .header {
    margin-bottom: 16px;
  }
  
  .mode-switcher {
    flex-direction: row;
    gap: 6px;
  }

  .mode-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .pinyin-grid, .alphabet-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 8px;
    max-width: 100%;
  }
  
  .pinyin-item, .alphabet-item {
    padding: 12px 4px;
    font-size: 16px;
    min-height: 50px;
  }
  
  .letter-upper {
    font-size: 20px;
  }

  .letter-lower {
    font-size: 14px;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .tone-mark {
    font-size: 14px;
    margin-bottom: 4px;
    height: 14px;
    font-weight: bold;
  }

  .exam-controls {
    flex-direction: column;
    gap: 8px;
  }

  .exam-btn {
    padding: 8px 16px;
    font-size: 14px;
  }

  .options-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .option-btn {
    padding: 8px;
    font-size: 16px;
    min-height: 60px;
  }

  .option-tone-mark {
    font-size: 16px;
    margin-bottom: 5px;
    height: 16px;
  }

  .option-text {
    font-size: 16px;
  }

  .score-circle {
    width: 80px;
    height: 80px;
    font-size: 24px;
  }
  
  .pinyin-section, .alphabet-section {
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .exam-container,
  .learning-container {
    width: 100%;
    padding: 0;
  }
  
  .question-section,
  .exam-result {
    padding: 20px;
    margin: 0 auto;
  }
}
</style> 