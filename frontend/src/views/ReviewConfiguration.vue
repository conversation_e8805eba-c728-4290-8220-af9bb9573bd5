<template>
  <div class="review-configuration-page">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Setting /></el-icon>
            复习周期配置
          </h1>
          <p class="page-description">
            为项目 "{{ projectInfo.name }}" 配置错题复习周期
          </p>
        </div>
        <div class="header-right">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回项目
          </el-button>
        </div>
      </div>
    </div>

    <div class="page-content">
      <div class="content-container">
        <review-configuration-form
          :project-id="projectId"
          @saved="handleConfigSaved"
          @cancel="goBack"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Setting, ArrowLeft } from '@element-plus/icons-vue'
import ReviewConfigurationForm from '@/components/ReviewConfigurationForm.vue'
import { projectAPI } from '@/api'
import { executeWithLoading } from '@/utils/apiUtils'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
console.log('🔧 ReviewConfiguration - 路由参数:', route.params)
console.log('🔧 ReviewConfiguration - 项目ID原始值:', route.params.id)

const projectId = ref(parseInt(route.params.id))
console.log('🔧 ReviewConfiguration - 项目ID转换后:', projectId.value)

const projectInfo = ref({})
const loading = ref(false)

// 方法
const loadProjectInfo = async () => {
  await executeWithLoading(async () => {
    const response = await projectAPI.getProject(projectId.value)
    projectInfo.value = response.data.data || {}
  }, {
    loadingRef: loading,
    errorMessage: '加载项目信息失败'
  })
}

const handleConfigSaved = (configData) => {
  ElMessage.success('复习配置保存成功')
  // 可以选择是否返回项目页面
  // goBack()
}

const goBack = () => {
  router.push(`/projects/${projectId.value}`)
}

// 生命周期
onMounted(() => {
  loadProjectInfo()
})
</script>

<style scoped>
.review-configuration-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #ebeef5;
  padding: 24px 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  flex-shrink: 0;
}

.page-content {
  padding: 32px 0;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-right {
    align-self: flex-end;
  }
  
  .page-content {
    padding: 16px 0;
  }
  
  .content-container {
    padding: 0 16px;
  }
}
</style>
