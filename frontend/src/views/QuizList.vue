<template>
  <div class="quiz-list-container">
    <h1 class="title">趣味考试</h1>
    
    <!-- 简单难度 (1-2星) -->
    <div class="difficulty-section">
      <h2 class="difficulty-title">
        <span class="difficulty-label">简单</span>
        <span class="difficulty-subtitle">1-2星 适合入门</span>
      </h2>
      <div class="quiz-grid">
        <div
          v-for="[key, quiz] in easyQuizzes"
          :key="key"
          class="quiz-card easy"
          @click="startQuiz(key)"
        >
          <div class="quiz-card-header">
            <h3 class="quiz-title">{{ quiz.title }}</h3>
            <DifficultyStars :difficulty="quiz.difficulty" class="difficulty-stars" />
          </div>
          <p class="quiz-description">{{ quiz.description }}</p>
          <div class="quiz-info">
            <span class="option-count">
              {{ getOptionCount(quiz.difficulty) }}个选项
            </span>
            <span class="question-count">
              题库数：{{ getQuestionCount(key) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 中等难度 (3-4星) -->
    <div class="difficulty-section">
      <h2 class="difficulty-title">
        <span class="difficulty-label">中等</span>
        <span class="difficulty-subtitle">3-4星 挑战升级</span>
      </h2>
      <div class="quiz-grid">
        <div
          v-for="[key, quiz] in mediumQuizzes"
          :key="key"
          class="quiz-card medium"
          @click="startQuiz(key)"
        >
          <div class="quiz-card-header">
            <h3 class="quiz-title">{{ quiz.title }}</h3>
            <DifficultyStars :difficulty="quiz.difficulty" class="difficulty-stars" />
          </div>
          <p class="quiz-description">{{ quiz.description }}</p>
          <div class="quiz-info">
            <span class="option-count">
              {{ getOptionCount(quiz.difficulty) }}个选项
            </span>
            <span class="question-count">
              题库数：{{ getQuestionCount(key) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 困难难度 (5星) -->
    <div class="difficulty-section">
      <h2 class="difficulty-title">
        <span class="difficulty-label">困难</span>
        <span class="difficulty-subtitle">5星 极限挑战</span>
      </h2>
      <div class="quiz-grid">
        <div
          v-for="[key, quiz] in hardQuizzes"
          :key="key"
          class="quiz-card hard"
          @click="startQuiz(key)"
        >
          <div class="quiz-card-header">
            <h3 class="quiz-title">{{ quiz.title }}</h3>
            <DifficultyStars :difficulty="quiz.difficulty" class="difficulty-stars" />
          </div>
          <p class="quiz-description">{{ quiz.description }}</p>
          <div class="quiz-info">
            <span class="option-count">
              {{ getOptionCount(quiz.difficulty) }}个选项
            </span>
            <span class="question-count">
              题库数：{{ getQuestionCount(key) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getQuizFactory } from '@/factories/quizFactory.js';
import { DifficultyStars } from '@/components';

const router = useRouter();
const quizFactory = getQuizFactory();

// 响应式数据
const quizzes = ref(new Map());
const questionCounts = ref(new Map());
const loading = ref(true);
const error = ref('');

// 初始化数据
onMounted(async () => {
  try {
    loading.value = true;
    
    // 获取可见的考试配置
    const visibleConfigs = quizFactory.getVisibleQuizConfigs();
    quizzes.value = visibleConfigs;
    
    // 异步获取题目数量
    const countPromises = [];
    for (const [type, config] of visibleConfigs) {
      countPromises.push(
        quizFactory.getQuestionCount(type).then(count => {
          questionCounts.value.set(type, count);
        })
      );
    }
    
    await Promise.all(countPromises);
  } catch (err) {
    console.error('Failed to load quiz data:', err);
    error.value = '加载考试数据失败';
  } finally {
    loading.value = false;
  }
});

// 过滤掉隐藏的quiz项目并按难度分类
const visibleQuizzes = computed(() => {
  const filtered = new Map();
  for (const [key, quiz] of quizzes.value) {
    if (!quiz.hidden) {
      filtered.set(key, quiz);
    }
  }
  return filtered;
});

// 简单难度 (1-2星)
const easyQuizzes = computed(() => {
  const easy = new Map();
  for (const [key, quiz] of visibleQuizzes.value) {
    if (quiz.difficulty >= 1 && quiz.difficulty <= 2) {
      easy.set(key, quiz);
    }
  }
  return easy;
});

// 中等难度 (3-4星)
const mediumQuizzes = computed(() => {
  const medium = new Map();
  for (const [key, quiz] of visibleQuizzes.value) {
    if (quiz.difficulty >= 3 && quiz.difficulty <= 4) {
      medium.set(key, quiz);
    }
  }
  return medium;
});

// 困难难度 (5星)
const hardQuizzes = computed(() => {
  const hard = new Map();
  for (const [key, quiz] of visibleQuizzes.value) {
    if (quiz.difficulty === 5) {
      hard.set(key, quiz);
    }
  }
  return hard;
});

const startQuiz = (quizType) => {
  router.push(`/quiz/${quizType}`);
};

const getOptionCount = (difficulty) => {
  return quizFactory.getOptionCount(difficulty);
};

const getQuestionCount = (quizType) => {
  return questionCounts.value.get(quizType) || 0;
};
</script>

<style scoped>
.quiz-list-container {
  padding: 20px;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  font-size: 2rem;
  margin-bottom: 40px;
  color: #2c3e50;
}

.difficulty-section {
  margin-bottom: 50px;
}

.difficulty-title {
  text-align: left;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 3px solid #e0e0e0;
}

.difficulty-label {
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 15px;
}

.difficulty-subtitle {
  font-size: 1rem;
  color: #7f8c8d;
  font-weight: normal;
}

.quiz-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.quiz-card {
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  border: 2px solid transparent;
  background: white;
}

.quiz-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 不同难度的颜色主题 */
.quiz-card.easy {
  border-color: #27ae60;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f8f5 100%);
}

.quiz-card.easy:hover {
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.2);
}

.quiz-card.medium {
  border-color: #f39c12;
  background: linear-gradient(135deg, #fefaf6 0%, #fdf2e9 100%);
}

.quiz-card.medium:hover {
  box-shadow: 0 8px 25px rgba(243, 156, 18, 0.2);
}

.quiz-card.hard {
  border-color: #e74c3c;
  background: linear-gradient(135deg, #fdf7f7 0%, #fadbd8 100%);
}

.quiz-card.hard:hover {
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
}

.quiz-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 10px;
}

.quiz-title {
  font-size: 1.3rem;
  margin: 0;
  flex: 1;
  text-align: left;
  color: #2c3e50;
}

.difficulty-stars {
  flex-shrink: 0;
  margin-top: 2px;
}

.quiz-description {
  font-size: 0.95rem;
  color: #666;
  margin-bottom: 15px;
  text-align: left;
  line-height: 1.4;
}

.quiz-info {
  text-align: left;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.option-count {
  font-size: 0.85rem;
  color: #7f8c8d;
  background-color: #ecf0f1;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.question-count {
  font-size: 0.85rem;
  color: #3498db;
  background-color: #e8f4fd;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .quiz-list-container {
    padding: 15px;
  }
  
  .title {
    font-size: 1.6rem;
    margin-bottom: 30px;
  }
  
  .difficulty-section {
    margin-bottom: 35px;
  }
  
  .difficulty-title {
    text-align: center;
  }
  
  .difficulty-label {
    font-size: 1.3rem;
  }
  
  .difficulty-subtitle {
    font-size: 0.9rem;
  }
  
  .quiz-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .quiz-card {
    padding: 16px;
  }
  
  .quiz-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .quiz-title {
    font-size: 1.2rem;
  }
}
</style>