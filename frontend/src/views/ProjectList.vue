<template>
  <div class="project-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>项目管理</h1>
        <p>管理您的知识点提取项目</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="createProject">
          <el-icon>
            <Plus/>
          </el-icon>
          创建项目
        </el-button>

        <el-button @click="refreshProjects" :loading="loading">
          <el-icon>
            <Refresh/>
          </el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="project-grid">
      <!-- 加载状态 - 使用骨架屏 -->
      <CardListSkeleton
        v-if="loading"
        :card-count="6"
        :show-header="false"
        :show-search="false"
      />

      <!-- 空状态 -->
      <div v-else-if="projects.length === 0" class="empty-state">
        <el-empty description="暂无项目">
          <el-button type="primary" @click="createProject">创建第一个项目</el-button>
        </el-empty>
      </div>

      <!-- 项目卡片列表 -->
      <div v-else class="project-cards">
        <el-card
            v-for="project in projects"
            :key="project.id"
            class="project-card"
            shadow="hover"
        >
          <template #header>

            <div class="card-header">
              <span class="project-name">{{ project.name }}</span>
              <el-dropdown @command="handleCommand">
                <el-button link size="small">
                  <el-icon>
                    <MoreFilled/>
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit-${project.id}`">
                      <el-icon>
                        <Edit/>
                      </el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item :command="`knowledge-${project.id}`">
                      <el-icon>
                        <Document/>
                      </el-icon>
                      知识点管理
                    </el-dropdown-item>
                    <el-dropdown-item :command="`delete-${project.id}`" divided>
                      <el-icon>
                        <Delete/>
                      </el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>

          <div class="card-content">
            <p class="project-description">
              {{ project.description || '暂无描述' }}
            </p>

            <div class="project-meta">
              <div class="meta-item">
                <el-icon>
                  <Calendar/>
                </el-icon>
                <span>{{ formatDate(project.createdAt, 'date') }}</span>
              </div>
              <div class="meta-item">
                <el-icon>
                  <User/>
                </el-icon>
                <span>{{ project.createdBy }}</span>
              </div>
            </div>
          </div>

          <template #footer>
            <div class="card-actions">

              <el-button size="small" @click="editProject(project.id)">
                编辑
              </el-button>
              <el-button
                  type="primary"
                  size="small"
                  @click="viewProject(project.id)"
              >
                项目详情
              </el-button>
              <el-button
                  type="info"
                  size="small"
                  @click="manageKnowledge(project.id)"
              >
                知识点管理
              </el-button>
              <el-button
                  type="warning"
                  size="small"
                  @click="manageExams(project.id)"
              >
                考卷管理
              </el-button>
            </div>
          </template>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessageBox, ElMessage} from 'element-plus'
import {Plus, MoreFilled, Edit, Document, Delete, Calendar, User, Refresh} from '@element-plus/icons-vue'
import {projectAPI} from '@/api'
import {useErrorHandler} from '@/composables/useErrorHandler'
import {useLoading} from '@/composables/useLoading'
import {formatDate} from '@/utils/environment'
import {useProjectEvents} from '@/utils/eventBus'

const router = useRouter()

// 使用 Composables
const {loading, execute} = useLoading()
const {handleApiError, confirmDelete} = useErrorHandler()
const {notifyProjectDeleted, onProjectListRefresh} = useProjectEvents()

const projects = ref([])

// 获取项目列表
const fetchProjects = async () => {
  await execute(async () => {
    console.log('🔄 开始获取项目列表...')
    const response = await projectAPI.getProjects()
    projects.value = response.data.data
    console.log(`✅ 成功获取 ${projects.value.length} 个项目`)
    return response.data.data
  })
}

// 手动刷新项目列表
const refreshProjects = async () => {
  console.log('🔄 手动刷新项目列表...')
  await fetchProjects()
  ElMessage.success('项目列表已刷新')
}

// 创建项目
const createProject = () => {
  router.push('/projects/new')
}

// 查看项目详情
const viewProject = (id) => {
  router.push(`/projects/${id}`)
}

// 编辑项目
const editProject = (id) => {
  router.push(`/projects/${id}/edit`)
}

// 知识点管理
const manageKnowledge = (id) => {
  router.push(`/projects/${id}/knowledge`)
}

// 考卷管理
const manageExams = (id) => {
  router.push(`/projects/${id}/exams`)
}

// 删除项目
const deleteProject = async (id) => {
  const project = projects.value.find(p => p.id === id)
  const projectName = project && project.name || `项目${id}`

  const confirmed = await confirmDelete(projectName, '项目')
  if (confirmed) {
    await execute(async () => {
      await projectAPI.deleteProject(id)
      ElMessage.success('项目删除成功')
      // 通知项目删除
      notifyProjectDeleted(id)
      await fetchProjects()
    })
  }
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const projectId = parseInt(id)

  switch (action) {
    case 'edit':
      editProject(projectId)
      break
    case 'knowledge':
      manageKnowledge(projectId)
      break
    case 'delete':
      deleteProject(projectId)
      break
  }
}

// 监听项目数据变更事件
let cleanupEventListener = null

onMounted(() => {
  // 立即开始加载项目列表
  fetchProjects()

  // 监听项目数据变更，自动刷新列表
  cleanupEventListener = onProjectListRefresh(() => {
    console.log('📢 收到项目数据变更通知，自动刷新列表')
    fetchProjects()
  })
})

onUnmounted(() => {
  // 清理事件监听
  if (cleanupEventListener) {
    cleanupEventListener()
  }
})
</script>

<style scoped>
.project-list {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.project-grid {
  min-height: 400px;
}

.project-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.project-card {
  transition: transform 0.2s;
}

.project-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-name {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
}

.project-description {
  color: #606266;
  font-size: 14px;
  line-height: 1;
  margin-bottom: 16px;
  margin-top: 0;
  height: 12px;
}

.project-meta {
  display: flex;
  gap: 16px;
}

.card-content {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}
</style>
