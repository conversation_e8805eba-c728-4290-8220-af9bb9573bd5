<template>
  <div class="exam-list">
    <!-- 页面头部 -->
    <PageHeader
      title="考卷管理"
      :subtitle="projectName"
      @back="goBack"
    >
      <template #actions>
        <el-button @click="goToGeneration" type="primary">
          <el-icon><Plus /></el-icon>
          生成考卷
        </el-button>
      </template>
    </PageHeader>

    <!-- 筛选和搜索 -->
    <div class="filter-bar">
      <div class="filter-left">
        <el-select v-model="statusFilter" placeholder="状态筛选" clearable style="width: 120px;">
          <el-option label="已生成" value="generated" />
          <el-option label="已审核" value="reviewed" />
          <el-option label="已发布" value="published" />
        </el-select>
        <el-select v-model="difficultyFilter" placeholder="难度筛选" clearable style="width: 120px;">
          <el-option label="简单" value="easy" />
          <el-option label="中等" value="medium" />
          <el-option label="困难" value="hard" />
          <el-option label="竞赛" value="competition" />
          <el-option label="中考" value="middle_school_exam" />
          <el-option label="高考" value="high_school_exam" />
        </el-select>
      </div>
      <div class="filter-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索考卷标题..."
          style="width: 250px;"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button @click="refreshList">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 考卷列表 -->
    <div class="exam-cards">
      <!-- 加载状态 - 使用骨架屏 -->
      <CardListSkeleton
        v-if="loading"
        :card-count="6"
        :show-header="false"
        :show-search="false"
      />

      <!-- 空状态 -->
      <div v-else-if="filteredExams.length === 0" class="empty-container">
        <el-empty description="暂无考卷">
          <el-button type="primary" @click="goToGeneration">
            <el-icon><Plus /></el-icon>
            生成第一份考卷
          </el-button>
        </el-empty>
      </div>

      <!-- 考卷卡片列表 -->
      <div v-else class="cards-grid">
        <transition-group name="exam-card" tag="div" class="cards-grid-inner">
          <el-card
            v-for="exam in filteredExams"
            :key="exam.examPaper.id"
            class="exam-card"
            shadow="hover"
          >
          <template #header>
            <div class="card-header">
              <div class="title-section">
                <h3 class="exam-title">{{ exam.examPaper.title }}</h3>
                <div class="exam-meta">
                  <StatusTag :status="exam.examPaper.status" type="status" size="small" />
                  <StatusTag :status="exam.examPaper.difficulty" type="difficulty" size="small" />
                </div>
              </div>
              <div class="actions">
                <el-dropdown @command="handleCommand" trigger="click">
                  <el-button link>
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`preview_${exam.examPaper.id}`">
                        <el-icon><View /></el-icon>
                        预览
                      </el-dropdown-item>
                      <el-dropdown-item :command="`records_${exam.examPaper.id}`">
                        <el-icon><Document /></el-icon>
                        考试记录
                      </el-dropdown-item>
                      <el-dropdown-item :command="`download_${exam.examPaper.id}`">
                        <el-icon><Download /></el-icon>
                        下载
                      </el-dropdown-item>
                      <el-dropdown-item :command="`regenerate_${exam.examPaper.id}`">
                        <el-icon><Refresh /></el-icon>
                        重新生成
                      </el-dropdown-item>
                      <el-dropdown-item divided :command="`delete_${exam.examPaper.id}`">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </template>

          <div class="card-content">
            <div class="exam-info">
              <div class="info-item">
                <span class="label">目标等级：</span>
                <span class="value">{{ exam.examPaper.targetLevel }}</span>
              </div>
              <div v-if="exam.examPaper.description" class="info-item">
                <span class="label">描述：</span>
                <span class="value">{{ exam.examPaper.description }}</span>
              </div>
              <div class="info-item">
                <span class="label">知识点：</span>
                <div class="value">
                  <KnowledgePointsDisplay
                    :project-id="projectId"
                    :knowledge-points="exam.examPaper.knowledgePoints"
                    :max-display="3"
                  />
                </div>
              </div>
              <div class="info-item">
                <span class="label">考试次数：</span>
                <span class="value">{{ exam.examCount || 0 }} 次</span>
              </div>
              <div class="info-item">
                <span class="label">平均分：</span>
                <span class="value">{{ exam.examCount > 0 ? (exam.avgScore || 0).toFixed(1) : '--' }} 分</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(exam.examPaper.createdAt) }}</span>
              </div>
            </div>

            <div class="card-actions">
              <el-button size="small" @click="previewExam(exam)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button size="small" @click="goToRecords(exam)">
                <el-icon><Document /></el-icon>
                考试记录
              </el-button>
              <el-button size="small" @click="regenerateExam(exam)">
                <el-icon><Refresh /></el-icon>
                重新生成
              </el-button>
            </div>
          </div>
        </el-card>
        </transition-group>
      </div>
    </div>

    <!-- 预览对话框 -->
    <ExamPreviewDialog
      v-model:visible="showPreviewDialog"
      :exam-content="currentExam?.examPaper?.contentMarkdown"
      :exam-title="currentExam?.examPaper?.title"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, View, Download,
  Delete, MoreFilled, Document
} from '@element-plus/icons-vue'
import { examAPI } from '@/api'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import ExamPreviewDialog from '@/components/ExamPreviewDialog.vue'
import KnowledgePointsDisplay from '@/components/knowledge/KnowledgePointsDisplay.vue'
import { useProject } from '@/composables/useProject'
import { useErrorHandler } from '@/composables/useErrorHandler'
import { formatDate } from '@/utils/environment'
import { executeWithLoading, executeParallel } from '@/utils/apiUtils'

const router = useRouter()
const route = useRoute()

// 使用 Composables
const { project, projectId, projectName, fetchProject } = useProject()
const { handleApiError, handleAsyncError, confirmDelete } = useErrorHandler()

// 状态管理
const examList = ref([])
const loading = ref(false)
const statusFilter = ref('')
const difficultyFilter = ref('')
const searchKeyword = ref('')
const showPreviewDialog = ref(false)
const currentExam = ref(null)

// 计算属性
const filteredExams = computed(() => {
  let filtered = examList.value

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(exam => exam.examPaper.status === statusFilter.value)
  }

  // 难度筛选
  if (difficultyFilter.value) {
    filtered = filtered.filter(exam => exam.examPaper.difficulty === difficultyFilter.value)
  }

  // 关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(exam =>
      exam.examPaper.title.toLowerCase().includes(keyword) ||
      (exam.examPaper.description && exam.examPaper.description.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

// 页面初始化 - 立即显示加载状态并开始加载
onMounted(async () => {
  // 使用工具类并行加载项目信息和考试列表
  await executeWithLoading(async () => {
    await executeParallel([
      loadProject,
      loadExamList
    ])
  }, {
    loadingRef: loading,
    errorMessage: '页面数据加载失败'
  })
})

// 加载项目信息
const loadProject = async () => {
  await fetchProject()
}

// 加载考卷列表
const loadExamList = async () => {
  const response = await examAPI.getExamPapersWithStatistics(projectId.value)
  examList.value = response.data.data || []
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 去考卷生成页面
const goToGeneration = () => {
  router.push(`/projects/${projectId.value}/exams/generate`)
}

// 刷新列表
const refreshList = async () => {
  await executeWithLoading(async () => {
    await loadExamList()
  }, {
    loadingRef: loading,
    errorMessage: '刷新数据失败'
  })
}

// 预览考卷
const previewExam = (exam) => {
  currentExam.value = exam
  showPreviewDialog.value = true
}

// 下载考卷
const downloadExam = (exam) => {
  if (!exam.examPaper.contentMarkdown) {
    ElMessage.error('考卷内容为空，无法下载')
    return
  }

  const blob = new Blob([exam.examPaper.contentMarkdown], { type: 'text/markdown;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')

  link.href = url
  link.download = `${exam.examPaper.title}.md`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('文件下载成功')
}

// 删除考卷
const deleteExam = async (exam) => {
  const confirmed = await confirmDelete(exam.examPaper.title, '考卷')
  if (confirmed) {
    await handleAsyncError(async () => {
      await examAPI.deleteExamPaper(projectId.value, exam.examPaper.id)

      // 直接从列表中移除该考卷，而不是重新加载整个列表
      const index = examList.value.findIndex(e => e.examPaper.id === exam.examPaper.id)
      if (index !== -1) {
        examList.value.splice(index, 1)
      }

      ElMessage.success('考卷删除成功')
    }, { errorMessage: '删除考卷失败' })
  }
}

// 跳转到考试记录页面
const goToRecords = (exam) => {
  router.push(`/projects/${projectId.value}/exams/${exam.examPaper.id}/records`)
}

// 重新生成考卷
const regenerateExam = (exam) => {
  // 跳转到考卷生成页面，并传递考卷信息作为参数
  router.push({
    path: `/projects/${projectId.value}/exams/generate`,
    query: {
      regenerate: 'true',
      examId: exam.examPaper.id,
      title: exam.examPaper.title,
      description: exam.examPaper.description || '',
      targetLevel: exam.examPaper.targetLevel,
      difficulty: exam.examPaper.difficulty,
      knowledgePoints: exam.examPaper.knowledgePoints || '[]',
      additionalRequirements: exam.examPaper.additionalRequirements || ''
    }
  })
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  const [action, examId] = command.split('_')
  const exam = examList.value.find(e => e.examPaper.id === parseInt(examId))

  if (!exam) return

  switch (action) {
    case 'preview':
      previewExam(exam)
      break
    case 'records':
      goToRecords(exam)
      break
    case 'download':
      downloadExam(exam)
      break
    case 'regenerate':
      regenerateExam(exam)
      break
    case 'delete':
      deleteExam(exam)
      break
  }
}

// 获取知识点文本（保留这个函数因为它是特定的业务逻辑）

// 获取知识点文本
const getKnowledgePointsText = (knowledgePointsData) => {
  try {
    let points = []

    // 处理不同的数据格式
    if (!knowledgePointsData) {
      return '无'
    }

    // 如果是字符串，尝试解析JSON
    if (typeof knowledgePointsData === 'string') {
      try {
        points = JSON.parse(knowledgePointsData)
      } catch (parseError) {
        // 如果解析失败，可能是简单的字符串
        return knowledgePointsData || '无'
      }
    }
    // 如果已经是数组
    else if (Array.isArray(knowledgePointsData)) {
      points = knowledgePointsData
    }
    // 如果是对象，可能需要提取特定字段
    else if (typeof knowledgePointsData === 'object') {
      // 如果对象有name或title字段，提取这些字段
      if (knowledgePointsData.name) {
        return knowledgePointsData.name
      } else if (knowledgePointsData.title) {
        return knowledgePointsData.title
      } else {
        return '格式不支持'
      }
    }

    // 处理数组数据
    if (!Array.isArray(points)) {
      return '数据格式错误'
    }

    if (points.length === 0) {
      return '无'
    }

    // 提取知识点名称
    const pointNames = points.map(point => {
      if (typeof point === 'string') {
        return point
      } else if (typeof point === 'object' && point !== null) {
        // 尝试提取对象的名称字段
        return point.name || point.title || point.fullPath || point.path || JSON.stringify(point)
      }
      return String(point)
    }).filter(name => name && name.trim())

    if (pointNames.length === 0) {
      return '无'
    }

    if (pointNames.length <= 3) {
      return pointNames.join('、')
    }

    return `${pointNames.slice(0, 3).join('、')} 等${pointNames.length}个`
  } catch (error) {
    console.warn('解析知识点数据失败:', error, knowledgePointsData)
    return '解析失败'
  }
}
</script>

<style scoped>
.exam-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.filter-left,
.filter-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.exam-cards {
  min-height: 400px;
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.cards-grid-inner {
  display: contents;
}

.exam-card {
  transition: all 0.3s ease;
}

.exam-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.exam-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.exam-meta {
  display: flex;
  gap: 8px;
}

.actions {
  margin-left: 10px;
}

.exam-info {
  margin-bottom: 15px;
  height: 155px;
}

.info-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #909399;
  min-width: 70px;
  flex-shrink: 0;
}

.value {
  color: #606266;
  flex: 1;
  word-break: break-word;
}

.card-actions {
  display: flex;
  gap: 8px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

/* 删除动画效果 */
.exam-card-enter-active,
.exam-card-leave-active {
  transition: all 0.3s ease;
}

.exam-card-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.exam-card-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.exam-card-move {
  transition: transform 0.3s ease;
}
</style>
