<template>
  <div class="mahjong-challenge-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载题目...</div>
    </div>

    <!-- 考试进行中 -->
    <div v-else-if="!examCompleted" class="exam-container">
      <!-- 头部信息 -->
      <div class="exam-header">
        <button @click="goBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
        </button>
        <div class="exam-info">
          <h2 class="exam-title">麻将胡牌挑战</h2>
          <div class="question-counter">
            第 {{ currentQuestionIndex + 1 }} 题 / 共 {{ questions.length }} 题
          </div>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: progressPercentage + '%' }"
        ></div>
      </div>

      <!-- 题目区域 -->
      <div class="question-area">
        <div class="question-title">请选择所有可能的听牌数字</div>
        
        <!-- 麻将牌显示 -->
        <div class="mahjong-tiles">
          <div 
            v-for="(tile, index) in currentQuestion.tiles" 
            :key="index"
            class="mahjong-tile"
          >
            <div class="tile-number">{{ tile }}</div>
            <div class="tile-character">{{ chineseToWan[tile] }}</div>
          </div>
        </div>

        <!-- 答案选项 -->
        <div class="answer-options">
          <div class="options-title"> 多 选 </div>
          <div class="options-grid">
            <div
              v-for="option in ['一', '二', '三', '四', '五', '六', '七', '八', '九']"
              :key="option"
              class="option-tile"
              :class="{ 
                selected: selectedAnswers.includes(option),
                correct: showResult && currentQuestion.answers.includes(option),
                incorrect: showResult && selectedAnswers.includes(option) && !currentQuestion.answers.includes(option)
              }"
              @click="toggleAnswer(option)"
            >
              <div class="tile-number">{{ option }}</div>
              <div class="tile-character">{{ chineseToWan[option] }}</div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button 
            v-if="!showResult"
            @click="submitAnswer" 
            class="submit-button"
            :disabled="selectedAnswers.length === 0"
          >
            提交答案
          </button>
          <button 
            v-else
            @click="nextQuestion" 
            class="next-button"
          >
            {{ currentQuestionIndex < questions.length - 1 ? '下一题' : '查看结果' }}
          </button>
        </div>

        <!-- 结果显示 -->
        <div v-if="showResult" class="result-display">
          <div class="result-status" :class="{ correct: isCurrentAnswerCorrect, incorrect: !isCurrentAnswerCorrect }">
            {{ isCurrentAnswerCorrect ? '✓ 回答正确！' : '✗ 回答错误' }}
          </div>
          <div class="correct-answer">
            正确答案：{{ currentQuestion.answers.join('、') }}
          </div>
          <div class="explanation">
            {{ currentQuestion.explanation }}
          </div>
        </div>
      </div>
    </div>

    <!-- 考试完成 -->
    <div v-else class="exam-result">
      <div class="result-header">
        <h2>考试完成！</h2>
        <div class="final-score">{{ score }} 分</div>
      </div>
      
      <div class="result-stats">
        <div class="stat-item">
          <div class="stat-number">{{ correctCount }}</div>
          <div class="stat-label">答对</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ questions.length - correctCount }}</div>
          <div class="stat-label">答错</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ Math.round((correctCount / questions.length) * 100) }}%</div>
          <div class="stat-label">正确率</div>
        </div>
      </div>

      <div class="result-actions">
        <button @click="restartExam" class="restart-button">
          重新挑战
        </button>
        <button @click="goBack" class="back-button-result">
          返回列表
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ArrowLeft } from '@element-plus/icons-vue';
import { getRandomQuestions, chineseNumbers, chineseToWan } from '@/data/mahjong-winning.js';

const router = useRouter();
const route = useRoute();

// 状态管理
const loading = ref(true);
const questions = ref([]);
const currentQuestionIndex = ref(0);
const selectedAnswers = ref([]);
const showResult = ref(false);
const examCompleted = ref(false);
const score = ref(0);
const correctCount = ref(0);

// 计算属性
const currentQuestion = computed(() => {
  return questions.value[currentQuestionIndex.value] || {};
});

const progressPercentage = computed(() => {
  return Math.round((currentQuestionIndex.value / questions.value.length) * 100);
});

const isCurrentAnswerCorrect = computed(() => {
  if (!currentQuestion.value.answers) return false;
  
  const correctAnswers = currentQuestion.value.answers.sort();
  const userAnswers = selectedAnswers.value.sort();
  
  return correctAnswers.length === userAnswers.length &&
         correctAnswers.every(answer => userAnswers.includes(answer));
});

// 方法
const initializeExam = () => {
  loading.value = true;
  questions.value = getRandomQuestions(10);
  currentQuestionIndex.value = 0;
  selectedAnswers.value = [];
  showResult.value = false;
  examCompleted.value = false;
  score.value = 0;
  correctCount.value = 0;
  loading.value = false;
};

const toggleAnswer = (option) => {
  if (showResult.value) return;
  
  const index = selectedAnswers.value.indexOf(option);
  if (index > -1) {
    selectedAnswers.value.splice(index, 1);
  } else {
    selectedAnswers.value.push(option);
  }
};

const submitAnswer = () => {
  if (selectedAnswers.value.length === 0) return;
  
  showResult.value = true;
  
  if (isCurrentAnswerCorrect.value) {
    score.value += 10;
    correctCount.value++;
  }
};

const nextQuestion = () => {
  if (currentQuestionIndex.value < questions.value.length - 1) {
    currentQuestionIndex.value++;
    selectedAnswers.value = [];
    showResult.value = false;
  } else {
    examCompleted.value = true;
  }
};

const restartExam = () => {
  initializeExam();
};

const goBack = () => {
  router.back();
};

// 生命周期
onMounted(() => {
  initializeExam();
});
</script>

<style scoped>
.mahjong-challenge-container {
  min-height: 100vh;
  background: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  overflow-y: auto;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4caf50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #666;
}

/* 考试容器 */
.exam-container {
  padding: 0;
  max-width: 800px;
  margin: 0 auto;
}

/* 头部信息 */
.exam-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  padding: 0;
  position: relative;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: absolute;
  left: 0;
}

.back-button:hover {
  background: #e0e0e0;
}

.exam-info {
  text-align: center;
  margin: 0;
}

.exam-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.question-counter {
  font-size: 14px;
  color: #666;
}

.score-display {
  font-size: 16px;
  font-weight: 600;
  color: #e74c3c;
  min-width: 80px;
  text-align: right;
}

/* 进度条 */
.progress-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 24px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #45a049);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 题目区域 */
.question-area {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

/* 麻将牌样式 */
.mahjong-tiles {
  display: flex;
  flex-wrap: nowrap;
  gap: 0;
  justify-content: center;
  margin-bottom: 32px;
  padding: 0;
  background: #f8f9fa;
  border-radius: 12px;
  overflow-x: auto;
}

.mahjong-tile {
  width: 35px;
  height: 50px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  flex-shrink: 0;
}

.tile-number {
  font-size: 18px;
  font-weight: 700;
  color: #e74c3c;
  line-height: 1;
  margin-bottom: 2px;
}

.tile-character {
  font-size: 18px;
  color: #2c3e50;
  font-weight: 700;
  line-height: 1;
}

/* 答案选项 */
.answer-options {
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.options-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  text-align: center;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  width: 270px;
  margin: 0 auto;
  justify-items: center;
}

.option-tile {
  width: 60px;
  height: 80px;
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.option-tile:hover {
  border-color: #4caf50;
  transform: translateY(-2px);
}

.option-tile.selected {
  border-color: #4caf50;
  background: #e8f5e8;
  transform: translateY(-2px);
}

.option-tile.correct {
  border-color: #4caf50;
  background: #e8f5e8;
}

.option-tile.incorrect {
  border-color: #f44336;
  background: #ffebee;
}

.option-tile .tile-number {
  font-size: 20px;
  font-weight: 700;
  color: #e74c3c;
  line-height: 1;
  margin-bottom: 4px;
}

.option-tile .tile-character {
  font-size: 20px;
  color: #2c3e50;
  font-weight: 700;
  line-height: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.submit-button,
.next-button {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button {
  background: #4caf50;
  color: white;
}

.submit-button:hover:not(:disabled) {
  background: #45a049;
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.next-button {
  background: #2196f3;
  color: white;
}

.next-button:hover {
  background: #1976d2;
}

/* 结果显示 */
.result-display {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.result-status {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

.result-status.correct {
  color: #4caf50;
}

.result-status.incorrect {
  color: #f44336;
}

.correct-answer {
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 12px;
}

.explanation {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 考试结果 */
.exam-result {
  padding: 32px 16px;
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.result-header {
  margin-bottom: 32px;
}

.result-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 16px 0;
}

.final-score {
  font-size: 48px;
  font-weight: 700;
  color: #e74c3c;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.result-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.restart-button,
.back-button-result {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.restart-button {
  background: #4caf50;
  color: white;
}

.restart-button:hover {
  background: #45a049;
}

.back-button-result {
  background: #6c757d;
  color: white;
}

.back-button-result:hover {
  background: #5a6268;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-container {
    padding: 0;
  }
  
  .exam-header {
    margin-bottom: 12px;
  }
  
  .exam-title {
    font-size: 18px;
  }
  
  .question-counter {
    font-size: 13px;
  }
  
  .score-display {
    font-size: 14px;
  }
  
  .question-area {
    padding: 12px;
  }
  
  .question-title {
    font-size: 16px;
  }
  
  .mahjong-tiles {
    gap: 0;
    padding: 0;
  }
  
  .mahjong-tile {
    width: 30px;
    height: 42px;
  }
  
  .mahjong-tile .tile-number {
    font-size: 16px;
  }
  
  .mahjong-tile .tile-character {
    font-size: 16px;
  }
  
  .options-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    width: 210px;
    justify-items: center;
  }
  
  .option-tile {
    width: 50px;
    height: 70px;
  }
  
  .option-tile .tile-number {
    font-size: 18px;
  }
  
  .option-tile .tile-character {
    font-size: 18px;
  }
  
  .result-header h2 {
    font-size: 24px;
  }
  
  .final-score {
    font-size: 36px;
  }
}

@media (max-width: 480px) {
  .exam-container {
    padding: 0;
  }
  
  .question-area {
    padding: 8px;
  }
  
  .mahjong-tiles {
    gap: 0;
    padding: 0;
  }
  
  .mahjong-tile {
    width: 26px;
    height: 36px;
  }
  
  .mahjong-tile .tile-number {
    font-size: 14px;
  }
  
  .mahjong-tile .tile-character {
    font-size: 14px;
  }
  
  .options-grid {
    gap: 6px;
    width: 180px;
    justify-items: center;
  }
  
  .option-tile {
    width: 45px;
    height: 60px;
  }
  
  .option-tile .tile-number {
    font-size: 16px;
  }
  
  .result-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .restart-button,
  .back-button-result {
    width: 100%;
  }
}
</style> 