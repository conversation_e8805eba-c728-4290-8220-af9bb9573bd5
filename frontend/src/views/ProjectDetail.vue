<template>
  <div class="project-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" link size="large">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1>{{ project?.name || '项目详情' }}</h1>
          <p v-if="project?.description">{{ project.description }}</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="editProject" type="primary">
          <el-icon><Edit /></el-icon>
          编辑项目
        </el-button>
      </div>
    </div>

    <!-- 项目信息卡片 -->
    <el-row :gutter="24" class="info-cards">
      <el-col :span="6">
        <el-card class="info-card">
          <div class="info-item">
            <div class="info-label">创建时间</div>
            <div class="info-value">{{ formatDate(project?.createdAt, 'datetime') }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="info-card">
          <div class="info-item">
            <div class="info-label">创建者</div>
            <div class="info-value">{{ project?.createdBy || '-' }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="info-card">
          <div class="info-item">
            <div class="info-label">考卷数量</div>
            <div class="info-value">{{ examPapers.length }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="info-card">
          <div class="info-item">
            <div class="info-label">考试记录</div>
            <div class="info-value">-</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航 -->
    <el-card class="nav-card">
      <template #header>
        <span>功能导航</span>
      </template>
      <div class="nav-buttons">
        <el-button @click="manageKnowledge" type="primary" size="large">
          <el-icon><Document /></el-icon>
          知识点管理
        </el-button>
        <el-button @click="manageExams" type="warning" size="large">
          <el-icon><EditPen /></el-icon>
          考卷管理
        </el-button>
        <el-button @click="manageFlashcards" type="primary" size="large">
          <el-icon><Postcard /></el-icon>
          卡片管理
        </el-button>
        <el-button @click="analyzeExam" type="success" size="large">
          <el-icon><DataAnalysis /></el-icon>
          考卷分析
        </el-button>
        <el-button @click="manageWrongQuestions" type="danger" size="large">
          <el-icon><Warning /></el-icon>
          错题管理
        </el-button>
        <el-button @click="configureReview" type="info" size="large">
          <el-icon><Setting /></el-icon>
          复习配置
        </el-button>
      </div>
    </el-card>

    <!-- 考试记录表格 -->
    <ProjectExamRecords
      v-if="projectId"
      :project-id="projectId"
      :exam-papers="examPapers"
      @refresh="loadAllData"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft, Edit, Document, EditPen, DataAnalysis, Warning, Setting, Postcard
} from '@element-plus/icons-vue'
import { projectAPI, examAPI } from '@/api'
import { formatDate } from '@/utils/environment'
import ProjectExamRecords from '@/components/project/ProjectExamRecords.vue'
import { executeWithLoading, executeParallel } from '@/utils/apiUtils'

// 参数验证函数
const validateAndConvertId = (id, type) => {
  const numId = parseInt(id)
  if (isNaN(numId) || numId <= 0) {
    return null
  }
  return numId
}

const isValidId = (id, type) => {
  return validateAndConvertId(id, type) !== null
}

const router = useRouter()
const route = useRoute()

const projectId = computed(() => {
  const id = route.params.id
  if (!isValidId(id, 'projectId')) {
    console.error('❌ 无效的项目ID:', id)
    return null
  }
  return validateAndConvertId(id, 'projectId')
})

const project = ref(null)
const examPapers = ref([])
const loading = ref(false)

// 页面初始化
onMounted(async () => {
  if (!projectId.value) {
    ElMessage.error('页面参数无效，请检查URL')
    router.back()
    return
  }

  await loadAllData()
})

// 加载所有数据
const loadAllData = async () => {
  await executeWithLoading(async () => {
    await executeParallel([
      loadProject,
      loadExamPapers
    ])
  }, {
    loadingRef: loading,
    errorMessage: '加载数据失败'
  })
}

// 加载项目信息
const loadProject = async () => {
  const response = await projectAPI.getProject(projectId.value)
  project.value = response.data.data
}

// 加载考卷列表
const loadExamPapers = async () => {
  const response = await examAPI.getExamPapers(projectId.value)
  examPapers.value = response.data.data || []
}

// 返回上一页
const goBack = () => {
  router.push('/')
}

// 编辑项目
const editProject = () => {
  router.push(`/projects/${projectId.value}/edit`)
}

// 知识点管理
const manageKnowledge = () => {
  router.push(`/projects/${projectId.value}/knowledge`)
}

// 考卷管理
const manageExams = () => {
  router.push(`/projects/${projectId.value}/exams`)
}

// 卡片管理
const manageFlashcards = () => {
  router.push(`/projects/${projectId.value}/flashcards`)
}

// 考卷分析
const analyzeExam = () => {
  router.push(`/projects/${projectId.value}/exam-analysis`)
}

// 错题管理
const manageWrongQuestions = () => {
  router.push(`/projects/${projectId.value}/wrong-questions`)
}

// 复习配置
const configureReview = () => {
  router.push(`/projects/${projectId.value}/review-configuration`)
}
</script>

<style scoped>
.project-detail {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.info-cards {
  margin-bottom: 24px;
}

.info-card {
  text-align: center;
}

.info-item {
  padding: 16px;
}

.info-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.info-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.nav-card {
  margin-bottom: 24px;
}

.nav-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 16px 0;
}

.nav-buttons .el-button {
  min-width: 140px;
  height: 48px;
}
</style>
