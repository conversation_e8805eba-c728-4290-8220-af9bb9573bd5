<template>
  <div class="wrong-questions-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>错题管理</h1>
      <p class="page-description">管理项目中的错题，支持按知识点分类查看</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索错题内容或知识点..."
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
          style="width: 300px; margin-right: 10px;"
        />
        <el-select
          v-model="selectedKnowledgePoint"
          placeholder="选择知识点"
          clearable
          @change="handleKnowledgePointFilter"
          style="width: 200px; margin-right: 10px;"
        >
          <el-option
            v-for="kp in knowledgePoints"
            :key="kp.knowledgePointId"
            :label="kp.knowledgePointName"
            :value="kp.knowledgePointId"
          />
        </el-select>
      </div>
      <div class="button-section">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加错题
        </el-button>
        <el-button @click="loadWrongQuestions">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="success" @click="showTodayReview">
          <el-icon><Reading /></el-icon>
          今日复习
          <el-badge
            v-if="reviewStatistics.todayReviewCount > 0"
            :value="reviewStatistics.todayReviewCount"
            class="review-badge"
          />
        </el-button>
        <el-button type="info" @click="configureReview">
          <el-icon><Setting /></el-icon>
          复习配置
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-bar">
      <el-card class="stat-card">
        <div class="stat-item">
          <span class="stat-label">总错题数：</span>
          <span class="stat-value">{{ statistics.totalCount || 0 }}</span>
        </div>
      </el-card>
    </div>

    <!-- 错题列表 -->
    <div class="questions-list">
      <!-- 加载状态 - 使用卡片骨架屏 -->
      <CardListSkeleton
        v-if="loading"
        :card-count="4"
        :show-header="false"
        :show-search="false"
      />

      <!-- 空状态 -->
      <el-card v-else-if="wrongQuestions.length === 0" class="empty-state">
        <div class="empty-content">
          <i class="el-icon-document-remove" style="font-size: 64px; color: #ddd;"></i>
          <p>暂无错题数据</p>
          <el-button type="primary" @click="showCreateDialog">添加第一个错题</el-button>
        </div>
      </el-card>

      <!-- 错题卡片列表 -->
      <el-card
        v-else
        v-for="question in wrongQuestions"
        :key="question.id"
        class="question-card"
        shadow="hover"
      >
        <div class="question-header">
          <div class="question-info">
            <h3 class="question-title">{{ question.knowledgePointName }}</h3>
            <p class="question-path">{{ question.knowledgePointPath }}</p>
            <p class="question-time">录入时间：{{ formatDate(question.createdAt) }}</p>
          </div>
          <div class="question-actions">
            <el-button size="small" link @click="showQuestionDetail(question)">
              查看详情
            </el-button>
            <el-button size="small" link @click="editQuestion(question)">
              编辑
            </el-button>
            <el-button
              size="small"
              link
              style="color: #f56c6c;"
              @click="deleteQuestion(question)"
            >
              删除
            </el-button>
          </div>
        </div>
        
        <div class="question-content">
          <div class="content-preview" v-html="renderMarkdownPreview(question.contentMarkdown)"></div>
        </div>
      </el-card>
    </div>

    <!-- 创建/编辑错题对话框 -->
    <el-dialog
      :title="dialogMode === 'create' ? '添加错题' : '编辑错题'"
      v-model="dialogVisible"
      width="90%"
      :before-close="handleDialogClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="wrong-question-dialog"
      top="2vh"
      align-center
    >
      <div class="dialog-content">
        <wrong-question-form
          ref="questionForm"
          :mode="dialogMode"
          :question="currentQuestion"
          :project-id="projectId"
          @submit="handleFormSubmit"
          @cancel="handleDialogClose"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleFormSubmit" :loading="submitting">
            {{ dialogMode === 'create' ? '创建' : '更新' }}
          </el-button>
          <el-button @click="handleDialogClose">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看错题详情对话框 -->
    <el-dialog
      title="错题详情"
      v-model="viewDialogVisible"
      width="80%"
      align-center
      class="question-detail-dialog"
    >
      <wrong-question-detail
        v-if="viewDialogVisible"
        :question="viewQuestion"
        :current-index="currentQuestionIndex"
        :total-count="wrongQuestions.length"
        :show-navigation="true"
        @previous="goToPreviousQuestion"
        @next="goToNextQuestion"
      />
    </el-dialog>

    <!-- 今日复习对话框 -->
    <el-dialog
      title="今日复习"
      v-model="reviewDialogVisible"
      width="80%"
      align-center
      class="review-dialog"
    >
      <div v-if="reviewQuestions.length === 0" class="empty-review">
        <el-empty description="今日暂无需要复习的错题">
          <el-button type="primary" @click="reviewDialogVisible = false">知道了</el-button>
        </el-empty>
      </div>
      <div v-else>
        <div class="review-progress">
          <span>复习进度：{{ currentReviewIndex + 1 }} / {{ reviewQuestions.length }}</span>
          <el-progress
            :percentage="Math.round(((currentReviewIndex + 1) / reviewQuestions.length) * 100)"
            :stroke-width="6"
            style="margin-top: 8px;"
          />
        </div>
        <wrong-question-detail
          v-if="currentReviewQuestion"
          :question="currentReviewQuestion"
          :current-index="currentReviewIndex"
          :total-count="reviewQuestions.length"
          :show-navigation="true"
          :review-id="currentReviewQuestion.reviewId"
          @previous="goToPreviousReview"
          @next="goToNextReview"
          @reviewCompleted="handleReviewCompleted"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Reading, Setting } from '@element-plus/icons-vue'
import { wrongQuestionAPI, reviewAPI } from '@/api'
import WrongQuestionForm from '@/components/WrongQuestionForm.vue'
import WrongQuestionDetail from '@/components/WrongQuestionDetail.vue'
import { 
  DataFetcher, 
  confirmDelete, 
  showSuccess, 
  showError,
  createSafeRouteParam
} from '@/utils'
import { executeWithLoading, executeParallel } from '@/utils/apiUtils'

// 响应式数据
const route = useRoute()
const router = useRouter()
const projectId = createSafeRouteParam(route, 'id')

// 使用新工具类
const loading = ref(false)
const error = ref(null)

const wrongQuestions = ref([])
const knowledgePoints = ref([])
const statistics = ref({})
const searchKeyword = ref('')
const selectedKnowledgePoint = ref('')
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogMode = ref('create') // 'create' or 'edit'
const currentQuestion = ref(null)
const viewQuestion = ref(null)
const currentQuestionIndex = ref(0) // 当前查看的错题索引
const searchTimer = ref(null)
const questionForm = ref(null) // 子组件引用
const submitting = ref(false) // 表单提交状态

// 复习相关数据
const reviewDialogVisible = ref(false)
const reviewQuestions = ref([])
const currentReviewIndex = ref(0)
const currentReviewQuestion = ref(null)
const reviewStatistics = ref({})

// 使用工具类的页面初始化
onMounted(async () => {
  await executeWithLoading(async () => {
    await executeParallel([
      loadWrongQuestions,
      loadKnowledgePoints,
      loadStatistics,
      loadReviewStatistics
    ])
  }, {
    loadingRef: loading,
    errorMessage: '页面数据加载失败'
  })
})

// 方法定义
const loadData = async () => {
  await executeWithLoading(async () => {
    await executeParallel([
      loadWrongQuestions,
      loadKnowledgePoints,
      loadStatistics,
      loadReviewStatistics
    ])
  }, {
    loadingRef: loading,
    errorMessage: '数据加载失败'
  })
}

const loadWrongQuestions = async () => {
  const params = {}

  if (searchKeyword.value) {
    params.keyword = searchKeyword.value
  }
  if (selectedKnowledgePoint.value) {
    params.knowledgePointId = selectedKnowledgePoint.value
  }

  const response = await wrongQuestionAPI.getWrongQuestions(projectId.value, params)
  wrongQuestions.value = response.data.data || []
}

const loadKnowledgePoints = async () => {
  const response = await wrongQuestionAPI.getWrongQuestionKnowledgePoints(projectId.value)
  knowledgePoints.value = response.data.data || []
}

const loadStatistics = async () => {
  const response = await wrongQuestionAPI.getWrongQuestionStatistics(projectId.value)
  statistics.value = response.data.data || {}
}

const handleSearch = () => {
  // 防抖处理
  clearTimeout(searchTimer.value)
  searchTimer.value = setTimeout(() => {
    loadWrongQuestions()
  }, 500)
}

const handleKnowledgePointFilter = () => {
  loadWrongQuestions()
}

const showCreateDialog = () => {
  console.log('🔧 点击添加错题按钮')
  dialogMode.value = 'create'
  currentQuestion.value = null
  dialogVisible.value = true
  console.log('🔧 对话框状态:', dialogVisible.value)
}

const editQuestion = (question) => {
  dialogMode.value = 'edit'
  currentQuestion.value = { ...question }
  dialogVisible.value = true
}

const showQuestionDetail = (question) => {
  // 找到当前错题在列表中的索引
  currentQuestionIndex.value = wrongQuestions.value.findIndex(q => q.id === question.id)
  viewQuestion.value = question
  viewDialogVisible.value = true
}

// 切换到上一题
const goToPreviousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
    viewQuestion.value = wrongQuestions.value[currentQuestionIndex.value]
  }
}

// 切换到下一题
const goToNextQuestion = () => {
  if (currentQuestionIndex.value < wrongQuestions.value.length - 1) {
    currentQuestionIndex.value++
    viewQuestion.value = wrongQuestions.value[currentQuestionIndex.value]
  }
}

const deleteQuestion = async (question) => {
  try {
    await confirmDelete('这个错题', '错题')
    
    await executeWithLoading(async () => {
      await wrongQuestionAPI.deleteWrongQuestion(projectId.value, question.id)
    }, {
      loadingRef: loading,
      errorMessage: '删除错题失败'
    })
    showSuccess('删除', '错题')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      showError('删除', error)
    }
  }
}

const handleFormSubmit = async (formData) => {
  // 如果是通过footer按钮调用，需要先验证表单
  if (!formData && questionForm.value) {
    return questionForm.value.handleSubmit()
  }

  try {
    submitting.value = true
    console.log('🚀 提交错题数据:', formData)
    console.log('🚀 项目ID:', projectId.value)
    console.log('🚀 对话框模式:', dialogMode.value)

    let response
    if (dialogMode.value === 'create') {
      response = await wrongQuestionAPI.createWrongQuestion(projectId.value, formData)
    } else {
      response = await wrongQuestionAPI.updateWrongQuestion(projectId.value, currentQuestion.value.id, formData)
    }

    ElMessage.success(dialogMode.value === 'create' ? '错题创建成功' : '错题更新成功')
    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.error?.message || error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  // 先重置子组件的表单
  if (questionForm.value) {
    questionForm.value.resetForm()
  }
  
  dialogVisible.value = false
  currentQuestion.value = null
  submitting.value = false
}

const renderMarkdownPreview = (markdown) => {
  if (!markdown) return ''
  // 简单的预览，只显示前100个字符
  const text = markdown.replace(/[#*`]/g, '').substring(0, 100)
  return text + (markdown.length > 100 ? '...' : '')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 复习相关方法
const loadReviewStatistics = async () => {
  const response = await reviewAPI.getReviewStatistics(projectId.value)
  reviewStatistics.value = response.data.data || {}
}

const showTodayReview = async () => {
  try {
    const response = await reviewAPI.getTodayReviewQuestions(projectId.value)
    reviewQuestions.value = response.data.data || []

    if (reviewQuestions.value.length > 0) {
      currentReviewIndex.value = 0
      currentReviewQuestion.value = reviewQuestions.value[0]
    }

    reviewDialogVisible.value = true
  } catch (error) {
    console.error('获取今日复习错题失败:', error)
    ElMessage.error('获取今日复习错题失败')
  }
}

const goToPreviousReview = () => {
  if (currentReviewIndex.value > 0) {
    currentReviewIndex.value--
    currentReviewQuestion.value = reviewQuestions.value[currentReviewIndex.value]
  }
}

const goToNextReview = () => {
  if (currentReviewIndex.value < reviewQuestions.value.length - 1) {
    currentReviewIndex.value++
    currentReviewQuestion.value = reviewQuestions.value[currentReviewIndex.value]
  }
}

const handleReviewCompleted = (reviewId) => {
  // 从复习列表中移除已完成的错题
  reviewQuestions.value = reviewQuestions.value.filter(q => q.reviewId !== reviewId)

  // 更新统计信息
  if (reviewStatistics.value.todayReviewCount > 0) {
    reviewStatistics.value.todayReviewCount--
  }

  // 如果还有未复习的错题，调整当前索引
  if (reviewQuestions.value.length > 0) {
    if (currentReviewIndex.value >= reviewQuestions.value.length) {
      currentReviewIndex.value = reviewQuestions.value.length - 1
    }
    currentReviewQuestion.value = reviewQuestions.value[currentReviewIndex.value]
  } else {
    // 如果没有更多错题需要复习，关闭对话框
    reviewDialogVisible.value = false
    ElMessage.success('今日复习任务已完成！')
  }
}

const configureReview = () => {
  console.log('🔧 配置复习 - 项目ID:', projectId.value)
  console.log('🔧 当前路由参数:', route.params)

  if (!projectId.value) {
    ElMessage.error('项目ID不存在，无法跳转到复习配置页面')
    return
  }

  router.push(`/projects/${projectId.value}/review-configuration`)
}
</script>

<style scoped>
.wrong-questions-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-section {
  display: flex;
  align-items: center;
}

.button-section {
  display: flex;
  gap: 10px;
}

.statistics-bar {
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card .el-card__body {
  padding: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-label {
  font-size: 16px;
  margin-right: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.empty-content p {
  margin: 16px 0;
  color: #909399;
  font-size: 16px;
}

.question-card {
  transition: all 0.3s ease;
}

.question-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.question-info {
  flex: 1;
}

.question-title {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.question-path {
  margin: 0 0 4px 0;
  color: #606266;
  font-size: 14px;
}

.question-time {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.question-actions {
  display: flex;
  gap: 8px;
}

.question-content {
  border-top: 1px solid #ebeef5;
  padding-top: 12px;
}

.content-preview {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

/* 错题对话框样式 */
:deep(.wrong-question-dialog) {
  max-width: 1400px;
  max-height: 95vh;
  display: flex;
  flex-direction: column;
}

:deep(.wrong-question-dialog .el-dialog) {
  max-height: 95vh;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

:deep(.wrong-question-dialog .el-dialog__body) {
  padding: 0;
  flex: 1;
  overflow: hidden;
  max-height: calc(95vh - 180px); /* 减去头部和底部的空间 */
  display: flex;
  flex-direction: column;
}

:deep(.wrong-question-dialog .el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

:deep(.wrong-question-dialog .el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #ebeef5;
  flex-shrink: 0;
  background: #fff;
}

:deep(.wrong-question-dialog .el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  min-height: 0; /* 确保能够收缩 */
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :deep(.wrong-question-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  :deep(.wrong-question-dialog) {
    width: 98% !important;
    max-height: 98vh;
  }

  :deep(.wrong-question-dialog .el-dialog__body) {
    max-height: calc(98vh - 160px);
    display: flex;
    flex-direction: column;
  }

  :deep(.wrong-question-dialog .el-dialog__footer) {
    padding: 12px 16px;
  }

  .dialog-footer {
    gap: 8px;
  }
}

/* 错题详情弹窗样式 */
:deep(.question-detail-dialog) {
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.question-detail-dialog .el-dialog) {
  margin: 0 auto;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:deep(.question-detail-dialog .el-dialog__body) {
  padding: 0;
  flex: 1;
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

:deep(.question-detail-dialog .el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

:deep(.question-detail-dialog .el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

@media (max-width: 1200px) {
  :deep(.question-detail-dialog) {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  :deep(.question-detail-dialog) {
    width: 98% !important;
    max-height: 95vh;
  }

  :deep(.question-detail-dialog .el-dialog__body) {
    max-height: calc(95vh - 100px);
  }
}

/* 复习对话框样式 */
.review-badge {
  margin-left: 8px;
}

.empty-review {
  text-align: center;
  padding: 40px;
}

.review-progress {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.review-progress span {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

:deep(.review-dialog) {
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.review-dialog .el-dialog) {
  margin: 0 auto;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:deep(.review-dialog .el-dialog__body) {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

:deep(.review-dialog .el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

@media (max-width: 1200px) {
  :deep(.review-dialog) {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  :deep(.review-dialog) {
    width: 98% !important;
    max-height: 95vh;
  }

  :deep(.review-dialog .el-dialog__body) {
    max-height: calc(95vh - 100px);
  }
}
</style>
