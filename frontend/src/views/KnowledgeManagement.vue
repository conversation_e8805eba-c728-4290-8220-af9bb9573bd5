<template>
  <div class="knowledge-management">
    <!-- 页面头部 -->
    <PageHeader
      title="知识点管理"
      :subtitle="projectName"
      @back="goBack"
    >
      <template #actions>
        <el-button @click="openVisualization" type="info">
          <el-icon><View /></el-icon>
          可视化
        </el-button>
        <el-button @click="openBatchScoreDialog" type="warning">
          <el-icon><Edit /></el-icon>
          批量评分
        </el-button>
        <el-button @click="openVideoTutorialPanel" type="info">
          <el-icon><VideoPlay /></el-icon>
          生成视频教程
        </el-button>
        <el-button @click="saveConfiguration" type="primary" :loading="saving">
          <el-icon><Check /></el-icon>
          保存配置
        </el-button>
      </template>
    </PageHeader>

    <!-- 主要内容区域 -->
    <div class="content-area" v-loading="loading">
      <el-row :gutter="20">
        <!-- JSON编辑器 -->
        <el-col :span="16">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>知识点配置 (JSON格式)</span>
                <div class="header-actions">
                  <el-button size="small" @click="$refs.jsonEditor?.formatJson()">
                    <el-icon><DocumentCopy /></el-icon>
                    格式化
                  </el-button>
                  <el-button size="small" @click="$refs.jsonEditor?.validateJson()">
                    <el-icon><CircleCheck /></el-icon>
                    验证
                  </el-button>
                  <el-button size="small" @click="toggleEditorTheme">
                    <el-icon><Refresh /></el-icon>
                    {{ editorTheme === 'dark' ? '浅色' : '深色' }}主题
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="json-editor">
              <LightweightJsonEditor
                ref="jsonEditor"
                v-model="jsonContent"
                :height="600"
                :theme="editorTheme"
                @validate="onJsonValidate"
                @change="onJsonChange"
                @save="saveConfiguration"
                placeholder="请输入知识点配置的 JSON 内容..."
              />
            </div>
          </el-card>
        </el-col>

        <!-- 侧边栏 -->
        <el-col :span="8">
          <KnowledgeInfoPanel
            :current-config="currentConfig"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 可视化对话框 -->
    <el-dialog
      v-model="showVisualizationDialog"
      title="知识点脑图可视化"
      width="90%"
      :close-on-click-modal="false"
      :show-close="false"
      custom-class="visualization-dialog"
    >
      <KnowledgeMindMap
        v-if="showVisualizationDialog && parsedKnowledgeData"
        :knowledge-data="parsedKnowledgeData"
        @close="closeVisualization"
      />
    </el-dialog>

    <!-- 批量评分对话框 -->
    <BatchScoreDialog
      v-model="showBatchScoreDialog"
      :knowledge-data="parsedKnowledgeData"
      @apply="applyBatchScores"
    />

    <!-- 视频教程面板对话框 -->
    <el-dialog
      v-model="showVideoTutorialPanel"
      title="视频教程管理"
      width="80%"
      :close-on-click-modal="false"
      custom-class="video-tutorial-panel-dialog"
    >
      <div class="tutorial-panel-container">
        <!-- 叶子节点列表 -->
        <div class="leaf-nodes-section">
          <div class="section-header">
            <h3>知识点列表</h3>
            <span class="leaf-count">共 {{ leafNodes.length }} 个叶子节点</span>
          </div>

          <div class="leaf-nodes-grid">
            <div
              v-for="node in leafNodes"
              :key="node.uid || node.name"
              class="leaf-node-card"
            >
              <div class="node-content">
                <div class="node-header">
                  <h4 class="node-name">{{ node.name }}</h4>
                  <el-tag v-if="node.score" :type="getScoreTagType(node.score)" size="small">
                    {{ node.score }}分
                  </el-tag>
                </div>

                <div class="node-path" v-if="node.uid || node.fullPath">
                  <el-icon><FolderOpened /></el-icon>
                  <KnowledgePathDisplay
                    v-if="node.uid"
                    mode="single"
                    :project-id="projectId"
                    :uid="node.uid"
                    :max-length="100"
                  />
                  <span v-else>{{ node.fullName || node.fullPath || '' }}</span>
                </div>

                <div class="node-description" v-if="node.description">
                  {{ node.description }}
                </div>

                <div class="node-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="generateVideoTutorial(node)"
                    :loading="generatingTutorials[node.uid || node.name]"
                  >
                    <el-icon><VideoPlay /></el-icon>
                    获取教程
                  </el-button>

                  <el-button
                    v-if="hasExistingTutorial(node)"
                    type="success"
                    size="small"
                    @click="viewExistingTutorial(node)"
                  >
                    <el-icon><View /></el-icon>
                    查看教程
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 视频教程对话框 -->
    <VideoTutorialDialog
      v-model="showVideoTutorialDialog"
      :project-id="projectId"
      :knowledge-point="selectedKnowledgePoint"
      :project-name="projectName"
      :project-description="projectDescription"
      @saved="onTutorialSaved"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  MagicStick, Check, DocumentCopy, CircleCheck,
  Refresh, View, Edit, VideoPlay, FolderOpened
} from '@element-plus/icons-vue'
import { knowledgeAPI, videoTutorialAPI } from '@/api'
import LightweightJsonEditor from '@/components/LightweightJsonEditor.vue'
import KnowledgeMindMap from '@/components/KnowledgeMindMap.vue'
import PageHeader from '@/components/common/PageHeader.vue'
import BatchScoreDialog from '@/components/knowledge/BatchScoreDialog.vue'

import KnowledgeInfoPanel from '@/components/knowledge/KnowledgeInfoPanel.vue'
import VideoTutorialDialog from '@/components/VideoTutorialDialog.vue'
import KnowledgePathDisplay from '@/components/knowledge/KnowledgePathDisplay.vue'
import { useProject } from '@/composables/useProject'
import { useLoading } from '@/composables/useLoading'
import { useErrorHandler } from '@/composables/useErrorHandler'
import { formatDate } from '@/utils/environment'
import { extractLeafNodes } from '@/utils/knowledgeUtils'
import { clearCacheAfterConfigSave } from '@/services/cacheService'
import { scoreUtils } from '@/utils/dataUtils'

const router = useRouter()
const route = useRoute()

// 使用 Composables
const { project, projectId, projectName, fetchProject } = useProject()
const { loading, execute } = useLoading()
const { handleApiError, handleAsyncError } = useErrorHandler()

// 状态管理
const saving = ref(false)
const showVisualizationDialog = ref(false)
const showBatchScoreDialog = ref(false)
const showVideoTutorialPanel = ref(false)
const showVideoTutorialDialog = ref(false)

// 数据
const currentConfig = ref(null)
const jsonContent = ref('')

// 视频教程相关
const selectedKnowledgePoint = ref(null)
const generatingTutorials = ref({})
const existingTutorials = ref([])

// 编辑器相关
const jsonEditor = ref(null)
const editorTheme = ref('dark')
const jsonValid = ref(true)

// 计算属性

// 解析后的知识点数据，用于可视化
const parsedKnowledgeData = computed(() => {
  if (!jsonContent.value) return null

  try {
    return JSON.parse(jsonContent.value)
  } catch (error) {
    console.error('解析知识点数据失败:', error)
    return null
  }
})

// 叶子节点列表
const leafNodes = computed(() => {
  if (!parsedKnowledgeData.value) return []
  const nodes = extractLeafNodes(parsedKnowledgeData.value)

  // 直接返回节点，保留原始的 fullName 和 fullPath
  return nodes
})

// 页面方法
const goBack = () => {
  router.back()
}

// 获取知识点配置
const fetchConfiguration = async () => {
  await execute(async () => {
    console.log('🔄 开始获取知识点配置...')
    const response = await knowledgeAPI.getKnowledgeConfiguration(projectId.value)

    if (response.data.data) {
      currentConfig.value = response.data.data
      jsonContent.value = JSON.stringify(response.data.data.configuration, null, 2)
      console.log('✅ 知识点配置获取成功')
    } else {
      currentConfig.value = null
      jsonContent.value = JSON.stringify([], null, 2)
      console.log('ℹ️ 暂无知识点配置，使用默认值')
    }
  })
}



// 编辑器事件处理
const onJsonValidate = (result) => {
  jsonValid.value = result.valid
}

const onJsonChange = (value) => {
  // JSON内容变化时的处理
  console.log('JSON内容已更改')
}

// 切换编辑器主题
const toggleEditorTheme = () => {
  editorTheme.value = editorTheme.value === 'dark' ? 'light' : 'dark'
  ElMessage.success(`已切换到${editorTheme.value === 'dark' ? '深色' : '浅色'}主题`)
}

// 打开可视化
const openVisualization = () => {
  if (!jsonContent.value || jsonContent.value.trim() === '') {
    ElMessage.warning('请先输入知识点配置数据')
    return
  }

  if (!jsonValid.value) {
    ElMessage.error('JSON格式错误，请先修正语法错误')
    return
  }

  if (!parsedKnowledgeData.value) {
    ElMessage.error('无法解析知识点数据')
    return
  }

  showVisualizationDialog.value = true
}

// 关闭可视化
const closeVisualization = () => {
  showVisualizationDialog.value = false
}

// 打开批量评分对话框
const openBatchScoreDialog = () => {
  if (!parsedKnowledgeData.value) {
    ElMessage.warning('请先配置知识点数据')
    return
  }
  showBatchScoreDialog.value = true
}

// 应用批量评分并保存
const applyBatchScores = async (leafNodes) => {
  await handleAsyncError(async () => {
    console.log('📊 开始应用批量评分...')

    // 更新原始JSON数据中的分数
    const updatedData = updateScoresInOriginalData(parsedKnowledgeData.value, leafNodes)

    // 更新JSON编辑器内容
    const formattedJson = JSON.stringify(updatedData, null, 2)
    jsonContent.value = formattedJson

    // 保存到后端
    await knowledgeAPI.saveKnowledgeConfiguration(projectId.value, {
      configuration: updatedData,
      notes: '批量评分保存'
    })

    console.log('✅ 批量评分应用成功')
    ElMessage.success('批量评分已应用并保存成功！')

    // 清空相关缓存
    await clearCacheAfterConfigSave(projectId.value)

    // 刷新数据
    fetchConfiguration()
  }, { errorMessage: '应用批量评分失败' })
}

// 更新原始数据中的分数
const updateScoresInOriginalData = (originalData, leafNodesWithScores) => {
  // 创建一个UID到分数的映射
  const scoreMap = {}
  leafNodesWithScores.forEach(leaf => {
    if (leaf.uid) {
      scoreMap[leaf.uid] = leaf.score
    }
  })

  // 递归更新数据
  function updateNode(node) {
    const updatedNode = { ...node }

    // 如果是叶子节点且有UID，更新分数
    if ((!node.children || node.children.length === 0) && node.uid && scoreMap.hasOwnProperty(node.uid)) {
      updatedNode.score = scoreMap[node.uid]
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      updatedNode.children = node.children.map(child => updateNode(child))
    }

    return updatedNode
  }

  // 处理数组或单个对象
  if (Array.isArray(originalData)) {
    return originalData.map(item => updateNode(item))
  } else {
    return updateNode(originalData)
  }
}

// 保存配置
const saveConfiguration = async () => {
  // 检查JSON有效性
  if (!jsonValid.value) {
    ElMessage.error('JSON格式错误，请先修正语法错误')
    return
  }

  // 验证JSON格式
  let configuration
  try {
    configuration = JSON.parse(jsonContent.value)
  } catch (error) {
    ElMessage.error('JSON格式错误，请检查语法')
    return
  }

  await handleAsyncError(async () => {
    console.log('💾 开始保存知识点配置...')
    console.log('配置数据:', configuration)

    saving.value = true
    await knowledgeAPI.saveKnowledgeConfiguration(projectId.value, {
      configuration,
      notes: '手动保存'
    })

    console.log('✅ 知识点配置保存成功')
    ElMessage.success('配置保存成功！后端已自动为知识点生成uid')

    // 清空相关缓存
    await clearCacheAfterConfigSave(projectId.value)

    fetchConfiguration()
  }, {
    errorMessage: '保存配置失败',
    finally: () => {
      saving.value = false
    }
  })
}



// 视频教程相关方法
const openVideoTutorialPanel = async () => {
  if (!parsedKnowledgeData.value) {
    ElMessage.warning('请先配置知识点数据')
    return
  }

  if (leafNodes.value.length === 0) {
    ElMessage.warning('当前配置中没有叶子节点')
    return
  }

  // 加载已有的视频教程
  await loadExistingTutorials()
  showVideoTutorialPanel.value = true
}

// 加载已有的视频教程
const loadExistingTutorials = async () => {
  try {
    const response = await videoTutorialAPI.getVideoTutorials(projectId.value)
    existingTutorials.value = response.data.data || []
  } catch (error) {
    console.error('加载视频教程失败:', error)
    // 不显示错误，因为可能是第一次使用
  }
}

// 检查是否有已存在的教程
const hasExistingTutorial = (node) => {
  return existingTutorials.value.some(tutorial =>
    tutorial.knowledgePointName === node.name
  )
}

// 生成视频教程
const generateVideoTutorial = (node) => {
  selectedKnowledgePoint.value = {
    ...node,
    fullPath: node.fullPath || node.name
  }
  showVideoTutorialDialog.value = true
}

// 查看已有教程
const viewExistingTutorial = async (node) => {
  await handleAsyncError(async () => {
    const response = await videoTutorialAPI.getVideoTutorialByKnowledgePoint(
      projectId.value,
      node.name
    )

    if (response.data.success) {
      selectedKnowledgePoint.value = {
        ...node,
        fullPath: node.fullPath || node.name,
        existingTutorial: response.data.data
      }
      showVideoTutorialDialog.value = true
    }
  }, { errorMessage: '获取视频教程失败' })
}

// 教程保存回调
const onTutorialSaved = (tutorial) => {
  // 刷新已有教程列表
  loadExistingTutorials()
}

// 获取分数标签类型
const getScoreTagType = (score) => {
  return scoreUtils.getScoreTagType(score)
}

// 初始化页面数据
const initPageData = async () => {
  try {
    console.log('🚀 开始初始化页面数据...')

    // 立即开始加载项目信息和配置（关键数据）
    const criticalDataPromises = [
      fetchProject(),
      fetchConfiguration()
    ]

    // 并行加载关键数据
    await Promise.allSettled(criticalDataPromises)

    // 异步加载非关键数据（不阻塞页面显示）
    // 目前没有需要异步加载的非关键数据

    console.log('✅ 页面关键数据初始化完成')
  } catch (error) {
    console.error('❌ 页面初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
}

onMounted(() => {
  // 立即开始初始化
  initPageData()
})
</script>

<style scoped>
.knowledge-management {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.content-area {
  min-height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.json-editor {
  height: 600px;
  border-radius: 4px;
  overflow: hidden;
}

.json-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.json-textarea :deep(.el-textarea__inner) {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.info-card, .history-card, .ai-logs-card {
  margin-bottom: 16px;
}

.config-info {
  padding: 8px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-item label {
  font-weight: bold;
  color: #606266;
}

.no-config {
  text-align: center;
  padding: 20px 0;
}

.history-list, .ai-logs-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.history-item:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.history-item.active {
  border-color: #409EFF;
  background-color: #e6f7ff;
}

.version-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.version {
  font-weight: bold;
  color: #409EFF;
}

.date {
  font-size: 12px;
  color: #909399;
}

.version-notes {
  font-size: 12px;
  color: #606266;
}

.log-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
}

.log-item.success {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.log-item.failed {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.status-icon {
  color: #67c23a;
}

.log-item.failed .status-icon {
  color: #f56c6c;
}

.log-content {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

/* 可视化对话框样式 */
:deep(.visualization-dialog) {
  .el-dialog__body {
    padding: 0;
    height: 80vh;
  }
}

:deep(.visualization-dialog .el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
}

/* 全屏模式样式 */
:deep(.visualization-dialog:fullscreen) {
  .el-dialog {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .el-dialog__body {
    height: calc(100vh - 60px) !important;
  }
}

/* 批量评分对话框样式 */
:deep(.batch-score-dialog) {
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.batch-score-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.score-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.toolbar-left .leaf-count {
  font-weight: bold;
  color: #495057;
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.leaf-nodes-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.leaf-node-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  min-height: 80px;
}

.leaf-node-item:hover {
  background-color: #f8f9fa;
}

.leaf-node-item:last-child {
  border-bottom: none;
}

.node-info {
  flex: 1;
  margin-right: 20px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.node-name {
  font-weight: bold;
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
}

.node-description {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 400px;
  cursor: help;
}

.node-path {
  color: #909399;
  font-size: 11px;
  font-style: italic;
  line-height: 1.3;
}

.score-selector {
  flex-shrink: 0;
}

.score-selector :deep(.el-radio-button) {
  margin-right: 0;
}

.score-selector :deep(.el-radio-button__inner) {
  padding: 8px 12px;
  font-size: 12px;
  border-radius: 4px;
}

.score-selector :deep(.score-1.is-active .el-radio-button__inner) {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

.score-selector :deep(.score-2.is-active .el-radio-button__inner) {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.score-selector :deep(.score-3.is-active .el-radio-button__inner) {
  background-color: #909399;
  border-color: #909399;
}

.score-selector :deep(.score-4.is-active .el-radio-button__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

.score-selector :deep(.score-5.is-active .el-radio-button__inner) {
  background-color: #67c23a;
  border-color: #67c23a;
}

.score-statistics {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-title {
  font-weight: bold;
  color: #495057;
  margin-bottom: 12px;
  font-size: 14px;
}

.stat-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stat-tag {
  font-size: 12px;
}

/* 视频教程面板样式 */
:deep(.video-tutorial-panel-dialog) {
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.tutorial-panel-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.leaf-nodes-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color);
}

.section-header h3 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.leaf-count {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.leaf-nodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  max-height: 500px;
  overflow-y: auto;
  padding: 4px;
}

.leaf-node-card {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 16px;
  background: var(--el-bg-color);
  transition: all 0.2s;
  cursor: pointer;
}

.leaf-node-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.node-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.node-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.4;
  flex: 1;
}

.node-path {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  line-height: 1.3;
}

.node-description {
  color: var(--el-text-color-regular);
  font-size: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.node-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.node-actions .el-button {
  flex: 1;
}
</style>
