<template>
  <div class="video-tutorial-history">
    <!-- 页面头部 -->
    <PageHeader
      title="视频教程历史"
      :subtitle="projectName"
      @back="goBack"
    >
      <template #actions>
        <el-button @click="refreshList" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </template>
    </PageHeader>

    <!-- 主要内容区域 -->
    <div class="content-area" v-loading="loading">
      <!-- 统计信息 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ tutorialList.length }}</div>
              <div class="stat-label">总教程数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ uniqueKnowledgePoints }}</div>
              <div class="stat-label">知识点数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ recentTutorials }}</div>
              <div class="stat-label">本周新增</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ totalContentLength }}</div>
              <div class="stat-label">总字数</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索和筛选 -->
      <el-card class="filter-card">
        <div class="filter-container">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索知识点名称或路径..."
            clearable
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <div class="filter-actions">
            <el-button @click="clearFilters">清空筛选</el-button>
          </div>
        </div>
      </el-card>

      <!-- 教程列表 -->
      <el-card class="tutorial-list-card">
        <template #header>
          <div class="card-header">
            <span>教程列表</span>
            <span class="tutorial-count">{{ filteredTutorials.length }} 个教程</span>
          </div>
        </template>

        <div v-if="filteredTutorials.length === 0" class="empty-state">
          <el-empty description="暂无视频教程" />
        </div>

        <div v-else class="tutorial-grid">
          <div 
            v-for="tutorial in filteredTutorials" 
            :key="tutorial.id"
            class="tutorial-card"
            @click="viewTutorial(tutorial)"
          >
            <div class="tutorial-header">
              <h4 class="tutorial-title">{{ tutorial.knowledgePointName }}</h4>
              <el-tag size="small" type="info">
                {{ formatDate(tutorial.createdAt) }}
              </el-tag>
            </div>
            
            <div class="tutorial-path">
              <el-icon><FolderOpened /></el-icon>
              {{ tutorial.knowledgePointPath }}
            </div>
            
            <div class="tutorial-description" v-if="tutorial.knowledgePointDescription">
              {{ tutorial.knowledgePointDescription }}
            </div>
            
            <div class="tutorial-meta">
              <span class="content-length">
                <el-icon><Document /></el-icon>
                {{ getContentLength(tutorial.tutorialContent) }} 字
              </span>
              <span class="created-by" v-if="tutorial.createdBy">
                <el-icon><User /></el-icon>
                {{ tutorial.createdBy }}
              </span>
            </div>
            
            <div class="tutorial-actions">
              <el-button type="primary" size="small" @click.stop="viewTutorial(tutorial)">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
              <el-button size="small" @click.stop="copyTutorialContent(tutorial)">
                <el-icon><DocumentCopy /></el-icon>
                复制内容
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 教程详情对话框 -->
    <VideoTutorialDialog
      v-model="showTutorialDialog"
      :project-id="projectId"
      :knowledge-point="selectedTutorial"
      @saved="onTutorialSaved"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Refresh, Search, FolderOpened, Document, User, View, DocumentCopy 
} from '@element-plus/icons-vue'
import { videoTutorialAPI } from '@/api'
import { useProject } from '@/composables/useProject'
import { useLoading } from '@/composables/useLoading'
import { useErrorHandler } from '@/composables/useErrorHandler'
import PageHeader from '@/components/common/PageHeader.vue'
import VideoTutorialDialog from '@/components/VideoTutorialDialog.vue'

const router = useRouter()
const route = useRoute()

// 使用 Composables
const { project, projectId, projectName, fetchProject } = useProject()
const { loading, execute } = useLoading()
const { handleApiError, handleAsyncError } = useErrorHandler()

// 响应式数据
const tutorialList = ref([])
const searchKeyword = ref('')
const showTutorialDialog = ref(false)
const selectedTutorial = ref(null)

// 计算属性
const filteredTutorials = computed(() => {
  if (!searchKeyword.value) return tutorialList.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return tutorialList.value.filter(tutorial => 
    tutorial.knowledgePointName.toLowerCase().includes(keyword) ||
    tutorial.knowledgePointPath.toLowerCase().includes(keyword) ||
    (tutorial.knowledgePointDescription && tutorial.knowledgePointDescription.toLowerCase().includes(keyword))
  )
})

const uniqueKnowledgePoints = computed(() => {
  const uniqueNames = new Set(tutorialList.value.map(t => t.knowledgePointName))
  return uniqueNames.size
})

const recentTutorials = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  
  return tutorialList.value.filter(tutorial => 
    new Date(tutorial.createdAt) > oneWeekAgo
  ).length
})

const totalContentLength = computed(() => {
  return tutorialList.value.reduce((total, tutorial) => 
    total + getContentLength(tutorial.tutorialContent), 0
  )
})

// 方法
const goBack = () => {
  router.back()
}

const loadTutorialList = async () => {
  await execute(async () => {
    console.log('🔄 开始加载视频教程列表...')
    const response = await videoTutorialAPI.getVideoTutorials(projectId.value)
    tutorialList.value = response.data.data || []
    console.log(`✅ 视频教程列表加载成功，共 ${tutorialList.value.length} 个教程`)
  })
}

const refreshList = () => {
  loadTutorialList()
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const clearFilters = () => {
  searchKeyword.value = ''
}

const viewTutorial = (tutorial) => {
  selectedTutorial.value = {
    name: tutorial.knowledgePointName,
    fullPath: tutorial.knowledgePointPath,
    description: tutorial.knowledgePointDescription,
    existingTutorial: tutorial
  }
  showTutorialDialog.value = true
}

const copyTutorialContent = async (tutorial) => {
  try {
    await navigator.clipboard.writeText(tutorial.tutorialContent)
    ElMessage.success('教程内容已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  }
}

const onTutorialSaved = () => {
  loadTutorialList()
}

const getContentLength = (content) => {
  if (!content) return 0
  // 移除Markdown标记，计算实际字数
  return content.replace(/[#*`\[\]()]/g, '').length
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 初始化页面数据
const initPageData = async () => {
  try {
    console.log('🚀 开始初始化页面数据...')
    await fetchProject()
    await loadTutorialList()
    console.log('✅ 页面数据初始化完成')
  } catch (error) {
    console.error('❌ 页面初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
}

onMounted(() => {
  initPageData()
})
</script>

<style scoped>
.video-tutorial-history {
  max-width: 1400px;
  margin: 0 auto;
}

.content-area {
  min-height: 600px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 8px;
}

.stat-label {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-actions {
  display: flex;
  gap: 12px;
}

.tutorial-list-card {
  min-height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tutorial-count {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.tutorial-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.tutorial-card {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 20px;
  background: var(--el-bg-color);
  transition: all 0.2s;
  cursor: pointer;
}

.tutorial-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.tutorial-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.4;
  flex: 1;
}

.tutorial-path {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-text-color-secondary);
  font-size: 13px;
  margin-bottom: 8px;
}

.tutorial-description {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tutorial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.content-length,
.created-by {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tutorial-actions {
  display: flex;
  gap: 8px;
}

.tutorial-actions .el-button {
  flex: 1;
}
</style>
