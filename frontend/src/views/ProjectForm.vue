<template>
  <div class="project-form">
    <!-- 页面头部 -->
    <PageHeader
      :title="isEdit ? '编辑项目' : '创建项目'"
      @back="goBack"
    />

    <!-- 表单内容 -->
    <div class="form-container">
      <el-card>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          size="large"
        >
          <el-form-item label="项目名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入项目名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="项目描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              placeholder="请输入项目描述"
              :rows="4"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item>
            <el-button 
              type="primary" 
              @click="submitForm"
              :loading="loading"
              size="large"
            >
              {{ isEdit ? '更新项目' : '创建项目' }}
            </el-button>
            <el-button @click="goBack" size="large">
              取消
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { projectAPI } from '@/api'
import { useFormHandler, useFormValidation } from '@/composables'
import { useProjectEvents } from '@/utils/eventBus'

const route = useRoute()
const { getValidationRules } = useFormValidation()
const { notifyProjectCreated, notifyProjectListRefresh } = useProjectEvents()

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)
const projectId = computed(() => route.params.id)

// 表单处理配置
const {
  formRef,
  formData: form,
  submitting: loading,
  submitForm,
  goBack
} = useFormHandler({
  initialData: {
    name: '',
    description: ''
  },
  validationRules: {
    name: getValidationRules('PROJECT_NAME'),
    description: getValidationRules('PROJECT_DESCRIPTION')
  },
  submitHandler: async (formData, isEditMode) => {
    let result
    if (isEditMode) {
      result = await projectAPI.updateProject(projectId.value, formData)
      notifyProjectListRefresh()
      return result
    } else {
      result = await projectAPI.createProject(formData)
      notifyProjectCreated(result.data.data)
      return result
    }
  },
  loadHandler: async (id) => {
    const response = await projectAPI.getProject(id || projectId.value)
    return response.data.data
  },
  successMessage: isEdit.value ? '项目更新成功' : '项目创建成功',
  redirectPath: '/',
  autoLoad: isEdit.value
})

// 表单验证规则（用于模板）
const rules = {
  name: getValidationRules('PROJECT_NAME'),
  description: getValidationRules('PROJECT_DESCRIPTION')
}


</script>

<style scoped>
.project-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-container {
  background: white;
  border-radius: 8px;
}

.el-form {
  padding: 20px;
}

.el-form-item:last-child {
  margin-bottom: 0;
  margin-top: 32px;
}
</style>
