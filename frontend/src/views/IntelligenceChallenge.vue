<template>
  <div class="intelligence-challenge-container">
    <div class="header">
      <button @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </button>
      <h1 class="title">智力大比拼</h1>
      <div class="placeholder"></div>
    </div>
    
    <div class="challenge-grid">
      <div
        v-for="(challenge, key) in challenges"
        :key="key"
        class="challenge-card"
        :class="challenge.difficulty"
        @click="startChallenge(key)"
      >
        <div class="challenge-header">
          <h3 class="challenge-title">{{ challenge.title }}</h3>
          <DifficultyStars :difficulty="challenge.difficulty" class="difficulty-stars" />
        </div>
        <p class="challenge-description">{{ challenge.description }}</p>
        <div class="challenge-info">
          <span class="question-count">{{ challenge.questionCount }}题</span>
          <span class="challenge-type">{{ challenge.type }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowLeft } from '@element-plus/icons-vue';
import { DifficultyStars } from '@/components';

const router = useRouter();

const challenges = ref({
  'mahjong-winning': {
    title: '麻将胡牌挑战',
    description: '识别麻将牌型，选择所有可能的听牌数字。高难度多选题，考验你的麻将技巧！',
    difficulty: 5,
    questionCount: 10,
    type: '多选题',
    totalQuestions: 30
  }
});

const startChallenge = (challengeType) => {
  router.push(`/intelligence-challenge/${challengeType}`);
};

const goBack = () => {
  router.back();
};
</script>

<style scoped>
.intelligence-challenge-container {
  min-height: 100vh;
  background: white;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  /* 隐藏系统导航栏 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  overflow-y: auto;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 0 4px;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #e0e0e0;
  transform: scale(1.05);
}

.title {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  text-align: center;
}

.placeholder {
  width: 40px;
}

.challenge-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  max-width: 500px;
  margin: 0 auto;
}

.challenge-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.challenge-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.challenge-card[class*="5"] {
  border-color: #e74c3c;
  background: linear-gradient(135deg, #fdf7f7 0%, #fadbd8 100%);
}

.challenge-card[class*="5"]:hover {
  box-shadow: 0 8px 30px rgba(231, 76, 60, 0.2);
}

.challenge-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 10px;
}

.challenge-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  flex: 1;
}

.difficulty-stars {
  flex-shrink: 0;
  margin-top: 2px;
}

.challenge-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

.challenge-info {
  display: flex;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #e0e0e0;
}

.question-count,
.challenge-type {
  font-size: 12px;
  color: #7f8c8d;
  background-color: #ecf0f1;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.challenge-type {
  background-color: #e8f4f8;
  color: #2980b9;
}

@media (max-width: 480px) {
  .intelligence-challenge-container {
    padding: 12px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .challenge-card {
    padding: 16px;
  }
  
  .challenge-title {
    font-size: 18px;
  }
  
  .challenge-description {
    font-size: 13px;
  }
}
</style> 