<template>
  <div class="mobile-home">
    <div class="menu" v-if="$route.path === '/m'">
      <router-link to="/card-exam" class="menu-item">
        <el-icon><CreditCard /></el-icon>
        <span>卡片考试</span>
      </router-link>
      <router-link to="/mobile/mental-arithmetic-exam" class="menu-item">
        <el-icon><Cpu /></el-icon>
        <span>数学口算</span>
      </router-link>
      <router-link to="/quiz-list" class="menu-item">
        <el-icon><Trophy /></el-icon>
        <span>趣味考试</span>
      </router-link>
      <router-link to="/intelligence-challenge" class="menu-item">
        <el-icon><Compass /></el-icon>
        <span>智力大比拼</span>
      </router-link>
      <router-link to="/courses" class="menu-item">
        <el-icon><Reading /></el-icon>
        <span>教学课程</span>
      </router-link>
      <router-link to="/pinyin" class="menu-item">
        <el-icon><ChatLineRound /></el-icon>
        <span>拼音/字母</span>
      </router-link>
    </div>
    <router-view />
  </div>
</template>

<script setup>
import { CreditCard, Cpu, Trophy, Reading, ChatLineRound, Compass } from '@element-plus/icons-vue';
</script>

<style scoped>
.mobile-home {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0f2f5;
}

.menu {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px;
  width: 100%;
  max-width: 400px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #ffffff;
  color: #333;
  text-decoration: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  aspect-ratio: 1 / 1;
}

.menu-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.menu-item .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
  color: #409EFF;
}

</style>