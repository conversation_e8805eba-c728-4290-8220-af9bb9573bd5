<template>
  <div class="mental-arithmetic-exam-container">
    <page-header title="数学口算在线考试"></page-header>
    <div v-if="!examStarted" class="difficulty-selector-wrapper">
      <difficulty-selector @start-exam="startExam"></difficulty-selector>
    </div>
    <div v-if="examStarted && !examFinished" class="question-display-wrapper">
      <question-display
        :difficulty="selectedDifficulty"
        @exam-finished="showResult"
      ></question-display>
    </div>
    <div v-if="examFinished" class="exam-result-wrapper">
      <exam-result :result="examResult" @restart="restartExam"></exam-result>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import PageHeader from '@/components/common/PageHeader.vue';
import DifficultySelector from '@/components/mental_arithmetic_exam/DifficultySelector.vue';
import QuestionDisplay from '@/components/mental_arithmetic_exam/QuestionDisplay.vue';
import ExamResult from '@/components/mental_arithmetic_exam/ExamResult.vue';

const examStarted = ref(false);
const examFinished = ref(false);
const selectedDifficulty = ref(null);
const examResult = ref(null);
const examStartTime = ref(null);

const startExam = (difficulty) => {
  selectedDifficulty.value = difficulty;
  examStarted.value = true;
  examFinished.value = false;
  examStartTime.value = new Date();
};

const showResult = (result) => {
  const examEndTime = new Date();
  const durationSeconds = Math.floor((examEndTime - examStartTime.value) / 1000);
  
  examResult.value = {
    ...result,
    difficulty: selectedDifficulty.value,
    durationSeconds: durationSeconds
  };
  examFinished.value = true;
};

const restartExam = () => {
  examStarted.value = false;
  examFinished.value = false;
  selectedDifficulty.value = null;
  examResult.value = null;
  examStartTime.value = null;
};
</script>

<style scoped>
.mental-arithmetic-exam-container {
  padding: 20px;
}

.difficulty-selector-wrapper,
.question-display-wrapper,
.exam-result-wrapper {
  margin-top: 20px;
}
</style>