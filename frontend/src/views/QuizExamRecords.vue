<template>
  <div class="quiz-exam-records">
    <div class="page-header">
      <h1>
        <i class="el-icon-trophy"></i>
        考试记录
      </h1>
      <p class="page-description">查看和管理各类考试的记录信息</p>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <el-card>
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="考试类别">
              <el-select 
                v-model="searchForm.examCategory" 
                placeholder="选择考试类别"
                clearable
                @change="onCategoryChange"
              >
                <el-option
                  v-for="category in categories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="考试子类别">
              <el-select 
                v-model="searchForm.examSubcategory" 
                placeholder="选择考试子类别"
                clearable
                :disabled="!searchForm.examCategory"
              >
                <el-option
                  v-for="subcategory in subcategories"
                  :key="subcategory"
                  :label="subcategory"
                  :value="subcategory"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="searchRecords">
                <i class="el-icon-search"></i>
                搜索
              </el-button>
              <el-button @click="resetSearch">
                <i class="el-icon-refresh"></i>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 考试记录列表 -->
    <div class="records-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>考试记录</span>
            <div class="header-actions">
              <el-button 
                type="success" 
                size="small" 
                @click="showAddDialog = true"
              >
                添加记录
              </el-button>
            </div>
          </div>
        </template>

        <el-table 
          :data="records" 
          v-loading="loading"
          stripe
          border
        >
          <el-table-column prop="examCategory" label="考试类别" width="120">
            <template #default="scope">
              <el-tag :type="getCategoryTagType(scope.row.examCategory)">
                {{ scope.row.examCategory }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="examSubcategory" label="考试子类别" width="120">
            <template #default="scope">
              <span>{{ scope.row.examSubcategory || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="examTitle" label="考试标题" min-width="200">
            <template #default="scope">
              <div class="exam-title">
                <strong>{{ scope.row.examTitle }}</strong>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="考试成绩" width="150">
            <template #default="scope">
              <div class="score-info">
                <div class="score-fraction">
                  {{ scope.row.correctAnswers }}/{{ scope.row.totalQuestions }}
                </div>
                <div class="accuracy-rate" :style="{ color: getAccuracyLevel(scope.row.accuracyRate).color }">
                  {{ scope.row.accuracyRate }}%
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="正确率等级" width="100">
            <template #default="scope">
              <el-tag 
                :type="getAccuracyTagType(scope.row.accuracyRate)"
                size="small"
              >
                {{ getAccuracyLevel(scope.row.accuracyRate).label }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="examDurationSeconds" label="考试用时" width="120">
            <template #default="scope">
              <span>{{ formatDuration(scope.row.examDurationSeconds) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="examDate" label="考试时间" width="160">
            <template #default="scope">
              <span>{{ formatDateTime(scope.row.examDate) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewDetails(scope.row)"
              >
                查看
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="deleteRecord(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </el-card>
    </div>

    <!-- 添加记录对话框 -->
    <el-dialog
      title="添加考试记录"
      v-model="showAddDialog"
      width="600px"
    >
      <el-form :model="addForm" :rules="addRules" ref="addForm" label-width="120px">
        <el-form-item label="考试类别" prop="examCategory">
          <el-select v-model="addForm.examCategory" placeholder="选择考试类别" @change="onAddCategoryChange">
            <el-option
              v-for="category in allCategories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="考试子类别">
          <el-select v-model="addForm.examSubcategory" placeholder="选择考试子类别" clearable>
            <el-option
              v-for="subcategory in addSubcategories"
              :key="subcategory"
              :label="subcategory"
              :value="subcategory"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="考试标题" prop="examTitle">
          <el-input v-model="addForm.examTitle" placeholder="请输入考试标题" />
        </el-form-item>

        <el-form-item label="总题数" prop="totalQuestions">
          <el-input-number v-model="addForm.totalQuestions" :min="1" :max="1000" />
        </el-form-item>

        <el-form-item label="正确题数" prop="correctAnswers">
          <el-input-number v-model="addForm.correctAnswers" :min="0" :max="addForm.totalQuestions || 1000" />
        </el-form-item>

        <el-form-item label="考试用时(秒)">
          <el-input-number v-model="addForm.examDurationSeconds" :min="0" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitAdd" :loading="submitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="考试记录详情"
      v-model="showDetailDialog"
      width="700px"
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="考试类别">
            <el-tag :type="getCategoryTagType(currentRecord.examCategory)">
              {{ currentRecord.examCategory }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="考试子类别">
            {{ currentRecord.examSubcategory || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="考试标题" :span="2">
            {{ currentRecord.examTitle }}
          </el-descriptions-item>
          <el-descriptions-item label="总题数">
            {{ currentRecord.totalQuestions }}
          </el-descriptions-item>
          <el-descriptions-item label="正确题数">
            {{ currentRecord.correctAnswers }}
          </el-descriptions-item>
          <el-descriptions-item label="正确率">
            <span :style="{ color: getAccuracyLevel(currentRecord.accuracyRate).color }">
              {{ currentRecord.accuracyRate }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="等级">
            <el-tag :type="getAccuracyTagType(currentRecord.accuracyRate)">
              {{ getAccuracyLevel(currentRecord.accuracyRate).label }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="考试用时">
            {{ formatDuration(currentRecord.examDurationSeconds) }}
          </el-descriptions-item>
          <el-descriptions-item label="考试时间">
            {{ formatDateTime(currentRecord.examDate) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 考试详情 -->
        <div v-if="examDetails" class="exam-details">
          <h4>考试详情</h4>
          <pre>{{ JSON.stringify(examDetails, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { quizExamRecordsApi, EXAM_CATEGORIES, getAccuracyLevel, formatDuration, createExamRecordData } from '@/api/quizExamRecords';

export default {
  name: 'QuizExamRecords',
  data() {
    return {
      // 搜索表单
      searchForm: {
        examCategory: '',
        examSubcategory: ''
      },

      // 分页信息
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },

      // 数据
      records: [],
      categories: [],
      subcategories: [],
      allCategories: Object.values(EXAM_CATEGORIES),

      // 状态
      loading: false,
      submitting: false,

      // 对话框
      showAddDialog: false,
      showDetailDialog: false,
      currentRecord: null,

      // 添加表单
      addForm: {
        examCategory: '',
        examSubcategory: '',
        examTitle: '',
        totalQuestions: 10,
        correctAnswers: 0,
        examDurationSeconds: null
      },
      addSubcategories: [],

      // 表单验证规则
      addRules: {
        examCategory: [
          { required: true, message: '请选择考试类别', trigger: 'change' }
        ],
        examTitle: [
          { required: true, message: '请输入考试标题', trigger: 'blur' }
        ],
        totalQuestions: [
          { required: true, message: '请输入总题数', trigger: 'blur' }
        ],
        correctAnswers: [
          { required: true, message: '请输入正确题数', trigger: 'blur' }
        ]
      }
    };
  },

  computed: {
    examDetails() {
      if (!this.currentRecord?.examDetails) return null;
      try {
        return JSON.parse(this.currentRecord.examDetails);
      } catch (e) {
        return null;
      }
    }
  },

  async mounted() {
    await this.loadInitialData();
  },

  methods: {
    async loadInitialData() {
      try {
        // 并行加载数据
        await Promise.all([
          this.loadCategories(),
          this.loadRecords()
        ]);
      } catch (error) {
        console.error('加载初始数据失败:', error);
        this.$message.error('加载数据失败');
      }
    },

    async loadCategories() {
      try {
        const response = await quizExamRecordsApi.getAllCategories();
        this.categories = response.data || [];
        console.log('📋 加载到的类别:', this.categories);
      } catch (error) {
        console.error('加载类别失败:', error);
        // 如果API失败，使用默认类别
        this.categories = Object.values(EXAM_CATEGORIES);
        console.log('📋 使用默认类别:', this.categories);
      }
    },

    async loadRecords() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.current,
          size: this.pagination.size,
          examCategory: this.searchForm.examCategory,
          examSubcategory: this.searchForm.examSubcategory
        };

        console.log('🔍 加载考试记录，参数:', params);
        const response = await quizExamRecordsApi.getExamRecords(params);
        console.log('📋 API响应:', response);
        console.log('📋 响应数据:', response.data);
        
        // MyBatis Plus IPage 对象直接包含 records 和 total
        this.records = response.data.records || [];
        this.pagination.total = response.data.total || 0;
        
        console.log('📋 设置records:', this.records);
        console.log('📋 设置total:', this.pagination.total);
      } catch (error) {
        console.error('加载考试记录失败:', error);
        this.$message.error('加载考试记录失败');
      } finally {
        this.loading = false;
      }
    },

    async onCategoryChange() {
      this.searchForm.examSubcategory = '';
      this.subcategories = [];
      
      if (this.searchForm.examCategory) {
        try {
          const response = await quizExamRecordsApi.getSubcategoriesByCategory(this.searchForm.examCategory);
          this.subcategories = response.data || [];
          console.log('📋 加载到的子类别:', this.subcategories);
        } catch (error) {
          console.error('加载子类别失败:', error);
          this.subcategories = [];
        }
      }
    },

    async onAddCategoryChange() {
      this.addForm.examSubcategory = '';
      this.addSubcategories = [];
      
      if (this.addForm.examCategory) {
        try {
          const response = await quizExamRecordsApi.getSubcategoriesByCategory(this.addForm.examCategory);
          this.addSubcategories = response.data || [];
          console.log('📋 添加表单加载到的子类别:', this.addSubcategories);
        } catch (error) {
          console.error('加载子类别失败:', error);
          this.addSubcategories = [];
        }
      }
    },

    searchRecords() {
      this.pagination.current = 1;
      this.loadRecords();
    },

    resetSearch() {
      this.searchForm = {
        examCategory: '',
        examSubcategory: ''
      };
      this.subcategories = [];
      this.searchRecords();
    },

    handleSizeChange(val) {
      this.pagination.size = val;
      this.pagination.current = 1;
      this.loadRecords();
    },

    handleCurrentChange(val) {
      this.pagination.current = val;
      this.loadRecords();
    },

    viewDetails(record) {
      this.currentRecord = record;
      this.showDetailDialog = true;
    },

    async deleteRecord(record) {
      try {
        await this.$confirm('确认删除这条考试记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        await quizExamRecordsApi.deleteById(record.id);
        this.$message.success('删除成功');
        await this.loadRecords();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除记录失败:', error);
          this.$message.error('删除失败');
        }
      }
    },

    async submitAdd() {
      try {
        await this.$refs.addForm.validate();
        
        this.submitting = true;
        
        const recordData = createExamRecordData({
          category: this.addForm.examCategory,
          subcategory: this.addForm.examSubcategory,
          title: this.addForm.examTitle,
          totalQuestions: this.addForm.totalQuestions,
          correctAnswers: this.addForm.correctAnswers,
          durationSeconds: this.addForm.examDurationSeconds
        });

        await quizExamRecordsApi.addExamRecord(recordData);
        this.$message.success('添加成功');
        
        this.showAddDialog = false;
        this.resetAddForm();
        await this.loadRecords();
      } catch (error) {
        console.error('添加记录失败:', error);
        this.$message.error('添加失败');
      } finally {
        this.submitting = false;
      }
    },

    resetAddForm() {
      this.addForm = {
        examCategory: '',
        examSubcategory: '',
        examTitle: '',
        totalQuestions: 10,
        correctAnswers: 0,
        examDurationSeconds: null
      };
      this.addSubcategories = [];
      this.$refs.addForm?.resetFields();
    },

    // 工具方法
    getAccuracyLevel,
    formatDuration,

    getCategoryTagType(category) {
      const typeMap = {
        '卡片考试': 'primary',
        '数学口算': 'success',
        '趣味考试': 'warning',
        '汉语拼音': 'info'
      };
      return typeMap[category] || '';
    },

    getAccuracyTagType(accuracyRate) {
      if (accuracyRate >= 90) return 'success';
      if (accuracyRate >= 80) return 'primary';
      if (accuracyRate >= 70) return 'warning';
      return 'danger';
    },

    formatAccuracy(rate) {
      return rate ? `${rate.toFixed(1)}%` : '0%';
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      return new Date(dateTime).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    }
  }
};
</script>

<style scoped>
.quiz-exam-records {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-section {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.records-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-title {
  line-height: 1.4;
}

.score-info {
  text-align: center;
}

.score-fraction {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.accuracy-rate {
  font-size: 16px;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.record-detail {
  margin-top: 20px;
}

.exam-details {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.exam-details h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.exam-details pre {
  margin: 0;
  background: none;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quiz-exam-records {
    padding: 10px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .el-form-item {
    margin-bottom: 10px;
  }
}
</style> 