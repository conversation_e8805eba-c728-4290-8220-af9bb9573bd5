<template>
  <div class="course-detail">
    <PageHeader :title="courseData?.name || '课程详情'" />
    
    <div class="course-info" v-if="courseData">
      <div class="course-header">
        <div class="course-icon">
          <el-icon><component :is="getCourseIcon(courseData)" /></el-icon>
        </div>
        <div class="course-meta">
          <h1>{{ courseData.name }}</h1>
          <p class="course-description">{{ courseData.description }}</p>
        </div>
      </div>
      
      <div class="chapters-container">
        <h2>课程章节</h2>
        <div class="chapters-grid">
          <div 
            class="chapter-card" 
            v-for="(chapter, index) in courseData.chapters" 
            :key="chapter.id"
            @click="goToChapter(chapter)"
          >
            <div class="chapter-header">
              <div class="chapter-icon">
                <el-icon><component :is="getChapterIcon(chapter)" /></el-icon>
              </div>
              <div class="chapter-content">
                <h3>{{ chapter.name }}</h3>
                <div class="chapter-stats">
                  <span class="lesson-count">{{ getChapterLessons(chapter.id).length }} 题目</span>
                </div>
              </div>
            </div>
            <p class="chapter-description">{{ chapter.description }}</p>
            <div class="chapter-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="loading">
      <el-empty description="课程不存在" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Reading, 
  ArrowRight,
  DataAnalysis,
  ChatDotRound, 
  EditPen,
  Tools,
  School,
  Document
} from '@element-plus/icons-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import { getCourseById, getChapterLessons } from '@/data/courses'

const route = useRoute()
const router = useRouter()

const courseData = ref(null)

const courseId = computed(() => route.params.courseId)

onMounted(() => {
  loadCourseData()
})

const loadCourseData = () => {
  courseData.value = getCourseById(courseId.value)
}

// 根据课程内容获取相应图标
const getCourseIcon = (course) => {
  const courseName = course.name.toLowerCase()
  const description = course.description.toLowerCase()
  
  if (courseName.includes('数学') || description.includes('数学') || 
      courseName.includes('算术') || description.includes('计算')) {
    return DataAnalysis
  } else if (courseName.includes('英语') || description.includes('英语') || 
             courseName.includes('english') || description.includes('字母')) {
    return ChatDotRound
  } else if (courseName.includes('语文') || description.includes('语文') || 
             description.includes('拼音') || description.includes('汉字')) {
    return EditPen
  } else if (courseName.includes('科学') || description.includes('科学') || 
             description.includes('常识') || description.includes('自然')) {
    return Tools
  } else if (courseName.includes('幼升小') || description.includes('幼升小')) {
    return School
  } else {
    return Reading
  }
}

// 根据章节内容获取相应图标
const getChapterIcon = (chapter) => {
  const chapterName = chapter.name.toLowerCase()
  const description = chapter.description.toLowerCase()
  
  if (chapterName.includes('数学') || description.includes('数学') || 
      chapterName.includes('算术') || description.includes('计算')) {
    return DataAnalysis
  } else if (chapterName.includes('英语') || description.includes('英语') || 
             chapterName.includes('english') || description.includes('字母')) {
    return ChatDotRound
  } else if (chapterName.includes('语文') || description.includes('语文') || 
             description.includes('拼音') || description.includes('汉字')) {
    return EditPen
  } else if (chapterName.includes('科学') || description.includes('科学') || 
             description.includes('常识') || description.includes('自然')) {
    return Tools
  } else {
    return Document
  }
}

const goToChapter = (chapter) => {
  // 直接打开章节的HTML文件
  if (chapter.htmlFile) {
    window.open(chapter.htmlFile, '_blank')
  }
}
</script>

<style scoped>
.course-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.course-info {
  margin-top: 24px;
}

.course-header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
}

.course-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
  flex-shrink: 0;
}

.course-icon .el-icon {
  font-size: 30px;
  color: white;
}

.course-meta h1 {
  color: #303133;
  font-size: 28px;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.course-description {
  color: #606266;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

.chapters-container h2 {
  color: #303133;
  font-size: 24px;
  margin: 0 0 24px 0;
  font-weight: 600;
}

.chapters-grid {
  display: grid;
  gap: 16px;
}

.chapter-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  position: relative;
}

.chapter-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.chapter-header {
  display: flex;
  align-items: center;
  flex: 1;
}

.chapter-icon {
  width: 42px;
  height: 42px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

/* 为不同章节设置不同的图标背景色 */
.chapter-card:nth-child(4n+1) .chapter-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 数学 - 蓝紫 */
}

.chapter-card:nth-child(4n+2) .chapter-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); /* 英语 - 粉红 */
}

.chapter-card:nth-child(4n+3) .chapter-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); /* 语文 - 青蓝 */
}

.chapter-card:nth-child(4n+4) .chapter-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); /* 科学 - 绿青 */
}

.chapter-icon .el-icon {
  font-size: 20px;
  color: white;
}

.chapter-content {
  flex: 1;
}

.chapter-content h3 {
  color: #303133;
  font-size: 18px;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.chapter-stats {
  display: flex;
  align-items: center;
}

.lesson-count {
  color: #909399;
  font-size: 12px;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.chapter-description {
  color: #606266;
  font-size: 14px;
  margin: 0 16px 0 0;
  line-height: 1.5;
  flex: 2;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.chapter-arrow {
  color: #c0c4cc;
  font-size: 18px;
  transition: all 0.3s ease;
}

.chapter-card:hover .chapter-arrow {
  color: #409eff;
  transform: translateX(4px);
}

.loading {
  margin-top: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-header {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }
  
  .course-icon {
    margin: 0 0 16px 0;
  }
  
  .chapter-card {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
  }
  
  .chapter-header {
    width: 100%;
    margin-bottom: 12px;
  }
  
  .chapter-description {
    margin: 0 0 12px 0;
  }
  
  .chapter-arrow {
    align-self: flex-end;
  }
}
</style>