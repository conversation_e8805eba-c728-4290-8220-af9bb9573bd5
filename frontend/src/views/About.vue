<template>
  <div class="about">
    <el-card>
      <div class="about-content">
        <div class="logo-section">
          <el-icon size="64" color="#409EFF"><Document /></el-icon>
          <h1>KPE - 知脉精练</h1>
          <p class="subtitle">Knowledge Pulse Edge</p>
        </div>

        <div class="description">
          <h2>系统介绍</h2>
          <p>
            KPE（Knowledge Pulse Edge）是一个基于AI的知识点提取和管理系统，
            旨在帮助教育工作者快速构建和管理知识点体系。
          </p>
        </div>

        <div class="features">
          <h2>主要功能</h2>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <el-icon size="32" color="#67c23a"><FolderOpened /></el-icon>
                <h3>项目管理</h3>
                <p>创建和管理知识点提取项目，支持项目分类和描述</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <el-icon size="32" color="#e6a23c"><MagicStick /></el-icon>
                <h3>AI生成</h3>
                <p>基于扣子智能体，通过自然语言提示生成知识点结构</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <el-icon size="32" color="#f56c6c"><EditPen /></el-icon>
                <h3>可视化编辑</h3>
                <p>JSON格式的知识点配置，支持格式化和验证</p>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="tech-stack">
          <h2>技术栈</h2>
          <div class="tech-list">
            <el-tag size="large" class="tech-tag">Vue 3</el-tag>
            <el-tag size="large" class="tech-tag">Element Plus</el-tag>
            <el-tag size="large" class="tech-tag">Node.js</el-tag>
            <el-tag size="large" class="tech-tag">Express</el-tag>
            <el-tag size="large" class="tech-tag">PostgreSQL</el-tag>
            <el-tag size="large" class="tech-tag">扣子AI</el-tag>
          </div>
        </div>

        <div class="version-info">
          <h2>版本信息</h2>
          <p>当前版本: v1.0.0</p>
          <p>发布日期: 2025-06-04</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Document, FolderOpened, MagicStick, EditPen } from '@element-plus/icons-vue'
</script>

<style scoped>
.about {
  max-width: 800px;
  margin: 0 auto;
}

.about-content {
  padding: 40px;
  text-align: center;
}

.logo-section {
  margin-bottom: 40px;
}

.logo-section h1 {
  margin: 16px 0 8px 0;
  font-size: 32px;
  color: #303133;
}

.subtitle {
  color: #909399;
  font-size: 16px;
  margin: 0;
}

.description, .features, .tech-stack, .version-info {
  margin-bottom: 40px;
  text-align: left;
}

.description h2, .features h2, .tech-stack h2, .version-info h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 20px;
}

.description p {
  color: #606266;
  line-height: 1.6;
  font-size: 16px;
}

.feature-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  height: 100%;
}

.feature-item h3 {
  margin: 12px 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.feature-item p {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.tech-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.tech-tag {
  background: #f0f9ff;
  border-color: #409EFF;
  color: #409EFF;
}

.version-info p {
  color: #606266;
  margin: 8px 0;
}
</style>
