<template>
  <div class="quiz-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载题目...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">❌</div>
      <h2>加载失败</h2>
      <p>{{ error }}</p>
      <button @click="retryLoad" class="retry-btn">重试</button>
      <button @click="goBack" class="back-btn">返回列表</button>
    </div>

    <!-- 正常考试界面 -->
    <div v-else-if="!quizFinished && hasValidQuestions">
      <!-- 头部区域 -->
      <div class="quiz-header">
        <button @click="goBack" class="back-button" title="返回趣味考试列表">
          <span class="back-icon">←</span>
        </button>
        <div class="quiz-title">{{ quizData?.title || '趣味考试' }}</div>
        <div class="question-counter">{{ currentQuestionIndex + 1 }}/{{ questions.length }}</div>
      </div>
      
      <div class="progress-bar">
        <div class="progress" :style="{ width: progress + '%' }"></div>
      </div>
      <div v-if="currentQuestion" class="question-container">
        <div class="image-container" @click="toggleImageZoom($event)">
          <img 
            :src="getQuestionImageSrc()" 
            alt="Quiz Image" 
            :class="['quiz-image', { 'zoomed': isImageZoomed }]"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="handleTouchEnd"
            @mousedown="handleMouseDown"
            @mousemove="handleMouseMove"
            @mouseup="handleMouseUp"
            @mouseleave="handleMouseLeave"
            @error="handleImageError"
            :style="imageStyle"
            ref="quizImage"
          />
          <!-- 隐藏放大提示 -->
          <!-- <div v-if="!isImageZoomed" class="zoom-hint">
            <span class="zoom-icon">🔍</span>
            <span class="zoom-text">{{ isMobileDevice ? '点击放大' : '点击放大' }}</span>
          </div> -->
          <!-- 隐藏拖拽提示 -->
          <!-- <div v-if="isImageZoomed" class="drag-hint">
            <span class="drag-icon">✋</span>
            <span class="drag-text">{{ isMobileDevice ? '拖拽移动或双指缩放' : '拖拽移动' }}</span>
          </div> -->
        </div>
        
        <!-- 年代考试：显示类别和名称 -->
        <div v-if="isDecadeQuiz && currentQuestion" class="decade-info">
          <div class="decade-category">{{ currentQuestion.category }}</div>
          <div class="decade-name">{{ currentQuestion.itemName }}</div>
        </div>
        
        <!-- 动物栖息地考试：显示栖息地名称 -->
        <div v-if="isAnimalHabitatQuiz && currentQuestion" class="habitat-info">
          <div class="habitat-name">{{ currentQuestion.itemName }}</div>
        </div>
        
        <!-- 全球女明星考试：选择答案后显示女明星名称 -->
        <div v-if="isGlobalActressQuiz && currentQuestion && answerChecked" class="actress-info">
          <div class="actress-name">{{ currentQuestion.actressName }}</div>
        </div>
        <div v-if="isGlobalActorQuiz && currentQuestion && answerChecked" class="actor-info">
          <div class="actor-name">{{ currentQuestion.actorName }}</div>
        </div>
        <!-- 地标建筑考试：选择答案后显示建筑名称 -->
        <div v-if="isLandmarkBuildingQuiz && currentQuestion && answerChecked" class="building-info">
          <div class="building-name">{{ currentQuestion.buildingName }}</div>
        </div>
        <!-- 世界美食考试：选择答案后显示食物名称 -->
        <div v-if="isWorldFoodsQuiz && currentQuestion && answerChecked" class="food-info">
          <div class="food-name">{{ currentQuestion.foodName }}</div>
        </div>
        <!-- 中国著名食物考试：选择答案后显示菜名 -->
        <div v-if="isChineseFoodsQuiz && currentQuestion && answerChecked" class="chinese-food-info">
          <div class="chinese-food-name">{{ currentQuestion.foodName }}</div>
        </div>
        <!-- 最美风景考试：选择答案后显示风景名称 -->
        <div v-if="isBeautifulSceneriesQuiz && currentQuestion && answerChecked" class="scenery-info">
          <div class="scenery-name">{{ currentQuestion.sceneryName }}</div>
        </div>

        <div :class="getOptionsGridClass()">
          <button
            v-for="(option, index) in (currentQuestion.options || [])"
            :key="index"
            @click="selectAnswer(option)"
            :class="[
              'option-button',
              { 'correct': answerChecked && option === currentQuestion.answer },
              { 'incorrect': answerChecked && selectedAnswer === option && option !== currentQuestion.answer }
            ]"
            :disabled="answerChecked"
          >
            {{ option }}
          </button>
        </div>
        <div v-if="answerChecked" class="navigation-container">
          <button @click="nextQuestion" class="next-button next-question-button">
            {{ isLastQuestion ? '查看结果' : '下一题' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 考试结果 -->
    <div v-else-if="quizFinished">
      <QuizResult 
        :score="score" 
        :total="questions.length" 
        :quizType="quizType.value" 
        :examStartTime="examStartTime"
        @restart="handleRestart"
      />
    </div>

    <!-- 无题目状态 -->
    <div v-else class="empty-container">
      <div class="empty-icon">📚</div>
      <h2>暂无题目</h2>
      <p>该考试类型暂时没有可用的题目</p>
      <button @click="goBack" class="back-btn">返回列表</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getQuizFactory } from '@/factories/quizFactory.js';
import imageConfig from '@/config/imageConfig.js';
import QuizResult from './QuizResult.vue';
import { 
  getCompatibilityReport, 
  applyAndroidOptimizations, 
  setupErrorReporting,
  isOldWebView 
} from '@/utils/androidCompatibility.js';

const route = useRoute();
const router = useRouter();
const quizFactory = getQuizFactory();

// 基础状态
const loading = ref(true);
const error = ref('');
const quizType = ref(route.params.type); // 改为响应式
const questions = ref([]);
const currentQuestionIndex = ref(0);
const score = ref(0);
const quizFinished = ref(false);
const selectedAnswer = ref(null);
const answerChecked = ref(false);
const isCorrect = ref(false);
const examStartTime = ref(null); // 添加考试开始时间
const quizData = ref(null); // 改为响应式

const hasValidQuestions = computed(() => {
  return questions.value && questions.value.length > 0;
});

// 检测是否为移动设备
const isMobileDevice = computed(() => {
  return 'ontouchstart' in window;
});

// 检测是否为城市考试
const isCityQuiz = computed(() => {
  return quizType.value === 'city-regions';
});

// 检测是否为全球女明星考试
const isGlobalActressQuiz = computed(() => {
  return quizType.value === 'global-actresses';
});

// 检测是否为全球男明星考试
const isGlobalActorQuiz = computed(() => {
  return quizType.value === 'global-actors';
});

// 检测是否为地标建筑考试
const isLandmarkBuildingQuiz = computed(() => {
  return quizType.value === 'landmark_buildings';
});

// 检测是否为世界美食考试
const isWorldFoodsQuiz = computed(() => {
  return quizType.value === 'world-foods';
});

// 检测是否为中国著名食物考试
const isChineseFoodsQuiz = computed(() => {
  return quizType.value === 'chinese_foods';
});

// 检测是否为宇宙天体考试
const isCelestialBodiesQuiz = computed(() => {
  return quizType.value === 'celestial_bodies';
});

// 检测是否为最美风景考试
const isBeautifulSceneriesQuiz = computed(() => {
  return quizType.value === 'beautiful_sceneries';
});



// 获取题目图片源
const getQuestionImageSrc = () => {
  if (!currentQuestion.value) return '';
  
  // 城市考试使用Canvas生成的DataURL
  if (isCityQuiz.value) {
    return currentQuestion.value.image;
  }
  
  // 其他考试使用传统的图片路径
  return `${imageBasePath.value}${currentQuestion.value.image}`;
};

const imageBasePath = computed(() => {
  const categoryMap = {
    'car-logos': 'carLogos',
    'country-flags': 'countryFlags',
    'world-flags-plus': 'world-flags-plus',
    'animals': 'animals',
    'plants': 'plants',
    'world-landmarks': 'worldLandmarks',
    'china-landmarks': 'chinaLandmarks',
    'ethnicities': 'ethnicities',
    'dog-cat-breeds': 'dog-cat-breeds',
    'military-equipment': 'military-equipment',
    'vehicles': 'vehicles',
    'global-actresses': 'global-actresses',
    'global-actors': 'global-actors',
    'landmark_buildings': 'landmark_buildings',
    'world-foods': 'world_foods',
    'chinese_foods': 'chinese_foods',
    'celestial_bodies': 'celestial_bodies',
    'beautiful_sceneries': 'beautiful_sceneries'
  };
  
  const category = categoryMap[quizType.value];
  if (category && imageConfig.paths && imageConfig.paths[category]) {
    return `${imageConfig.baseUrl}${imageConfig.paths[category]}/`;
  }
  return `${imageConfig.baseUrl}/images/${quizType.value}/`; // Default fallback
});

// 图片缩放相关状态
const isImageZoomed = ref(false);
const imageScale = ref(1);
const imageTranslateX = ref(0);
const imageTranslateY = ref(0);
const quizImage = ref(null);

// 触摸手势相关状态
const touchState = ref({
  initialDistance: 0,
  initialScale: 1,
  initialTouchX: 0,
  initialTouchY: 0,
  initialTranslateX: 0,
  initialTranslateY: 0,
  isDragging: false,
  isPinching: false,
  // 单击检测相关
  touchStartTime: 0,
  hasMoved: false,
  tapThreshold: 10, // 移动超过10px则不算tap
  tapTimeThreshold: 300 // 300ms内算作tap
});

// 鼠标拖拽相关状态
const mouseState = ref({
  isMouseDown: false,
  isDragging: false,
  initialMouseX: 0,
  initialMouseY: 0,
  initialTranslateX: 0,
  initialTranslateY: 0
});

const initializeQuiz = async () => {
  try {
    loading.value = true;
    error.value = '';

    // 验证路由参数
    if (!quizType.value || typeof quizType.value !== 'string') {
      throw new Error('无效的考试类型参数');
    }

    // 使用工厂创建考试
    const quiz = await quizFactory.createQuiz(quizType.value);
    
    if (!quiz) {
      throw new Error(`考试类型 "${quizType.value}" 不存在`);
    }

    if (!quiz.questions || !Array.isArray(quiz.questions)) {
      throw new Error('考试题目数据无效');
    }

    if (quiz.questions.length === 0) {
      throw new Error('该考试类型暂时没有可用的题目');
    }

    // 设置考试数据
    quizData.value = quiz;
    questions.value = quiz.questions;
    
    // 验证每个题目的完整性
    questions.value.forEach((q, index) => {
      if (!q.options || !Array.isArray(q.options) || q.options.length === 0) {
        throw new Error(`题目 ${index + 1} 的选项数据无效`);
      }
      if (!q.answer) {
        throw new Error(`题目 ${index + 1} 缺少正确答案`);
      }
      // 城市考试可能使用DataURL图片，其他考试需要image字段
      if (!q.image && !isCityQuiz.value) {
        throw new Error(`题目 ${index + 1} 缺少图片`);
      }
    });

    // 记录考试开始时间
    examStartTime.value = new Date();
    console.log(`✅ 成功加载 ${questions.value.length} 道题目`);
  } catch (err) {
    console.error('❌ 加载考试失败:', err);
    error.value = err.message || '加载考试失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 添加调试信息
const debugInfo = () => {
  console.log('🔍 调试信息:');
  console.log('  - 设备信息:', navigator.userAgent);
  console.log('  - quizType:', quizType.value);
  console.log('  - quizData:', quizData.value);
  console.log('  - imageConfig:', imageConfig);
  console.log('  - 题目数量:', questions.value.length);
  
  // 生成兼容性报告
  getCompatibilityReport();
};

// 监听路由参数变化
watch(() => route.params.type, (newType) => {
  if (newType && newType !== quizType.value) {
    console.log('🔄 路由参数变化，重新初始化考试:', newType);
    quizType.value = newType;
    
    // 重置所有状态
    currentQuestionIndex.value = 0;
    score.value = 0;
    quizFinished.value = false;
    selectedAnswer.value = null;
    answerChecked.value = false;
    isCorrect.value = false;
    examStartTime.value = null; // 重置考试开始时间
    resetImageZoom();
    
    // 重新初始化考试
    initializeQuiz();
  }
}, { immediate: false });

onMounted(() => {
  // 设置错误报告
  setupErrorReporting();
  
  // 应用安卓优化
  applyAndroidOptimizations();
  
  // 输出调试信息
  debugInfo();
  
  // 为旧版WebView添加额外的延迟
  if (isOldWebView()) {
    console.log('🐌 检测到旧版WebView，添加初始化延迟');
    setTimeout(() => {
      initializeQuiz();
    }, 100);
  } else {
    initializeQuiz();
  }
});

const currentQuestion = computed(() => questions.value[currentQuestionIndex.value]);
const progress = computed(() => ((currentQuestionIndex.value) / questions.value.length) * 100);
const isLastQuestion = computed(() => currentQuestionIndex.value === questions.value.length - 1);

// 获取选项网格的CSS类
const getOptionsGridClass = () => {
  if (!currentQuestion.value?.options) return 'options-grid';
  
  const optionCount = currentQuestion.value.options.length;
  const baseClass = 'options-grid';
  
  if (optionCount === 4) {
    return baseClass + ' four-options';
  } else if (optionCount === 6) {
    return baseClass + ' six-options';
  } else if (optionCount === 8) {
    return baseClass + ' eight-options';
  }
  
  return baseClass;
};

// 图片样式计算属性
const imageStyle = computed(() => ({
  transform: `scale(${imageScale.value}) translate(${imageTranslateX.value}px, ${imageTranslateY.value}px)`,
  transition: touchState.value.isDragging || touchState.value.isPinching || mouseState.value.isDragging ? 'none' : 'transform 0.3s ease'
}));

const selectAnswer = (selectedOption) => {
  if (answerChecked.value) return;
  
  try {
    if (!selectedOption || !currentQuestion.value) {
      console.error('无效的选项或题目数据');
      return;
    }

    answerChecked.value = true;
    selectedAnswer.value = selectedOption;
    
    if (selectedOption === currentQuestion.value.answer) {
      score.value++;
      isCorrect.value = true;
    } else {
      isCorrect.value = false;
    }
    
    console.log(`答题结果: ${selectedOption === currentQuestion.value.answer ? '正确' : '错误'}`);
  } catch (err) {
    console.error('答题处理失败:', err);
    // 发生错误时重置状态
    answerChecked.value = false;
    selectedAnswer.value = null;
  }
};

const nextQuestion = () => {
  try {
    if (currentQuestionIndex.value < questions.value.length - 1) {
      currentQuestionIndex.value++;
      answerChecked.value = false;
      selectedAnswer.value = null;
      isCorrect.value = false;
      // 重置图片缩放状态
      resetImageZoom();
      
      // 确保下一题存在
      if (!questions.value[currentQuestionIndex.value]) {
        throw new Error('题目数据不完整');
      }
    } else {
      quizFinished.value = true;
    }
  } catch (err) {
    console.error('切换题目失败:', err);
    error.value = '切换题目时发生错误，请重试';
  }
};

// 图片缩放相关方法
const toggleImageZoom = (event) => {
  if (isImageZoomed.value) {
    resetImageZoom();
  } else {
    isImageZoomed.value = true;
    
    // 根据设备类型设置不同的缩放倍数
    const isMobile = 'ontouchstart' in window;
    imageScale.value = isMobile ? 2.5 : 2; // 移动端放得更大一些
    
    // 如果有点击位置信息，将图片中心调整到点击位置
    if (event && quizImage.value) {
      const rect = quizImage.value.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      let clickX, clickY;
      
      if (event.touches && event.touches.length > 0) {
        // 触摸事件
        clickX = event.touches[0].clientX - rect.left;
        clickY = event.touches[0].clientY - rect.top;
      } else if (event.clientX !== undefined) {
        // 鼠标事件
        clickX = event.clientX - rect.left;
        clickY = event.clientY - rect.top;
      } else {
        // 默认居中
        clickX = centerX;
        clickY = centerY;
      }
      
      // 计算平移量，使点击位置成为新的中心
      const offsetX = (centerX - clickX) * (imageScale.value - 1);
      const offsetY = (centerY - clickY) * (imageScale.value - 1);
      
      imageTranslateX.value = offsetX / imageScale.value;
      imageTranslateY.value = offsetY / imageScale.value;
    }
    
    // 更新鼠标样式
    if (quizImage.value) {
      quizImage.value.style.cursor = 'grab';
    }
    
    console.log(`🔍 图片放大: 缩放${imageScale.value}倍`);
  }
};

const resetImageZoom = () => {
  isImageZoomed.value = false;
  imageScale.value = 1;
  imageTranslateX.value = 0;
  imageTranslateY.value = 0;
  // 重置鼠标状态
  mouseState.value.isMouseDown = false;
  mouseState.value.isDragging = false;
  // 恢复鼠标样式
  if (quizImage.value) {
    quizImage.value.style.cursor = 'pointer';
  }
};

// 触摸手势处理方法
const getTouchDistance = (touch1, touch2) => {
  const dx = touch1.clientX - touch2.clientX;
  const dy = touch1.clientY - touch2.clientY;
  return Math.sqrt(dx * dx + dy * dy);
};

const handleTouchStart = (event) => {
  event.preventDefault();
  const touches = event.touches;
  
  if (touches.length === 1) {
    // 单指触摸 - 记录初始状态
    touchState.value.touchStartTime = Date.now();
    touchState.value.hasMoved = false;
    touchState.value.initialTouchX = touches[0].clientX;
    touchState.value.initialTouchY = touches[0].clientY;
    touchState.value.initialTranslateX = imageTranslateX.value;
    touchState.value.initialTranslateY = imageTranslateY.value;
    
    // 如果图片已经放大，准备拖拽
    if (isImageZoomed.value) {
      touchState.value.isDragging = true;
    }
  } else if (touches.length === 2) {
    // 双指触摸 - 准备缩放
    touchState.value.isPinching = true;
    touchState.value.isDragging = false;
    touchState.value.hasMoved = true; // 双指操作不算tap
    touchState.value.initialDistance = getTouchDistance(touches[0], touches[1]);
    touchState.value.initialScale = imageScale.value;
    isImageZoomed.value = true;
  }
};

const handleTouchMove = (event) => {
  event.preventDefault();
  const touches = event.touches;
  
  if (touches.length === 1) {
    // 计算移动距离
    const deltaX = touches[0].clientX - touchState.value.initialTouchX;
    const deltaY = touches[0].clientY - touchState.value.initialTouchY;
    const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    // 检查是否移动超过阈值
    if (moveDistance > touchState.value.tapThreshold) {
      touchState.value.hasMoved = true;
      
      // 如果图片已放大且在拖拽，则更新位置
      if (touchState.value.isDragging && isImageZoomed.value) {
        imageTranslateX.value = touchState.value.initialTranslateX + deltaX / imageScale.value;
        imageTranslateY.value = touchState.value.initialTranslateY + deltaY / imageScale.value;
      }
    }
  } else if (touches.length === 2 && touchState.value.isPinching) {
    // 双指缩放
    const currentDistance = getTouchDistance(touches[0], touches[1]);
    const scaleChange = currentDistance / touchState.value.initialDistance;
    let newScale = touchState.value.initialScale * scaleChange;
    
    // 限制缩放范围
    newScale = Math.max(1, Math.min(4, newScale));
    imageScale.value = newScale;
    
    if (newScale <= 1) {
      resetImageZoom();
    } else {
      isImageZoomed.value = true;
    }
  }
};

const handleTouchEnd = (event) => {
  event.preventDefault();
  
  // 检查是否为有效的tap操作
  const touchEndTime = Date.now();
  const touchDuration = touchEndTime - touchState.value.touchStartTime;
  
  if (!touchState.value.hasMoved && 
      touchDuration < touchState.value.tapTimeThreshold && 
      event.changedTouches.length === 1) {
    // 这是一个有效的tap操作
    console.log('📱 检测到单击操作');
    toggleImageZoom();
  }
  
  // 重置触摸状态
  touchState.value.isDragging = false;
  touchState.value.isPinching = false;
  touchState.value.hasMoved = false;
  
  // 如果缩放比例接近1，则重置
  if (imageScale.value <= 1.1) {
    resetImageZoom();
  }
};

// 鼠标事件处理方法
const handleMouseDown = (event) => {
  // 只在图片放大状态下才允许拖拽
  if (!isImageZoomed.value) return;
  
  event.preventDefault();
  mouseState.value.isMouseDown = true;
  mouseState.value.initialMouseX = event.clientX;
  mouseState.value.initialMouseY = event.clientY;
  mouseState.value.initialTranslateX = imageTranslateX.value;
  mouseState.value.initialTranslateY = imageTranslateY.value;
  
  // 设置鼠标样式
  if (quizImage.value) {
    quizImage.value.style.cursor = 'grabbing';
  }
};

const handleMouseMove = (event) => {
  if (!mouseState.value.isMouseDown || !isImageZoomed.value) return;
  
  event.preventDefault();
  
  // 开始拖拽
  if (!mouseState.value.isDragging) {
    mouseState.value.isDragging = true;
  }
  
  // 计算移动距离
  const deltaX = event.clientX - mouseState.value.initialMouseX;
  const deltaY = event.clientY - mouseState.value.initialMouseY;
  
  // 更新图片位置
  imageTranslateX.value = mouseState.value.initialTranslateX + deltaX / imageScale.value;
  imageTranslateY.value = mouseState.value.initialTranslateY + deltaY / imageScale.value;
};

const handleMouseUp = (event) => {
  event.preventDefault();
  
  // 重置鼠标状态
  mouseState.value.isMouseDown = false;
  mouseState.value.isDragging = false;
  
  // 恢复鼠标样式
  if (quizImage.value) {
    quizImage.value.style.cursor = isImageZoomed.value ? 'grab' : 'pointer';
  }
};

const handleMouseLeave = (event) => {
  // 当鼠标离开图片区域时，停止拖拽
  if (mouseState.value.isMouseDown) {
    handleMouseUp(event);
  }
};

const handleImageError = (event) => {
  console.error('图片加载失败', event);
  error.value = '图片加载失败，请检查网络连接或刷新页面重试';
};

const retryLoad = () => {
  // 重置状态
  currentQuestionIndex.value = 0;
  score.value = 0;
  quizFinished.value = false;
  selectedAnswer.value = null;
  answerChecked.value = false;
  isCorrect.value = false;
  examStartTime.value = null; // 重置考试开始时间
  resetImageZoom();
  
  // 重新初始化
  initializeQuiz();
};

const goBack = () => {
  router.push('/quiz-list');
};

const handleRestart = () => {
  retryLoad();
};

</script>

<style scoped>
.quiz-container {
  padding: 15px 20px 20px;
  max-width: 500px;
  margin: auto;
  touch-action: manipulation; /* 优化移动端触摸体验 */
  /* 安卓设备兼容性优化 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 防止安卓设备上的渲染问题 */
  overflow-x: hidden;
  position: relative;
}

/* 头部区域样式 */
.quiz-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 0 5px;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background-color: #f5f5f5;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background-color: #e0e0e0;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-icon {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  line-height: 1;
}

.quiz-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.question-counter {
  font-size: 14px;
  color: #888;
  font-weight: 500;
  background-color: #f0f8ff;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 50px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  background-color: #f0f0f0;
  border-radius: 5px;
  margin-bottom: 20px;
}

.progress {
  height: 10px;
  background-color: #4caf50;
  border-radius: 5px;
}

.question-container {
  text-align: center;
}

.image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
  overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
  touch-action: none; /* 防止默认的触摸行为 */
  /* 改善触摸反馈 */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.quiz-image {
  max-width: 100%;
  height: auto;
  max-height: 300px;
  border-radius: 8px;
  transition: transform 0.3s ease;
  transform-origin: center center;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  cursor: pointer;
  /* 安卓设备图片优化 */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* 防止图片加载问题 */
  display: block;
  /* 强制硬件加速 */
  will-change: transform;
}

.quiz-image.zoomed {
  cursor: grab;
  max-height: 80vh;
  position: relative;
  z-index: 10;
}

.quiz-image.zoomed:active {
  cursor: grabbing;
}

/* 年代考试信息样式 */
.decade-info {
  margin-top: 15px;
  margin-bottom: 10px;
  text-align: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 动物栖息地考试信息样式 */
.habitat-info {
  margin-top: 15px;
  margin-bottom: 10px;
  text-align: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border-radius: 12px;
  border: 1px solid #c3e6cb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.habitat-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #155724;
  margin: 0;
}

/* 全球女明星和男明星考试信息样式 */
.actress-info, .actor-info {
  margin-top: 15px;
  margin-bottom: 10px;
  text-align: center;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.actress-info {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  border-color: #f48fb1;
}

.actor-info {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
  border-color: #42a5f5;
}

.actress-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #880e4f;
  margin: 0;
}

.actor-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0d47a1;
  margin: 0;
}

/* 世界美食考试信息样式 */
.food-info {
  margin-top: 15px;
  margin-bottom: 10px;
  text-align: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
  border-radius: 12px;
  border: 1px solid #ffb74d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.food-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e65100;
  margin: 0;
}

/* 中国著名食物考试信息样式 */
.chinese-food-info {
  margin-top: 15px;
  margin-bottom: 10px;
  text-align: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fff8e1 0%, #ffcc02 100%);
  border-radius: 12px;
  border: 1px solid #ffc107;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.chinese-food-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #f57c00;
  margin: 0;
}

/* 宇宙天体考试信息样式 */
.celestial-body-info {
  margin-top: 15px;
  margin-bottom: 10px;
  text-align: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #e8eaf6 0%, #9c27b0 100%);
  border-radius: 12px;
  border: 1px solid #7b1fa2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.celestial-body-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4a148c;
  margin: 0;
}

.decade-category {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.decade-name {
  font-size: 1.1rem;
  color: #343a40;
  font-weight: 600;
  line-height: 1.3;
}

/* 隐藏放大和拖拽提示样式 */
/*
.zoom-hint {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
  pointer-events: none;
  opacity: 0.8;
}

.zoom-icon {
  font-size: 1rem;
}

.zoom-text {
  font-size: 0.75rem;
}

.drag-hint {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
  pointer-events: none;
  opacity: 0.8;
}

.drag-icon {
  font-size: 1rem;
}

.drag-text {
  font-size: 0.75rem;
}
*/

.options-grid {
  display: grid;
  gap: 10px;
  margin-top: 20px;
}

/* 4个选项的布局 - 2x2网格 */
.options-grid.four-options {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
}

/* 6个选项的布局 - 2x3网格 */
.options-grid.six-options {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8px;
}

.options-grid.six-options .option-button {
  min-height: 55px;
  padding: 12px 10px;
  font-size: 0.95rem;
}

/* 8个选项的布局 - 2x4网格 */
.options-grid.eight-options {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 8px;
}

.options-grid.eight-options .option-button {
  min-height: 50px;
  padding: 10px 8px;
  font-size: 0.9rem;
}

.option-button {
  padding: 15px;
  font-size: 1rem;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s, transform 0.1s;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  touch-action: manipulation;
}

.option-button:hover {
  background-color: #e0e0e0;
  transform: translateY(-1px);
}

.option-button:active {
  transform: translateY(0);
}

.option-button.correct {
  background-color: #d4edda; /* light green */
  border-color: #c3e6cb;
}

.option-button.incorrect {
  background-color: #f8d7da; /* light red */
  border-color: #f5c6cb;
}

.navigation-container {
  margin-top: 20px;
}

.next-button {
    padding: 10px 20px;
    font-size: 1rem;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.next-question-button {
    width: 100%;
    padding: 15px;
    font-size: 1.2rem;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .quiz-container {
    padding: 10px 15px 15px;
    max-width: 100%;
  }
  
  .quiz-header {
    margin-bottom: 12px;
    padding: 0 2px;
  }
  
  .back-button {
    width: 36px;
    height: 36px;
  }
  
  .back-icon {
    font-size: 16px;
  }
  
  .quiz-title {
    font-size: 15px;
    margin: 0 10px;
  }
  
  .question-counter {
    font-size: 13px;
    padding: 3px 6px;
    min-width: 45px;
  }
  
  .quiz-image {
    max-height: 250px;
  }
  
  .quiz-image.zoomed {
    max-height: 75vh; /* 移动端放大时占用更多屏幕空间 */
  }
  
  /* 移动端默认所有选项保持2列布局 */
  .options-grid.four-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .options-grid.six-options {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
  
  .options-grid.six-options .option-button {
    min-height: 50px;
    padding: 10px 8px;
    font-size: 0.9rem;
  }
  
  .options-grid.eight-options {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, 1fr);
  }
  
  .options-grid.eight-options .option-button {
    min-height: 45px;
    padding: 8px 6px;
    font-size: 0.85rem;
  }
  
  .option-button {
    padding: 18px;
    font-size: 1.1rem;
    min-height: 65px;
  }
  
  /* 移动端年代信息样式 */
  .decade-info {
    margin-top: 12px;
    margin-bottom: 8px;
    padding: 10px 12px;
    border-radius: 10px;
  }
  
  .decade-category {
    font-size: 0.8rem;
    margin-bottom: 3px;
  }
  
  .decade-name {
    font-size: 1rem;
  }
  
  /* 隐藏移动端放大和拖拽提示样式 */
  /*
  .zoom-hint {
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    font-size: 0.7rem;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .zoom-text {
    font-size: 0.7rem;
  }
  
  .drag-hint {
    top: 8px;
    left: 8px;
    padding: 4px 8px;
    font-size: 0.7rem;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    max-width: 120px;
    word-wrap: break-word;
  }
  
  .drag-text {
    font-size: 0.7rem;
    line-height: 1.2;
  }
  */
  
  /* 移动端图片容器增强触摸体验 */
  .image-container {
    /* 添加轻微的阴影提示可点击 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .image-container:active {
    /* 点击时的视觉反馈 */
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}

@media (max-width: 480px) {
  .quiz-container {
    padding: 8px 10px 10px;
  }
  
  .quiz-header {
    margin-bottom: 10px;
  }
  
  .back-button {
    width: 32px;
    height: 32px;
  }
  
  .back-icon {
    font-size: 14px;
  }
  
  .quiz-title {
    font-size: 14px;
    margin: 0 8px;
  }
  
  .question-counter {
    font-size: 12px;
    padding: 2px 5px;
    min-width: 40px;
  }
  
  .quiz-image {
    max-height: 200px;
  }
  
  .quiz-image.zoomed {
    max-height: 65vh;
  }
  
  .option-button {
    padding: 16px;
    font-size: 1rem;
    min-height: 60px;
  }
  
  .progress {
    height: 8px;
  }
  
  /* 小屏幕年代信息样式 */
  .decade-info {
    margin-top: 10px;
    margin-bottom: 6px;
    padding: 8px 10px;
    border-radius: 8px;
  }
  
  .decade-category {
    font-size: 0.75rem;
    margin-bottom: 2px;
  }
  
  .decade-name {
    font-size: 0.95rem;
  }
  
  /* 小屏幕栖息地信息样式 */
  .habitat-info {
    margin-top: 10px;
    margin-bottom: 6px;
    padding: 8px 10px;
    border-radius: 8px;
  }
  
  .habitat-name {
    font-size: 0.95rem;
  }
  
  /* 小屏幕女明星信息样式 */
  .actress-info {
    margin-top: 10px;
    margin-bottom: 6px;
    padding: 8px 10px;
    border-radius: 8px;
  }
  
  .actress-name {
    font-size: 0.95rem;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4caf50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #666;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 20px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-container h2 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #333;
}

.error-container p {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
}

.retry-btn, .back-btn {
  padding: 12px 24px;
  margin: 0 8px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.retry-btn {
  background-color: #4caf50;
  color: white;
}

.retry-btn:hover {
  background-color: #45a049;
}

.back-btn {
  background-color: #f0f0f0;
  color: #333;
}

.back-btn:hover {
  background-color: #e0e0e0;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-container h2 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #333;
}

.empty-container p {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.5;
}

/* 安卓设备特殊优化 */
.android-device .quiz-container {
  /* 减少GPU负载 */
  transform: none;
  -webkit-transform: none;
}

.old-android .quiz-image {
  /* 简化图片处理 */
  will-change: auto;
  transform: none;
  -webkit-transform: none;
}

.old-webview .quiz-image.zoomed {
  /* 禁用复杂的缩放效果 */
  transform: scale(1.5) !important;
  -webkit-transform: scale(1.5) !important;
}

.old-webview .option-button {
  /* 简化按钮效果 */
  transition: none;
  transform: none;
  -webkit-transform: none;
}

.old-webview .option-button:hover {
  transform: none;
  -webkit-transform: none;
}

/* 针对安卓7以下的特殊处理 */
.old-android .loading-spinner {
  /* 使用简单的动画 */
  animation: none;
  border: 3px solid #ccc;
  border-top: 3px solid #4caf50;
  background: radial-gradient(circle, #4caf50, transparent);
}

.old-webview .progress {
  /* 简化进度条动画 */
  transition: none;
}
</style>