/**
 * 缓存管理服务
 * 统一管理前后端缓存清理操作
 */

import { knowledgeAPI } from '@/api'
import { clearProjectCache, clearAllCache as clearKnowledgePathCache } from './knowledgePathService'
import { ElMessage } from 'element-plus'

/**
 * 清空项目的所有相关缓存
 * @param {number} projectId - 项目ID
 * @param {boolean} showMessage - 是否显示成功消息
 * @returns {Promise<boolean>} 是否成功
 */
export async function clearProjectAllCache(projectId, showMessage = true) {
  try {
    console.log('🗑️ 开始清空项目缓存:', projectId)
    
    // 1. 清空后端知识点缓存
    await knowledgeAPI.clearKnowledgeCache(projectId)
    console.log('✅ 后端知识点缓存清空成功')
    
    // 2. 清空前端知识点路径缓存
    clearProjectCache(projectId)
    console.log('✅ 前端知识点路径缓存清空成功')
    
    if (showMessage) {
      ElMessage.success('项目缓存清空成功')
    }
    
    console.log('✅ 项目所有缓存清空完成:', projectId)
    return true
    
  } catch (error) {
    console.error('❌ 清空项目缓存失败:', error)
    if (showMessage) {
      ElMessage.error('清空项目缓存失败: ' + error.message)
    }
    return false
  }
}

/**
 * 清空所有缓存
 * @param {boolean} showMessage - 是否显示成功消息
 * @returns {Promise<boolean>} 是否成功
 */
export async function clearAllCache(showMessage = true) {
  try {
    console.log('🗑️ 开始清空所有缓存')
    
    // 1. 清空前端知识点路径缓存
    clearKnowledgePathCache()
    console.log('✅ 前端知识点路径缓存清空成功')
    
    // 注意：这里没有调用后端清空所有缓存的接口，因为那个接口在CacheController中
    // 如果需要清空后端所有缓存，需要单独调用
    
    if (showMessage) {
      ElMessage.success('前端缓存清空成功')
    }
    
    console.log('✅ 所有前端缓存清空完成')
    return true
    
  } catch (error) {
    console.error('❌ 清空所有缓存失败:', error)
    if (showMessage) {
      ElMessage.error('清空缓存失败: ' + error.message)
    }
    return false
  }
}

/**
 * 在知识点更新后自动清空相关缓存
 * @param {number} projectId - 项目ID
 */
export async function clearCacheAfterKnowledgeUpdate(projectId) {
  console.log('📝 知识点更新后清空缓存:', projectId)
  
  // 静默清空缓存，不显示消息
  await clearProjectAllCache(projectId, false)
  
  console.log('✅ 知识点更新后缓存清空完成')
}

/**
 * 在知识点评分更新后自动清空相关缓存
 * @param {number} projectId - 项目ID
 */
export async function clearCacheAfterScoreUpdate(projectId) {
  console.log('📊 知识点评分更新后清空缓存:', projectId)
  
  // 静默清空缓存，不显示消息
  await clearProjectAllCache(projectId, false)
  
  console.log('✅ 知识点评分更新后缓存清空完成')
}

/**
 * 在知识点配置保存后自动清空相关缓存
 * @param {number} projectId - 项目ID
 */
export async function clearCacheAfterConfigSave(projectId) {
  console.log('💾 知识点配置保存后清空缓存:', projectId)
  
  // 静默清空缓存，不显示消息
  await clearProjectAllCache(projectId, false)
  
  console.log('✅ 知识点配置保存后缓存清空完成')
}

export default {
  clearProjectAllCache,
  clearAllCache,
  clearCacheAfterKnowledgeUpdate,
  clearCacheAfterScoreUpdate,
  clearCacheAfterConfigSave
}
