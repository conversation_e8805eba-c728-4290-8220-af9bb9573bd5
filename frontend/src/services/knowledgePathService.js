/**
 * 知识点路径服务
 * 统一管理知识点路径的获取，主要依赖后端缓存
 */

import api from '@/api'

// 轻量级前端缓存，主要用于减少重复请求
const requestCache = new Map()

// 缓存过期时间（1分钟，主要用于避免重复请求）
const CACHE_EXPIRE_TIME = 60 * 1000

/**
 * 请求缓存项结构
 */
class RequestCacheItem {
  constructor(promise) {
    this.promise = promise
    this.timestamp = Date.now()
  }

  isExpired() {
    return Date.now() - this.timestamp > CACHE_EXPIRE_TIME
  }
}

/**
 * 批量获取知识点路径信息（父级路径，用于知识点管理）
 * 主要依赖后端缓存，前端只做请求去重
 * @param {number} projectId - 项目ID
 * @returns {Promise<Object>} 路径映射对象 {uid: path}
 */
export async function getKnowledgePointPaths(projectId) {
  const cacheKey = `paths_${projectId}`

  // 检查是否有正在进行的请求
  const cached = requestCache.get(cacheKey)
  if (cached && !cached.isExpired()) {
    console.log('🔄 复用正在进行的路径请求:', projectId)
    return await cached.promise
  }

  // 创建新的请求
  const requestPromise = (async () => {
    try {
      console.log('🔍 从后端获取知识点父级路径（利用后端缓存）:', projectId)
      const response = await api.get(`/projects/${projectId}/knowledge-paths`)

      if (response.data.success) {
        console.log('✅ 知识点父级路径获取成功:', projectId, '数量:', Object.keys(response.data.data).length)
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取知识点父级路径失败')
      }
    } catch (error) {
      console.error('❌ 获取知识点路径失败:', error)
      throw error
    } finally {
      // 请求完成后清除缓存
      requestCache.delete(cacheKey)
    }
  })()

  // 缓存请求
  requestCache.set(cacheKey, new RequestCacheItem(requestPromise))

  return await requestPromise
}

/**
 * 批量获取知识点完整路径信息（包含叶子节点本身，用于卡片管理）
 * 主要依赖后端缓存，前端只做请求去重
 * @param {number} projectId - 项目ID
 * @returns {Promise<Object>} 路径映射对象 {uid: fullPath}
 */
export async function getKnowledgePointFullPaths(projectId) {
  const cacheKey = `full_paths_${projectId}`

  // 检查是否有正在进行的请求
  const cached = requestCache.get(cacheKey)
  if (cached && !cached.isExpired()) {
    console.log('🔄 复用正在进行的完整路径请求:', projectId)
    return await cached.promise
  }

  // 创建新的请求
  const requestPromise = (async () => {
    try {
      console.log('🔍 从后端获取知识点完整路径（利用后端缓存）:', projectId)
      const response = await api.get(`/projects/${projectId}/knowledge-paths/full`)

      if (response.data.success) {
        console.log('✅ 知识点完整路径获取成功:', projectId, '数量:', Object.keys(response.data.data).length)
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取知识点完整路径失败')
      }
    } catch (error) {
      console.error('❌ 获取知识点完整路径失败:', error)
      throw error
    } finally {
      // 请求完成后清除缓存
      requestCache.delete(cacheKey)
    }
  })()

  // 缓存请求
  requestCache.set(cacheKey, new RequestCacheItem(requestPromise))

  return await requestPromise
}

/**
 * 获取带路径的叶子节点知识点列表
 * 专门用于获取叶子节点，不能复用普通路径接口
 * 主要依赖后端缓存，前端只做请求去重
 * @param {number} projectId - 项目ID
 * @returns {Promise<Array>} 叶子节点知识点列表
 */
export async function getLeafKnowledgePointsWithPaths(projectId) {
  const cacheKey = `leaf_points_${projectId}`

  // 检查是否有正在进行的请求
  const cached = requestCache.get(cacheKey)
  if (cached && !cached.isExpired()) {
    console.log('🔄 复用正在进行的叶子节点请求:', projectId)
    return await cached.promise
  }

  // 创建新的请求
  const requestPromise = (async () => {
    try {
      console.log('🔍 从后端获取带路径的叶子节点（利用后端缓存）:', projectId)
      const response = await api.get(`/projects/${projectId}/knowledge-paths/leaf-points`)

      if (response.data.success) {
        console.log('✅ 带路径的叶子节点获取成功:', projectId, '数量:', response.data.data.length)
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取带路径的叶子节点失败')
      }
    } catch (error) {
      console.error('❌ 获取带路径的叶子节点失败:', error)
      throw error
    } finally {
      // 请求完成后清除缓存
      requestCache.delete(cacheKey)
    }
  })()

  // 缓存请求
  requestCache.set(cacheKey, new RequestCacheItem(requestPromise))

  return await requestPromise
}

/**
 * 获取指定知识点的路径信息
 * 优先从批量路径中获取，利用后端缓存
 * @param {number} projectId - 项目ID
 * @param {string} uid - 知识点UID
 * @returns {Promise<string>} 知识点路径
 */
export async function getKnowledgePointPath(projectId, uid) {
  try {
    // 优先从批量路径中获取（利用后端缓存）
    const pathMap = await getKnowledgePointPaths(projectId)
    const path = pathMap[uid]

    if (path !== undefined) {
      console.log('✅ 从批量路径中获取到路径:', uid, '->', path)
      return path
    }

    // 如果批量获取中没有，直接请求单个路径
    console.log('🔍 从后端获取单个知识点路径:', projectId, uid)
    const response = await api.get(`/projects/${projectId}/knowledge-paths/${uid}`)

    if (response.data.success) {
      console.log('✅ 单个路径获取成功:', uid, '->', response.data.data)
      return response.data.data
    } else {
      throw new Error(response.data.message || '获取知识点路径失败')
    }
  } catch (error) {
    console.error('❌ 获取知识点路径失败:', error)
    throw error
  }
}

/**
 * 批量获取多个知识点的路径信息
 * 利用后端缓存，性能优化的批量方法
 * @param {number} projectId - 项目ID
 * @param {Array<string>} uids - 知识点UID数组
 * @returns {Promise<Object>} 路径映射对象 {uid: path}
 */
export async function getBatchKnowledgePointPaths(projectId, uids) {
  if (!uids || uids.length === 0) {
    return {}
  }

  try {
    console.log('🔍 批量获取知识点路径:', projectId, '数量:', uids.length)

    // 获取所有路径（利用后端缓存）
    const allPaths = await getKnowledgePointPaths(projectId)

    // 筛选出需要的路径
    const result = {}
    let foundCount = 0
    uids.forEach(uid => {
      if (allPaths[uid] !== undefined) {
        result[uid] = allPaths[uid]
        foundCount++
      }
    })

    console.log('✅ 批量路径获取完成:', foundCount, '/', uids.length)
    return result
  } catch (error) {
    console.error('❌ 批量获取知识点路径失败:', error)
    throw error
  }
}

/**
 * 通过知识点名称查找对应的UID
 * @param {number} projectId - 项目ID
 * @param {string} knowledgePointName - 知识点名称
 * @returns {Promise<string|null>} 知识点UID，未找到返回null
 */
export async function findUidByKnowledgePointName(projectId, knowledgePointName) {
  try {
    console.log('🔍 查找知识点UID:', projectId, knowledgePointName)

    // 获取带路径的叶子节点列表
    const leafPoints = await getLeafKnowledgePointsWithPaths(projectId)

    // 查找匹配的知识点
    const matchedPoint = leafPoints.find(point => point.name === knowledgePointName)

    if (matchedPoint) {
      console.log('✅ 找到匹配的知识点:', matchedPoint.uid)
      return matchedPoint.uid
    } else {
      console.warn('⚠️ 未找到匹配的知识点:', knowledgePointName)
      return null
    }
  } catch (error) {
    console.error('❌ 查找知识点UID失败:', error)
    throw error
  }
}

/**
 * 清除指定项目的请求缓存
 * 注意：主要缓存在后端，这里只清除前端请求缓存
 * @param {number} projectId - 项目ID
 */
export function clearProjectCache(projectId) {
  const pathCacheKey = `paths_${projectId}`
  const leafPointsCacheKey = `leaf_points_${projectId}`

  requestCache.delete(pathCacheKey)
  requestCache.delete(leafPointsCacheKey)

  console.log('🗑️ 已清除项目前端请求缓存:', projectId)
  console.log('💡 主要缓存在后端，如需清除后端缓存请调用后端API')
}

/**
 * 清除所有前端请求缓存
 */
export function clearAllCache() {
  requestCache.clear()
  console.log('🗑️ 已清除所有前端请求缓存')
  console.log('💡 主要缓存在后端，如需清除后端缓存请调用后端API')
}

/**
 * 获取前端请求缓存状态
 * @returns {Object} 缓存状态信息
 */
export function getCacheStatus() {
  const requestCacheSize = requestCache.size

  return {
    requestCacheSize,
    note: '主要缓存在后端，这里只显示前端请求缓存状态'
  }
}

/**
 * 预加载项目的知识点路径
 * 触发后端缓存预热，提升后续访问性能
 * @param {number} projectId - 项目ID
 */
export async function preloadKnowledgePointPaths(projectId) {
  try {
    console.log('🚀 预加载知识点路径（触发后端缓存）:', projectId)
    await Promise.all([
      getKnowledgePointPaths(projectId),
      getLeafKnowledgePointsWithPaths(projectId)
    ])
    console.log('✅ 知识点路径预加载完成，后端缓存已预热:', projectId)
  } catch (error) {
    console.error('❌ 知识点路径预加载失败:', error)
  }
}

export default {
  getKnowledgePointPaths,
  getKnowledgePointFullPaths,
  getLeafKnowledgePointsWithPaths,
  getKnowledgePointPath,
  getBatchKnowledgePointPaths,
  findUidByKnowledgePointName,
  clearProjectCache,
  clearAllCache,
  getCacheStatus,
  preloadKnowledgePointPaths
}
