// 难度等级常量
export const DIFFICULTY_LEVELS = {
  1: { stars: 1, options: 4, label: '1星（简单）' },
  2: { stars: 2, options: 4, label: '2星（简单）' },
  3: { stars: 3, options: 6, label: '3星（中等）' },
  4: { stars: 4, options: 6, label: '4星（中等）' },
  5: { stars: 5, options: 8, label: '5星（困难）' }
};

// 选项类型枚举
export const OPTION_TYPES = {
  TEXT: 'text',
  IMAGE: 'image'
};

// 题目生成策略类型
export const QUESTION_STRATEGIES = {
  SIMPLE: 'simple',           // 简单随机选择
  CATEGORY_BASED: 'category', // 基于分类选择
  REGION_BASED: 'region',     // 基于地区选择
  CUSTOM: 'custom'            // 自定义逻辑
};

// 考试类型配置，注意要和图片路径一直
export const QUIZ_TYPES = {
  ANIMALS: 'animals',
  CAR_LOGOS: 'car-logos',
  COUNTRY_FLAGS: 'country-flags',
  WORLD_LANDMARKS: 'world-landmarks',
  CHINA_LANDMARKS: 'china-landmarks',
  WORLD_FLAGS_PLUS: 'world-flags-plus',
  ETHNICITIES: 'ethnicities',
  CITY_REGIONS: 'city-regions',
  JAPANESE_ACTRESSES: 'japanese-actresses',
  MILITARY_EQUIPMENT: 'military-equipment',
  VEHICLES: 'vehicles',
  DOG_CAT_BREEDS: 'dog-cat-breeds',
  HOUSEPLANTS: 'houseplants',
  HOME_APPLIANCES: 'home-appliances',
  VEGETABLES: 'vegetables',
  FRUITS: 'fruits',
  DISHES: 'dishes',
  SPORTS: 'sports',
  FISH: 'fish',
  WORLD_FOODS: 'world-foods',
  GLOBAL_ACTRESSES: 'global-actresses',
  GLOBAL_ACTORS: 'global-actors',
  LANDMARK_BUILDINGS: 'landmark_buildings',
  FAIRY_TALES: 'fairy_tales',
  CHINESE_FOODS: 'chinese_foods',
  WEATHERS: 'weathers', // 新增气象识别考试类型
  CELESTIAL_BODIES: 'celestial_bodies',
  CLOTHES: 'clothes',
  TRAFFIC_SIGNS: 'traffic_signs',
  LIVESTOCK: 'livestock',
  BEAUTIFUL_SCENERIES: 'beautiful_sceneries',
  OCCUPATIONS: 'occupations',
  HAIRSTYLES: 'hairstyles',
  TROPICAL_FISH: 'tropical-fish',
  TRADITIONAL_CRAFTS: 'traditional_crafts',
  DANGEROUS_SPORTS: 'dangerous_sports',
  PUBLIC_FACILITIES: 'public-facilities',
  AMUSEMENT_PARKS: 'amusement-parks',
  SPORTS_STARS: 'sports_stars',
  BEAUTIFUL_WOMEN: 'beautiful_women'
};

// 默认配置
export const DEFAULT_CONFIG = {
  maxQuestions: 20,
  shuffleOptions: true,
  shuffleQuestions: true,
  optionType: OPTION_TYPES.TEXT,
  questionStrategy: QUESTION_STRATEGIES.SIMPLE
};
