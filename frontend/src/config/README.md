# 图片配置说明

## 概述

`imageConfig.js` 是统一管理项目中所有图片URL的配置文件。通过这个配置文件，可以轻松切换图片的来源（本地 ↔ OSS）。

## 配置结构

```javascript
const imageConfig = {
  baseUrl: 'https://kpe-system.oss-cn-shanghai.aliyuncs.com',  // 图片基础URL
  paths: {
    animals: '/images/animals',
    carLogos: '/images/car-logos',
    // ... 其他分类
  },
  getImageUrl: (category, fileName) => { /* 生成完整URL */ }
};
```

## 使用方法

在数据文件中导入并使用：

```javascript
import imageConfig from '../config/imageConfig.js';

export const animals = animalFiles.map(animal => ({
  ...animal,
  imageUrl: imageConfig.getImageUrl('animals', animal.fileName)
}));
```

## 切换图片来源

### 使用OSS（当前配置）
```javascript
imageConfig.baseUrl = 'https://kpe-system.oss-cn-shanghai.aliyuncs.com';
```

### 切换到本地图片
```javascript
imageConfig.baseUrl = '';  // 空字符串表示使用本地路径
```

### 切换到其他CDN
```javascript
imageConfig.baseUrl = 'https://your-cdn-domain.com';
```

## 优势

1. **统一管理**: 所有图片URL配置集中在一个文件中
2. **快速切换**: 修改一个配置即可切换所有图片来源
3. **易于维护**: 新增图片分类或修改路径结构都很简单
4. **环境适配**: 可以根据不同环境使用不同的图片源

## 注意事项

- 修改配置后需要重启开发服务器
- 确保OSS或CDN的跨域配置正确
- 新增图片分类时记得在 `paths` 中添加对应配置 