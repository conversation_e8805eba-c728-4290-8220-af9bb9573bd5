// 图片配置文件
const imageConfig = {
  // 图片基础URL配置
  baseUrl: 'https://kpe-system.oss-cn-shanghai.aliyuncs.com',
  
  // 各分类的图片路径
  paths: {
    animals: '/images/animals',
    carLogos: '/images/car-logos',
    chinaLandmarks: '/images/china-landmarks',
    countryFlags: '/images/country-flags',
    'world-flags-plus': '/images/world-flags-plus',

    worldLandmarks: '/images/world-landmarks',
    ethnicities: '/images/ethnicities',
    'military-equipment': '/images/military-equipment',
    vehicles: '/images/vehicles',

    'dog-cat-breeds': '/images/dog-cat-breeds',
    vegetables: '/images/vegetables',
    fruits: '/images/fruits',
    dishes: '/images/dishes',
    'global-actresses': '/images/global-actresses',
    'global-actors': '/images/global-actors',
    landmark_buildings: '/images/landmark_buildings',
    world_foods: '/images/world_foods',
    fish: '/images/fish',
    celestial_bodies: '/images/celestial_bodies',
    beautiful_sceneries: '/images/beautiful_sceneries',
    'public-facilities': '/images/public-facilities',
    'tropical-fish': '/images/tropical-fish',
    'dangerous_sports': '/images/dangerous_sports',
    'amusement_parks': '/images/amusement_parks'
  },
  
  // 生成完整图片URL的函数
  getImageUrl: (category, fileName) => {
    return `${imageConfig.baseUrl}${imageConfig.paths[category]}/${fileName}`;
  }
};

// 如果需要切换回本地图片，只需要修改baseUrl即可
// imageConfig.baseUrl = '';  // 本地图片

export default imageConfig;
