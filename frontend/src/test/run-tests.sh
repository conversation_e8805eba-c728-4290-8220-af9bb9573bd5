#!/bin/bash

# 前端单元测试运行脚本
# 使用方法: ./run-tests.sh [选项]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}KPE Frontend Test Runner${NC}"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -a, --all               运行所有测试"
    echo "  -w, --watch             监听模式运行测试"
    echo "  -c, --coverage          生成覆盖率报告"
    echo "  -u, --ui                启动测试UI界面"
    echo "  -f, --file <file>       运行指定测试文件"
    echo "  -t, --type <type>       运行特定类型测试 (utils/composables/services/components)"
    echo "  --unit                  只运行单元测试"
    echo "  --integration           只运行集成测试"
    echo "  --verbose               详细输出"
    echo "  --silent                静默模式"
    echo ""
    echo "示例:"
    echo "  $0 -a                   # 运行所有测试"
    echo "  $0 -c                   # 运行测试并生成覆盖率"
    echo "  $0 -f dataUtils.test.js # 运行特定测试文件"
    echo "  $0 -t utils             # 运行所有工具函数测试"
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查依赖...${NC}"
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: npm 未安装${NC}"
        exit 1
    fi
    
    if [ ! -f "package.json" ]; then
        echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查通过${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}安装测试依赖...${NC}"
    npm install
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 依赖安装完成${NC}"
    else
        echo -e "${RED}✗ 依赖安装失败${NC}"
        exit 1
    fi
}

# 运行所有测试
run_all_tests() {
    echo -e "${BLUE}运行所有测试...${NC}"
    npm run test:run
}

# 监听模式
run_watch_mode() {
    echo -e "${BLUE}启动监听模式...${NC}"
    npm run test
}

# 生成覆盖率报告
run_coverage() {
    echo -e "${BLUE}生成覆盖率报告...${NC}"
    npm run test:coverage
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 覆盖率报告已生成${NC}"
        echo -e "${YELLOW}查看报告: coverage/index.html${NC}"
    fi
}

# 启动UI界面
run_ui() {
    echo -e "${BLUE}启动测试UI界面...${NC}"
    npx vitest --ui
}

# 运行指定文件
run_specific_file() {
    local file=$1
    echo -e "${BLUE}运行测试文件: $file${NC}"
    
    if [ ! -f "src/test/**/$file" ] && [ ! -f "src/test/$file" ]; then
        echo -e "${YELLOW}警告: 文件 $file 不存在，尝试模糊匹配...${NC}"
    fi
    
    npx vitest run --reporter=verbose "$file"
}

# 运行特定类型测试
run_by_type() {
    local type=$1
    echo -e "${BLUE}运行 $type 类型测试...${NC}"
    
    case $type in
        utils)
            npx vitest run "src/test/utils/"
            ;;
        composables)
            npx vitest run "src/test/composables/"
            ;;
        services)
            npx vitest run "src/test/services/"
            ;;
        components)
            npx vitest run "src/test/components/"
            ;;
        unit)
            npx vitest run "src/test/unit/"
            ;;
        integration)
            npx vitest run "src/test/integration/"
            ;;
        *)
            echo -e "${RED}错误: 未知的测试类型 '$type'${NC}"
            echo -e "${YELLOW}支持的类型: utils, composables, services, components, unit, integration${NC}"
            exit 1
            ;;
    esac
}

# 测试结果统计
show_test_summary() {
    echo -e "\n${BLUE}=== 测试统计 ===${NC}"
    
    # 获取测试文件数量
    local utils_count=$(find src/test/utils -name "*.test.js" 2>/dev/null | wc -l)
    local composables_count=$(find src/test/composables -name "*.test.js" 2>/dev/null | wc -l)
    local services_count=$(find src/test/services -name "*.test.js" 2>/dev/null | wc -l)
    local components_count=$(find src/test/components -name "*.test.js" 2>/dev/null | wc -l)
    local unit_count=$(find src/test/unit -name "*.test.js" 2>/dev/null | wc -l)
    
    echo -e "${GREEN}工具函数测试: $utils_count 个${NC}"
    echo -e "${GREEN}组合函数测试: $composables_count 个${NC}"
    echo -e "${GREEN}服务测试: $services_count 个${NC}"
    echo -e "${GREEN}组件测试: $components_count 个${NC}"
    echo -e "${GREEN}单元测试: $unit_count 个${NC}"
    
    local total=$((utils_count + composables_count + services_count + components_count + unit_count))
    echo -e "${BLUE}总计: $total 个测试文件${NC}"
}

# 清理测试环境
cleanup() {
    echo -e "${BLUE}清理测试环境...${NC}"
    
    # 删除临时文件
    rm -rf coverage/ 2>/dev/null
    rm -rf .vitest/ 2>/dev/null
    
    echo -e "${GREEN}✓ 清理完成${NC}"
}

# 主函数
main() {
    local ARGS=()
    local VERBOSE=false
    local SILENT=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                check_dependencies
                run_all_tests
                show_test_summary
                exit 0
                ;;
            -w|--watch)
                check_dependencies
                run_watch_mode
                exit 0
                ;;
            -c|--coverage)
                check_dependencies
                run_coverage
                exit 0
                ;;
            -u|--ui)
                check_dependencies
                run_ui
                exit 0
                ;;
            -f|--file)
                if [ -z "$2" ]; then
                    echo -e "${RED}错误: 需要指定文件名${NC}"
                    exit 1
                fi
                check_dependencies
                run_specific_file "$2"
                shift
                shift
                exit 0
                ;;
            -t|--type)
                if [ -z "$2" ]; then
                    echo -e "${RED}错误: 需要指定测试类型${NC}"
                    exit 1
                fi
                check_dependencies
                run_by_type "$2"
                shift
                shift
                exit 0
                ;;
            --unit)
                check_dependencies
                run_by_type "unit"
                exit 0
                ;;
            --integration)
                check_dependencies
                run_by_type "integration"
                exit 0
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --silent)
                SILENT=true
                shift
                ;;
            --cleanup)
                cleanup
                exit 0
                ;;
            --install)
                install_dependencies
                exit 0
                ;;
            *)
                echo -e "${RED}错误: 未知选项 '$1'${NC}"
                echo -e "${YELLOW}使用 '$0 --help' 查看帮助${NC}"
                exit 1
                ;;
        esac
    done
    
    # 如果没有参数，显示帮助
    if [ ${#ARGS[@]} -eq 0 ]; then
        show_help
        echo ""
        show_test_summary
    fi
}

# 捕获中断信号
trap 'echo -e "\n${YELLOW}测试已中断${NC}"; exit 1' INT

# 执行主函数
main "$@" 