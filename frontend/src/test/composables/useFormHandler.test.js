/**
 * useForm<PERSON>andler composable 测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useFormHandler } from '@/composables/useFormHandler'

// Mock dependencies
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn()
  })
}))

vi.mock('@/composables/useLoading', () => ({
  useLoading: () => ({
    loading: { value: false },
    execute: vi.fn((fn) => fn())
  })
}))

vi.mock('@/composables/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleApiError: vi.fn(),
    handleAsyncError: vi.fn()
  })
}))

vi.mock('@/composables/useFormValidation', () => ({
  useFormValidation: () => ({
    validateFormAsync: vi.fn().mockResolvedValue(true),
    getValidationRules: vi.fn().mockReturnValue([])
  })
}))

describe('useFormHandler', () => {
  let mockSubmitHandler
  let mockLoadHandler

  beforeEach(() => {
    mockSubmitHandler = vi.fn().mockResolvedValue({ success: true })
    mockLoadHandler = vi.fn().mockResolvedValue({ name: 'Test', description: 'Test desc' })
  })

  it('应该初始化表单数据', () => {
    const initialData = { name: '', description: '' }
    const { formData, isEdit, isDirty } = useFormHandler({
      initialData
    })

    expect(formData.name).toBe('')
    expect(formData.description).toBe('')
    expect(isEdit.value).toBe(false)
    expect(isDirty.value).toBe(false)
  })

  it('应该正确设置表单数据', () => {
    const { formData, setFormData, isEdit } = useFormHandler({
      initialData: { name: '', description: '' }
    })

    const testData = { name: 'Test Project', description: 'Test Description' }
    setFormData(testData)

    expect(formData.name).toBe('Test Project')
    expect(formData.description).toBe('Test Description')
    expect(isEdit.value).toBe(true)
  })

  it('应该检测表单变化', () => {
    const { formData, hasChanges, updateField } = useFormHandler({
      initialData: { name: '', description: '' }
    })

    expect(hasChanges.value).toBe(false)

    updateField('name', 'New Name')
    expect(hasChanges.value).toBe(true)
  })

  it('应该重置表单', () => {
    const initialData = { name: '', description: '' }
    const { formData, resetForm, updateField, hasChanges, isDirty } = useFormHandler({
      initialData
    })

    updateField('name', 'Test')
    expect(hasChanges.value).toBe(true)
    expect(isDirty.value).toBe(true)

    resetForm()
    expect(formData.name).toBe('')
    expect(hasChanges.value).toBe(false)
    expect(isDirty.value).toBe(false)
  })

  it('应该批量更新字段', () => {
    const { formData, updateFields, isDirty } = useFormHandler({
      initialData: { name: '', description: '' }
    })

    updateFields({ name: 'Test', description: 'Test desc' })
    
    expect(formData.name).toBe('Test')
    expect(formData.description).toBe('Test desc')
    expect(isDirty.value).toBe(true)
  })

  it('应该处理表单提交', async () => {
    const { submitForm, formData, updateField, formRef } = useFormHandler({
      initialData: { name: '' },
      submitHandler: mockSubmitHandler
    })

    // 模拟formRef
    formRef.value = {
      validate: vi.fn().mockResolvedValue(true)
    }

    // 先更新数据以产生变化
    updateField('name', 'Test')

    const result = await submitForm()
    expect(mockSubmitHandler).toHaveBeenCalledWith({ name: 'Test' }, false)
    expect(result).toEqual({ success: true })
  })

  it('应该加载数据', async () => {
    const { loadData, formData, isEdit } = useFormHandler({
      initialData: { name: '', description: '' },
      loadHandler: mockLoadHandler
    })

    await loadData('123')
    
    expect(mockLoadHandler).toHaveBeenCalledWith('123')
    expect(formData.name).toBe('Test')
    expect(formData.description).toBe('Test desc')
    expect(isEdit.value).toBe(true)
  })

  it('应该计算是否可以提交', () => {
    const { canSubmit, updateField } = useFormHandler({
      initialData: { name: '' }
    })

    expect(canSubmit.value).toBe(false) // 没有变化

    updateField('name', 'Test')
    expect(canSubmit.value).toBe(true) // 有变化且不在提交中
  })
})
