/**
 * useProject composable 测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useProject } from '@/composables/useProject'
import { ElMessage } from 'element-plus'
import { projectAPI } from '@/api'

// Mock dependencies
vi.mock('vue-router', () => ({
  useRoute: () => ({
    params: { id: '1', projectId: '2' }
  })
}))

vi.mock('@/api', () => ({
  projectAPI: {
    getProject: vi.fn().mockResolvedValue({
      data: {
        data: { id: 1, name: 'Test Project', description: 'Test Description' }
      }
    }),
    createProject: vi.fn(),
    updateProject: vi.fn(),
    deleteProject: vi.fn()
  }
}))

vi.mock('@/utils/validation', () => ({
  validateAndConvertId: vi.fn((id) => {
    if (id === '1' || id === '2' || id === 1 || id === 2) return parseInt(id)
    return null
  }),
  isValidId: vi.fn((id) => {
    return id === '1' || id === '2' || id === 1 || id === 2
  })
}))

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn()
  }
}))

describe('useProject', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确获取项目ID', () => {
    const { projectId } = useProject()
    
    // 应该优先使用 route.params.id
    expect(projectId.value).toBe(1)
  })

  it('应该成功获取项目信息', async () => {
    const mockProject = {
      id: 1,
      name: 'Test Project',
      description: 'Test Description'
    }

    vi.mocked(projectAPI.getProject).mockResolvedValue({
      data: { data: mockProject }
    })

    const { fetchProject, project, loading, error } = useProject()

    const result = await fetchProject(1)

    expect(projectAPI.getProject).toHaveBeenCalledWith(1)
    expect(project.value).toEqual(mockProject)
    expect(loading.value).toBe(false)
    expect(error.value).toBe(null)
    expect(result).toEqual(mockProject)
  })

  it('应该处理获取项目信息失败', async () => {
    const mockError = new Error('API Error')
    mockError.response = {
      data: { message: 'Project not found' }
    }

    vi.mocked(projectAPI.getProject).mockRejectedValue(mockError)

    const { fetchProject, project, loading, error } = useProject()

    await expect(fetchProject(1)).rejects.toThrow('API Error')

    expect(project.value).toBe(null)
    expect(loading.value).toBe(false)
    expect(error.value).toBe('Project not found')
    expect(ElMessage.error).toHaveBeenCalledWith('Project not found')
  })

  it('应该处理无效的项目ID', async () => {
    const { fetchProject, error } = useProject()

    const result = await fetchProject('invalid')

    expect(result).toBe(null)
    expect(error.value).toBe('项目ID无效: invalid')
    expect(ElMessage.error).toHaveBeenCalledWith('项目ID无效: invalid')
  })

  it('应该计算项目名称', () => {
    const { project, projectName } = useProject()

    // 初始状态
    expect(projectName.value).toBe('')

    // 设置项目后
    project.value = { name: 'Test Project' }
    expect(projectName.value).toBe('Test Project')
  })

  it('应该计算项目描述', () => {
    const { project, projectDescription } = useProject()

    // 初始状态
    expect(projectDescription.value).toBe('')

    // 设置项目后
    project.value = { description: 'Test Description' }
    expect(projectDescription.value).toBe('Test Description')
  })

  it('应该在加载过程中设置loading状态', async () => {
    let resolvePromise
    const pendingPromise = new Promise(resolve => {
      resolvePromise = resolve
    })

    vi.mocked(projectAPI.getProject).mockReturnValue(pendingPromise)

    const { fetchProject, loading } = useProject()

    const fetchPromise = fetchProject(1)
    
    // 加载中
    expect(loading.value).toBe(true)

    // 完成加载
    resolvePromise({ data: { data: { id: 1, name: 'Test' } } })
    await fetchPromise

    expect(loading.value).toBe(false)
  })

  it('应该重置项目信息', () => {
    const { project, error, reset } = useProject()

    // 设置一些数据
    project.value = { id: 1, name: 'Test' }
    error.value = 'Some error'

    reset()

    expect(project.value).toBe(null)
    expect(error.value).toBe(null)
  })

  it('应该刷新项目信息', async () => {
    const mockProject = { id: 1, name: 'Updated Project' }
    vi.mocked(projectAPI.getProject).mockResolvedValue({
      data: { data: mockProject }
    })

    const { fetchProject, project, projectId } = useProject()

    await fetchProject(projectId.value)

    expect(projectAPI.getProject).toHaveBeenCalled()
    expect(project.value).toEqual(mockProject)
  })
}) 