/**
 * useLoading composable 测试
 */

import { describe, it, expect, vi } from 'vitest'
import { useLoading } from '@/composables/useLoading'

describe('useLoading', () => {
  it('应该初始化为非加载状态', () => {
    const { loading, error, data } = useLoading()
    
    expect(loading.value).toBe(false)
    expect(error.value).toBe(null)
    expect(data.value).toBe(null)
  })

  it('应该在执行异步操作时设置加载状态', async () => {
    const { loading, execute } = useLoading()
    
    const asyncFn = vi.fn().mockResolvedValue('test result')
    
    const promise = execute(asyncFn)
    expect(loading.value).toBe(true)
    
    const result = await promise
    expect(loading.value).toBe(false)
    expect(result).toBe('test result')
  })

  it('应该处理异步操作错误', async () => {
    const { loading, error, execute } = useLoading()
    
    const testError = new Error('Test error')
    const asyncFn = vi.fn().mockRejectedValue(testError)
    
    try {
      await execute(asyncFn)
    } catch (err) {
      expect(err).toBe(testError)
    }
    
    expect(loading.value).toBe(false)
    expect(error.value).toBe(testError)
  })

  it('应该支持不显示加载状态的选项', async () => {
    const { loading, execute } = useLoading()
    
    const asyncFn = vi.fn().mockResolvedValue('test')
    
    await execute(asyncFn, { showLoading: false })
    
    expect(loading.value).toBe(false)
  })

  it('应该正确重置状态', () => {
    const { loading, error, data, reset } = useLoading()
    
    // 设置一些状态
    loading.value = true
    error.value = new Error('test')
    data.value = 'test data'
    
    reset()
    
    expect(loading.value).toBe(false)
    expect(error.value).toBe(null)
    expect(data.value).toBe(null)
  })

  it('应该支持手动设置状态', () => {
    const { loading, error, data, setLoading, setError, setData } = useLoading()
    
    setLoading(true)
    expect(loading.value).toBe(true)
    
    const testError = new Error('test')
    setError(testError)
    expect(error.value).toBe(testError)
    expect(loading.value).toBe(false) // setError 应该清除 loading
    
    setData('test data')
    expect(data.value).toBe('test data')
    expect(error.value).toBe(null) // setData 应该清除 error
  })
})
