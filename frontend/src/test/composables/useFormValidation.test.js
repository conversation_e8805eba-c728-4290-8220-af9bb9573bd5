/**
 * useFormValidation composable 测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useFormValidation } from '@/composables/useFormValidation'
import { ElMessage } from 'element-plus'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn()
  }
}))

// Mock constants
vi.mock('@/utils/constants', () => ({
  VALIDATION_RULES: {
    REQUIRED: 'required',
    EMAIL: 'email',
    MIN_LENGTH: 'minLength',
    MAX_LENGTH: 'maxLength',
    PHONE: 'phone',
    URL: 'url',
    NUMBER: 'number',
    POSITIVE_NUMBER: 'positiveNumber'
  }
}))

describe('useFormValidation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('validateField', () => {
    it('应该验证必填字段', () => {
      const { validateField } = useFormValidation()

      const rules = [{ required: true, message: '字段是必填的' }]

      expect(validateField('', rules)).toEqual({ valid: false, message: '字段是必填的' })
      expect(validateField(null, rules)).toEqual({ valid: false, message: '字段是必填的' })
      expect(validateField(undefined, rules)).toEqual({ valid: false, message: '字段是必填的' })
      expect(validateField('value', rules)).toEqual({ valid: true })
    })

    it('应该验证最小长度', () => {
      const { validateField } = useFormValidation()

      const rules = [{ min: 3, message: '最少3个字符' }]

      expect(validateField('ab', rules)).toEqual({ valid: false, message: '最少3个字符' })
      expect(validateField('abc', rules)).toEqual({ valid: true })
      expect(validateField('abcd', rules)).toEqual({ valid: true })
    })

    it('应该验证最大长度', () => {
      const { validateField } = useFormValidation()

      const rules = [{ max: 5, message: '最多5个字符' }]

      expect(validateField('abcdef', rules)).toEqual({ valid: false, message: '最多5个字符' })
      expect(validateField('abcde', rules)).toEqual({ valid: true })
      expect(validateField('abcd', rules)).toEqual({ valid: true })
    })

    it('应该验证邮箱格式', () => {
      const { validateField } = useFormValidation()

      const rules = [{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }]

      expect(validateField('invalid-email', rules)).toEqual({ valid: false, message: '邮箱格式不正确' })
      expect(validateField('<EMAIL>', rules)).toEqual({ valid: true })
      expect(validateField('<EMAIL>', rules)).toEqual({ valid: true })
    })

    it('应该验证URL格式', () => {
      const { validateField } = useFormValidation()

      const rules = [{ pattern: /^https?:\/\/.+/, message: 'URL格式不正确' }]

      expect(validateField('invalid-url', rules)).toEqual({ valid: false, message: 'URL格式不正确' })
      expect(validateField('http://example.com', rules)).toEqual({ valid: true })
      expect(validateField('https://www.example.com', rules)).toEqual({ valid: true })
    })

    it('应该验证数字', () => {
      const { validateField } = useFormValidation()

      const rules = [{ pattern: /^-?\d+(\.\d+)?$/, message: '必须是数字' }]

      expect(validateField('abc', rules)).toEqual({ valid: false, message: '必须是数字' })
      expect(validateField('123', rules)).toEqual({ valid: true })
      expect(validateField('123.45', rules)).toEqual({ valid: true })
      expect(validateField('-123', rules)).toEqual({ valid: true })
    })

    it('应该验证自定义验证器', () => {
      const { validateField } = useFormValidation()

      const customValidator = (value) => {
        if (value !== 'valid') {
          return '值必须是 "valid"'
        }
        return true
      }

      const rules = [{ validator: customValidator }]

      expect(validateField('invalid', rules)).toEqual({ valid: false, message: '值必须是 "valid"' })
      expect(validateField('valid', rules)).toEqual({ valid: true })
    })

    it('应该处理多个验证规则', () => {
      const { validateField } = useFormValidation()

      const rules = [
        { required: true, message: '字段是必填的' },
        { min: 3, message: '最少3个字符' },
        { max: 10, message: '最多10个字符' }
      ]

      expect(validateField('', rules)).toEqual({ valid: false, message: '字段是必填的' })
      expect(validateField('ab', rules)).toEqual({ valid: false, message: '最少3个字符' })
      expect(validateField('abcdefghijk', rules)).toEqual({ valid: false, message: '最多10个字符' })
      expect(validateField('abcde', rules)).toEqual({ valid: true })
    })

    it('应该跳过非必填的空值', () => {
      const { validateField } = useFormValidation()

      const rules = [
        { min: 3, message: '最少3个字符' },
        { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
      ]

      expect(validateField('', rules)).toEqual({ valid: true })
      expect(validateField(null, rules)).toEqual({ valid: true })
      expect(validateField(undefined, rules)).toEqual({ valid: true })
    })
  })

  describe('validateForm', () => {
    it('应该验证整个表单', () => {
      const { validateForm } = useFormValidation()

      const formData = {
        name: '',
        email: 'invalid-email',
        age: 'abc'
      }

      const rules = {
        name: [{ required: true, message: '姓名是必填的' }],
        email: [{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }],
        age: [{ pattern: /^-?\d+(\.\d+)?$/, message: '年龄必须是数字' }]
      }

      const result = validateForm(formData, rules)

      expect(result.valid).toBe(false)
      expect(result.errors.name).toBe('姓名是必填的')
      expect(result.errors.email).toBe('邮箱格式不正确')
      expect(result.errors.age).toBe('年龄必须是数字')
    })

    it('应该处理有效的表单数据', () => {
      const { validateForm } = useFormValidation()

      const formData = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: '25'
      }

      const rules = {
        name: [{ required: true, message: '姓名是必填的' }],
        email: [{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }],
        age: [{ pattern: /^-?\d+(\.\d+)?$/, message: '年龄必须是数字' }]
      }

      const result = validateForm(formData, rules)

      expect(result.valid).toBe(true)
      expect(result.errors).toEqual({})
    })
  })

  describe('validateFormAsync', () => {
    it('应该异步验证表单', async () => {
      const { validateFormAsync } = useFormValidation()
      
      const mockFormRef = {
        validate: vi.fn().mockResolvedValue(true)
      }

      const result = await validateFormAsync(mockFormRef)

      expect(result).toBe(true)
      expect(mockFormRef.validate).toHaveBeenCalled()
    })

    it('应该处理异步验证错误', async () => {
      const { validateFormAsync } = useFormValidation()
      
      const mockFormRef = {
        validate: vi.fn().mockRejectedValue(new Error('Validation failed'))
      }

      const result = await validateFormAsync(mockFormRef)

      expect(result).toBe(false)
      expect(mockFormRef.validate).toHaveBeenCalled()
    })
  })

  describe('getValidationRules', () => {
    it('应该获取验证规则', () => {
      const { getValidationRules } = useFormValidation()

      const rules = getValidationRules('required')

      expect(Array.isArray(rules)).toBe(true)
    })

    it('应该处理未知规则类型', () => {
      const { getValidationRules } = useFormValidation()

      const rules = getValidationRules('unknownRule')

      expect(Array.isArray(rules)).toBe(true)
      expect(rules).toEqual([])
    })
  })

  describe('createValidationRule', () => {
    it('应该创建验证规则', () => {
      const { createValidationRule } = useFormValidation()

      const rule = createValidationRule({ 
        required: true, 
        message: '字段是必填的' 
      })

      expect(rule.trigger).toBe('blur')
      expect(rule.required).toBe(true)
      expect(rule.message).toBe('字段是必填的')
    })

    it('应该创建带参数的验证规则', () => {
      const { createValidationRule } = useFormValidation()

      const rule = createValidationRule({ 
        min: 3, 
        message: '最少3个字符' 
      })

      expect(rule.trigger).toBe('blur')
      expect(rule.min).toBe(3)
      expect(rule.message).toBe('最少3个字符')
    })
  })

  describe('clearValidationErrors', () => {
    it('应该清除所有验证错误', () => {
      const { validationErrors, setValidationError, clearValidationErrors } = useFormValidation()

      setValidationError('name', '姓名错误')
      setValidationError('email', '邮箱错误')

      expect(Object.keys(validationErrors.value)).toHaveLength(2)

      clearValidationErrors()

      expect(validationErrors.value).toEqual({})
    })

    it('应该清除特定字段的验证错误', () => {
      const { validationErrors, setValidationError, clearValidationErrors } = useFormValidation()

      setValidationError('name', '姓名错误')
      setValidationError('email', '邮箱错误')

      clearValidationErrors('name')

      expect(validationErrors.value.name).toBeUndefined()
      expect(validationErrors.value.email).toBe('邮箱错误')
    })
  })

  describe('resetValidation', () => {
    it('应该重置验证状态', () => {
      const { validationErrors, isValidating, setValidationError, resetValidation } = useFormValidation()

      setValidationError('name', '姓名错误')
      isValidating.value = true

      resetValidation()

      expect(validationErrors.value).toEqual({})
      expect(isValidating.value).toBe(false)
    })
  })

  describe('validators', () => {
    it('应该提供内置验证器', () => {
      const { validators } = useFormValidation()

      expect(typeof validators.email).toBe('function')
      expect(typeof validators.url).toBe('function')
      expect(typeof validators.number).toBe('function')
      expect(typeof validators.phone).toBe('function')
      expect(typeof validators.positiveInteger).toBe('function')
    })

    it('phone 验证器应该正确工作', () => {
      const { validators } = useFormValidation()

      expect(validators.phone('12345678901')).toBe('请输入有效的手机号码')
      expect(validators.phone('13812345678')).toBe(true)
      expect(validators.phone('15987654321')).toBe(true)
    })

    it('email 验证器应该正确工作', () => {
      const { validators } = useFormValidation()

      expect(validators.email('invalid-email')).toBe('请输入有效的邮箱地址')
      expect(validators.email('<EMAIL>')).toBe(true)
      expect(validators.email('<EMAIL>')).toBe(true)
    })

    it('number 验证器应该正确工作', () => {
      const { validators } = useFormValidation()

      expect(validators.number('abc')).toBe('请输入有效的数字')
      expect(validators.number('123')).toBe(true)
      expect(validators.number('123.45')).toBe(true)
      expect(validators.number('-123')).toBe(true)
    })
  })
}) 