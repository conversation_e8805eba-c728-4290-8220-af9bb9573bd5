/**
 * useErrorHandler composable 测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useErrorHandler } from '@/composables/useErrorHandler'
import { ElMessage, ElMessageBox } from 'element-plus'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    warning: vi.fn(),
    success: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn().mockResolvedValue('confirm'),
    alert: vi.fn().mockResolvedValue('confirm'),
    prompt: vi.fn().mockResolvedValue({ value: 'test' })
  }
}))

// Mock console
const mockConsole = {
  error: vi.fn(),
  warn: vi.fn(),
  log: vi.fn()
}
global.console = { ...console, ...mockConsole }

describe('useErrorHandler', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('handleApiError', () => {
    it('应该处理API响应错误', () => {
      const { handleApiError } = useErrorHandler()
      
      const error = {
        response: {
          data: { message: 'API错误信息' },
          status: 400
        }
      }

      const result = handleApiError(error)

      expect(ElMessage.error).toHaveBeenCalledWith('API错误信息')
      expect(mockConsole.error).toHaveBeenCalled()
      expect(result.message).toBe('API错误信息')
      expect(result.status).toBe(400)
    })

    it('应该处理网络错误', () => {
      const { handleApiError } = useErrorHandler()
      
      const error = {
        code: 'NETWORK_ERROR',
        message: 'Network Error'
      }

      const result = handleApiError(error)

      expect(ElMessage.error).toHaveBeenCalledWith('Network Error')
      expect(mockConsole.error).toHaveBeenCalled()
      expect(result.message).toBe('Network Error')
    })

    it('应该处理404不存在错误', () => {
      const { handleApiError } = useErrorHandler()
      
      const error = {
        response: {
          status: 404,
          data: { message: 'Not Found' }
        }
      }

      const result = handleApiError(error)

      expect(ElMessage.error).toHaveBeenCalledWith('Not Found')
      expect(mockConsole.error).toHaveBeenCalled()
      expect(result.message).toBe('Not Found')
    })

    it('应该使用自定义错误消息', () => {
      const { handleApiError } = useErrorHandler()
      
      const error = new Error('Some error')

      const result = handleApiError(error, { defaultMessage: '自定义操作失败' })

      expect(ElMessage.error).toHaveBeenCalledWith('Some error')
      expect(mockConsole.error).toHaveBeenCalled()
    })

    it('应该支持静默模式', () => {
      const { handleApiError } = useErrorHandler()
      
      const error = new Error('Some error')

      const result = handleApiError(error, { showMessage: false })

      expect(ElMessage.error).not.toHaveBeenCalled()
      expect(mockConsole.error).toHaveBeenCalled() // 仍然记录到控制台
    })
  })

  describe('handleAsyncError', () => {
    it('应该处理异步操作成功', async () => {
      const { handleAsyncError } = useErrorHandler()
      
      const successFn = vi.fn().mockResolvedValue('success result')

      const result = await handleAsyncError(successFn)

      expect(result).toBe('success result')
      expect(successFn).toHaveBeenCalled()
    })

    it('应该处理异步操作失败', async () => {
      const { handleAsyncError } = useErrorHandler()
      
      const failFn = vi.fn().mockRejectedValue(new Error('Async error'))

      const result = await handleAsyncError(failFn, { errorMessage: '异步操作失败' })

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(ElMessage.error).toHaveBeenCalled()
      expect(mockConsole.error).toHaveBeenCalled()
    })

    it('应该支持抛出错误选项', async () => {
      const { handleAsyncError } = useErrorHandler()
      
      const failFn = vi.fn().mockRejectedValue(new Error('Async error'))

      await expect(
        handleAsyncError(failFn, { rethrow: true })
      ).rejects.toThrow('Async error')

      expect(ElMessage.error).toHaveBeenCalled()
      expect(mockConsole.error).toHaveBeenCalled()
    })

    it('应该支持静默模式', async () => {
      const { handleAsyncError } = useErrorHandler()
      
      const failFn = vi.fn().mockRejectedValue(new Error('Async error'))

      const result = await handleAsyncError(failFn, { showError: false })

      expect(result.success).toBe(false)
      expect(ElMessage.error).not.toHaveBeenCalled()
      expect(mockConsole.error).toHaveBeenCalled()
    })
  })

  describe('confirmAction', () => {
    it('应该处理确认对话框', async () => {
      const { confirmAction } = useErrorHandler()
      
      vi.mocked(ElMessageBox.confirm).mockResolvedValue('confirm')

      const result = await confirmAction('确定要执行此操作吗？')

      expect(result).toBe(true)
      expect(ElMessageBox.confirm).toHaveBeenCalled()
    })

    it('应该处理取消操作', async () => {
      const { confirmAction } = useErrorHandler()
      
      vi.mocked(ElMessageBox.confirm).mockRejectedValue('cancel')

      const result = await confirmAction('确定要执行此操作吗？')

      expect(result).toBe(false)
    })
  })

  describe('confirmDelete', () => {
    it('应该处理删除确认', async () => {
      const { confirmDelete } = useErrorHandler()
      
      vi.mocked(ElMessageBox.confirm).mockResolvedValue('confirm')

      const result = await confirmDelete('测试项目')

      expect(result).toBe(true)
      expect(ElMessageBox.confirm).toHaveBeenCalled()
    })
  })
}) 