/**
 * useStatusMapping composable 测试
 */

import { describe, it, expect, vi } from 'vitest'
import { useStatusMapping } from '@/composables/useStatusMapping'

// Mock utils/constants
vi.mock('@/utils/constants', () => ({
  STATUS_MAPPINGS: {
    EXAM_STATUS: {
      DRAFT: 'draft',
      PUBLISHED: 'published',
      ARCHIVED: 'archived'
    },
    PROJECT_STATUS: {
      ACTIVE: 'active',
      INACTIVE: 'inactive',
      COMPLETED: 'completed'
    },
    DIFFICULTY: {
      EASY: 'easy',
      MEDIUM: 'medium',
      HARD: 'hard'
    },
    SCORE_LEVELS: {
      EXCELLENT: 'excellent',
      GOOD: 'good',
      AVERAGE: 'average',
      POOR: 'poor'
    }
  },
  STATUS_TEXT: {
    draft: '草稿',
    published: '已发布',
    archived: '已归档',
    active: '活跃',
    inactive: '非活跃',
    completed: '已完成',
    easy: '简单',
    medium: '中等',
    hard: '困难',
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  },
  TAG_TYPES: {
    draft: '',
    published: 'success',
    archived: 'info',
    active: 'success',
    inactive: 'warning',
    completed: 'success',
    easy: 'success',
    medium: 'warning',
    hard: 'danger',
    excellent: 'success',
    good: 'primary',
    average: 'warning',
    poor: 'danger'
  }
}))

describe('useStatusMapping', () => {
  describe('getStatusTagType', () => {
    it('应该返回正确的状态标签类型', () => {
      const { getStatusTagType } = useStatusMapping()

      expect(getStatusTagType('published')).toBe('success')
      expect(getStatusTagType('draft')).toBe('')
      expect(getStatusTagType('archived')).toBe('info')
      expect(getStatusTagType('active')).toBe('success')
      expect(getStatusTagType('inactive')).toBe('warning')
    })

    it('应该处理未知状态', () => {
      const { getStatusTagType } = useStatusMapping()

      expect(getStatusTagType('unknown')).toBe('')
      expect(getStatusTagType(null)).toBe('')
      expect(getStatusTagType(undefined)).toBe('')
    })
  })

  describe('getStatusText', () => {
    it('应该返回正确的状态文本', () => {
      const { getStatusText } = useStatusMapping()

      expect(getStatusText('published')).toBe('已发布')
      expect(getStatusText('draft')).toBe('草稿')
      expect(getStatusText('archived')).toBe('已归档')
      expect(getStatusText('active')).toBe('活跃')
      expect(getStatusText('completed')).toBe('已完成')
    })

    it('应该返回原始状态当找不到映射时', () => {
      const { getStatusText } = useStatusMapping()

      expect(getStatusText('unknown')).toBe('unknown')
      expect(getStatusText('custom_status')).toBe('custom_status')
    })
  })

  describe('getDifficultyTagType', () => {
    it('应该返回正确的难度标签类型', () => {
      const { getDifficultyTagType } = useStatusMapping()

      expect(getDifficultyTagType('easy')).toBe('success')
      expect(getDifficultyTagType('medium')).toBe('warning')
      expect(getDifficultyTagType('hard')).toBe('danger')
    })

    it('应该处理未知难度', () => {
      const { getDifficultyTagType } = useStatusMapping()

      expect(getDifficultyTagType('unknown')).toBe('')
    })
  })

  describe('getDifficultyText', () => {
    it('应该返回正确的难度文本', () => {
      const { getDifficultyText } = useStatusMapping()

      expect(getDifficultyText('easy')).toBe('简单')
      expect(getDifficultyText('medium')).toBe('中等')
      expect(getDifficultyText('hard')).toBe('困难')
    })

    it('应该返回原始难度当找不到映射时', () => {
      const { getDifficultyText } = useStatusMapping()

      expect(getDifficultyText('unknown')).toBe('unknown')
    })
  })

  describe('getScoreTagType', () => {
    it('应该返回正确的分数标签类型', () => {
      const { getScoreTagType } = useStatusMapping()

      expect(getScoreTagType('excellent')).toBe('success')
      expect(getScoreTagType('good')).toBe('primary')
      expect(getScoreTagType('average')).toBe('warning')
      expect(getScoreTagType('poor')).toBe('danger')
    })

    it('应该处理未知分数等级', () => {
      const { getScoreTagType } = useStatusMapping()

      expect(getScoreTagType('unknown')).toBe('')
    })
  })

  describe('getScoreText', () => {
    it('应该返回格式化的分数文本', () => {
      const { getScoreText } = useStatusMapping()

      expect(getScoreText(95)).toBe('95分')
      expect(getScoreText(80)).toBe('80分')
      expect(getScoreText(60)).toBe('60分')
      expect(getScoreText(0)).toBe('0分')
    })

    it('应该处理小数分数', () => {
      const { getScoreText } = useStatusMapping()

      expect(getScoreText(95.5)).toBe('95.5分')
      expect(getScoreText(88.25)).toBe('88.25分')
    })
  })

  describe('isValidStatus', () => {
    it('应该验证考试状态', () => {
      const { isValidStatus } = useStatusMapping()

      expect(isValidStatus('draft', 'exam')).toBe(true)
      expect(isValidStatus('published', 'exam')).toBe(true)
      expect(isValidStatus('archived', 'exam')).toBe(true)
      expect(isValidStatus('invalid', 'exam')).toBe(false)
    })

    it('应该验证项目状态', () => {
      const { isValidStatus } = useStatusMapping()

      expect(isValidStatus('active', 'project')).toBe(true)
      expect(isValidStatus('inactive', 'project')).toBe(true)
      expect(isValidStatus('completed', 'project')).toBe(true)
      expect(isValidStatus('invalid', 'project')).toBe(false)
    })

    it('应该验证难度等级', () => {
      const { isValidStatus } = useStatusMapping()

      expect(isValidStatus('easy', 'difficulty')).toBe(true)
      expect(isValidStatus('medium', 'difficulty')).toBe(true)
      expect(isValidStatus('hard', 'difficulty')).toBe(true)
      expect(isValidStatus('invalid', 'difficulty')).toBe(false)
    })

    it('应该处理无效的类型', () => {
      const { isValidStatus } = useStatusMapping()

      expect(isValidStatus('active', 'invalid_type')).toBe(false)
      expect(isValidStatus('active')).toBe(false) // 默认类型
    })
  })

  describe('formatStatus', () => {
    it('应该返回完整的状态信息', () => {
      const { formatStatus } = useStatusMapping()

      const info = formatStatus('published', 'exam')

      expect(info.value).toBe('published')
      expect(info.label).toBe('已发布')
      expect(info.type).toBe('success')
      expect(info.valid).toBe(true)
    })

    it('应该处理未知状态', () => {
      const { formatStatus } = useStatusMapping()

      const info = formatStatus('unknown', 'exam')

      expect(info.value).toBe('unknown')
      expect(info.label).toBe('unknown')
      expect(info.type).toBe('')
      expect(info.valid).toBe(false)
    })
  })

  describe('getAvailableStatuses', () => {
    it('应该获取考试状态列表', () => {
      const { getAvailableStatuses } = useStatusMapping()

      const statuses = getAvailableStatuses('exam')

      expect(Array.isArray(statuses)).toBe(true)
      expect(statuses.length).toBeGreaterThan(0)
      expect(statuses[0]).toHaveProperty('value')
      expect(statuses[0]).toHaveProperty('label')
      expect(statuses[0]).toHaveProperty('type')
    })

    it('应该获取项目状态列表', () => {
      const { getAvailableStatuses } = useStatusMapping()

      const statuses = getAvailableStatuses('project')

      expect(Array.isArray(statuses)).toBe(true)
      expect(statuses.length).toBeGreaterThan(0)
    })

    it('应该处理未知类型', () => {
      const { getAvailableStatuses } = useStatusMapping()

      const statuses = getAvailableStatuses('unknown')

      expect(Array.isArray(statuses)).toBe(true)
      expect(statuses).toEqual([])
    })
  })
}) 