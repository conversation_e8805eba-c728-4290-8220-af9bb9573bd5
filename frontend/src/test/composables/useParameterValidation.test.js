/**
 * useParameterValidation composable 测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useParameterValidation } from '@/composables/useParameterValidation'
import { ElMessage } from 'element-plus'

// Mock dependencies
vi.mock('@/utils/validation', () => ({
  validateAndConvertId: vi.fn((id) => {
    if (id === '1' || id === 1) return 1
    if (id === '2' || id === 2) return 2
    return null
  }),
  isValidId: vi.fn((id) => {
    return id === '1' || id === '2' || id === 1 || id === 2
  }),
  validateRouteParams: vi.fn((params, requiredParams) => {
    const errors = []
    for (const param of requiredParams) {
      if (!params[param] || !['1', '2', 1, 2].includes(params[param])) {
        errors.push(`参数 ${param} 无效: ${params[param]}`)
      }
    }
    return {
      valid: errors.length === 0,
      errors
    }
  })
}))

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn()
  }
}))

describe('useParameterValidation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('validateId', () => {
    it('应该验证有效的ID', () => {
      const { validateId, validationErrors } = useParameterValidation()

      const result = validateId('1', 'projectId')

      expect(result).toBe(1)
      expect(validationErrors.value).toEqual([])
      expect(ElMessage.error).not.toHaveBeenCalled()
    })

    it('应该处理无效的ID', () => {
      const { validateId, validationErrors } = useParameterValidation()

      const result = validateId('invalid', 'projectId')

      expect(result).toBe(null)
      expect(validationErrors.value).toContain('projectId无效: invalid')
      expect(ElMessage.error).toHaveBeenCalledWith('projectId无效: invalid')
    })

    it('应该使用默认参数名称', () => {
      const { validateId, validationErrors } = useParameterValidation()

      const result = validateId('invalid')

      expect(result).toBe(null)
      expect(validationErrors.value).toContain('ID无效: invalid')
      expect(ElMessage.error).toHaveBeenCalledWith('ID无效: invalid')
    })

    it('应该处理数字ID', () => {
      const { validateId } = useParameterValidation()

      const result = validateId(2, 'userId')

      expect(result).toBe(2)
    })
  })

  describe('validateRoute', () => {
    it('应该验证有效的路由参数', () => {
      const { validateRoute, validationErrors, isValidating } = useParameterValidation()

      const routeParams = { id: '1', projectId: '2' }
      const requiredParams = ['id', 'projectId']

      const result = validateRoute(routeParams, requiredParams)

      expect(result).toBe(true)
      expect(validationErrors.value).toEqual([])
      expect(isValidating.value).toBe(false)
      expect(ElMessage.error).not.toHaveBeenCalled()
    })

    it('应该处理无效的路由参数', () => {
      const { validateRoute, validationErrors, isValidating } = useParameterValidation()

      const routeParams = { id: 'invalid', projectId: '2' }
      const requiredParams = ['id', 'projectId']

      const result = validateRoute(routeParams, requiredParams)

      expect(result).toBe(false)
      expect(validationErrors.value).toContain('参数 id 无效: invalid')
      expect(isValidating.value).toBe(false)
      expect(ElMessage.error).toHaveBeenCalled()
    })

    it('应该处理缺失的路由参数', () => {
      const { validateRoute, validationErrors } = useParameterValidation()

      const routeParams = { id: '1' }
      const requiredParams = ['id', 'projectId']

      const result = validateRoute(routeParams, requiredParams)

      expect(result).toBe(false)
      expect(validationErrors.value).toContain('参数 projectId 无效: undefined')
    })

    it('应该在验证过程中设置loading状态', () => {
      const { validateRoute, isValidating } = useParameterValidation()

      // 在同步函数中，状态会立即改变，但我们可以检查最终状态
      validateRoute({ id: '1' }, ['id'])

      expect(isValidating.value).toBe(false) // 验证完成后应该为false
    })

    it('应该处理空的必需参数列表', () => {
      const { validateRoute } = useParameterValidation()

      const result = validateRoute({ id: '1' }, [])

      expect(result).toBe(true)
    })
  })

  describe('validateApiParams', () => {
    it('应该验证API参数', () => {
      const { validateApiParams } = useParameterValidation()

      const params = { id: 1, name: 'test' }
      const rules = { name: { required: true } }
      const result = validateApiParams(params, rules)

      expect(result).toEqual(params)
    })

    it('应该处理包含无效参数的对象', () => {
      const { validateApiParams } = useParameterValidation()

      const params = { id: 'invalid', name: '' }
      const rules = { name: { required: true } }
      const result = validateApiParams(params, rules)

      expect(result).toBeNull()
    })

    it('应该处理空参数对象', () => {
      const { validateApiParams } = useParameterValidation()

      const result = validateApiParams({}, {})

      expect(result).toEqual({})
    })
  })

  describe('clearErrors', () => {
    it('应该清除所有验证错误', () => {
      const { validateId, validationErrors, clearErrors } = useParameterValidation()

      // 先产生一些错误
      validateId('invalid1')
      validateId('invalid2')

      expect(validationErrors.value.length).toBeGreaterThan(0)

      clearErrors()

      expect(validationErrors.value).toEqual([])
    })
  })

  describe('isEmptyValue', () => {
    it('应该正确检测空值', () => {
      const { isEmptyValue } = useParameterValidation()

      expect(isEmptyValue(null)).toBe(true)
      expect(isEmptyValue(undefined)).toBe(true)
      expect(isEmptyValue('')).toBe(true)
      expect(isEmptyValue('null')).toBe(true)
      expect(isEmptyValue('undefined')).toBe(true)
      expect(isEmptyValue(NaN)).toBe(true)
      
      expect(isEmptyValue('test')).toBe(false)
      expect(isEmptyValue(0)).toBe(false)
      expect(isEmptyValue(false)).toBe(false)
    })
  })

  describe('filterEmptyParams', () => {
    it('应该过滤空值参数', () => {
      const { filterEmptyParams } = useParameterValidation()

      const params = {
        name: 'test',
        id: 1,
        empty: '',
        nullValue: null,
        undefinedValue: undefined,
        nanValue: NaN
      }

      const filtered = filterEmptyParams(params)

      expect(filtered).toEqual({
        name: 'test',
        id: 1
      })
    })

    it('应该处理完全空的参数对象', () => {
      const { filterEmptyParams } = useParameterValidation()

      const params = {
        empty: '',
        nullValue: null,
        undefinedValue: undefined
      }

      const filtered = filterEmptyParams(params)

      expect(filtered).toEqual({})
    })
  })

  describe('safeApiCall', () => {
    it('应该安全地调用API', async () => {
      const { safeApiCall } = useParameterValidation()

      const mockApiCall = vi.fn().mockResolvedValue('success')
      const result = await safeApiCall(mockApiCall, [1, 2])

      expect(result).toBe('success')
      expect(mockApiCall).toHaveBeenCalledWith(1, 2)
    })

    it('应该处理API调用失败', async () => {
      const { safeApiCall } = useParameterValidation()

      const mockApiCall = vi.fn().mockRejectedValue(new Error('API failed'))
      
      await expect(safeApiCall(mockApiCall, [])).rejects.toThrow('API failed')
      expect(ElMessage.error).toHaveBeenCalledWith('API failed')
    })
  })
}) 