/**
 * usePageInit composable 测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { usePageInit } from '@/composables/usePageInit'

// Mock dependencies
vi.mock('@/composables/useProject', () => ({
  useProject: () => ({
    project: { value: null },
    projectId: { value: null },
    fetchProject: vi.fn().mockResolvedValue({ id: 1, name: 'Test Project' })
  })
}))

vi.mock('@/composables/useLoading', () => ({
  useLoading: () => ({
    loading: { value: false },
    execute: vi.fn((fn) => fn())
  })
}))

vi.mock('@/composables/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleAsyncError: vi.fn()
  })
}))

// Mock onMounted
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    onMounted: vi.fn((fn) => fn()) // 立即执行
  }
})

describe('usePageInit', () => {
  let mockCriticalTask
  let mockBackgroundTask

  beforeEach(() => {
    mockCriticalTask = vi.fn().mockResolvedValue('critical result')
    mockBackgroundTask = vi.fn().mockResolvedValue('background result')
    vi.clearAllMocks()
  })

  it('应该初始化为未初始化状态', () => {
    const { initialized, initError } = usePageInit({
      showLoadingOnMount: false
    })

    expect(initialized.value).toBe(false)
    expect(initError.value).toBe(null)
  })

  it('应该执行关键任务', async () => {
    const { executeCriticalTasks } = usePageInit({
      requireProject: false,
      criticalTasks: [mockCriticalTask],
      showLoadingOnMount: false
    })

    await executeCriticalTasks()
    expect(mockCriticalTask).toHaveBeenCalled()
  })

  it('应该执行后台任务', async () => {
    const { executeBackgroundTasks } = usePageInit({
      backgroundTasks: [mockBackgroundTask],
      showLoadingOnMount: false
    })

    await executeBackgroundTasks()
    expect(mockBackgroundTask).toHaveBeenCalled()
  })

  it('应该处理关键任务失败', async () => {
    const failingTask = vi.fn().mockRejectedValue(new Error('Task failed'))
    
    const { executeCriticalTasks } = usePageInit({
      requireProject: false,
      criticalTasks: [failingTask],
      showLoadingOnMount: false
    })

    await expect(executeCriticalTasks()).rejects.toThrow('关键任务失败')
    expect(failingTask).toHaveBeenCalled()
  })

  it('应该正确处理后台任务失败', async () => {
    const failingBackgroundTask = vi.fn().mockRejectedValue(new Error('Background task failed'))

    const { executeBackgroundTasks } = usePageInit({
      backgroundTasks: [failingBackgroundTask],
      showLoadingOnMount: false
    })

    // 后台任务失败不应该抛出错误，但会返回结果数组
    const result = await executeBackgroundTasks()
    expect(failingBackgroundTask).toHaveBeenCalled()
    // 验证返回的是Promise.allSettled的结果
    expect(Array.isArray(result)).toBe(true)
  })

  it('应该完整初始化页面', async () => {
    const { initializePage, initialized, initError } = usePageInit({
      requireProject: false,
      criticalTasks: [mockCriticalTask],
      backgroundTasks: [mockBackgroundTask],
      showLoadingOnMount: false
    })

    await initializePage()
    
    expect(initialized.value).toBe(true)
    expect(initError.value).toBe(null)
    expect(mockCriticalTask).toHaveBeenCalled()
    // 后台任务是异步执行的，可能还没完成
  })

  it('应该支持添加任务', () => {
    const { addCriticalTask, addBackgroundTask } = usePageInit({
      showLoadingOnMount: false
    })

    const newCriticalTask = vi.fn()
    const newBackgroundTask = vi.fn()

    addCriticalTask(newCriticalTask)
    addBackgroundTask(newBackgroundTask)

    // 这里我们无法直接测试任务是否被添加，但可以确保方法存在且可调用
    expect(typeof addCriticalTask).toBe('function')
    expect(typeof addBackgroundTask).toBe('function')
  })

  it('应该支持重新初始化', async () => {
    const { reinitialize, initialized } = usePageInit({
      requireProject: false,
      showLoadingOnMount: false
    })

    await reinitialize()
    expect(initialized.value).toBe(true)
  })

  it('应该在需要时加载项目', async () => {
    const { initializePage } = usePageInit({
      requireProject: true,
      showLoadingOnMount: false
    })

    await initializePage()
    // 由于我们mock了fetchProject，这里主要验证没有抛出错误
    expect(true).toBe(true)
  })
})
