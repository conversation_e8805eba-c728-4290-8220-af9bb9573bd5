/**
 * cacheService.js 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  clearProjectAllCache,
  clearAllCache,
  clearCacheAfterKnowledgeUpdate,
  clearCacheAfterScoreUpdate,
  clearCacheAfterConfigSave
} from '@/services/cacheService'

// Mock dependencies
vi.mock('@/api', () => ({
  knowledgeAPI: {
    clearKnowledgeCache: vi.fn()
  }
}))

vi.mock('@/services/knowledgePathService', () => ({
  clearProjectCache: vi.fn(),
  clearAllCache: vi.fn()
}))

vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

// Import mocked modules
import { knowledgeAPI } from '@/api'
import { clearProjectCache, clearAllCache as clearKnowledgePathCache } from '@/services/knowledgePathService'
import { ElMessage } from 'element-plus'

describe('cacheService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    console.log = vi.fn()
    console.error = vi.fn()
  })

  describe('clearProjectAllCache', () => {
    it('应该成功清空项目缓存', async () => {
      knowledgeAPI.clearKnowledgeCache.mockResolvedValue()
      
      const result = await clearProjectAllCache(123)
      
      expect(knowledgeAPI.clearKnowledgeCache).toHaveBeenCalledWith(123)
      expect(clearProjectCache).toHaveBeenCalledWith(123)
      expect(ElMessage.success).toHaveBeenCalledWith('项目缓存清空成功')
      expect(result).toBe(true)
    })

    it('应该处理清空缓存失败', async () => {
      const error = new Error('清空失败')
      knowledgeAPI.clearKnowledgeCache.mockRejectedValue(error)
      
      const result = await clearProjectAllCache(123)
      
      expect(ElMessage.error).toHaveBeenCalledWith('清空项目缓存失败: 清空失败')
      expect(result).toBe(false)
    })

    it('应该支持静默模式', async () => {
      knowledgeAPI.clearKnowledgeCache.mockResolvedValue()
      
      const result = await clearProjectAllCache(123, false)
      
      expect(ElMessage.success).not.toHaveBeenCalled()
      expect(result).toBe(true)
    })
  })

  describe('clearAllCache', () => {
    it('应该成功清空所有前端缓存', async () => {
      const result = await clearAllCache()
      
      expect(clearKnowledgePathCache).toHaveBeenCalled()
      expect(ElMessage.success).toHaveBeenCalledWith('前端缓存清空成功')
      expect(result).toBe(true)
    })

    it('应该处理清空缓存失败', async () => {
      const error = new Error('清空失败')
      clearKnowledgePathCache.mockImplementation(() => {
        throw error
      })
      
      const result = await clearAllCache()
      
      expect(ElMessage.error).toHaveBeenCalledWith('清空缓存失败: 清空失败')
      expect(result).toBe(false)
    })

    it('应该支持静默模式', async () => {
      // 确保clearKnowledgePathCache不会抛出异常
      clearKnowledgePathCache.mockImplementation(() => {})
      
      const result = await clearAllCache(false)
      
      expect(clearKnowledgePathCache).toHaveBeenCalled()
      expect(ElMessage.success).not.toHaveBeenCalled()
      expect(result).toBe(true)
    })
  })

  describe('clearCacheAfterKnowledgeUpdate', () => {
    it('应该在知识点更新后清空缓存', async () => {
      knowledgeAPI.clearKnowledgeCache.mockResolvedValue()
      
      await clearCacheAfterKnowledgeUpdate(123)
      
      expect(knowledgeAPI.clearKnowledgeCache).toHaveBeenCalledWith(123)
      expect(clearProjectCache).toHaveBeenCalledWith(123)
      expect(ElMessage.success).not.toHaveBeenCalled() // 静默模式
    })

    it('应该处理知识点更新后清空失败', async () => {
      const error = new Error('清空失败')
      knowledgeAPI.clearKnowledgeCache.mockRejectedValue(error)
      
      await clearCacheAfterKnowledgeUpdate(123)
      
      expect(console.error).toHaveBeenCalled()
      expect(ElMessage.error).not.toHaveBeenCalled() // 静默模式
    })
  })

  describe('clearCacheAfterScoreUpdate', () => {
    it('应该在评分更新后清空缓存', async () => {
      knowledgeAPI.clearKnowledgeCache.mockResolvedValue()
      
      await clearCacheAfterScoreUpdate(456)
      
      expect(knowledgeAPI.clearKnowledgeCache).toHaveBeenCalledWith(456)
      expect(clearProjectCache).toHaveBeenCalledWith(456)
      expect(ElMessage.success).not.toHaveBeenCalled() // 静默模式
    })
  })

  describe('clearCacheAfterConfigSave', () => {
    it('应该在配置保存后清空缓存', async () => {
      knowledgeAPI.clearKnowledgeCache.mockResolvedValue()
      
      await clearCacheAfterConfigSave(789)
      
      expect(knowledgeAPI.clearKnowledgeCache).toHaveBeenCalledWith(789)
      expect(clearProjectCache).toHaveBeenCalledWith(789)
      expect(ElMessage.success).not.toHaveBeenCalled() // 静默模式
    })
  })

  describe('边界情况', () => {
    it('应该处理无效的项目ID', async () => {
      knowledgeAPI.clearKnowledgeCache.mockResolvedValue()
      
      await clearProjectAllCache(null)
      expect(knowledgeAPI.clearKnowledgeCache).toHaveBeenCalledWith(null)
      
      await clearProjectAllCache(undefined)
      expect(knowledgeAPI.clearKnowledgeCache).toHaveBeenCalledWith(undefined)
    })

    it('应该处理API调用异常', async () => {
      knowledgeAPI.clearKnowledgeCache.mockImplementation(() => {
        throw new Error('Network error')
      })
      
      const result = await clearProjectAllCache(123)
      expect(result).toBe(false)
      expect(console.error).toHaveBeenCalled()
    })
  })
})
