/**
 * 知识点路径服务测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import api from '@/api'
import knowledgePathService, {
  getKnowledgePointPaths,
  getKnowledgePointFullPaths,
  getLeafKnowledgePointsWithPaths,
  getKnowledgePointPath,
  getBatchKnowledgePointPaths,
  findUidByKnowledgePointName,
  clearProjectCache,
  clearAllCache,
  getCacheStatus,
  preloadKnowledgePointPaths
} from '@/services/knowledgePathService'

// Mock API
vi.mock('@/api', () => ({
  default: {
    get: vi.fn()
  }
}))

// Mock console methods
const consoleSpy = {
  log: vi.spyOn(console, 'log').mockImplementation(() => {}),
  error: vi.spyOn(console, 'error').mockImplementation(() => {}),
  warn: vi.spyOn(console, 'warn').mockImplementation(() => {})
}

describe('知识点路径服务', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    clearAllCache()
    Object.values(consoleSpy).forEach(spy => spy.mockClear())
  })

  afterEach(() => {
    clearAllCache()
  })

  describe('getKnowledgePointPaths', () => {
    it('应该成功获取知识点父级路径', async () => {
      const mockPathData = {
        'uid1': '数学/代数',
        'uid2': '数学/几何',
        'uid3': '物理/力学'
      }

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockPathData
        }
      })

      const result = await getKnowledgePointPaths(1)

      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths')
      expect(result).toEqual(mockPathData)
    })

    it('应该复用正在进行的请求（请求去重）', async () => {
      const mockPathData = { 'uid1': '数学/代数' }

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockPathData
        }
      })

      // 同时发起多个请求
      const [result1, result2, result3] = await Promise.all([
        getKnowledgePointPaths(1),
        getKnowledgePointPaths(1),
        getKnowledgePointPaths(1)
      ])

      // 应该只调用一次API
      expect(api.get).toHaveBeenCalledTimes(1)
      expect(result1).toEqual(mockPathData)
      expect(result2).toEqual(mockPathData)
      expect(result3).toEqual(mockPathData)
    })

    it('应该处理API请求失败', async () => {
      const errorMessage = '网络错误'
      api.get.mockRejectedValue(new Error(errorMessage))

      await expect(getKnowledgePointPaths(1)).rejects.toThrow(errorMessage)
      expect(consoleSpy.error).toHaveBeenCalled()
    })

    it('应该处理后端返回的业务错误', async () => {
      api.get.mockResolvedValue({
        data: {
          success: false,
          message: '项目不存在'
        }
      })

      await expect(getKnowledgePointPaths(1)).rejects.toThrow('项目不存在')
    })
  })

  describe('getKnowledgePointFullPaths', () => {
    it('应该成功获取知识点完整路径', async () => {
      const mockFullPathData = {
        'uid1': '数学/代数/一元二次方程',
        'uid2': '数学/几何/三角形',
        'uid3': '物理/力学/牛顿定律'
      }

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockFullPathData
        }
      })

      const result = await getKnowledgePointFullPaths(1)

      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths/full')
      expect(result).toEqual(mockFullPathData)
    })

    it('应该复用正在进行的完整路径请求', async () => {
      const mockData = { 'uid1': '数学/代数/一元二次方程' }

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockData
        }
      })

      // 同时发起多个请求
      const [result1, result2] = await Promise.all([
        getKnowledgePointFullPaths(1),
        getKnowledgePointFullPaths(1)
      ])

      expect(api.get).toHaveBeenCalledTimes(1)
      expect(result1).toEqual(mockData)
      expect(result2).toEqual(mockData)
    })
  })

  describe('getLeafKnowledgePointsWithPaths', () => {
    it('应该成功获取带路径的叶子节点列表', async () => {
      const mockLeafPoints = [
        { uid: 'uid1', name: '一元二次方程', path: '数学/代数/一元二次方程' },
        { uid: 'uid2', name: '三角形', path: '数学/几何/三角形' },
        { uid: 'uid3', name: '牛顿定律', path: '物理/力学/牛顿定律' }
      ]

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockLeafPoints
        }
      })

      const result = await getLeafKnowledgePointsWithPaths(1)

      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths/leaf-points')
      expect(result).toEqual(mockLeafPoints)
    })

    it('应该复用正在进行的叶子节点请求', async () => {
      const mockData = [{ uid: 'uid1', name: '测试知识点', path: '测试路径' }]

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockData
        }
      })

      const [result1, result2] = await Promise.all([
        getLeafKnowledgePointsWithPaths(1),
        getLeafKnowledgePointsWithPaths(1)
      ])

      expect(api.get).toHaveBeenCalledTimes(1)
      expect(result1).toEqual(mockData)
      expect(result2).toEqual(mockData)
    })
  })

  describe('getKnowledgePointPath', () => {
    it('应该优先从批量路径中获取单个路径', async () => {
      const mockPathData = {
        'uid1': '数学/代数',
        'uid2': '数学/几何'
      }

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockPathData
        }
      })

      const result = await getKnowledgePointPath(1, 'uid1')

      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths')
      expect(result).toBe('数学/代数')
    })

    it('应该在批量获取中没有时请求单个路径', async () => {
      // 第一次调用返回批量路径（不包含目标uid）
      api.get
        .mockResolvedValueOnce({
          data: {
            success: true,
            data: { 'uid1': '数学/代数' } // 不包含uid2
          }
        })
        // 第二次调用返回单个路径
        .mockResolvedValueOnce({
          data: {
            success: true,
            data: '物理/力学'
          }
        })

      const result = await getKnowledgePointPath(1, 'uid2')

      expect(api.get).toHaveBeenCalledTimes(2)
      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths')
      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths/uid2')
      expect(result).toBe('物理/力学')
    })

    it('应该处理单个路径请求失败', async () => {
      // 批量获取成功但不包含目标uid
      api.get
        .mockResolvedValueOnce({
          data: {
            success: true,
            data: {}
          }
        })
        // 单个路径请求失败
        .mockRejectedValueOnce(new Error('路径不存在'))

      await expect(getKnowledgePointPath(1, 'nonexistent')).rejects.toThrow('路径不存在')
    })
  })

  describe('getBatchKnowledgePointPaths', () => {
    it('应该批量获取多个知识点路径', async () => {
      const mockPathData = {
        'uid1': '数学/代数',
        'uid2': '数学/几何',
        'uid3': '物理/力学'
      }

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockPathData
        }
      })

      const result = await getBatchKnowledgePointPaths(1, ['uid1', 'uid3'])

      expect(result).toEqual({
        'uid1': '数学/代数',
        'uid3': '物理/力学'
      })
    })

    it('应该处理空的uid数组', async () => {
      const result = await getBatchKnowledgePointPaths(1, [])
      expect(result).toEqual({})
      expect(api.get).not.toHaveBeenCalled()
    })

    it('应该处理null或undefined的uid数组', async () => {
      const result1 = await getBatchKnowledgePointPaths(1, null)
      const result2 = await getBatchKnowledgePointPaths(1, undefined)
      
      expect(result1).toEqual({})
      expect(result2).toEqual({})
      expect(api.get).not.toHaveBeenCalled()
    })

    it('应该处理部分uid不存在的情况', async () => {
      const mockPathData = {
        'uid1': '数学/代数',
        'uid2': '数学/几何'
      }

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockPathData
        }
      })

      const result = await getBatchKnowledgePointPaths(1, ['uid1', 'uid_nonexistent', 'uid2'])

      expect(result).toEqual({
        'uid1': '数学/代数',
        'uid2': '数学/几何'
        // uid_nonexistent 不应该出现在结果中
      })
    })
  })

  describe('findUidByKnowledgePointName', () => {
    it('应该根据知识点名称找到对应的UID', async () => {
      const mockLeafPoints = [
        { uid: 'uid1', name: '一元二次方程', path: '数学/代数/一元二次方程' },
        { uid: 'uid2', name: '三角形', path: '数学/几何/三角形' },
        { uid: 'uid3', name: '牛顿定律', path: '物理/力学/牛顿定律' }
      ]

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockLeafPoints
        }
      })

      const result = await findUidByKnowledgePointName(1, '三角形')

      expect(result).toBe('uid2')
    })

    it('应该在找不到匹配知识点时返回null', async () => {
      const mockLeafPoints = [
        { uid: 'uid1', name: '一元二次方程', path: '数学/代数/一元二次方程' }
      ]

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: mockLeafPoints
        }
      })

      const result = await findUidByKnowledgePointName(1, '不存在的知识点')

      expect(result).toBe(null)
      expect(consoleSpy.warn).toHaveBeenCalled()
    })

    it('应该处理获取叶子节点失败的情况', async () => {
      api.get.mockRejectedValue(new Error('获取叶子节点失败'))

      await expect(findUidByKnowledgePointName(1, '测试知识点')).rejects.toThrow('获取叶子节点失败')
    })
  })

  describe('缓存管理', () => {
    it('应该清除指定项目的缓存', () => {
      clearProjectCache(1)
      // 验证缓存状态
      const status = getCacheStatus()
      expect(status.requestCacheSize).toBe(0)
    })

    it('应该清除所有缓存', () => {
      clearAllCache()
      const status = getCacheStatus()
      expect(status.requestCacheSize).toBe(0)
    })

    it('应该返回正确的缓存状态', () => {
      const status = getCacheStatus()
      expect(status).toHaveProperty('requestCacheSize')
      expect(status).toHaveProperty('note')
      expect(typeof status.requestCacheSize).toBe('number')
    })
  })

  describe('preloadKnowledgePointPaths', () => {
    it('应该预加载知识点路径', async () => {
      api.get
        .mockResolvedValueOnce({
          data: {
            success: true,
            data: { 'uid1': '数学/代数' }
          }
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            data: [{ uid: 'uid1', name: '测试', path: '数学/代数/测试' }]
          }
        })

      await preloadKnowledgePointPaths(1)

      expect(api.get).toHaveBeenCalledTimes(2)
      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths')
      expect(api.get).toHaveBeenCalledWith('/projects/1/knowledge-paths/leaf-points')
    })

    it('应该处理预加载失败', async () => {
      api.get.mockRejectedValue(new Error('预加载失败'))

      // 预加载失败不应该抛出错误
      await expect(preloadKnowledgePointPaths(1)).resolves.toBeUndefined()
      expect(consoleSpy.error).toHaveBeenCalled()
    })
  })

  describe('并发请求处理', () => {
    it('应该正确处理并发的不同项目请求', async () => {
      api.get
        .mockImplementation((url) => {
          if (url.includes('/projects/1/')) {
            return Promise.resolve({
              data: {
                success: true,
                data: { 'uid1': '项目1路径' }
              }
            })
          } else if (url.includes('/projects/2/')) {
            return Promise.resolve({
              data: {
                success: true,
                data: { 'uid2': '项目2路径' }
              }
            })
          }
        })

      const [result1, result2] = await Promise.all([
        getKnowledgePointPaths(1),
        getKnowledgePointPaths(2)
      ])

      expect(api.get).toHaveBeenCalledTimes(2)
      expect(result1).toEqual({ 'uid1': '项目1路径' })
      expect(result2).toEqual({ 'uid2': '项目2路径' })
    })

    it('应该处理缓存过期后的新请求', async () => {
      // Mock Date.now 来模拟时间流逝
      const originalDateNow = Date.now
      let currentTime = 1000000

      Date.now = vi.fn(() => currentTime)

      api.get.mockResolvedValue({
        data: {
          success: true,
          data: { 'uid1': '路径' }
        }
      })

      // 第一次请求
      await getKnowledgePointPaths(1)
      expect(api.get).toHaveBeenCalledTimes(1)

      // 模拟时间流逝超过缓存过期时间（60秒）
      currentTime += 61 * 1000

      // 第二次请求应该重新发起
      await getKnowledgePointPaths(1)
      expect(api.get).toHaveBeenCalledTimes(2)

      // 恢复原始的Date.now
      Date.now = originalDateNow
    })
  })

  describe('默认导出', () => {
    it('应该导出所有主要函数', () => {
      expect(knowledgePathService).toHaveProperty('getKnowledgePointPaths')
      expect(knowledgePathService).toHaveProperty('getKnowledgePointFullPaths')
      expect(knowledgePathService).toHaveProperty('getLeafKnowledgePointsWithPaths')
      expect(knowledgePathService).toHaveProperty('getKnowledgePointPath')
      expect(knowledgePathService).toHaveProperty('getBatchKnowledgePointPaths')
      expect(knowledgePathService).toHaveProperty('findUidByKnowledgePointName')
      expect(knowledgePathService).toHaveProperty('clearProjectCache')
      expect(knowledgePathService).toHaveProperty('clearAllCache')
      expect(knowledgePathService).toHaveProperty('getCacheStatus')
      expect(knowledgePathService).toHaveProperty('preloadKnowledgePointPaths')
    })
  })
}) 