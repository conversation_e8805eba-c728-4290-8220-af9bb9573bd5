/**
 * Vitest 测试配置
 */

import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  test: {
    // 全局设置
    globals: true,
    environment: 'jsdom',
    
    // 设置文件
    setupFiles: ['./src/test/setup.js'],
    
    // 包含的测试文件
    include: [
      'src/test/**/*.{test,spec}.{js,ts}',
      'src/test/**/*.test.{js,ts}'
    ],
    
    // 排除的文件
    exclude: [
      'node_modules',
      'dist',
      'src/test/setup.js'
    ],
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      include: [
        'src/utils/**/*.js',
        'src/services/**/*.js',
        'src/composables/**/*.js',
        'src/components/**/*.vue'
      ],
      exclude: [
        'src/test/**',
        'src/main.js',
        'src/router/**',
        'src/data/**',
        'src/api/**'
      ],
      // 覆盖率阈值
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // 测试超时
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // 并发设置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false
      }
    },
    
    // 报告器
    reporter: ['verbose', 'json', 'html'],
    
    // 监听模式配置
    watch: {
      include: ['src/**/*.{js,ts,vue}']
    }
  },
  
  // 路径别名
  resolve: {
    alias: {
      '@': resolve(__dirname, '../'),
      '~': resolve(__dirname, '../')
    }
  },
  
  // 定义全局变量
  define: {
    __TEST__: true
  }
}) 