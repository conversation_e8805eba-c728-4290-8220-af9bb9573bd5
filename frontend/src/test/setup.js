/**
 * Vitest 测试环境设置
 */

import { vi } from 'vitest'

// Mock Element Plus 消息组件
global.ElMessage = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn()
}

global.ElMessageBox = {
  confirm: vi.fn().mockResolvedValue('confirm'),
  alert: vi.fn().mockResolvedValue('confirm'),
  prompt: vi.fn().mockResolvedValue({ value: 'test' })
}

// Mock 路由
global.mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn()
}

global.mockRoute = {
  params: {},
  query: {},
  path: '/',
  name: 'test'
}

// Mock API
global.mockAPI = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

// Mock constants
const mockConstants = {
  VALIDATION_RULES: {
    required: [{ required: true, message: '此字段为必填项' }],
    email: [{ type: 'email', message: '请输入有效的邮箱地址' }],
    phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }]
  }
}

// Mock modules
vi.mock('@/utils/constants', () => mockConstants)

vi.mock('element-plus', () => ({
  ElMessage: global.ElMessage,
  ElMessageBox: global.ElMessageBox
}))

vi.mock('vue-router', () => ({
  useRoute: () => global.mockRoute,
  useRouter: () => global.mockRouter
}))

vi.mock('@/api', () => ({
  projectAPI: {
    getProject: vi.fn(),
    createProject: vi.fn(),
    updateProject: vi.fn(),
    deleteProject: vi.fn()
  }
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
global.localStorage = localStorageMock

// Mock fetch
global.fetch = vi.fn()

// Mock console methods for cleaner test output
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}
global.console = {
  ...console,
  ...mockConsole
}
global.mockConsole = mockConsole
