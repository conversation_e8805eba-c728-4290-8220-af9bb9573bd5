/**
 * 路径工具函数测试
 */

import { describe, it, expect } from 'vitest'
import {
  formatKnowledgePathDisplay,
  batchFormatKnowledgePaths,
  generateKnowledgePath,
  parsePathString,
  getParentPath,
  getLeafName,
  getCommonPathPrefix,
  isSubPath,
  getPathDepth,
  beautifyPathDisplay,
  validatePath
} from '@/utils/pathUtils'

describe('pathUtils', () => {
  describe('formatKnowledgePathDisplay', () => {
    it('应该返回占位符当路径为空时', () => {
      expect(formatKnowledgePathDisplay('')).toBe('暂无路径')
      expect(formatKnowledgePathDisplay(null)).toBe('暂无路径')
      expect(formatKnowledgePathDisplay(undefined)).toBe('暂无路径')
    })

    it('应该返回原路径当长度小于最大长度时', () => {
      const path = '数学 > 代数'
      expect(formatKnowledgePathDisplay(path)).toBe(path)
    })

    it('应该截断长路径', () => {
      const longPath = '数学 > 代数 > 线性代数 > 矩阵运算 > 矩阵乘法 > 特殊矩阵乘法'
      const result = formatKnowledgePathDisplay(longPath, { maxLength: 20 })
      expect(result).toBe('数学 > 代数 > 线性代数 > ...')
    })

    it('应该返回带tooltip的对象当showTooltip为true时', () => {
      const longPath = '数学 > 代数 > 线性代数 > 矩阵运算'
      const result = formatKnowledgePathDisplay(longPath, { maxLength: 20, showTooltip: true })
      expect(result).toEqual({
        display: '数学 > 代数 > 线性代数 > ...',
        tooltip: longPath
      })
    })
  })

  describe('batchFormatKnowledgePaths', () => {
    it('应该格式化知识点数组', () => {
      const knowledgePoints = [
        { uid: '1', name: '知识点1' },
        { uid: '2', name: '知识点2' }
      ]
      const pathMap = {
        '1': '数学 > 代数',
        '2': '物理 > 力学'
      }

      const result = batchFormatKnowledgePaths(knowledgePoints, pathMap)
      
      expect(result).toHaveLength(2)
      expect(result[0]).toMatchObject({
        uid: '1',
        name: '知识点1',
        path: '数学 > 代数',
        formattedPath: '数学 > 代数'
      })
    })

    it('应该处理空数组', () => {
      expect(batchFormatKnowledgePaths([], {})).toEqual([])
      expect(batchFormatKnowledgePaths(null, {})).toEqual([])
    })
  })

  describe('generateKnowledgePath', () => {
    it('应该生成路径字符串', () => {
      const pathArray = ['根节点', '数学', '代数', '线性代数']
      const result = generateKnowledgePath(pathArray)
      expect(result).toBe('数学 > 代数 > 线性代数')
    })

    it('应该支持自定义分隔符', () => {
      const pathArray = ['根节点', '数学', '代数']
      const result = generateKnowledgePath(pathArray, { separator: ' / ' })
      expect(result).toBe('数学 / 代数')
    })

    it('应该支持不排除根节点', () => {
      const pathArray = ['根节点', '数学', '代数']
      const result = generateKnowledgePath(pathArray, { excludeRoot: false })
      expect(result).toBe('根节点 > 数学 > 代数')
    })
  })

  describe('parsePathString', () => {
    it('应该解析路径字符串为数组', () => {
      const pathString = '数学 > 代数 > 线性代数'
      const result = parsePathString(pathString)
      expect(result).toEqual(['数学', '代数', '线性代数'])
    })

    it('应该处理空字符串', () => {
      expect(parsePathString('')).toEqual([])
      expect(parsePathString(null)).toEqual([])
    })

    it('应该去除空白部分', () => {
      const pathString = '数学 >  > 代数 > '
      const result = parsePathString(pathString)
      expect(result).toEqual(['数学', '代数'])
    })
  })

  describe('getParentPath', () => {
    it('应该获取父级路径', () => {
      const fullPath = '数学 > 代数 > 线性代数'
      const result = getParentPath(fullPath)
      expect(result).toBe('数学 > 代数')
    })

    it('应该处理单级路径', () => {
      expect(getParentPath('数学')).toBe('')
    })

    it('应该处理空路径', () => {
      expect(getParentPath('')).toBe('')
    })
  })

  describe('getLeafName', () => {
    it('应该获取叶子节点名称', () => {
      const fullPath = '数学 > 代数 > 线性代数'
      const result = getLeafName(fullPath)
      expect(result).toBe('线性代数')
    })

    it('应该处理单级路径', () => {
      expect(getLeafName('数学')).toBe('数学')
    })
  })

  describe('getCommonPathPrefix', () => {
    it('应该获取公共前缀', () => {
      const path1 = '数学 > 代数 > 线性代数'
      const path2 = '数学 > 代数 > 抽象代数'
      const result = getCommonPathPrefix(path1, path2)
      expect(result).toBe('数学 > 代数')
    })

    it('应该处理无公共前缀的情况', () => {
      const path1 = '数学 > 代数'
      const path2 = '物理 > 力学'
      const result = getCommonPathPrefix(path1, path2)
      expect(result).toBe('')
    })
  })

  describe('isSubPath', () => {
    it('应该正确判断子路径', () => {
      const childPath = '数学 > 代数 > 线性代数'
      const parentPath = '数学 > 代数'
      expect(isSubPath(childPath, parentPath)).toBe(true)
    })

    it('应该正确判断非子路径', () => {
      const path1 = '数学 > 代数'
      const path2 = '物理 > 力学'
      expect(isSubPath(path1, path2)).toBe(false)
    })

    it('应该处理相同路径', () => {
      const path = '数学 > 代数'
      expect(isSubPath(path, path)).toBe(false)
    })
  })

  describe('getPathDepth', () => {
    it('应该计算路径深度', () => {
      expect(getPathDepth('数学 > 代数 > 线性代数')).toBe(3)
      expect(getPathDepth('数学')).toBe(1)
      expect(getPathDepth('')).toBe(0)
    })
  })

  describe('beautifyPathDisplay', () => {
    it('应该美化长路径显示', () => {
      const longPath = '数学 > 代数 > 线性代数 > 矩阵运算 > 矩阵乘法'
      const result = beautifyPathDisplay(longPath, 15)
      expect(result.display).toBe('数学...矩阵乘法')
      expect(result.tooltip).toBe(longPath)
    })

    it('应该处理短路径', () => {
      const shortPath = '数学 > 代数'
      const result = beautifyPathDisplay(shortPath, 50)
      expect(result.display).toBe(shortPath)
      expect(result.tooltip).toBe(shortPath)
    })
  })

  describe('validatePath', () => {
    it('应该验证有效路径', () => {
      const path = '数学 > 代数 > 线性代数'
      const result = validatePath(path)
      expect(result.valid).toBe(true)
      expect(result.depth).toBe(3)
      expect(result.parts).toEqual(['数学', '代数', '线性代数'])
    })

    it('应该检测无效路径', () => {
      const path = '数学 >  > 代数'
      const result = validatePath(path)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('路径中不能包含空的部分')
    })

    it('应该检查路径深度', () => {
      const path = '数学'
      const result = validatePath(path, { minDepth: 2 })
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('路径深度不能小于2')
    })
  })
})
