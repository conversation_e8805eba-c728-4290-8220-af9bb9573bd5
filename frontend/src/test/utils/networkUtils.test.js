/**
 * networkUtils.js 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  getLocalNetworkIP,
  isPrivateIP,
  generateAccessibleUrl,
  isValidIP,
  getCommonIPRanges,
  getIPDetectionGuide,
  testIPConnectivity
} from '@/utils/networkUtils'

// Mock RTCPeerConnection
global.RTCPeerConnection = vi.fn().mockImplementation(() => ({
  onicecandidate: null,
  createDataChannel: vi.fn(),
  createOffer: vi.fn().mockResolvedValue({}),
  setLocalDescription: vi.fn().mockResolvedValue(),
  close: vi.fn()
}))

describe('networkUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        hostname: 'localhost',
        port: '3000',
        protocol: 'http:',
        origin: 'http://localhost:3000'
      },
      writable: true
    })
  })

  describe('isPrivateIP', () => {
    it('应该识别私有IP地址', () => {
      expect(isPrivateIP('***********00')).toBe(true)
      expect(isPrivateIP('***********')).toBe(true)
      expect(isPrivateIP('********')).toBe(true)
      expect(isPrivateIP('**************')).toBe(true)
      expect(isPrivateIP('**********')).toBe(true)
      expect(isPrivateIP('**************')).toBe(true)
    })

    it('应该识别公网IP地址', () => {
      expect(isPrivateIP('*******')).toBe(false)
      expect(isPrivateIP('***************')).toBe(false)
      expect(isPrivateIP('**************')).toBe(false)
      expect(isPrivateIP('**********')).toBe(false)
    })

    it('应该处理无效IP', () => {
      expect(isPrivateIP('')).toBe(false)
      expect(isPrivateIP(null)).toBe(false)
      expect(isPrivateIP('invalid')).toBe(false)
      expect(isPrivateIP('192.168.1')).toBe(false)
      expect(isPrivateIP('***********.1')).toBe(false)
    })
  })

  describe('isValidIP', () => {
    it('应该验证有效的IP地址', () => {
      expect(isValidIP('***********')).toBe(true)
      expect(isValidIP('0.0.0.0')).toBe(true)
      expect(isValidIP('***************')).toBe(true)
      expect(isValidIP('*******')).toBe(true)
    })

    it('应该拒绝无效的IP地址', () => {
      expect(isValidIP('256.1.1.1')).toBe(false)
      expect(isValidIP('192.168.1')).toBe(false)
      expect(isValidIP('***********.1')).toBe(false)
      expect(isValidIP('abc.def.ghi.jkl')).toBe(false)
      expect(isValidIP('')).toBe(false)
      expect(isValidIP('192.168.-1.1')).toBe(false)
    })
  })

  describe('getCommonIPRanges', () => {
    it('应该返回常见IP段信息', () => {
      const ranges = getCommonIPRanges()
      
      expect(Array.isArray(ranges)).toBe(true)
      expect(ranges.length).toBeGreaterThan(0)
      expect(ranges[0]).toHaveProperty('range')
      expect(ranges[0]).toHaveProperty('description')
      expect(ranges.some(r => r.range.includes('192.168.1.x'))).toBe(true)
    })
  })

  describe('getIPDetectionGuide', () => {
    it('应该为Windows返回正确的指导', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        writable: true
      })
      
      const guide = getIPDetectionGuide()
      expect(guide.os).toBe('Windows')
      expect(Array.isArray(guide.steps)).toBe(true)
      expect(guide.steps.some(step => step.includes('ipconfig'))).toBe(true)
    })

    it('应该为macOS返回正确的指导', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        writable: true
      })
      
      const guide = getIPDetectionGuide()
      expect(guide.os).toBe('macOS')
      expect(Array.isArray(guide.steps)).toBe(true)
      expect(guide.steps.some(step => step.includes('系统偏好设置'))).toBe(true)
    })

    it('应该为Linux返回正确的指导', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (X11; Linux x86_64)',
        writable: true
      })
      
      const guide = getIPDetectionGuide()
      expect(guide.os).toBe('Linux')
      expect(Array.isArray(guide.steps)).toBe(true)
      expect(guide.steps.some(step => step.includes('ifconfig'))).toBe(true)
    })

    it('应该为未知系统返回通用指导', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Unknown System)',
        writable: true
      })
      
      const guide = getIPDetectionGuide()
      expect(guide.os).toBe('通用')
      expect(Array.isArray(guide.steps)).toBe(true)
    })
  })

  describe('generateAccessibleUrl', () => {
    it('应该保持非localhost的hostname', async () => {
      window.location.hostname = 'example.com'
      window.location.origin = 'https://example.com'
      
      const url = await generateAccessibleUrl()
      expect(url).toBe('https://example.com')
    })

    it('应该为localhost尝试获取局域网IP', async () => {
      window.location.hostname = 'localhost'
      window.location.port = '3000'
      window.location.protocol = 'http:'
      
      // Mock getLocalNetworkIP 返回null
      const url = await generateAccessibleUrl()
      expect(url).toContain('[请替换为您的局域网IP]')
      expect(url).toContain(':3000')
    })
  })

  describe('testIPConnectivity', () => {
    it('应该测试IP连通性', async () => {
      // Mock Image constructor
      const mockImage = {
        onload: null,
        onerror: null,
        src: ''
      }
      
      global.Image = vi.fn().mockImplementation(() => mockImage)
      
      // 模拟成功连接
      const connectivityPromise = testIPConnectivity('***********00', '3000')
      
      // 触发onload
      setTimeout(() => {
        if (mockImage.onload) {
          mockImage.onload()
        }
      }, 100)
      
      const result = await connectivityPromise
      expect(result).toBe(true)
    })

    it('应该处理连接失败', async () => {
      const mockImage = {
        onload: null,
        onerror: null,
        src: ''
      }
      
      global.Image = vi.fn().mockImplementation(() => mockImage)
      
      const connectivityPromise = testIPConnectivity('***********00', '3000')
      
      // 触发onerror
      setTimeout(() => {
        if (mockImage.onerror) {
          mockImage.onerror()
        }
      }, 100)
      
      const result = await connectivityPromise
      expect(result).toBe(false)
    })

    it('应该处理超时', async () => {
      const mockImage = {
        onload: null,
        onerror: null,
        src: ''
      }
      
      global.Image = vi.fn().mockImplementation(() => mockImage)
      
      // 不触发任何事件，让它超时
      const result = await testIPConnectivity('***********00', '3000')
      expect(result).toBe(false)
    }, 4000)
  })

  describe('getLocalNetworkIP', () => {
    it('应该在WebRTC失败时返回null', async () => {
      // Mock WebRTC失败
      global.RTCPeerConnection = vi.fn().mockImplementation(() => {
        throw new Error('WebRTC not supported')
      })
      
      const ip = await getLocalNetworkIP()
      expect(ip).toBeNull()
    })

    it('应该处理WebRTC超时', async () => {
      // Mock WebRTC但不触发onicecandidate
      global.RTCPeerConnection = vi.fn().mockImplementation(() => ({
        onicecandidate: null,
        createDataChannel: vi.fn(),
        createOffer: vi.fn().mockResolvedValue({}),
        setLocalDescription: vi.fn().mockResolvedValue(),
        close: vi.fn()
      }))
      
      const ip = await getLocalNetworkIP()
      expect(ip).toBeNull()
    })
  })

  describe('边界情况', () => {
    it('应该处理空值和null', () => {
      expect(isPrivateIP(null)).toBe(false)
      expect(isPrivateIP(undefined)).toBe(false)
      expect(isValidIP(null)).toBe(false)
      expect(isValidIP(undefined)).toBe(false)
    })

    it('应该处理异常情况', async () => {
      // 测试没有端口的情况
      const result = await testIPConnectivity('***********')
      expect(typeof result).toBe('boolean')
    })
  })
})
