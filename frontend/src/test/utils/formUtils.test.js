/**
 * formUtils.js 单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  createFormState,
  safeSubmit,
  commonRules,
  createRules,
  detectFormChanges,
  formatters,
  formatFormData
} from '@/utils/formUtils'

describe('formUtils', () => {
  describe('createFormState', () => {
    it('应该创建表单状态管理对象', () => {
      const initialData = { name: '', age: 0 }
      const rules = { name: [{ required: true }] }
      
      const formState = createFormState(initialData, rules)
      
      expect(formState.formData).toEqual(initialData)
      expect(formState.rules).toEqual(rules)
      expect(formState.submitting.value).toBe(false)
      expect(formState.validating.value).toBe(false)
      expect(typeof formState.resetForm).toBe('function')
      expect(typeof formState.validateForm).toBe('function')
    })

    it('应该重置表单到初始状态', () => {
      const initialData = { name: '<PERSON>', age: 25 }
      const formState = createFormState(initialData)
      
      // 修改表单数据
      formState.formData.name = 'Jane'
      formState.formData.age = 30
      
      // 重置表单
      formState.resetForm()
      
      expect(formState.formData.name).toBe('John')
      expect(formState.formData.age).toBe(25)
    })
  })

  describe('formatters', () => {
    it('应该移除空白字符', () => {
      expect(formatters.trim('  hello  ')).toBe('hello')
      expect(formatters.trim(123)).toBe(123)
      expect(formatters.trim('')).toBe('')
    })

    it('应该转换为数字', () => {
      expect(formatters.toNumber('123')).toBe(123)
      expect(formatters.toNumber('123.45')).toBe(123.45)
      expect(formatters.toNumber('abc')).toBe('abc')
    })

    it('应该移除HTML标签', () => {
      expect(formatters.stripHtml('<p>Hello <b>World</b></p>')).toBe('Hello World')
      expect(formatters.stripHtml('Plain text')).toBe('Plain text')
      expect(formatters.stripHtml(123)).toBe(123)
    })
  })

  describe('formatFormData', () => {
    it('应该批量格式化表单数据', () => {
      const data = {
        name: '  John  ',
        age: '25',
        description: '<p>Test</p>'
      }
      
      const formatConfig = {
        name: 'trim',
        age: 'toNumber',
        description: 'stripHtml'
      }
      
      const result = formatFormData(data, formatConfig)
      
      expect(result).toEqual({
        name: 'John',
        age: 25,
        description: 'Test'
      })
    })

    it('应该支持自定义格式化函数', () => {
      const data = { email: '<EMAIL>' }
      const formatConfig = {
        email: (value) => value.toLowerCase()
      }
      
      const result = formatFormData(data, formatConfig)
      
      expect(result.email).toBe('<EMAIL>')
    })
  })

  describe('detectFormChanges', () => {
    it('应该检测表单数据变化', () => {
      const original = { name: 'John', age: 25 }
      const current = { name: 'Jane', age: 25 }
      
      const result = detectFormChanges(original, current)
      
      expect(result.hasChanges).toBe(true)
      expect(result.changes).toEqual({
        name: { from: 'John', to: 'Jane' }
      })
    })

    it('应该检测没有变化的情况', () => {
      const original = { name: 'John', age: 25 }
      const current = { name: 'John', age: 25 }
      
      const result = detectFormChanges(original, current)
      
      expect(result.hasChanges).toBe(false)
      expect(result.changes).toEqual({})
    })
  })
})
