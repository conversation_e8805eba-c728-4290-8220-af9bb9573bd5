/**
 * routeUtils.js 单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { computed } from 'vue'
import {
  createSafeRouteParam,
  createRouteParams,
  validateRouteParams,
  ProjectNavigator,
  createProjectNavigator,
  safeNavigate
} from '@/utils/routeUtils'

// Mock vue-router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn()
}

const mockRoute = {
  params: {
    projectId: '123',
    examId: '456',
    invalidId: 'invalid',
    emptyId: ''
  }
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRoute
}))

describe('routeUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createSafeRouteParam', () => {
    it('应该创建安全的路由参数计算属性', () => {
      const projectId = createSafeRouteParam(mockRoute, 'projectId')
      expect(projectId.value).toBe(123) // 转换为数字
    })

    it('应该处理无效的ID参数', () => {
      const invalidId = createSafeRouteParam(mockRoute, 'invalidId')
      expect(invalidId.value).toBeNull()
    })

    it('应该支持非必需参数', () => {
      const optionalParam = createSafeRouteParam(mockRoute, 'nonExistent', {
        required: false,
        fallback: 'default'
      })
      expect(optionalParam.value).toBe('default')
    })

    it('应该支持自定义回退值', () => {
      const paramWithFallback = createSafeRouteParam(mockRoute, 'nonExistent', {
        fallback: 999
      })
      expect(paramWithFallback.value).toBe(999)
    })

    it('应该处理非ID参数', () => {
      const mockRouteWithName = {
        params: { name: 'test-name' }
      }
      const nameParam = createSafeRouteParam(mockRouteWithName, 'name')
      expect(nameParam.value).toBe('test-name')
    })

    it('应该处理空字符串参数', () => {
      const emptyParam = createSafeRouteParam(mockRoute, 'emptyId')
      expect(emptyParam.value).toBeNull()
    })
  })

  describe('createRouteParams', () => {
    it('应该批量创建路由参数（数组格式）', () => {
      const params = createRouteParams(mockRoute, ['projectId', 'examId'])
      
      expect(params.projectId.value).toBe(123)
      expect(params.examId.value).toBe(456)
    })

    it('应该批量创建路由参数（对象格式）', () => {
      const config = {
        projectId: { required: true },
        examId: { required: false, fallback: null },
        optionalParam: { required: false, fallback: 'default' }
      }
      
      const params = createRouteParams(mockRoute, config)
      
      expect(params.projectId.value).toBe(123)
      expect(params.examId.value).toBe(456)
      expect(params.optionalParam.value).toBe('default')
    })

    it('应该处理混合配置', () => {
      const config = {
        projectId: {},
        nonExistent: { required: false, fallback: 'fallback-value' }
      }
      
      const params = createRouteParams(mockRoute, config)
      
      expect(params.projectId.value).toBe(123)
      expect(params.nonExistent.value).toBe('fallback-value')
    })
  })

  describe('validateRouteParams', () => {
    it('应该验证所有必需参数', () => {
      const params = {
        projectId: computed(() => 123),
        examId: computed(() => 456)
      }
      
      const result = validateRouteParams(params, ['projectId', 'examId'])
      
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.validParams).toEqual({
        projectId: 123,
        examId: 456
      })
    })

    it('应该检测缺失的参数', () => {
      const params = {}  // 完全空的参数对象，缺少所有必需参数
      
      const result = validateRouteParams(params, ['projectId', 'examId'])
      
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('缺少必需的参数: projectId')
      expect(result.errors).toContain('缺少必需的参数: examId')
      expect(result.validParams).toEqual({})
    })

    it('应该处理普通值（非计算属性）', () => {
      const params = {
        projectId: 123,
        examId: 456
      }
      
      const result = validateRouteParams(params, ['projectId', 'examId'])
      
      expect(result.valid).toBe(true)
      expect(result.validParams).toEqual({
        projectId: 123,
        examId: 456
      })
    })

    it('应该处理空的必需参数列表', () => {
      const params = { projectId: 123 }
      const result = validateRouteParams(params, [])
      
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('ProjectNavigator', () => {
    let navigator

    beforeEach(() => {
      navigator = new ProjectNavigator(mockRouter, 123)
    })

    it('应该创建项目导航器', () => {
      expect(navigator.router).toBe(mockRouter)
      expect(navigator.projectId).toBe(123)
    })

    it('应该导航到项目详情页', () => {
      navigator.goToDetail()
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123')
    })

    it('应该导航到知识点管理页', () => {
      navigator.goToKnowledge()
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123/knowledge')
    })

    it('应该导航到考试列表页', () => {
      navigator.goToExams()
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123/exams')
    })

    it('应该导航到卡片管理页', () => {
      navigator.goToFlashcards()
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123/flashcards')
    })

    it('应该导航到错题管理页', () => {
      navigator.goToWrongQuestions()
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123/wrong-questions')
    })

    it('应该导航到考试分析页', () => {
      navigator.goToExamAnalysis()
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123/exam-analysis')
    })

    it('应该导航到复习配置页', () => {
      navigator.goToReviewConfig()
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123/review-configuration')
    })

    it('应该导航到考试记录页', () => {
      navigator.goToExamRecords(456)
      expect(mockRouter.push).toHaveBeenCalledWith('/projects/123/exams/456/records')
    })

    it('应该导航到移动端学习页', () => {
      navigator.goToMobileStudy()
      expect(mockRouter.push).toHaveBeenCalledWith('/mobile/study/123')
    })
  })

  describe('createProjectNavigator', () => {
    it('应该创建项目导航器（普通值）', () => {
      const navigator = createProjectNavigator(mockRouter, 123)
      expect(navigator.projectId).toBe(123)
    })

    it('应该创建项目导航器（计算属性）', () => {
      const projectIdRef = computed(() => 456)
      const navigator = createProjectNavigator(mockRouter, projectIdRef)
      expect(navigator.projectId).toBe(456)
    })
  })

  describe('safeNavigate', () => {
    it('应该安全地导航到指定路径', async () => {
      mockRouter.push.mockResolvedValue()
      
      await safeNavigate(mockRouter, '/test-path')
      
      expect(mockRouter.push).toHaveBeenCalledWith({
        path: '/test-path',
        params: {},
        query: {}
      })
    })

    it('应该支持replace模式', async () => {
      mockRouter.replace.mockResolvedValue()
      
      await safeNavigate(mockRouter, '/test-path', { replace: true })
      
      expect(mockRouter.replace).toHaveBeenCalledWith({
        path: '/test-path',
        params: {},
        query: {}
      })
    })

    it('应该传递参数和查询', async () => {
      mockRouter.push.mockResolvedValue()
      
      await safeNavigate(mockRouter, '/test-path', {
        params: { id: 123 },
        query: { page: 1 }
      })
      
      expect(mockRouter.push).toHaveBeenCalledWith({
        path: '/test-path',
        params: { id: 123 },
        query: { page: 1 }
      })
    })

    it('应该处理导航错误', async () => {
      const error = new Error('Navigation failed')
      mockRouter.push.mockRejectedValue(error)
      
      const onError = vi.fn()
      console.error = vi.fn()
      
      await safeNavigate(mockRouter, '/test-path', { onError })
      
      expect(console.error).toHaveBeenCalledWith('❌ 路由跳转失败:', error)
      expect(onError).toHaveBeenCalledWith(error)
    })

    it('应该在没有错误处理器时静默处理错误', async () => {
      const error = new Error('Navigation failed')
      mockRouter.push.mockRejectedValue(error)
      
      console.error = vi.fn()
      
      await expect(safeNavigate(mockRouter, '/test-path')).resolves.toBeUndefined()
      expect(console.error).toHaveBeenCalledWith('❌ 路由跳转失败:', error)
    })
  })

  describe('边界情况处理', () => {
    it('应该处理null/undefined项目ID', () => {
      const navigatorNull = new ProjectNavigator(mockRouter, null)
      const navigatorUndef = new ProjectNavigator(mockRouter, undefined)
      
      expect(navigatorNull.projectId).toBeNull()
      expect(navigatorUndef.projectId).toBeUndefined()
    })

    it('应该处理空路径导航', async () => {
      mockRouter.push.mockResolvedValue()
      
      await safeNavigate(mockRouter, '')
      
      expect(mockRouter.push).toHaveBeenCalledWith({
        path: '',
        params: {},
        query: {}
      })
    })

    it('应该处理无效的路由参数', () => {
      const mockRouteInvalid = {
        params: {
          invalidParam: undefined,
          nullParam: null
        }
      }
      
      const param1 = createSafeRouteParam(mockRouteInvalid, 'invalidParam')
      const param2 = createSafeRouteParam(mockRouteInvalid, 'nullParam')
      
      expect(param1.value).toBeNull()
      expect(param2.value).toBeNull()
    })
  })
}) 