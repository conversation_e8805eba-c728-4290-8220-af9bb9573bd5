/**
 * scoreUtils.js 单元测试
 */

import { describe, it, expect } from 'vitest'
import {
  getScoreTagType,
  getScoreColors,
  getScoreIcon,
  getScoreClassName
} from '@/utils/scoreUtils'

describe('scoreUtils', () => {
  describe('getScoreTagType', () => {
    it('应该返回正确的标签类型', () => {
      expect(getScoreTagType(1)).toBe('danger')   // 红色
      expect(getScoreTagType(2)).toBe('warning')  // 黄色
      expect(getScoreTagType(3)).toBe('')         // 白色（默认）
      expect(getScoreTagType(4)).toBe('info')     // 蓝色
      expect(getScoreTagType(5)).toBe('success')  // 绿色
    })

    it('应该处理无效分数', () => {
      expect(getScoreTagType(0)).toBe('')         // 默认
      expect(getScoreTagType(6)).toBe('')         // 默认
      expect(getScoreTagType(-1)).toBe('')        // 默认
      expect(getScoreTagType(null)).toBe('')      // 默认
      expect(getScoreTagType(undefined)).toBe('')  // 默认
    })

    it('应该处理边界值', () => {
      expect(getScoreTagType(1)).toBe('danger')
      expect(getScoreTagType(5)).toBe('success')
    })
  })

  describe('getScoreColors', () => {
    it('应该返回正确的颜色配置', () => {
      const colors1 = getScoreColors(1)
      expect(colors1.color).toBe('#ff4d4f')
      expect(colors1.backgroundColor).toBe('#fff2f0')
      expect(colors1.borderColor).toBe('#ffccc7')

      const colors2 = getScoreColors(2)
      expect(colors2.color).toBe('#faad14')
      expect(colors2.backgroundColor).toBe('#fffbe6')
      expect(colors2.borderColor).toBe('#ffe58f')

      const colors3 = getScoreColors(3)
      expect(colors3.color).toBe('#666666')
      expect(colors3.backgroundColor).toBe('#ffffff')
      expect(colors3.borderColor).toBe('#d9d9d9')

      const colors4 = getScoreColors(4)
      expect(colors4.color).toBe('#1890ff')
      expect(colors4.backgroundColor).toBe('#e6f7ff')
      expect(colors4.borderColor).toBe('#91d5ff')

      const colors5 = getScoreColors(5)
      expect(colors5.color).toBe('#52c41a')
      expect(colors5.backgroundColor).toBe('#f6ffed')
      expect(colors5.borderColor).toBe('#b7eb8f')
    })

    it('应该返回默认颜色配置对于无效分数', () => {
      const defaultColors = getScoreColors(0)
      expect(defaultColors.color).toBe('#666666')
      expect(defaultColors.backgroundColor).toBe('#ffffff')
      expect(defaultColors.borderColor).toBe('#d9d9d9')

      const invalidColors = getScoreColors(6)
      expect(invalidColors.color).toBe('#666666')
      expect(invalidColors.backgroundColor).toBe('#ffffff')
      expect(invalidColors.borderColor).toBe('#d9d9d9')
    })

    it('应该处理null和undefined', () => {
      const nullColors = getScoreColors(null)
      expect(nullColors.color).toBe('#666666')

      const undefinedColors = getScoreColors(undefined)
      expect(undefinedColors.color).toBe('#666666')
    })
  })

  describe('getScoreIcon', () => {
    it('应该返回正确的图标', () => {
      expect(getScoreIcon(1)).toBe('🔴')  // 红色圆圈
      expect(getScoreIcon(2)).toBe('🟡')  // 黄色圆圈
      expect(getScoreIcon(3)).toBe('⚪')  // 白色圆圈
      expect(getScoreIcon(4)).toBe('🔵')  // 蓝色圆圈
      expect(getScoreIcon(5)).toBe('🟢')  // 绿色圆圈
    })

    it('应该返回默认图标对于无效分数', () => {
      expect(getScoreIcon(0)).toBe('⚪')   // 默认白色圆圈
      expect(getScoreIcon(6)).toBe('⚪')   // 默认白色圆圈
      expect(getScoreIcon(-1)).toBe('⚪')  // 默认白色圆圈
      expect(getScoreIcon(null)).toBe('⚪')
      expect(getScoreIcon(undefined)).toBe('⚪')
    })

    it('应该处理边界值', () => {
      expect(getScoreIcon(1)).toBe('🔴')
      expect(getScoreIcon(5)).toBe('🟢')
    })
  })

  describe('getScoreClassName', () => {
    it('应该返回正确的CSS类名', () => {
      expect(getScoreClassName(1)).toBe('score-tag-1')
      expect(getScoreClassName(2)).toBe('score-tag-2')
      expect(getScoreClassName(3)).toBe('score-tag-3')
      expect(getScoreClassName(4)).toBe('score-tag-4')
      expect(getScoreClassName(5)).toBe('score-tag-5')
    })

    it('应该处理无效分数', () => {
      expect(getScoreClassName(null)).toBe('score-tag-default')
      expect(getScoreClassName(undefined)).toBe('score-tag-default')
      expect(getScoreClassName(0)).toBe('score-tag-0')
      expect(getScoreClassName(6)).toBe('score-tag-6')
      expect(getScoreClassName(-1)).toBe('score-tag--1')
    })

    it('应该处理字符串类型的分数', () => {
      expect(getScoreClassName('1')).toBe('score-tag-1')
      expect(getScoreClassName('5')).toBe('score-tag-5')
    })

    it('应该处理边界值', () => {
      expect(getScoreClassName(1)).toBe('score-tag-1')
      expect(getScoreClassName(5)).toBe('score-tag-5')
    })
  })

  describe('综合测试', () => {
    it('应该为相同分数返回一致的样式', () => {
      const score = 4
      const tagType = getScoreTagType(score)
      const colors = getScoreColors(score)
      const icon = getScoreIcon(score)
      const className = getScoreClassName(score)

      expect(tagType).toBe('info')
      expect(colors.color).toBe('#1890ff')
      expect(icon).toBe('🔵')
      expect(className).toBe('score-tag-4')
    })

    it('应该处理所有有效分数范围', () => {
      for (let score = 1; score <= 5; score++) {
        expect(getScoreTagType(score)).toBeDefined()
        expect(getScoreColors(score)).toHaveProperty('color')
        expect(getScoreIcon(score)).toBeDefined()
        expect(getScoreClassName(score)).toBeDefined()
      }
    })

    it('应该一致地处理无效输入', () => {
      const invalidInputs = [null, undefined, 0, 6, -1, 'invalid', {}, []]
      
      invalidInputs.forEach(input => {
        expect(getScoreTagType(input)).toBeDefined()
        expect(getScoreColors(input)).toHaveProperty('color')
        expect(getScoreIcon(input)).toBeDefined()
        expect(getScoreClassName(input)).toBeDefined()
      })
    })
  })
}) 