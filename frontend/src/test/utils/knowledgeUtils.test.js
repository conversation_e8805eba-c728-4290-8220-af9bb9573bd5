/**
 * knowledgeUtils.js 单元测试
 */

import { describe, it, expect } from 'vitest'
import {
  flattenKnowledgeTree,
  extractLeafNodes,
  filterKnowledgeByScore,
  calculateScoreDistribution,
  batchSetScore,
  clearAllScores,
  validateKnowledgeData,
  searchKnowledgePoints,
  exportKnowledgeToJson,
  importKnowledgeFromJson
} from '@/utils/knowledgeUtils'

const mockKnowledgeTree = [
  {
    name: '数学',
    uid: 'math',
    description: '数学基础',
    children: [
      {
        name: '代数',
        uid: 'algebra',
        description: '代数学',
        score: 4,
        children: [
          { name: '线性代数', uid: 'linear', description: '线性代数基础', score: 3 },
          { name: '高等代数', uid: 'advanced', description: '高等代数', score: 5 }
        ]
      },
      {
        name: '几何',
        uid: 'geometry',
        description: '几何学',
        score: 3,
        children: [
          { name: '平面几何', uid: 'plane', description: '平面几何基础', score: 4 }
        ]
      }
    ]
  }
]

describe('knowledgeUtils', () => {
  describe('flattenKnowledgeTree', () => {
    it('应该扁平化知识点树结构', () => {
      const result = flattenKnowledgeTree(mockKnowledgeTree)
      
      expect(result).toHaveLength(3) // 只包含叶子节点
      expect(result[0].name).toBe('线性代数')
      expect(result[0].fullPath).toBe('代数 > 线性代数')
      expect(result[0].score).toBe(3)
      expect(result[0].isParent).toBe(false)
    })

    it('应该包含父节点', () => {
      const result = flattenKnowledgeTree(mockKnowledgeTree, { includeParents: true })
      
      expect(result.length).toBeGreaterThan(3)
      const parentNodes = result.filter(item => item.isParent)
      expect(parentNodes.length).toBeGreaterThan(0)
    })

    it('应该处理空数组', () => {
      const result = flattenKnowledgeTree([])
      expect(result).toEqual([])
    })

    it('应该使用默认分数', () => {
      const treeWithoutScores = [
        {
          name: '测试',
          uid: 'test',
          children: [
            { name: '子测试', uid: 'subtest' }
          ]
        }
      ]
      
      const result = flattenKnowledgeTree(treeWithoutScores, { defaultScore: 2 })
      expect(result[0].score).toBe(2)
    })
  })

  describe('extractLeafNodes', () => {
    it('应该只提取叶子节点', () => {
      const result = extractLeafNodes(mockKnowledgeTree)
      
      expect(result).toHaveLength(3)
      expect(result.every(node => !node.isParent)).toBe(true)
      expect(result.map(node => node.name)).toEqual(['线性代数', '高等代数', '平面几何'])
    })
  })

  describe('filterKnowledgeByScore', () => {
    const knowledgePoints = [
      { name: '点1', score: 3 },
      { name: '点2', score: 4 },
      { name: '点3', score: 5 },
      { name: '点4', score: 3 }
    ]

    it('应该按分数筛选知识点', () => {
      const result = filterKnowledgeByScore(knowledgePoints, [3, 5])
      
      expect(result).toHaveLength(3)
      expect(result.map(p => p.name)).toEqual(['点1', '点3', '点4'])
    })

    it('应该在没有筛选器时返回所有知识点', () => {
      const result = filterKnowledgeByScore(knowledgePoints, [])
      expect(result).toEqual(knowledgePoints)
      
      const result2 = filterKnowledgeByScore(knowledgePoints, null)
      expect(result2).toEqual(knowledgePoints)
    })
  })

  describe('calculateScoreDistribution', () => {
    const knowledgePoints = [
      { name: '点1', score: 3 },
      { name: '点2', score: 4 },
      { name: '点3', score: 3 },
      { name: '点4', score: 5 },
      { name: '点5' } // 无分数
    ]

    it('应该计算分数分布', () => {
      const result = calculateScoreDistribution(knowledgePoints)
      
      expect(result.distribution).toEqual({
        3: 2,
        4: 1,
        5: 1
      })
      expect(result.unscored).toBe(1)
    })

    it('应该处理空数组', () => {
      const result = calculateScoreDistribution([])
      expect(result.distribution).toEqual({})
      expect(result.unscored).toBe(0)
    })
  })

  describe('batchSetScore', () => {
    it('应该批量设置知识点分数', () => {
      const knowledgePoints = [
        { name: '点1', score: 3 },
        { name: '点2', score: 4 }
      ]
      
      const result = batchSetScore(knowledgePoints, 5)
      
      expect(result).toHaveLength(2)
      expect(result[0].score).toBe(5)
      expect(result[1].score).toBe(5)
      expect(result[0].name).toBe('点1') // 保持其他属性
    })
  })

  describe('clearAllScores', () => {
    it('应该清空所有分数', () => {
      const knowledgePoints = [
        { name: '点1', score: 3, description: '描述1' },
        { name: '点2', score: 4, description: '描述2' }
      ]
      
      const result = clearAllScores(knowledgePoints)
      
      expect(result).toHaveLength(2)
      expect(result[0]).not.toHaveProperty('score')
      expect(result[1]).not.toHaveProperty('score')
      expect(result[0].name).toBe('点1') // 保持其他属性
      expect(result[0].description).toBe('描述1')
    })
  })

  describe('validateKnowledgeData', () => {
    it('应该验证有效的知识点数据', () => {
      const validData = [
        {
          name: '数学',
          score: 4,
          children: [
            { name: '代数', score: 3 }
          ]
        }
      ]
      
      const result = validateKnowledgeData(validData)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
    })

    it('应该检测无效的数据格式', () => {
      const result = validateKnowledgeData('not an array')
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('知识点数据必须是数组格式')
    })

    it('应该检测缺少名称的节点', () => {
      const invalidData = [{ score: 3 }]
      const result = validateKnowledgeData(invalidData)
      expect(result.valid).toBe(false)
      expect(result.errors.some(error => error.includes('缺少有效的名称'))).toBe(true)
    })

    it('应该检测无效的分数', () => {
      const invalidData = [{ name: '测试', score: 6 }]
      const result = validateKnowledgeData(invalidData)
      expect(result.valid).toBe(false)
      expect(result.errors.some(error => error.includes('分数必须是1-5之间的数字'))).toBe(true)
    })
  })

  describe('searchKnowledgePoints', () => {
    const knowledgePoints = [
      { name: '线性代数', description: '数学基础', fullPath: '数学 > 代数 > 线性代数' },
      { name: '平面几何', description: '几何学习', fullPath: '数学 > 几何 > 平面几何' },
      { name: '物理力学', description: '力学原理', fullPath: '物理 > 力学' }
    ]

    it('应该按关键词搜索知识点', () => {
      const result = searchKnowledgePoints(knowledgePoints, '代数')
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('线性代数')
    })

    it('应该在多个字段中搜索', () => {
      const result = searchKnowledgePoints(knowledgePoints, '几何')
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('平面几何')
    })

    it('应该支持大小写不敏感搜索', () => {
      const result = searchKnowledgePoints(knowledgePoints, 'MATH', { 
        searchFields: ['fullPath'],
        caseSensitive: false 
      })
      expect(result).toHaveLength(0) // 'MATH'不在中文路径中
      
      const result2 = searchKnowledgePoints(knowledgePoints, '数学')
      expect(result2).toHaveLength(2)
    })

    it('应该在空关键词时返回所有结果', () => {
      const result = searchKnowledgePoints(knowledgePoints, '')
      expect(result).toEqual(knowledgePoints)
      
      const result2 = searchKnowledgePoints(knowledgePoints, '   ')
      expect(result2).toEqual(knowledgePoints)
    })
  })

  describe('exportKnowledgeToJson', () => {
    const knowledgePoints = [
      { name: '数学', score: 4, uid: 'math', isParent: true },
      { name: '代数', score: 3, uid: 'algebra', isParent: false }
    ]

    it('应该导出为JSON格式', () => {
      const result = exportKnowledgeToJson(knowledgePoints)
      const parsed = JSON.parse(result)
      
      expect(Array.isArray(parsed)).toBe(true)
      expect(parsed).toHaveLength(2)
    })

    it('应该移除元数据', () => {
      const result = exportKnowledgeToJson(knowledgePoints, { includeMetadata: false })
      const parsed = JSON.parse(result)
      
      expect(parsed[0]).not.toHaveProperty('uid')
      expect(parsed[0]).not.toHaveProperty('isParent')
      expect(parsed[0]).toHaveProperty('name')
      expect(parsed[0]).toHaveProperty('score')
    })

    it('应该保留元数据', () => {
      const result = exportKnowledgeToJson(knowledgePoints, { includeMetadata: true })
      const parsed = JSON.parse(result)
      
      expect(parsed[0]).toHaveProperty('uid')
      expect(parsed[0]).toHaveProperty('isParent')
    })
  })

  describe('importKnowledgeFromJson', () => {
    it('应该成功导入有效的JSON', () => {
      const validJson = JSON.stringify([
        { name: '数学', score: 4 },
        { name: '物理', score: 3 }
      ])
      
      const result = importKnowledgeFromJson(validJson)
      
      expect(result.success).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.data).toHaveLength(2)
    })

    it('应该处理无效的JSON', () => {
      const result = importKnowledgeFromJson('invalid json')
      
      expect(result.success).toBe(false)
      expect(result.errors.some(error => error.includes('JSON解析失败'))).toBe(true)
      expect(result.data).toBeNull()
    })

    it('应该验证导入的数据结构', () => {
      const invalidDataJson = JSON.stringify([{ score: 4 }]) // 缺少name
      
      const result = importKnowledgeFromJson(invalidDataJson)
      
      expect(result.success).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.data).toBeNull()
    })
  })

  describe('边界情况', () => {
    it('应该处理undefined和null参数', () => {
      expect(flattenKnowledgeTree(null)).toEqual([])
      expect(flattenKnowledgeTree(undefined)).toEqual([])
      expect(filterKnowledgeByScore([], null)).toEqual([])
      expect(searchKnowledgePoints([], null)).toEqual([])
    })

    it('应该处理空的children数组', () => {
      const treeWithEmptyChildren = [
        { name: '测试', children: [] }
      ]
      
      const result = flattenKnowledgeTree(treeWithEmptyChildren)
      // 有空children的节点应该被作为叶子节点处理
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('测试')
    })
  })
})
