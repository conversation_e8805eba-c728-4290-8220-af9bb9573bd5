/**
 * apiUtils 工具函数测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { executeWithLoading, formatApiResponse, retryRequest, createRequestConfig, handleApiError } from '@/utils/apiUtils'

// Mock useLoading
const mockLoading = {
  value: false
}

const mockExecute = vi.fn()

vi.mock('@/composables/useLoading', () => ({
  useLoading: () => ({
    loading: mockLoading,
    execute: mockExecute
  })
}))

// Mock ElMessage
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn()
  }
}))

describe('apiUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLoading.value = false
  })

  describe('executeWithLoading', () => {
    it('应该执行异步函数并管理loading状态', async () => {
      const asyncFn = vi.fn().mockResolvedValue('success result')
      const options = {
        loadingRef: mockLoading,
        successMessage: '操作成功',
        errorMessage: '操作失败'
      }

      const result = await executeWithLoading(asyncFn, options)

      expect(asyncFn).toHaveBeenCalled()
      expect(result).toBe('success result')
    })

    it('应该处理异步函数错误', async () => {
      const error = new Error('Test error')
      const asyncFn = vi.fn().mockRejectedValue(error)
      const options = {
        loadingRef: mockLoading,
        errorMessage: '操作失败'
      }

      await expect(executeWithLoading(asyncFn, options)).rejects.toThrow('Test error')
      expect(asyncFn).toHaveBeenCalled()
    })

    it('应该支持静默模式', async () => {
      const error = new Error('Test error')
      const asyncFn = vi.fn().mockRejectedValue(error)
      const options = {
        loadingRef: mockLoading,
        silent: true
      }

      await expect(executeWithLoading(asyncFn, options)).rejects.toThrow('Test error')
      expect(asyncFn).toHaveBeenCalled()
    })

    it('应该使用默认选项', async () => {
      const asyncFn = vi.fn().mockResolvedValue('result')

      const result = await executeWithLoading(asyncFn)

      expect(result).toBe('result')
    })
  })

  describe('formatApiResponse', () => {
    it('应该格式化成功的API响应', () => {
      const response = {
        data: {
          success: true,
          message: 'Success',
          data: { id: 1, name: 'Test' }
        }
      }

      const result = formatApiResponse(response)

      expect(result).toEqual({
        success: true,
        message: 'Success',
        data: { id: 1, name: 'Test' }
      })
    })

    it('应该格式化失败的API响应', () => {
      const response = {
        data: {
          success: false,
          message: 'Bad Request',
          data: null
        }
      }

      const result = formatApiResponse(response)

      expect(result.success).toBe(false)
      expect(result.message).toBe('Bad Request')
      expect(result.data).toEqual({ data: null, message: 'Bad Request', success: false })
    })

    it('应该处理没有标准格式的响应', () => {
      const response = {
        data: { result: 'some data' }
      }

      const result = formatApiResponse(response)

      expect(result).toEqual({
        success: true,
        message: '操作成功',
        data: { result: 'some data' }
      })
    })

    it('应该处理空响应', () => {
      const result = formatApiResponse(null)

      expect(result).toEqual({
        success: false,
        message: '响应为空',
        data: null
      })
    })
  })

  describe('retryRequest', () => {
    it('应该在第一次尝试成功时返回结果', async () => {
      const requestFn = vi.fn().mockResolvedValue('success')

      const result = await retryRequest(requestFn, { maxRetries: 3 })

      expect(result).toBe('success')
      expect(requestFn).toHaveBeenCalledTimes(1)
    })

    it('应该在失败后重试', async () => {
      const requestFn = vi.fn()
        .mockRejectedValueOnce(new Error('First attempt failed'))
        .mockRejectedValueOnce(new Error('Second attempt failed'))
        .mockResolvedValue('success on third attempt')

      const result = await retryRequest(requestFn, { maxRetries: 3, retryDelay: 10 })

      expect(result).toBe('success on third attempt')
      expect(requestFn).toHaveBeenCalledTimes(3)
    })

    it('应该在达到最大重试次数后抛出错误', async () => {
      const error = new Error('Always fails')
      const requestFn = vi.fn().mockRejectedValue(error)

      await expect(
        retryRequest(requestFn, { maxRetries: 2, retryDelay: 10 })
      ).rejects.toThrow('Always fails')

      expect(requestFn).toHaveBeenCalledTimes(3) // 1 initial + 2 retries
    })

    it('应该使用默认选项', async () => {
      const requestFn = vi.fn().mockResolvedValue('success')

      const result = await retryRequest(requestFn)

      expect(result).toBe('success')
      expect(requestFn).toHaveBeenCalledTimes(1)
    })

    it('应该应用重试条件', async () => {
      const networkError = new Error('Network Error')
      networkError.code = 'NETWORK_ERROR'
      
      const requestFn = vi.fn()
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue('success')

      const shouldRetry = (error) => error.code === 'NETWORK_ERROR'

      const result = await retryRequest(requestFn, { 
        maxRetries: 1, 
        retryDelay: 10,
        shouldRetry 
      })

      expect(result).toBe('success')
      expect(requestFn).toHaveBeenCalledTimes(2)
    })

    it('应该在不满足重试条件时立即抛出错误', async () => {
      const validationError = new Error('Validation Error')
      validationError.code = 'VALIDATION_ERROR'
      
      const requestFn = vi.fn().mockRejectedValue(validationError)
      const shouldRetry = (error) => error.code === 'NETWORK_ERROR'

      await expect(
        retryRequest(requestFn, { maxRetries: 3, shouldRetry })
      ).rejects.toThrow('Validation Error')

      expect(requestFn).toHaveBeenCalledTimes(1) // 不重试
    })
  })

  describe('createRequestConfig', () => {
    it('应该创建请求配置', () => {
      const config = createRequestConfig({
        timeout: 5000,
        headers: { 'X-Custom': 'value' }
      })

      expect(config.timeout).toBe(5000)
      expect(config.headers['X-Custom']).toBe('value')
      expect(config.headers['Content-Type']).toBe('application/json')
      expect(config.withCredentials).toBe(true)
    })
  })

  describe('handleApiError', () => {
    it('应该处理不同类型的API错误', () => {
      const networkError = { code: 'NETWORK_ERROR' }
      const authError = { response: { status: 401 } }
      const unknownError = { message: 'Unknown error' }

      const networkResult = handleApiError(networkError)
      const authResult = handleApiError(authError)
      const unknownResult = handleApiError(unknownError)

      expect(networkResult.type).toBe('network')
      expect(networkResult.message).toBe('网络连接失败')
      expect(authResult.type).toBe('auth')
      expect(authResult.message).toBe('认证失败')
      expect(unknownResult.type).toBe('unknown')
      expect(unknownResult.message).toBe('Unknown error')
    })
  })
}) 