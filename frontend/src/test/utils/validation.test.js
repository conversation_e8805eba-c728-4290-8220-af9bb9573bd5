/**
 * 验证工具函数测试
 */

import { describe, it, expect, vi } from 'vitest'
import { 
  isValidId, 
  validateRouteParams, 
  validateAndConvertId, 
  validateApiParams, 
  createSafeApiCall, 
  isEmpty, 
  safeGet 
} from '@/utils/validation'

describe('validation utils', () => {
  describe('isValidId', () => {
    it('应该验证有效的数字ID', () => {
      expect(isValidId(1)).toBe(true)
      expect(isValidId(123)).toBe(true)
      expect(isValidId('1')).toBe(true)
      expect(isValidId('123')).toBe(true)
    })

    it('应该拒绝无效的ID', () => {
      expect(isValidId(0)).toBe(false)
      expect(isValidId(-1)).toBe(false)
      expect(isValidId('0')).toBe(false)
      expect(isValidId('-1')).toBe(false)
      expect(isValidId('')).toBe(false)
      expect(isValidId(null)).toBe(false)
      expect(isValidId(undefined)).toBe(false)
      expect(isValidId('abc')).toBe(false)
      expect(isValidId(NaN)).toBe(false)
    })

    it('应该处理边界情况', () => {
      expect(isValidId(1.5)).toBe(false) // 小数
      expect(isValidId('1.5')).toBe(false) // 字符串小数
      expect(isValidId(Infinity)).toBe(false) // 无穷大
      expect(isValidId(-Infinity)).toBe(false) // 负无穷大
    })

    it('应该拒绝字符串形式的null和undefined', () => {
      expect(isValidId('null')).toBe(false)
      expect(isValidId('undefined')).toBe(false)
    })

    it('应该支持自定义参数名称', () => {
      // 通过spy监听console.warn来验证参数名称是否正确使用
      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      isValidId(null, '用户ID')
      expect(warnSpy).toHaveBeenCalledWith('⚠️  用户ID 为空:', null)
      
      warnSpy.mockRestore()
    })
  })

  describe('validateAndConvertId', () => {
    it('应该成功转换有效ID', () => {
      expect(validateAndConvertId('123')).toBe(123)
      expect(validateAndConvertId(456)).toBe(456)
      expect(validateAndConvertId('1')).toBe(1)
    })

    it('应该对无效ID返回null', () => {
      expect(validateAndConvertId(0)).toBe(null)
      expect(validateAndConvertId(-1)).toBe(null)
      expect(validateAndConvertId('')).toBe(null)
      expect(validateAndConvertId(null)).toBe(null)
      expect(validateAndConvertId(undefined)).toBe(null)
      expect(validateAndConvertId('abc')).toBe(null)
    })

    it('应该支持自定义参数名称', () => {
      const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      validateAndConvertId(null, '项目ID')
      expect(warnSpy).toHaveBeenCalledWith('⚠️  项目ID 为空:', null)
      
      warnSpy.mockRestore()
    })
  })

  describe('validateRouteParams', () => {
    it('应该验证有效的路由参数', () => {
      const params = { id: '1', projectId: '123' }
      const result = validateRouteParams(params, ['id', 'projectId'])
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validParams).toEqual({ id: 1, projectId: 123 })
    })

    it('应该检测缺失的参数', () => {
      const params = { id: '1' }
      const result = validateRouteParams(params, ['id', 'projectId'])
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 projectId 无效: undefined')
    })

    it('应该检测无效的参数', () => {
      const params = { id: '0', projectId: 'abc' }
      const result = validateRouteParams(params, ['id', 'projectId'])
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 id 无效: 0')
      expect(result.errors).toContain('参数 projectId 无效: abc')
    })

    it('应该处理空参数对象', () => {
      const result = validateRouteParams({}, ['id'])
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 id 无效: undefined')
    })

    it('应该处理非ID类型参数', () => {
      const params = { name: 'test', title: '测试' }
      const result = validateRouteParams(params, ['name', 'title'])
      expect(result.valid).toBe(true)
      expect(result.validParams).toEqual({ name: 'test', title: '测试' })
    })

    it('应该拒绝空的非ID参数', () => {
      const params = { name: '', title: null }
      const result = validateRouteParams(params, ['name', 'title'])
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 name 不能为空')
      expect(result.errors).toContain('参数 title 不能为空')
    })
  })

  describe('validateApiParams', () => {
    it('应该验证符合规则的参数', () => {
      const params = { 
        id: '123', 
        name: 'test', 
        age: '25',
        projectId: '456'
      }
      const rules = {
        id: { required: true, type: 'id' },
        name: { required: true, type: 'string' },
        age: { required: false, type: 'number' },
        projectId: { required: true, type: 'id' }
      }
      
      const result = validateApiParams(params, rules)
      expect(result.valid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validParams).toEqual({
        id: 123,
        name: 'test',
        age: 25,
        projectId: 456
      })
    })

    it('应该检测必需参数缺失', () => {
      const params = { name: 'test', id: null }
      const rules = {
        id: { required: true, type: 'id' },
        name: { required: true, type: 'string' }
      }
      
      const result = validateApiParams(params, rules)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 id 是必需的')
    })

    it('应该检测无效的ID类型参数', () => {
      const params = { id: 'invalid' }
      const rules = { id: { required: true, type: 'id' } }
      
      const result = validateApiParams(params, rules)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 id 必须是有效的ID: invalid')
    })

    it('应该检测无效的数字类型参数', () => {
      const params = { age: 'not-a-number' }
      const rules = { age: { required: true, type: 'number' } }
      
      const result = validateApiParams(params, rules)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 age 必须是数字: not-a-number')
    })

    it('应该检测无效的字符串类型参数', () => {
      const params = { name: 123 }
      const rules = { name: { required: true, type: 'string' } }
      
      const result = validateApiParams(params, rules)
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('参数 name 必须是字符串: 123')
    })

    it('应该跳过非必需的空参数', () => {
      const params = { name: 'test', optional: null }
      const rules = {
        name: { required: true, type: 'string' },
        optional: { required: false, type: 'string' }
      }
      
      const result = validateApiParams(params, rules)
      expect(result.valid).toBe(true)
      expect(result.validParams).toEqual({ name: 'test' })
    })

    it('应该处理字符串参数的trim操作', () => {
      const params = { name: '  test  ' }
      const rules = { name: { required: true, type: 'string' } }
      
      const result = validateApiParams(params, rules)
      expect(result.valid).toBe(true)
      expect(result.validParams.name).toBe('test')
    })
  })

  describe('createSafeApiCall', () => {
    it('应该成功调用验证通过的API', async () => {
      const mockApiFunction = vi.fn().mockResolvedValue({ data: 'success' })
      const rules = {
        id: { required: true, type: 'id' },
        name: { required: true, type: 'string' }
      }
      
      const safeApiCall = createSafeApiCall(mockApiFunction, rules)
      const result = await safeApiCall('123', 'test')
      
      expect(mockApiFunction).toHaveBeenCalledWith(123, 'test')
      expect(result).toEqual({ data: 'success' })
    })

    it('应该在参数验证失败时抛出错误', async () => {
      const mockApiFunction = vi.fn()
      const rules = {
        id: { required: true, type: 'id' }
      }
      
      const safeApiCall = createSafeApiCall(mockApiFunction, rules)
      
      await expect(safeApiCall('invalid')).rejects.toThrow('API参数验证失败')
      expect(mockApiFunction).not.toHaveBeenCalled()
    })

    it('应该在抛出的错误中包含验证错误信息', async () => {
      const mockApiFunction = vi.fn()
      const rules = {
        id: { required: true, type: 'id' }
      }
      
      const safeApiCall = createSafeApiCall(mockApiFunction, rules)
      
      try {
        await safeApiCall('invalid')
      } catch (error) {
        expect(error.message).toContain('API参数验证失败')
        expect(error.validationErrors).toBeDefined()
        expect(Array.isArray(error.validationErrors)).toBe(true)
      }
    })
  })

  describe('isEmpty', () => {
    it('应该检测null和undefined', () => {
      expect(isEmpty(null)).toBe(true)
      expect(isEmpty(undefined)).toBe(true)
    })

    it('应该检测空字符串和特殊字符串', () => {
      expect(isEmpty('')).toBe(true)
      expect(isEmpty('   ')).toBe(true)
      expect(isEmpty('null')).toBe(true)
      expect(isEmpty('undefined')).toBe(true)
    })

    it('应该检测NaN', () => {
      expect(isEmpty(NaN)).toBe(true)
    })

    it('应该检测空数组', () => {
      expect(isEmpty([])).toBe(true)
    })

    it('应该检测空对象', () => {
      expect(isEmpty({})).toBe(true)
    })

    it('应该识别非空值', () => {
      expect(isEmpty('test')).toBe(false)
      expect(isEmpty(0)).toBe(false)
      expect(isEmpty(false)).toBe(false)
      expect(isEmpty([1, 2, 3])).toBe(false)
      expect(isEmpty({ a: 1 })).toBe(false)
      expect(isEmpty(123)).toBe(false)
    })
  })

  describe('safeGet', () => {
    const testObj = {
      a: {
        b: {
          c: 'value'
        },
        d: null
      },
      e: [1, 2, 3],
      f: 0,
      g: false
    }

    it('应该获取存在的嵌套属性', () => {
      expect(safeGet(testObj, 'a.b.c')).toBe('value')
      expect(safeGet(testObj, 'a.d')).toBe(null)
      expect(safeGet(testObj, 'f')).toBe(0)
      expect(safeGet(testObj, 'g')).toBe(false)
    })

    it('应该对不存在的属性返回默认值', () => {
      expect(safeGet(testObj, 'a.b.x')).toBe(null)
      expect(safeGet(testObj, 'a.b.x', 'default')).toBe('default')
      expect(safeGet(testObj, 'x.y.z')).toBe(null)
    })

    it('应该处理无效的对象', () => {
      expect(safeGet(null, 'a.b.c')).toBe(null)
      expect(safeGet(undefined, 'a.b.c')).toBe(null)
      expect(safeGet('string', 'a.b.c')).toBe(null)
      expect(safeGet(123, 'a.b.c')).toBe(null)
    })

    it('应该支持自定义默认值', () => {
      expect(safeGet(testObj, 'a.b.x', 'custom')).toBe('custom')
      expect(safeGet(null, 'a.b.c', [])).toEqual([])
      expect(safeGet(undefined, 'a.b.c', {})).toEqual({})
    })

    it('应该处理单层属性路径', () => {
      expect(safeGet(testObj, 'a')).toEqual(testObj.a)
      expect(safeGet(testObj, 'nonexistent')).toBe(null)
      expect(safeGet(testObj, 'nonexistent', 'default')).toBe('default')
    })
  })
})
