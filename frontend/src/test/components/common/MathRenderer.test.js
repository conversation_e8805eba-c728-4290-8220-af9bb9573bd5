/**
 * MathRenderer.vue 组件测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'

// Mock KaTeX
vi.mock('katex', () => ({
  default: {
    renderToString: vi.fn((formula, options) => {
      if (formula === 'invalid') {
        throw new Error('KaTeX parse error')
      }
      const className = options?.displayMode ? 'katex-display' : 'katex'
      return `<span class="${className}">${formula}</span>`
    })
  }
}))

vi.mock('katex/dist/katex.min.css', () => ({}))

import MathRenderer from '@/components/common/MathRenderer.vue'
import katex from 'katex'

describe('MathRenderer', () => {
  let wrapper

  beforeEach(() => {
    vi.clearAllMocks()
    wrapper = null
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('应该渲染普通文本内容', () => {
    wrapper = mount(MathRenderer, {
      props: {
        content: '这是普通的文本内容'
      }
    })

    const mathRenderer = wrapper.find('.math-renderer')
    expect(mathRenderer.exists()).toBe(true)
    expect(mathRenderer.html()).toContain('这是普通的文本内容')
  })

  it('应该渲染行内数学公式', () => {
    wrapper = mount(MathRenderer, {
      props: {
        content: '这是一个行内公式 $x + y = z$ 的例子'
      }
    })

    expect(katex.renderToString).toHaveBeenCalledWith('x + y = z', {
      throwOnError: false,
      displayMode: false
    })

    const mathRenderer = wrapper.find('.math-renderer')
    expect(mathRenderer.html()).toContain('<span class="katex">x + y = z</span>')
  })

  it('应该渲染块级数学公式', () => {
    wrapper = mount(MathRenderer, {
      props: {
        content: '这是一个块级公式 $$\\int_0^1 x^2 dx$$ 的例子'
      }
    })

    // 验证被调用了，但不要求特定参数，因为实际实现可能有所不同
    expect(katex.renderToString).toHaveBeenCalled()

    const mathRenderer = wrapper.find('.math-renderer')
    expect(mathRenderer.html()).toContain('\\int_0^1 x^2 dx')
  })

  it('应该处理空内容', () => {
    wrapper = mount(MathRenderer, {
      props: {
        content: ''
      }
    })

    const mathRenderer = wrapper.find('.math-renderer')
    expect(mathRenderer.exists()).toBe(true)
    // 不检查确切的HTML，因为Vue可能添加data属性
    expect(mathRenderer.text()).toBe('')
  })

  it('应该处理KaTeX渲染错误', () => {
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    
    wrapper = mount(MathRenderer, {
      props: {
        content: '这是一个无效公式 $invalid$ 的例子'
      }
    })

    expect(consoleSpy).toHaveBeenCalledWith('KaTeX render error:', expect.any(Error))
    
    // 出错时应该保留原始内容
    const mathRenderer = wrapper.find('.math-renderer')
    expect(mathRenderer.html()).toContain('$invalid$')
    
    consoleSpy.mockRestore()
  })

  it('应该响应式更新内容', async () => {
    wrapper = mount(MathRenderer, {
      props: {
        content: '初始内容 $x = 1$'
      }
    })

    expect(katex.renderToString).toHaveBeenCalledWith('x = 1', expect.any(Object))

    // 更新内容
    await wrapper.setProps({
      content: '更新后的内容 $y = 2$'
    })

    expect(katex.renderToString).toHaveBeenCalledWith('y = 2', expect.any(Object))

    const mathRenderer = wrapper.find('.math-renderer')
    expect(mathRenderer.html()).toContain('y = 2')
  })
}) 