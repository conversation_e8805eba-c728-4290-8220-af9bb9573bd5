/**
 * 全局评分标签样式
 * 统一全系统的评分标签颜色
 * 1分红色，2分黄色，3分白色，4分蓝色，5分绿色，没有分数也是白色
 */

/* 3分和默认分数的白色样式 */
.el-tag:not(.el-tag--danger):not(.el-tag--warning):not(.el-tag--info):not(.el-tag--success),
.score-tag:not(.el-tag--danger):not(.el-tag--warning):not(.el-tag--info):not(.el-tag--success),
.score-stat:not(.el-tag--danger):not(.el-tag--warning):not(.el-tag--info):not(.el-tag--success),
.stat-tag:not(.el-tag--danger):not(.el-tag--warning):not(.el-tag--info):not(.el-tag--success) {
  background-color: white !important;
  border-color: #d9d9d9 !important;
  color: #666 !important;
}

/* 确保4分标签是蓝色 */
.el-tag--info,
.score-tag.el-tag--info,
.score-stat.el-tag--info,
.stat-tag.el-tag--info {
  background-color: #e6f7ff !important;
  border-color: #91d5ff !important;
  color: #1890ff !important;
}

/* 自定义评分标签类 */
.score-tag-1 {
  background-color: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.score-tag-2 {
  background-color: #fffbe6;
  border-color: #ffe58f;
  color: #faad14;
}

.score-tag-3,
.score-tag-default {
  background-color: #ffffff;
  border-color: #d9d9d9;
  color: #666666;
}

.score-tag-4 {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.score-tag-5 {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

/* 通用评分标签样式 */
.score-tag,
.score-stat,
.stat-tag {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid;
  display: inline-block;
  text-align: center;
  font-weight: normal;
}

/* 小尺寸评分标签 */
.score-tag.el-tag--small,
.score-stat.el-tag--small,
.stat-tag.el-tag--small {
  font-size: 11px;
  padding: 1px 4px;
}

/* 确保在不同容器中的一致性 */
.knowledge-item .el-tag,
.leaf-node-item .el-tag,
.tree-node .el-tag,
.score-distribution .el-tag,
.score-statistics .el-tag {
  margin: 0 2px;
}

/* 悬停效果 */
.score-tag:hover,
.score-stat:hover,
.stat-tag:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}
