/* 
 * Dialog 统一样式规范
 * 基于添加错题dialog的标准实现
 * 用法：为dialog添加 class="standard-dialog" 即可应用统一样式
 */

/* 基础dialog容器样式 */
.standard-dialog {
  max-width: 1400px !important;
  max-height: 95vh !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 auto !important;
}

.standard-dialog .el-dialog {
  max-height: 95vh !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 auto !important;
}

/* Header样式 - 顶部标题区域 */
.standard-dialog .el-dialog__header {
  padding: 20px 24px 16px !important;
  border-bottom: 1px solid #ebeef5 !important;
  flex-shrink: 0 !important;
  background: #fff !important;
}

.standard-dialog .el-dialog__title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #303133 !important;
  margin: 0 !important;
}

/* Body样式 - 中间内容区域（自动滚动） */
.standard-dialog .el-dialog__body {
  padding: 0 !important;
  flex: 1 !important;
  overflow: hidden !important;
  max-height: calc(95vh - 180px) !important; /* 减去头部和底部的空间 */
  display: flex !important;
  flex-direction: column !important;
}

/* Footer样式 - 底部操作按钮区域 */
.standard-dialog .el-dialog__footer {
  padding: 16px 24px !important;
  border-top: 1px solid #ebeef5 !important;
  flex-shrink: 0 !important;
  background: #fff !important;
}

/* 内容滚动区域 */
.standard-dialog-content {
  flex: 1 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 0 !important;
  min-height: 0 !important; /* 确保能够收缩 */
}

/* Footer按钮组样式 */
.standard-dialog-footer {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 12px !important;
}

/* 自定义Header样式（带操作按钮） */
.standard-dialog-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
}

.standard-dialog-header .dialog-title {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #303133 !important;
}

.standard-dialog-header .header-actions {
  display: flex !important;
  gap: 8px !important;
  align-items: center !important;
}

/* 特殊类型dialog样式变体 */

/* 小型dialog - 适用于简单确认对话框 */
.standard-dialog.small-dialog {
  max-width: 600px !important;
  width: 600px !important;
}

.standard-dialog.small-dialog .el-dialog__body {
  max-height: calc(80vh - 160px) !important;
}

/* 中型dialog - 适用于表单类对话框 */
.standard-dialog.medium-dialog {
  max-width: 800px !important;
  width: 80% !important;
}

/* 大型dialog - 适用于复杂内容展示 */
.standard-dialog.large-dialog {
  max-width: 1200px !important;
  width: 90% !important;
}

/* 超大dialog - 适用于详细内容查看 */
.standard-dialog.xlarge-dialog {
  max-width: 1400px !important;
  width: 95% !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .standard-dialog {
    width: 95% !important;
    margin: 0 auto !important;
  }
  
  .standard-dialog.small-dialog {
    width: 85% !important;
    max-width: 550px !important;
  }
  
  .standard-dialog.medium-dialog {
    width: 90% !important;
  }
}

@media (max-width: 768px) {
  .standard-dialog {
    width: 98% !important;
    max-height: 98vh !important;
  }

  .standard-dialog .el-dialog__body {
    max-height: calc(98vh - 160px) !important;
  }

  .standard-dialog .el-dialog__header {
    padding: 16px 20px 12px !important;
  }

  .standard-dialog .el-dialog__footer {
    padding: 12px 16px !important;
  }

  .standard-dialog-footer {
    gap: 8px !important;
  }

  .standard-dialog .el-dialog__title,
  .standard-dialog-header .dialog-title {
    font-size: 16px !important;
  }
  
  /* 移动端所有dialog都使用相同尺寸 */
  .standard-dialog.small-dialog,
  .standard-dialog.medium-dialog,
  .standard-dialog.large-dialog,
  .standard-dialog.xlarge-dialog {
    width: 98% !important;
    max-width: none !important;
  }
}

@media (max-height: 700px) {
  .standard-dialog {
    height: 90vh !important;
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
  }

  .standard-dialog .el-dialog__body {
    max-height: calc(90vh - 160px) !important;
  }
}

@media (max-height: 600px) {
  .standard-dialog {
    height: 95vh !important;
    margin-top: 2.5vh !important;
    margin-bottom: 2.5vh !important;
  }

  .standard-dialog .el-dialog__body {
    max-height: calc(95vh - 140px) !important;
  }
}

/* 特殊场景样式覆盖 */

/* 当dialog content需要padding时使用 */
.standard-dialog-content.with-padding {
  padding: 20px 24px !important;
}

/* 移动端padding调整 */
@media (max-width: 768px) {
  .standard-dialog-content.with-padding {
    padding: 16px 20px !important;
  }
}

/* 确保富文本编辑器等复杂组件的下拉菜单能正常显示 */
.standard-dialog .ql-picker-options,
.standard-dialog .el-select-dropdown,
.standard-dialog .el-popper {
  z-index: 2000 !important;
  position: absolute !important;
  background: white !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* 通用过渡动画 */
.standard-dialog .el-dialog {
  transition: all 0.3s ease !important;
}

/* 全屏模式优化 */
.standard-dialog.is-fullscreen .el-dialog__body {
  max-height: calc(100vh - 140px) !important;
}

.standard-dialog.is-fullscreen .standard-dialog-content {
  height: calc(100vh - 140px) !important;
} 