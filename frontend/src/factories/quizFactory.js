import { QuestionGeneratorFactory } from '@/generators/questionGenerators.js';
import { QUIZ_TYPES, DIFFICULTY_LEVELS, DEFAULT_CONFIG } from '@/config/quizConfig.js';

/**
 * 考试工厂类
 */
export class QuizFactory {
  constructor() {
    this.quizRegistry = new Map();
    this.dataRegistry = new Map();
    this.initializeRegistry();
  }

  /**
   * 初始化考试注册表
   */
  initializeRegistry() {
    // 动态导入数据文件
    this.registerQuizTypes();
  }

  /**
   * 注册考试类型
   */
  registerQuizTypes() {
    // 简单类型考试
    this.registerQuiz(QUIZ_TYPES.ANIMALS, {
      title: '动物世界',
      description: '看图识动物，测试你的动物知识！',
      difficulty: 1,
      generatorType: 'category',
      dataModule: () => import('@/data/animals.js'),
      dataField: 'animals'
    });

    this.registerQuiz(QUIZ_TYPES.CAR_LOGOS, {
      title: '车标达人挑战',
      description: '看看你认识多少个车标？',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/car-logos.js'),
      dataField: 'carLogos'
    });

    this.registerQuiz(QUIZ_TYPES.COUNTRY_FLAGS, {
      title: '国旗大全',
      description: '来挑战一下你认识多少国旗吧！',
      difficulty: 3,
      generatorType: 'simple',
      dataModule: () => import('@/data/country-flags.js'),
      dataField: 'countryFlags'
    });

    this.registerQuiz(QUIZ_TYPES.WORLD_LANDMARKS, {
      title: '世界名胜古迹',
      description: '看图识别世界著名的名胜古迹，测试你的地理知识！',
      difficulty: 3,
      generatorType: 'simple',
      dataModule: () => import('@/data/world-landmarks.js'),
      dataField: 'worldLandmarks'
    });

    this.registerQuiz(QUIZ_TYPES.CHINA_LANDMARKS, {
      title: '中国旅游景点',
      description: '探索祖国大好河山，测试你对中国著名旅游景点的了解！',
      difficulty: 3,
      generatorType: 'category',
      dataModule: () => import('@/data/china-landmarks.js'),
      dataField: 'chinaLandmarks'
    });

    this.registerQuiz(QUIZ_TYPES.WORLD_FLAGS_PLUS, {
      title: '世界国旗Plus',
      description: '终极挑战！识别世界上所有195个国家的国旗！',
      difficulty: 5,
      generatorType: 'category',
      dataModule: () => import('@/data/world-flags-plus.js'),
      dataField: 'worldFlagsPlus',
      generatorConfig: { categoryField: 'continent' }
    });

    this.registerQuiz(QUIZ_TYPES.ETHNICITIES, {
      title: '世界民族文化',
      description: '看传统服饰识民族，测试你对世界各民族文化的了解！',
      difficulty: 5,
      generatorType: 'category',
      dataModule: () => import('@/data/ethnicities.js'),
      dataField: 'ethnicities'
    });

    this.registerQuiz(QUIZ_TYPES.CITY_REGIONS, {
      title: '城市归属省份',
      description: '看城市名猜省份，测试你的地理知识！选择车牌最靠后的城市',
      difficulty: 4,
      generatorType: 'city-regions',
      dataModule: () => import('@/data/city-regions.js'),
      dataField: 'cityRegions'
    });

    this.registerQuiz(QUIZ_TYPES.JAPANESE_ACTRESSES, {
      title: '日本女优知识',
      description: '看图识别日本历史上最有名的女优，测试你的知识面！',
      difficulty: 4,
      hidden: true,
      generatorType: 'simple',
      dataModule: () => import('@/data/japanese-actresses.js'),
      dataField: 'japaneseActresses'
    });

    this.registerQuiz(QUIZ_TYPES.MILITARY_EQUIPMENT, {
      title: '军事装备大全',
      description: '看图识别世界各国著名的军事装备，测试你的军事知识！涵盖步枪、坦克、战斗机、舰船等20个类别。',
      difficulty: 5,
      generatorType: 'category',
      dataModule: () => import('@/data/military-equipment.js'),
      dataField: 'militaryEquipment'
    });

    this.registerQuiz(QUIZ_TYPES.VEHICLES, {
      title: '交通工具大全',
      description: '看图识别各种交通工具，测试你对陆地、天空、水里交通工具的了解！',
      difficulty: 2,
      generatorType: 'category',
      dataModule: () => import('@/data/vehicles.js'),
      dataField: 'vehicles'
    });

    this.registerQuiz(QUIZ_TYPES.DOG_CAT_BREEDS, {
      title: '猫狗品种大全',
      description: '看图识别猫狗品种！从金毛、哈士奇到英短、布偶猫，测试你对宠物品种的了解程度！',
      difficulty: 2,
      generatorType: 'category',
      dataModule: () => import('@/data/dogCatBreeds.js'),
      dataField: 'dogCatBreeds'
    });

    this.registerQuiz(QUIZ_TYPES.HOUSEPLANTS, {
      title: '家养植物品种',
      description: '看图识别家养植物品种！从观叶植物、观花植物到多肉植物，测试你对花鸟市场常见植物的了解！',
      difficulty: 3,
      generatorType: 'category',
      dataModule: () => import('@/data/houseplants.js'),
      dataField: 'houseplants'
    });

    this.registerQuiz(QUIZ_TYPES.HOME_APPLIANCES, {
      title: '家用电器大全',
      description: '看图识别常见家用电器！从厨房电器到个人护理用品，测试你对日常生活用品的了解！',
      difficulty: 1,
      generatorType: 'simple',
      dataModule: () => import('@/data/home-appliances.js'),
      dataField: 'homeAppliances'
    });

    this.registerQuiz(QUIZ_TYPES.VEGETABLES, {
      title: '常见蔬菜',
      description: '看图识别常见蔬菜！从根茎类到叶菜类，从瓜果类到豆类，测试你对日常蔬菜的了解！',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/vegetables.js'),
      dataField: 'vegetables'
    });

    this.registerQuiz(QUIZ_TYPES.FRUITS, {
      title: '常见水果',
      description: '看图识别常见水果！从苹果香蕉到热带水果，从浆果类到柑橘类，测试你对日常水果的了解！',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/fruits.js'),
      dataField: 'fruits'
    });

    this.registerQuiz(QUIZ_TYPES.DISHES, {
      title: '家常菜大全',
      description: '看图识别常见家常菜！从红烧肉到宫保鸡丁，从麻婆豆腐到糖醋排骨，测试你对中式家常菜的了解！',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/dishes.js'),
      dataField: 'dishes'
    });

    this.registerQuiz(QUIZ_TYPES.WEATHERS, {
      title: '气象识别',
      description: '识别20种最常见的气象',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/weathers.js'),
      dataField: 'weathers'
    });

    this.registerQuiz(QUIZ_TYPES.SPORTS, {
      title: '体育运动',
      description: '看图识别各种体育运动！从足球篮球到游泳跑步，从武术太极到攀岩滑雪，测试你对体育运动的了解！',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/sports.js'),
      dataField: 'sports'
    });

    this.registerQuiz(QUIZ_TYPES.FISH, {
      title: '鱼类大全',
      description: '看图识别常见鱼类！从淡水鱼到海水鱼，从鲤鱼草鱼到带鱼鲈鱼，测试你对鱼类的了解！',
      difficulty: 3,
      generatorType: 'simple',
      dataModule: () => import('@/data/fish.js'),
      dataField: 'fish'
    });

    this.registerQuiz(QUIZ_TYPES.PUBLIC_FACILITIES, {
      title: '公共设施识别',
      description: '看图识别常见公共设施，测试你对城市设施的了解！',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/publicFacilities.js'),
      dataField: 'publicFacilities'
    });

    this.registerQuiz(QUIZ_TYPES.WORLD_FOODS, {
      title: '世界各国著名食物',
      description: '看图识别世界各国的著名美食！从中国的北京烤鸭到意大利的披萨，从日本的寿司到法国的鹅肝，猜猜这些美食来自哪个国家！',
      difficulty: 3,
      generatorType: 'world-foods',
      dataModule: () => import('@/data/worldFoods.js'),
      dataField: 'worldFoods'
    });

    this.registerQuiz(QUIZ_TYPES.GLOBAL_ACTRESSES, {
      title: '世界各国代表女明星',
      description: '看图识别世界各国最具代表性的女明星，按黄种人、白种人、黑种人分类，猜猜她们来自哪个国家！',
      difficulty: 4,
      generatorType: 'global-celebrity',
      dataModule: () => import('@/data/globalActresses.js'),
      dataField: 'globalActresses'
    });

    this.registerQuiz(QUIZ_TYPES.GLOBAL_ACTORS, {
      title: '世界各国代表男明星',
      description: '看图识别世界各国最具代表性的男明星，按黄种人、白种人、黑种人分类，猜猜他们来自哪个国家！',
      difficulty: 4,
      generatorType: 'global-celebrity',
      dataModule: () => import('@/data/globalActors.js'),
      dataField: 'globalActors'
    });

    this.registerQuiz(QUIZ_TYPES.LANDMARK_BUILDINGS, {
      title: '各国标志性建筑',
      description: '看图识别世界各国的标志性建筑！包含城市地标和乡村特色建筑，猜猜这些建筑来自哪个国家！',
      difficulty: 3,
      generatorType: 'landmark-buildings',
      dataModule: () => import('@/data/landmarkBuildings.js'),
      dataField: 'landmarkBuildings'
    });

    this.registerQuiz(QUIZ_TYPES.FAIRY_TALES, {
      title: '童话故事',
      description: '看图识别经典童话故事和卡通片！包含中国、日本、美国和其他国家的著名童话故事，猜猜它们来自哪个国家！',
      difficulty: 2,
      generatorType: 'category',
      dataModule: () => import('@/data/fairy_tales.js'),
      dataField: 'fairyTales'
    });

    this.registerQuiz(QUIZ_TYPES.CHINESE_FOODS, {
      title: '中国著名食物',
      description: '看图识别中国各个城市最有名的食物！从北京烤鸭到重庆火锅，从兰州拉面到广州肠粉，猜猜这些美食来自哪个城市！',
      difficulty: 3,
      generatorType: 'chinese-foods',
      dataModule: () => import('@/data/chinese_foods.js'),
      dataField: 'chineseFoods'
    });

    this.registerQuiz(QUIZ_TYPES.CLOTHES, {
      title: '服装类型',
      description: '看图识别各种服装类型！从衣服到裤子、袜子再到帽子，测试你对日常服装的了解！',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/clothes.js'),
      dataField: 'clothes'
    });

    this.registerQuiz(QUIZ_TYPES.TRAFFIC_SIGNS, {
      title: '交通指示牌',
      description: '看图识别中国交通指示牌！从禁令标志到警告标志，从指示标志到指路标志，测试你对道路交通标志的了解！',
      difficulty: 3,
      generatorType: 'category',
      dataModule: () => import('@/data/trafficSigns.js'),
      dataField: 'trafficSigns'
    });

    this.registerQuiz(QUIZ_TYPES.LIVESTOCK, {
      title: '家禽和家畜',
      description: '看图识别常见的家禽和家畜！从鸡鸭鹅到猪牛羊，从家养宠物到农场动物，测试你对农业养殖动物的了解！',
      difficulty: 2,
      generatorType: 'simple',
      dataModule: () => import('@/data/livestock.js'),
      dataField: 'livestock'
    });

    this.registerQuiz(QUIZ_TYPES.BEAUTIFUL_SCENERIES, {
      title: '最美风景',
      description: '看图识别世界最美风景！从自然奇观到人文景观，测试你对全球美景的了解！',
      difficulty: 3,
      generatorType: 'simple',
      dataModule: () => import('@/data/beautiful_sceneries.js'),
      dataField: 'beautifulSceneries'
    });

    this.registerQuiz(QUIZ_TYPES.OCCUPATIONS, {
      title: '职业问答',
      description: '看图识别常见职业！从医生到教师，测试你对各种职业的了解！',
      difficulty: 1,
      generatorType: 'simple',
      dataModule: () => import('@/data/occupations.js'),
      dataField: 'occupations'
    });

    // 新增女子发型考试
    this.registerQuiz(QUIZ_TYPES.HAIRSTYLES, {
      title: '女子发型',
      description: '识别历史上最常见的20种女子发型！测试你对发型文化的了解！',
      difficulty: 4,
      generatorType: 'simple',
      dataModule: () => import('@/data/hairstyles.js'),
      dataField: 'hairstyles'
    });

    // 新增热带鱼识别考试
    this.registerQuiz(QUIZ_TYPES.TROPICAL_FISH, {
      title: '热带鱼识别',
      description: '看图识别最常见的热带鱼品种！从孔雀鱼到七彩神仙鱼，测试你对观赏鱼的了解！',
      difficulty: 3,
      generatorType: 'simple',
      dataModule: () => import('@/data/tropicalFish.js'),
      dataField: 'tropicalFish'
    });

    // 新增中国传统手艺考试
    this.registerQuiz(QUIZ_TYPES.TRADITIONAL_CRAFTS, {
      title: '中国传统手艺',
      description: '看图识别25种最常见的中国传统手艺！从剪纸刺绣到木雕石雕，从陶瓷漆器到景泰蓝玉雕，测试你对中华传统工艺的了解！',
      difficulty: 3,
      generatorType: 'simple',
      dataModule: () => import('@/data/traditional_crafts.js'),
      dataField: 'traditionalCrafts'
    });

    // 新增世界上最危险的运动考试
    this.registerQuiz(QUIZ_TYPES.DANGEROUS_SPORTS, {
      title: '世界上最危险的运动',
      description: '看图识别25种世界上最危险的运动！从自由潜水到翼装飞行，从攀岩到跳伞，测试你对极限运动的了解！',
      difficulty: 4,
      generatorType: 'simple',
      dataModule: () => import('@/data/dangerous_sports.js'),
      dataField: 'dangerousSports'
    });

    // 新增世界上最好玩游乐场考试
    this.registerQuiz(QUIZ_TYPES.AMUSEMENT_PARKS, {
      title: '世界上最好玩游乐场',
      description: '看图识别25个世界著名游乐场！从迪士尼乐园到环球影城，从乐高乐园到六旗乐园，测试你对全球主题公园的了解！',
      difficulty: 4,
      generatorType: 'simple',
      dataModule: () => import('@/data/amusement_parks.js'),
      dataField: 'amusementParks'
    });

    // 新增体育明星识别考试
    this.registerQuiz(QUIZ_TYPES.SPORTS_STARS, {
      title: '体育明星识别',
      description: '看图识别世界上最有名的100个体育明星！从足球巨星梅西、C罗到篮球传奇乔丹、科比，从网球天王费德勒到田径飞人博尔特，测试你对全球体育明星的了解！',
      difficulty: 3,
      generatorType: 'simple',
      dataModule: () => import('@/data/sports_stars.js'),
      dataField: 'sportsStars'
    });

    this.registerQuiz(QUIZ_TYPES.BEAUTIFUL_WOMEN, {
      title: '世界上最美女人',
      description: '识别世界上最被大众评价最美的女性明星',
      difficulty: 4,
      generatorType: 'image',
      dataModule: () => import('@/data/beautiful_women.js'),
      dataField: 'beautifulWomen'
    });
  }

  /**
   * 注册考试
   * @param {string} type - 考试类型
   * @param {Object} config - 考试配置
   */
  registerQuiz(type, config) {
    this.quizRegistry.set(type, {
      ...DEFAULT_CONFIG,
      ...config
    });
  }

  /**
   * 获取考试配置
   * @param {string} type - 考试类型
   * @returns {Object} 考试配置
   */
  getQuizConfig(type) {
    return this.quizRegistry.get(type);
  }

  /**
   * 获取所有考试配置
   * @returns {Map} 所有考试配置
   */
  getAllQuizConfigs() {
    return new Map(this.quizRegistry);
  }

  /**
   * 获取可见的考试配置
   * @returns {Map} 可见的考试配置
   */
  getVisibleQuizConfigs() {
    const visible = new Map();
    for (const [type, config] of this.quizRegistry) {
      if (!config.hidden) {
        visible.set(type, config);
      }
    }
    return visible;
  }

  /**
   * 创建考试
   * @param {string} type - 考试类型
   * @returns {Promise<Object>} 考试对象
   */
  async createQuiz(type) {
    const config = this.getQuizConfig(type);
    if (!config) {
      throw new Error(`Unknown quiz type: ${type}`);
    }

    try {
      // 动态加载数据
      const dataModule = await config.dataModule();
      const data = dataModule[config.dataField];
      
      if (!data || !Array.isArray(data)) {
        throw new Error(`Invalid data for quiz type: ${type}`);
      }

      // 创建题目生成器
      const generator = QuestionGeneratorFactory.create(config.generatorType, {
        ...DEFAULT_CONFIG,
        ...config.generatorConfig
      });

      // 生成题目
      const questions = generator.generateQuestions(data, config.difficulty);

      return {
        type,
        title: config.title,
        description: config.description,
        difficulty: config.difficulty,
        questions: questions.slice(0, config.maxQuestions || DEFAULT_CONFIG.maxQuestions),
        totalQuestions: questions.length
      };
    } catch (error) {
      console.error(`Failed to create quiz ${type}:`, error);
      throw error;
    }
  }

  /**
   * 获取考试题目数量
   * @param {string} type - 考试类型
   * @returns {Promise<number>} 题目数量
   */
  async getQuestionCount(type) {
    try {
      const config = this.getQuizConfig(type);
      if (!config) return 0;

      const dataModule = await config.dataModule();
      const data = dataModule[config.dataField];
      
      return data ? data.length : 0;
    } catch (error) {
      console.warn(`Failed to get question count for ${type}:`, error);
      return 0;
    }
  }

  /**
   * 获取选项数量
   * @param {number} difficulty - 难度等级
   * @returns {number} 选项数量
   */
  getOptionCount(difficulty) {
    return DIFFICULTY_LEVELS[difficulty]?.options || 4;
  }
}

// 单例模式
let instance = null;

export function getQuizFactory() {
  if (!instance) {
    instance = new QuizFactory();
  }
  return instance;
}

export default QuizFactory;
