import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import ProjectList from '@/views/ProjectList.vue'
import ProjectForm from '@/views/ProjectForm.vue'
import ProjectDetail from '@/views/ProjectDetail.vue'
import KnowledgeManagement from '@/views/KnowledgeManagement.vue'
import ExamAnalysis from '@/views/ExamAnalysis.vue'
import ExamGeneration from '@/views/ExamGeneration.vue'
import ExamList from '@/views/ExamList.vue'
import ExamRecords from '@/views/ExamRecords.vue'
import VideoTutorialManagement from '@/views/VideoTutorialManagement.vue'
import WrongQuestions from '@/views/WrongQuestions.vue'
import FlashcardManagement from '@/views/FlashcardManagement.vue'
import MentalArithmeticExam from '@/views/MentalArithmeticExam.vue'
import MobileMentalArithmeticExam from '@/views/MobileMentalArithmeticExam.vue'
import MobileStudy from '@/views/MobileStudy.vue';
import MobileHome from '@/views/MobileHome.vue';
import QuizList from '@/views/QuizList.vue';
import Quiz from '@/views/Quiz.vue';
import StudyCourses from '@/views/StudyCourses.vue';
import CourseDetail from '@/views/CourseDetail.vue';
import PinyinLearning from '@/views/PinyinLearning.vue';
import QuizExamRecords from '@/views/QuizExamRecords.vue';
import IntelligenceChallenge from '@/views/IntelligenceChallenge.vue';
import MahjongWinningChallenge from '@/views/MahjongWinningChallenge.vue';


import { validateRouteParams } from '@/utils/validation'

const routes = [
  {
    path: '/',
    name: 'ProjectList',
    component: ProjectList,
    meta: { title: '项目列表' }
  },
  {
    path: '/projects/new',
    name: 'ProjectCreate',
    component: ProjectForm,
    meta: { title: '创建项目' }
  },
  {
    path: '/projects/:id',
    name: 'ProjectDetail',
    component: ProjectDetail,
    meta: { title: '项目详情' }
  },
  {
    path: '/projects/:id/edit',
    name: 'ProjectEdit',
    component: ProjectForm,
    meta: { title: '编辑项目' }
  },
  {
    path: '/projects/:id/knowledge',
    name: 'KnowledgeManagement',
    component: KnowledgeManagement,
    meta: { title: '知识点管理' }
  },
  {
    path: '/projects/:id/exam-analysis',
    name: 'ExamAnalysis',
    component: ExamAnalysis,
    meta: { title: '考卷分析' }
  },
  {
    path: '/projects/:id/exams',
    name: 'ExamList',
    component: ExamList,
    meta: { title: '考卷管理' }
  },
  {
    path: '/projects/:id/exams/generate',
    name: 'ExamGeneration',
    component: ExamGeneration,
    meta: { title: '考卷生成' }
  },
  {
    path: '/projects/:projectId/exams/:examId/records',
    name: 'ExamRecords',
    component: ExamRecords,
    meta: { title: '考试记录' }
  },
  {
    path: '/projects/:id/wrong-questions',
    name: 'WrongQuestions',
    component: WrongQuestions,
    meta: { title: '错题管理' }
  },
  {
    path: '/projects/:id/flashcards',
    name: 'FlashcardManagement',
    component: FlashcardManagement,
    meta: { title: '卡片管理' }
  },
  {
    path: '/mobile/study/:projectId?',
    name: 'MobileStudy',
    component: MobileStudy,
    meta: { title: '卡片学习' }
  },
  {
    path: '/card-exam',
    name: 'CardExam',
    component: MobileStudy,
    meta: { title: '卡片考试' }
  },
  {
    path: '/projects/:id/review-configuration',
    name: 'ReviewConfiguration',
    component: () => import('@/views/ReviewConfiguration.vue'),
    meta: { title: '复习配置' }
  },
  {
    path: '/video-tutorials',
    name: 'VideoTutorialManagement',
    component: VideoTutorialManagement,
    meta: { title: '视频教程管理' }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About.vue'),
    meta: { title: '关于' }
  },
  {
    path: '/mental-arithmetic-exam',
    name: 'MentalArithmeticExam',
    component: MentalArithmeticExam,
    meta: { title: '数学口算在线考试' }
  },
  {
    path: '/mobile/mental-arithmetic-exam',
    name: 'MobileMentalArithmeticExam',
    component: MobileMentalArithmeticExam,
    meta: { title: '数学口算在线考试' }
  },
  {
    path: '/quiz-list',
    name: 'QuizList',
    component: QuizList,
    meta: { title: '趣味考试' }
  },
  {
    path: '/quiz/:type',
    name: 'Quiz',
    component: Quiz,
    meta: { title: '开始考试' }
  },
  {
    path: '/m',
    name: 'MobileHome',
    component: MobileHome,
    meta: { title: '首页' }
  },
  {
    path: '/courses',
    name: 'StudyCourses',
    component: StudyCourses,
    meta: { title: '学习课程' }
  },
  {
    path: '/courses/:courseId',
    name: 'CourseDetail',
    component: CourseDetail,
    meta: { title: '课程详情' }
  },
  {
    path: '/pinyin',
    name: 'PinyinLearning',
    component: PinyinLearning,
    meta: { title: '汉语拼音' }
  },
  {
    path: '/quiz-exam-records',
    name: 'QuizExamRecords',
    component: QuizExamRecords,
    meta: { title: '趣味考试记录' }
  },
  {
    path: '/intelligence-challenge',
    name: 'IntelligenceChallenge',
    component: IntelligenceChallenge,
    meta: { title: '智力大比拼' }
  },
  {
    path: '/intelligence-challenge/mahjong-winning',
    name: 'MahjongWinningChallenge',
    component: MahjongWinningChallenge,
    meta: { title: '麻将胡牌挑战' }
  },

]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 参数验证和页面标题设置
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - KPE`
  }

  // 验证需要项目ID的路由
  const routesRequiringProjectId = [
    'ProjectDetail',
    'ProjectEdit',
    'KnowledgeManagement',
    'ExamAnalysis',
    'ExamList',
    'ExamGeneration',
    'WrongQuestions',
    'ReviewConfiguration',
    'FlashcardManagement'
  ]

  if (routesRequiringProjectId.includes(to.name)) {
    console.log('🔧 路由守卫 - 验证路由:', to.name)
    console.log('🔧 路由守卫 - 路由参数:', to.params)

    const validation = validateRouteParams(to.params, ['id'])
    console.log('🔧 路由守卫 - 验证结果:', validation)

    if (!validation.valid) {
      console.error('❌ 路由参数验证失败:', validation.errors)
      ElMessage.error(`页面参数错误: ${validation.errors.join(', ')}`)
      next('/')
      return
    }
  }

  // 验证需要项目ID和考卷ID的路由
  if (to.name === 'ExamRecords') {
    const validation = validateRouteParams(to.params, ['projectId', 'examId'])
    if (!validation.valid) {
      console.error('❌ 路由参数验证失败:', validation.errors)
      ElMessage.error(`页面参数错误: ${validation.errors.join(', ')}`)
      next('/')
      return
    }
  }

  next()
})

export default router
