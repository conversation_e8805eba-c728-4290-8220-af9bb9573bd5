<template>
  <div class="knowledge-tree-selector">
    <div class="tree-container">
      <el-tree
        ref="treeRef"
        :data="processedData"
        :props="treeProps"
        show-checkbox
        node-key="uid"
        :default-checked-keys="selected"
        :check-strictly="false"
        @check="handleCheck"
        class="knowledge-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <span class="node-label">{{ data.name }}</span>
            <div class="node-info">
              <el-tag
                v-if="showScores && data.score !== undefined"
                :type="getScoreTagType(data.score)"
                size="small"
                class="score-tag"
              >
                {{ data.score }}分
              </el-tag>
              <el-icon
                v-if="data.description"
                class="info-icon"
                @click.stop="showNodeInfo(data)"
              >
                <InfoFilled />
              </el-icon>
            </div>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 选择统计 -->
    <div class="selection-summary">
      <div class="summary-item">
        <span class="label">已选择：</span>
        <span class="value">{{ selectedCount }} 个知识点</span>
      </div>
    </div>

    <!-- 节点信息对话框 -->
    <el-dialog
      v-model="showInfoDialog"
      :title="currentNodeInfo?.name"
      width="500px"
      append-to-body
    >
      <div v-if="currentNodeInfo" class="node-info-content">
        <div class="info-item">
          <strong>名称：</strong>{{ currentNodeInfo.name }}
        </div>
        <div v-if="currentNodeInfo.fullPath" class="info-item">
          <strong>完整路径：</strong>{{ currentNodeInfo.fullPath }}
        </div>
        <div v-if="currentNodeInfo.description" class="info-item">
          <strong>描述：</strong>{{ currentNodeInfo.description }}
        </div>
        <div v-if="showScores && currentNodeInfo.score !== undefined" class="info-item">
          <strong>掌握分数：</strong>
          <el-tag :type="getScoreTagType(currentNodeInfo.score)" size="small">
            {{ currentNodeInfo.score }}分
          </el-tag>
        </div>
        <div v-if="currentNodeInfo.uid" class="info-item">
          <strong>ID：</strong>{{ currentNodeInfo.uid }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'
import { getScoreTagType } from '@/utils/scoreUtils'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  selected: {
    type: Array,
    default: () => []
  },
  showScores: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:selected', 'selection-change'])

// Refs
const treeRef = ref(null)
const showInfoDialog = ref(false)
const currentNodeInfo = ref(null)

// Tree配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 处理树形数据，确保每个节点都有uid
const treeData = computed(() => {
  return processTreeData(props.data)
})

// 选中数量统计
const selectedCount = computed(() => {
  return props.selected?.length || 0
})

// 处理树形数据
function processTreeData(data) {
  if (!Array.isArray(data)) return []
  
  return data.map(item => {
    const processed = {
      ...item,
      uid: item.uid || generateUID(item.name)
    }
    
    if (item.children && Array.isArray(item.children)) {
      processed.children = processTreeData(item.children)
    }
    
    return processed
  })
}

// 生成UID
function generateUID(name) {
  return `${name}_${Math.random().toString(36).substr(2, 9)}`
}

// 获取选中的节点数据
function getSelectedNodes() {
  if (!treeRef.value || !props.selected) return []
  
  const selectedNodes = []
  const checkedKeys = props.selected
  
  function findNodesByKeys(nodes, keys) {
    nodes.forEach(node => {
      if (keys.includes(node.uid)) {
        selectedNodes.push(node)
      }
      if (node.children) {
        findNodesByKeys(node.children, keys)
      }
    })
  }
  
  findNodesByKeys(treeData.value, checkedKeys)
  return selectedNodes
}

// 处理节点选中
function handleCheck(data, checkedInfo) {
  const checkedKeys = checkedInfo.checkedKeys
  const leafKeys = getLeafNodeKeys(checkedKeys)

  emit('update:selected', leafKeys)
  emit('selection-change', leafKeys)
}

// 获取叶子节点的keys
function getLeafNodeKeys(checkedKeys) {
  const leafKeys = []

  function findLeafNodes(nodes) {
    nodes.forEach(node => {
      if (checkedKeys.includes(node.uid)) {
        if (!node.children || node.children.length === 0) {
          leafKeys.push(node.name) // 返回名称而不是uid
        } else {
          findLeafNodes(node.children)
        }
      }
    })
  }

  findLeafNodes(treeData.value)
  return leafKeys
}



// 显示节点信息
function showNodeInfo(nodeData) {
  // 计算节点的完整路径
  const fullPath = getNodePath(nodeData, treeData.value)
  currentNodeInfo.value = {
    ...nodeData,
    fullPath
  }
  showInfoDialog.value = true
}

// 获取节点的完整路径（不包含根节点和自己）
function getNodePath(targetNode, nodes, currentPath = '', isRoot = true) {
  for (const node of nodes) {
    if (node.uid === targetNode.uid || node.name === targetNode.name) {
      return currentPath || '' // 只返回父级路径，不包含自己
    }

    if (node.children && node.children.length > 0) {
      // 非叶子节点，构建路径传递给子节点
      const nodePath = isRoot ? '' : (currentPath ? `${currentPath} > ${node.name}` : node.name)
      const childPath = getNodePath(targetNode, node.children, nodePath, false)
      if (childPath !== null) {
        return childPath
      }
    }
  }
  return null
}

// 监听选中状态变化，更新树的选中状态
watch(() => props.selected, (newSelected) => {
  if (treeRef.value && newSelected) {
    nextTick(() => {
      treeRef.value.setCheckedKeys(newSelected)
    })
  }
}, { immediate: true })

// 暴露方法给父组件
defineExpose({
  getCheckedKeys: () => treeRef.value?.getCheckedKeys() || [],
  getCheckedNodes: () => treeRef.value?.getCheckedNodes() || [],
  setCheckedKeys: (keys) => treeRef.value?.setCheckedKeys(keys),
  selectAll: () => {
    const allLeafKeys = getAllLeafKeys(treeData.value)
    treeRef.value?.setCheckedKeys(allLeafKeys)
    emit('update:selected', allLeafKeys)
    emit('selection-change', allLeafKeys)
  },
  clearSelection: () => {
    treeRef.value?.setCheckedKeys([])
    emit('update:selected', [])
    emit('selection-change', [])
  }
})

// 获取所有叶子节点的keys
function getAllLeafKeys(nodes) {
  const leafKeys = []
  
  function traverse(nodeList) {
    nodeList.forEach(node => {
      if (!node.children || node.children.length === 0) {
        leafKeys.push(node.uid)
      } else {
        traverse(node.children)
      }
    })
  }
  
  traverse(nodes)
  return leafKeys
}
</script>

<style scoped>
.knowledge-tree-selector {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-container {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 10px;
}

.knowledge-tree {
  background: transparent;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 10px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-tag {
  font-size: 12px;
  min-width: 35px;
  text-align: center;
}

.info-icon {
  color: #909399;
  cursor: pointer;
  font-size: 16px;
}

.info-icon:hover {
  color: #409eff;
}

.selection-summary {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.summary-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.value {
  color: #303133;
  font-weight: 500;
}

.score-distribution {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.score-stat {
  font-size: 12px;
}

.node-info-content {
  line-height: 1.6;
}

.info-item {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-item strong {
  color: #303133;
  margin-right: 8px;
}

/* 树形组件样式覆盖 */
.knowledge-tree :deep(.el-tree-node__content) {
  height: auto;
  min-height: 32px;
  padding: 4px 0;
}

.knowledge-tree :deep(.el-tree-node__label) {
  flex: 1;
}

.knowledge-tree :deep(.el-checkbox) {
  margin-right: 8px;
}
</style>
