<template>
  <div class="markdown-editor">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button size="small" icon="el-icon-picture" @click="showImageUpload">
          插入图片
        </el-button>
        <el-button size="small" icon="el-icon-crop" @click="showImageCrop">
          截图上传
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button size="small" :type="previewMode ? 'primary' : ''" @click="togglePreview">
          {{ previewMode ? '编辑' : '预览' }}
        </el-button>
      </div>
    </div>

    <div class="editor-content" :style="{ height: height + 'px' }">
      <!-- 编辑模式 -->
      <div v-show="!previewMode" class="editor-panel">
        <el-input
          ref="textarea"
          v-model="content"
          type="textarea"
          :placeholder="placeholder"
          input-style="height: 100%;"
          :style="{ height: '100%' }"
          resize="none"
          @input="handleInput"
        />
      </div>

      <!-- 预览模式 -->
      <div v-show="previewMode" class="preview-panel">
        <markdown-viewer :content="content" />
      </div>
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog
      title="上传图片"
      v-model="uploadDialogVisible"
      width="500px"
    >
      <el-upload
        ref="upload"
        :action="uploadImageUrl"
        :headers="uploadHeaders"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :show-file-list="false"
        drag
        accept="image/*"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          支持 jpg、png、gif 格式，文件大小不超过 10MB
        </div>
      </el-upload>
    </el-dialog>

    <!-- 截图上传对话框 -->
    <el-dialog
      title="截图上传"
      v-model="cropDialogVisible"
      width="90%"
      :close-on-click-modal="false"
      @close="cancelCrop"
    >
      <div class="crop-container">
        <div class="crop-upload">
          <el-upload
            ref="cropUpload"
            :action="uploadImageUrl"
            :headers="uploadHeaders"
            :on-change="handleCropImageChange"
            :auto-upload="false"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button type="primary">选择图片</el-button>
          </el-upload>
          <div class="upload-tip">
            支持 jpg、png、gif 格式，选择图片后可以进行裁剪
          </div>
        </div>

        <div v-if="cropImageSrc" class="crop-area">
          <div class="crop-image-container">
            <img ref="cropImage" :src="cropImageSrc" style="max-width: 100%;" />
          </div>
          <div class="crop-actions">
            <el-button @click="cancelCrop">取消</el-button>
            <el-button type="primary" @click="confirmCrop" :loading="uploading">
              {{ uploading ? '上传中...' : '确认截取并上传' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import MarkdownViewer from '@/components/MarkdownViewer.vue'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import api from '@/api/index.js'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 300
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  uploadImageUrl: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const textarea = ref(null)
const content = ref(props.modelValue)
const previewMode = ref(false)
const uploadDialogVisible = ref(false)
const cropDialogVisible = ref(false)
const cropImageSrc = ref('')
const cropImageFile = ref(null)
const cropImage = ref(null)
const cropperInstance = ref(null)
const uploading = ref(false)
// 不要手动设置Content-Type，让浏览器自动设置multipart/form-data和boundary
const uploadHeaders = {}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue
  }
})

watch(content, (newValue) => {
  emit('update:modelValue', newValue)
})
// 方法
const handleInput = () => {
  emit('update:modelValue', content.value)
}

const togglePreview = () => {
  previewMode.value = !previewMode.value
}

const showImageUpload = () => {
  uploadDialogVisible.value = true
}

const showImageCrop = () => {
  cropDialogVisible.value = true
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response) => {
  if (response.success && response.data && response.data.url) {
    insertImage(response.data.url, response.data.filename)
    uploadDialogVisible.value = false
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleUploadError = (error) => {
  console.error('图片上传失败:', error)
  ElMessage.error('图片上传失败')
}

const handleCropImageChange = (file) => {
  if (file.raw) {
    cropImageFile.value = file.raw
    const reader = new FileReader()
    reader.onload = (e) => {
      cropImageSrc.value = e.target.result
      // 等待图片加载完成后初始化 cropper
      nextTick(() => {
        // 再等一个 tick 确保图片元素已经渲染
        setTimeout(() => {
          initCropper()
        }, 100)
      })
    }
    reader.readAsDataURL(file.raw)
  }
}

// 初始化 cropper
const initCropper = () => {
  if (cropperInstance.value) {
    cropperInstance.value.destroy()
  }

  if (cropImage.value) {
    cropperInstance.value = new Cropper(cropImage.value, {
      aspectRatio: NaN, // 允许自由裁剪
      viewMode: 1, // 限制裁剪框在画布内
      dragMode: 'move',
      autoCropArea: 0.8,
      restore: false,
      guides: true,
      center: true,
      highlight: false,
      cropBoxMovable: true,
      cropBoxResizable: true,
      toggleDragModeOnDblclick: false,
    })
  }
}

const confirmCrop = async () => {
  if (!cropperInstance.value) {
    ElMessage.error('请先选择图片')
    return
  }

  uploading.value = true

  // 获取裁剪后的画布
  const canvas = cropperInstance.value.getCroppedCanvas({
    width: 800, // 限制最大宽度
    height: 600, // 限制最大高度
    imageSmoothingEnabled: true,
    imageSmoothingQuality: 'high',
  })

  // 将画布转换为 Blob
  canvas.toBlob(async (blob) => {
    if (!blob) {
      ElMessage.error('图片处理失败')
      uploading.value = false
      return
    }

    // 创建 FormData 并上传
    const formData = new FormData()
    const fileName = `cropped_${Date.now()}.png`
    formData.append('file', blob, fileName)

    try {
      // 使用 axios 上传，它会自动处理代理和请求头
      // 移除 /api 前缀，因为 api 实例已经设置了 baseURL
      const apiPath = props.uploadImageUrl.replace('/api', '')
      const response = await api.post(apiPath, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      const result = response.data
      if (result.success && result.data && result.data.url) {
        insertImage(result.data.url, result.data.filename || fileName)
        cancelCrop()
        ElMessage.success('图片上传成功')
      } else {
        ElMessage.error(result.message || '图片上传失败')
      }
    } catch (error) {
      console.error('图片上传失败:', error)
      ElMessage.error('图片上传失败: ' + (error.response?.data?.message || error.message))
    } finally {
      uploading.value = false
    }
  }, 'image/png', 0.9) // 使用 PNG 格式，质量 0.9
}

const cancelCrop = () => {
  // 销毁 cropper 实例
  if (cropperInstance.value) {
    cropperInstance.value.destroy()
    cropperInstance.value = null
  }

  cropDialogVisible.value = false
  cropImageSrc.value = ''
  cropImageFile.value = null
  uploading.value = false
}

const insertImage = (url, filename) => {
  const imageMarkdown = `![${filename || '图片'}](${url})\n`

  // 获取当前光标位置
  const textareaEl = textarea.value?.$refs?.textarea
  if (textareaEl) {
    const start = textareaEl.selectionStart
    const end = textareaEl.selectionEnd

    // 插入图片markdown
    const newContent = content.value.substring(0, start) +
                      imageMarkdown +
                      content.value.substring(end)

    content.value = newContent

    // 设置新的光标位置
    nextTick(() => {
      textareaEl.focus()
      const newPosition = start + imageMarkdown.length
      textareaEl.setSelectionRange(newPosition, newPosition)
    })
  } else {
    // 如果无法获取光标位置，直接追加到末尾
    content.value += imageMarkdown
  }
}

const focus = () => {
  if (textarea.value) {
    textarea.value.focus()
  }
}

// 组件卸载时清理 cropper 实例
onUnmounted(() => {
  if (cropperInstance.value) {
    cropperInstance.value.destroy()
  }
})
</script>

<style scoped>
.markdown-editor {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 8px;
}

.editor-content {
  position: relative;
}

.editor-panel,
.preview-panel {
  height: 100%;
}

.editor-panel .el-textarea {
  height: 100%;
  width: 100%;
}

.editor-panel .el-textarea__inner {
  border: none;
  border-radius: 0;
  resize: none;
  width: 100% !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.preview-panel {
  padding: 12px;
  overflow-y: auto;
  background: #fff;
}

.crop-container {
  text-align: center;
}

.crop-upload {
  margin-bottom: 20px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.crop-area {
  margin-top: 20px;
}

.crop-image-container {
  max-height: 500px;
  overflow: hidden;
  margin: 0 auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.crop-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 8px;
}
</style>
