<template>
  <div class="knowledge-mind-map">
    <div class="mind-map-header">
      <h3>知识点脑图可视化</h3>
      <div class="header-actions">
        <div class="zoom-control">
          <span class="zoom-label">缩放:</span>
          <el-select
            v-model="currentZoom"
            @change="handleZoomChange"
            size="small"
            style="width: 80px;"
          >
            <el-option label="25%" :value="0.25" />
            <el-option label="50%" :value="0.5" />
            <el-option label="75%" :value="0.75" />
            <el-option label="100%" :value="1" />
            <el-option label="125%" :value="1.25" />
            <el-option label="150%" :value="1.5" />
            <el-option label="175%" :value="1.75" />
            <el-option label="200%" :value="2" />
          </el-select>
        </div>
        <el-button size="small" @click="resetView">
          <el-icon><Refresh /></el-icon>
          重置视图
        </el-button>
        <el-button size="small" @click="exportImage">
          <el-icon><Download /></el-icon>
          导出图片
        </el-button>
        <el-button size="small" @click="toggleFullscreen">
          <el-icon><FullScreen v-if="!isFullscreen" /><Aim v-else /></el-icon>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
        <el-button size="small" @click="$emit('close')">
          <el-icon><Close /></el-icon>
          关闭
        </el-button>
      </div>
    </div>
    
    <div class="mind-map-container" ref="mindMapContainer"></div>
    
    <div class="mind-map-legend">
      <h4>分数图标和颜色说明：</h4>
      <div class="legend-items">
        <div class="legend-item">
          <span class="icon-demo">🔴</span>
          <span style="color: #ff4d4f; font-weight: bold;">1分 - 红色</span>
        </div>
        <div class="legend-item">
          <span class="icon-demo">🟡</span>
          <span style="color: #faad14; font-weight: bold;">2分 - 黄色</span>
        </div>
        <div class="legend-item">
          <span class="icon-demo">⚪</span>
          <span style="color: #666666;">3分 - 白色</span>
        </div>
        <div class="legend-item">
          <span class="icon-demo">🔵</span>
          <span style="color: #1890ff; font-weight: bold;">4分 - 蓝色</span>
        </div>
        <div class="legend-item">
          <span class="icon-demo">🟢</span>
          <span style="color: #52c41a; font-weight: bold;">5分 - 绿色</span>
        </div>
        <div class="legend-item">
          <span class="icon-demo">⚪</span>
          <span style="color: #666666;">无分数 - 白色（默认3分）</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download, Close, FullScreen, Aim } from '@element-plus/icons-vue'
import MindMap from 'simple-mind-map'

const props = defineProps({
  knowledgeData: {
    type: [Array, Object],
    required: true
  }
})

const emit = defineEmits(['close'])

const mindMapContainer = ref(null)
let mindMapInstance = null
const isFullscreen = ref(false)
const currentZoom = ref(1) // 当前缩放比例，默认100%

// 分数对应的颜色和图标映射
const scoreStyleMap = {
  1: {
    color: '#ff4d4f', // 红色
    icon: '🔴', // 红色圆圈
    fontWeight: 'bold'
  },
  2: {
    color: '#faad14', // 黄色
    icon: '🟡', // 黄色圆圈
    fontWeight: 'bold'
  },
  3: {
    color: '#666666', // 灰色文字，白色背景
    icon: '⚪', // 白色圆圈
    fontWeight: 'normal',
    fillColor: '#ffffff',
    borderColor: '#d9d9d9'
  },
  4: {
    color: '#1890ff', // 蓝色
    icon: '🔵', // 蓝色圆圈
    fontWeight: 'bold'
  },
  5: {
    color: '#52c41a', // 绿色
    icon: '🟢', // 绿色圆圈
    fontWeight: 'bold'
  },
  default: {
    color: '#666666', // 灰色文字，白色背景
    icon: '⚪', // 白色圆圈
    fontWeight: 'normal',
    fillColor: '#ffffff',
    borderColor: '#d9d9d9'
  }
}

// 获取节点样式
const getNodeStyle = (score) => {
  if (score >= 1 && score <= 5) {
    return scoreStyleMap[score]
  }
  return scoreStyleMap.default
}

// 转换知识点数据为脑图数据格式
const transformKnowledgeData = (data) => {
  if (!data || (Array.isArray(data) && data.length === 0)) {
    return {
      data: {
        text: '暂无知识点数据',
        fillColor: scoreStyleMap.default.fillColor || '#ffffff',
        borderColor: scoreStyleMap.default.borderColor || '#d9d9d9',
        color: scoreStyleMap.default.color
      }
    }
  }

  // 如果是数组，创建一个根节点
  if (Array.isArray(data)) {
    if (data.length === 1) {
      return transformNode(data[0])
    } else {
      return {
        data: {
          text: '知识点体系',
          fillColor: scoreStyleMap.default.fillColor || '#ffffff',
          borderColor: scoreStyleMap.default.borderColor || '#d9d9d9',
          color: scoreStyleMap.default.color
        },
        children: data.map(item => transformNode(item))
      }
    }
  } else {
    return transformNode(data)
  }
}

// 转换单个节点
const transformNode = (node) => {
  const nodeStyle = getNodeStyle(node.score)

  // 构建节点文本，包含图标
  let nodeText = `${nodeStyle.icon} ${node.name || '未命名知识点'}`

  // 如果有描述，添加到文本中
  if (node.description) {
    nodeText += `\n${node.description}`
  }

  // 如果有分数，显示分数
  if (node.score !== undefined && node.score !== null) {
    nodeText += `\n(${node.score}分)`
  }

  const nodeData = {
    text: nodeText,
    fillColor: nodeStyle.fillColor || '#ffffff', // 背景色
    borderColor: nodeStyle.borderColor || '#d9d9d9', // 边框色
    borderWidth: 1,
    color: nodeStyle.color, // 文字颜色
    fontSize: 14,
    fontWeight: nodeStyle.fontWeight || 'normal'
  }

  // 如果有uid，可以在调试时显示
  if (node.uid && import.meta.env.DEV) {
    console.log(`节点 ${node.name} 的UID: ${node.uid}, 样式:`, nodeStyle)
  }

  const result = {
    data: nodeData
  }

  // 递归处理子节点
  if (node.children && Array.isArray(node.children) && node.children.length > 0) {
    result.children = node.children.map(child => transformNode(child))
  }

  return result
}

// 初始化脑图
const initMindMap = async () => {
  if (!mindMapContainer.value) return

  try {
    const mindMapData = transformKnowledgeData(props.knowledgeData)
    
    mindMapInstance = new MindMap({
      el: mindMapContainer.value,
      data: mindMapData,
      layout: 'mindMap', // 左右对称结构
      theme: 'default',
      enableFreeDrag: true,
      enableNodeEdit: false,
      enableShortcutKey: true,
      mousewheelAction: 'zoom',
      mousewheelMoveStep: 100,
      readonly: true,
      initRootNodePosition: ['center', 'center'],
      nodeTextEditZIndex: 1000,
      nodeNoteTooltipZIndex: 1000,
      isEndNodeTextEditOnClickOuter: true,
      maxTag: 5,
      exportPadding: 20,
      nodeTextEditMaxWidth: 500,
      customNoteContentShow: {
        text: '备注',
        icon: ''
      },
      // 主题配置，确保节点样式能正确应用
      themeConfig: {
        // 确保节点能显示自定义颜色
        node: {
          shape: 'rectangle',
          borderRadius: 5,
          borderWidth: 1
        },
        second: {
          shape: 'rectangle',
          borderRadius: 5,
          borderWidth: 1
        },
        root: {
          shape: 'rectangle',
          borderRadius: 5,
          borderWidth: 1
        }
      }
    })

    // 监听节点点击事件
    mindMapInstance.on('node_click', (node, e) => {
      console.log('点击节点:', node.nodeData.data.text)
    })

    // 监听缩放变化事件
    mindMapInstance.on('view_data_change', (data) => {
      if (data && data.scale !== undefined) {
        // 找到最接近的缩放级别
        const zoomLevels = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2]
        const currentScale = data.scale
        const closestZoom = zoomLevels.reduce((prev, curr) =>
          Math.abs(curr - currentScale) < Math.abs(prev - currentScale) ? curr : prev
        )

        // 只有当差异较小时才更新选择器
        if (Math.abs(closestZoom - currentScale) < 0.1) {
          currentZoom.value = closestZoom
        }
      }
    })

    // 调试：查看可用的方法
    console.log('脑图实例:', mindMapInstance)
    console.log('view对象:', mindMapInstance.view)
    if (mindMapInstance.view) {
      console.log('view可用方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(mindMapInstance.view)))
    }

    console.log('脑图初始化成功')
  } catch (error) {
    console.error('脑图初始化失败:', error)
    ElMessage.error('脑图初始化失败: ' + error.message)
  }
}

// 重置视图
const resetView = () => {
  if (mindMapInstance) {
    mindMapInstance.view.reset()
    currentZoom.value = 1 // 重置缩放比例
    ElMessage.success('视图已重置')
  }
}

// 处理缩放变化
const handleZoomChange = (zoom) => {
  if (mindMapInstance) {
    try {
      // 尝试不同的缩放API
      if (mindMapInstance.view && mindMapInstance.view.setScale) {
        mindMapInstance.view.setScale(zoom)
      } else if (mindMapInstance.setZoom) {
        mindMapInstance.setZoom(zoom)
      } else if (mindMapInstance.view && mindMapInstance.view.zoom) {
        mindMapInstance.view.zoom(zoom)
      } else if (mindMapInstance.view && mindMapInstance.view.setZoomScale) {
        mindMapInstance.view.setZoomScale(zoom)
      } else {
        // 手动设置变换
        const svg = mindMapContainer.value.querySelector('svg')
        if (svg) {
          const g = svg.querySelector('g')
          if (g) {
            g.setAttribute('transform', `scale(${zoom})`)
          }
        }
      }
      ElMessage.success(`缩放已设置为 ${Math.round(zoom * 100)}%`)
    } catch (error) {
      console.error('设置缩放失败:', error)
      ElMessage.error('设置缩放失败: ' + error.message)
    }
  }
}

// 导出图片
const exportImage = () => {
  if (mindMapInstance) {
    try {
      mindMapInstance.export('png', true, '知识点脑图')
      ElMessage.success('图片导出成功')
    } catch (error) {
      console.error('导出图片失败:', error)
      ElMessage.error('导出图片失败: ' + error.message)
    }
  }
}

// 切换全屏
const toggleFullscreen = async () => {
  try {
    if (!isFullscreen.value) {
      // 进入全屏 - 直接对整个知识点脑图容器进行全屏
      const element = mindMapContainer.value?.parentElement || document.querySelector('.knowledge-mind-map')
      console.log('尝试全屏的元素:', element)

      if (element) {
        if (element.requestFullscreen) {
          await element.requestFullscreen()
        } else if (element.webkitRequestFullscreen) {
          await element.webkitRequestFullscreen()
        } else if (element.mozRequestFullScreen) {
          await element.mozRequestFullScreen()
        } else if (element.msRequestFullscreen) {
          await element.msRequestFullscreen()
        } else {
          throw new Error('浏览器不支持全屏API')
        }

        isFullscreen.value = true
        ElMessage.success('已进入全屏模式')

        // 全屏后重新调整脑图大小
        setTimeout(() => {
          if (mindMapInstance) {
            mindMapInstance.resize()
          }
        }, 200)
      } else {
        throw new Error('未找到可全屏的元素')
      }
    } else {
      // 退出全屏
      if (document.exitFullscreen) {
        await document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen()
      }

      isFullscreen.value = false
      ElMessage.success('已退出全屏模式')

      // 退出全屏后重新调整脑图大小
      setTimeout(() => {
        if (mindMapInstance) {
          mindMapInstance.resize()
        }
      }, 200)
    }
  } catch (error) {
    console.error('全屏切换失败:', error)
    ElMessage.error('全屏切换失败: ' + error.message)
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  const isCurrentlyFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  )

  if (isFullscreen.value !== isCurrentlyFullscreen) {
    isFullscreen.value = isCurrentlyFullscreen

    // 调整脑图大小
    setTimeout(() => {
      if (mindMapInstance) {
        mindMapInstance.resize()
      }
    }, 100)
  }
}

onMounted(async () => {
  await nextTick()
  initMindMap()

  // 添加全屏状态监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
})

onUnmounted(() => {
  if (mindMapInstance) {
    mindMapInstance.destroy()
    mindMapInstance = null
  }

  // 移除全屏状态监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
})
</script>

<style scoped>
.knowledge-mind-map {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mind-map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.mind-map-header h3 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.zoom-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.mind-map-container {
  flex: 1;
  min-height: 500px;
  background: #f5f5f5;
  position: relative;
}

.mind-map-legend {
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.mind-map-legend h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.icon-demo {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* 全屏模式样式 */
.knowledge-mind-map:fullscreen {
  background: #f5f5f5;
  z-index: 9999;
}

.knowledge-mind-map:fullscreen .mind-map-container {
  height: calc(100vh - 120px) !important;
}

.knowledge-mind-map:fullscreen .mind-map-header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.knowledge-mind-map:fullscreen .mind-map-legend {
  background: #fff;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}
</style>
