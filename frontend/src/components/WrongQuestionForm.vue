<template>
  <div class="wrong-question-form">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="140px"
      label-position="left"
    >
      <!-- 知识点选择 -->
      <el-form-item label="知识点" prop="knowledgePointId" class="form-item-horizontal">
        <knowledge-point-selector
          v-model="formData.knowledgePointId"
          :project-id="projectId"
          @change="handleKnowledgePointChange"
          placeholder="请选择知识点"
          class="full-width-input"
        />
      </el-form-item>

      <!-- Markdown字段手风琴 -->
      <div class="markdown-accordion">
        <el-collapse
          v-model="activeCollapse"
          accordion
          class="markdown-collapse"
        >
          <!-- 错题内容 -->
          <el-collapse-item name="content" class="required-field">
            <template #title>
              <span class="collapse-title">
                <span class="required-star">*</span>
                错题内容
              </span>
            </template>
            <el-form-item prop="contentMarkdown" class="collapse-form-item">
              <markdown-editor
                v-model="formData.contentMarkdown"
                :height="300"
                :upload-image-url="`/api/projects/${props.projectId}/wrong-questions/upload-image`"
                placeholder="请输入错题内容，支持Markdown格式和图片上传..."
                class="full-width-editor"
              />
            </el-form-item>
          </el-collapse-item>

          <!-- 做错原因说明 -->
          <el-collapse-item name="wrongReason">
            <template #title>
              <span class="collapse-title">做错原因说明</span>
            </template>
            <el-form-item class="collapse-form-item">
              <markdown-editor
                v-model="formData.wrongReasonMarkdown"
                :height="300"
                :upload-image-url="`/api/projects/${props.projectId}/wrong-questions/upload-image`"
                placeholder="请说明做错的原因，支持Markdown格式和图片上传..."
                class="full-width-editor"
              />
            </el-form-item>
          </el-collapse-item>

          <!-- 正确解题说明 -->
          <el-collapse-item name="correctSolution">
            <template #title>
              <span class="collapse-title">正确解题说明</span>
            </template>
            <el-form-item class="collapse-form-item">
              <markdown-editor
                v-model="formData.correctSolutionMarkdown"
                :height="300"
                :upload-image-url="`/api/projects/${props.projectId}/wrong-questions/upload-image`"
                placeholder="请说明正确的解题方法，支持Markdown格式和图片上传..."
                class="full-width-editor"
              />
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </div>


    </el-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import KnowledgePointSelector from '@/components/KnowledgePointSelector.vue'
import MarkdownEditor from '@/components/MarkdownEditor.vue'
import { VALIDATION_RULES } from '@/utils/constants'

// Props
const props = defineProps({
  mode: {
    type: String,
    default: 'create', // 'create' or 'edit'
    validator: value => ['create', 'edit'].includes(value)
  },
  question: {
    type: Object,
    default: null
  },
  projectId: {
    type: [String, Number],
    required: true
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const form = ref(null)
const activeCollapse = ref('content') // 默认展开第一个（错题内容）
const formData = ref({
  knowledgePointId: '',
  knowledgePointName: '',
  knowledgePointPath: '',
  contentMarkdown: '',
  wrongReasonMarkdown: '',
  correctSolutionMarkdown: ''
})

const rules = {
  knowledgePointId: VALIDATION_RULES.KNOWLEDGE_POINT,
  contentMarkdown: VALIDATION_RULES.WRONG_QUESTION_CONTENT
}

// 方法定义（需要在监听器之前定义）
const resetForm = () => {
  formData.value = {
    knowledgePointId: '',
    knowledgePointName: '',
    knowledgePointPath: '',
    contentMarkdown: '',
    wrongReasonMarkdown: '',
    correctSolutionMarkdown: ''
  }
  form.value && form.value.resetFields()
  // 重置手风琴为默认展开第一个
  activeCollapse.value = 'content'
}

// 监听器
watch(() => props.question, (newQuestion) => {
  if (newQuestion && props.mode === 'edit') {
    formData.value = {
      knowledgePointId: newQuestion.knowledgePointId || '',
      knowledgePointName: newQuestion.knowledgePointName || '',
      knowledgePointPath: newQuestion.knowledgePointPath || '',
      contentMarkdown: newQuestion.contentMarkdown || '',
      wrongReasonMarkdown: newQuestion.wrongReasonMarkdown || '',
      correctSolutionMarkdown: newQuestion.correctSolutionMarkdown || ''
    }
  } else if (!newQuestion && props.mode === 'create') {
    // 创建模式时重置表单
    resetForm()
  }
}, { immediate: true })

// 监听模式变化
watch(() => props.mode, (newMode) => {
  if (newMode === 'create') {
    resetForm()
  }
})

// 其他方法
const handleKnowledgePointChange = (knowledgePoint) => {
  if (knowledgePoint) {
    formData.value.knowledgePointId = knowledgePoint.uid
    formData.value.knowledgePointName = knowledgePoint.name
    formData.value.knowledgePointPath = knowledgePoint.path
  } else {
    formData.value.knowledgePointId = ''
    formData.value.knowledgePointName = ''
    formData.value.knowledgePointPath = ''
  }
}

const handleSubmit = () => {
  form.value.validate((valid) => {
    if (valid) {
      emit('submit', { ...formData.value })
    } else {
      ElMessage.error('请检查表单填写是否正确')
    }
  })
}

const handleCancel = () => {
  emit('cancel')
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  handleSubmit
})
</script>

<style scoped>
.wrong-question-form {
  padding: 20px 24px;
  max-width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.wrong-question-form .el-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子元素收缩 */
}

/* 水平布局的表单项 */
.form-item-horizontal {
  margin-bottom: 24px;
}

.form-item-horizontal .full-width-input {
  width: 100%;
}

/* Markdown手风琴样式 */
.markdown-accordion {
  margin-bottom: 20px;
  flex: 1;
  min-height: 0; /* 允许flex子元素收缩 */
}

.markdown-collapse {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: visible; /* 改为visible，让内容可以正常滚动 */
  flex: 1;
}

.markdown-collapse :deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  padding: 16px 20px;
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  transition: all 0.3s ease;
  cursor: pointer;
}

.markdown-collapse :deep(.el-collapse-item__header:hover) {
  background-color: #f0f2f5;
  transform: translateX(2px);
}

.markdown-collapse :deep(.el-collapse-item.is-active .el-collapse-item__header) {
  background-color: #e8f4fd;
  border-bottom: 1px solid #409eff;
}

.markdown-collapse :deep(.el-collapse-item__content) {
  padding: 0;
  background-color: #fff;
  transition: all 0.3s ease;
  max-height: 450px; /* 限制最大高度，确保能看到内容 */
  overflow-y: auto;
}

.markdown-collapse :deep(.el-collapse-item__wrap) {
  border-bottom: none;
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 6px;
}

.required-star {
  color: #f56c6c;
  font-weight: bold;
}

.collapse-form-item {
  margin-bottom: 0;
  padding: 20px;
}

.collapse-form-item :deep(.el-form-item__label) {
  display: none;
}

.collapse-form-item :deep(.el-form-item__content) {
  margin-left: 0 !important;
  width: 100% !important;
}

/* Markdown编辑器表单项 */
.form-item-markdown {
  margin-bottom: 32px;
}

.form-item-markdown :deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  line-height: 1.5;
  padding-bottom: 8px;
}

.form-item-markdown :deep(.el-form-item__content) {
  width: 100%;
  max-width: 100%;
}

.full-width-editor {
  width: 100% !important;
  max-width: 100% !important;
}

/* 确保markdown编辑器内部组件也是全宽 */
.form-item-markdown :deep(.markdown-editor) {
  width: 100% !important;
}

.form-item-markdown :deep(.editor-container) {
  width: 100% !important;
}

.form-item-markdown :deep(.el-textarea) {
  width: 100% !important;
}

.form-item-markdown :deep(.el-textarea__inner) {
  width: 100% !important;
  resize: vertical;
}



/* 通用表单项样式 */
.el-form-item {
  margin-bottom: 24px;
}

.el-form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wrong-question-form {
    padding: 16px;
  }

  :deep(.el-form--label-left .el-form-item__label) {
    text-align: left;
    width: 100% !important;
    margin-bottom: 8px;
  }

  :deep(.el-form--label-left .el-form-item__content) {
    margin-left: 0 !important;
  }

  .form-item-markdown {
    margin-bottom: 24px;
  }

  .markdown-collapse :deep(.el-collapse-item__header) {
    padding: 12px 16px;
    font-size: 13px;
  }

  .collapse-form-item {
    padding: 16px;
  }

  /* 移动设备上调整编辑器高度 */
  .collapse-form-item :deep(.markdown-editor) {
    height: 280px !important;
  }
}
</style>
