<template>
  <el-card class="exam-records-card">
    <template #header>
      <div class="card-header">
        <span>考试记录</span>
        <div class="header-actions">
          <el-button @click="refreshData" size="small">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </template>

    <!-- 统计信息 -->
    <div v-if="statistics" class="statistics-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalRecords }}</div>
            <div class="stat-label">总考试次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.passedRecords }}</div>
            <div class="stat-label">及格次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatScore(statistics.avgScore) }}分</div>
            <div class="stat-label">平均分</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatScore(statistics.maxScore) }}分</div>
            <div class="stat-label">最高分</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 考试记录表格 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else-if="examRecords.length === 0" class="empty-container">
      <el-empty description="暂无考试记录">
        <el-button type="primary" @click="goToExamList">
          <el-icon><Plus /></el-icon>
          去考卷管理
        </el-button>
      </el-empty>
    </div>

    <el-table v-else :data="examRecords" stripe>
      <el-table-column prop="examPaperTitle" label="考卷名称" min-width="200">
        <template #default="{ row }">
          <el-link @click="goToExamDetail(row.examPaperId)" type="primary">
            {{ row.examPaperTitle }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="examDate" label="考试日期" width="120">
        <template #default="{ row }">
          {{ formatDate(row.examDate, 'date') }}
        </template>
      </el-table-column>
      <el-table-column prop="score" label="成绩" width="100">
        <template #default="{ row }">
          <el-tag :type="getScoreTagType(row.score)" size="large">
            {{ formatScore(row.score) }}分
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remarks" label="备注" show-overflow-tooltip>
        <template #default="{ row }">
          {{ row.remarks || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editRecord(row)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button size="small" @click="goToExamRecords(row.examPaperId)">
            <el-icon><View /></el-icon>
            详情
          </el-button>
          <el-button size="small" type="danger" @click="deleteRecord(row)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑记录对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="editingRecord ? '编辑考试记录' : '添加考试记录'"
      width="500px"
    >
      <el-form
        ref="recordFormRef"
        :model="recordForm"
        :rules="recordRules"
        label-width="100px"
      >
        <el-form-item label="考试日期" prop="examDate">
          <el-date-picker
            v-model="recordForm.examDate"
            type="date"
            placeholder="选择考试日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="考试成绩" prop="score">
          <el-input-number
            v-model="recordForm.score"
            :min="0"
            :max="100"
            :precision="2"
            placeholder="请输入考试成绩"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="recordForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRecord" :loading="submitting">
            {{ editingRecord ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Plus, Edit, View, Delete
} from '@element-plus/icons-vue'
import { examRecordAPI, projectAPI } from '@/api'
import { formatDate } from '@/utils/environment'
import { scoreUtils } from '@/utils/dataUtils'
import { VALIDATION_RULES } from '@/utils/constants'
import { executeWithLoading } from '@/utils/apiUtils'

const props = defineProps({
  projectId: {
    type: Number,
    required: true
  },
  examPapers: {
    type: Array,
    default: () => []
  },
  examRecords: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['refresh'])

const router = useRouter()

const examRecords = ref([])
const statistics = ref(null)
const loading = ref(false)
const showEditDialog = ref(false)
const editingRecord = ref(null)
const submitting = ref(false)

// 表单数据
const recordForm = ref({
  examDate: '',
  score: null,
  remarks: ''
})

// 表单验证规则
const recordRules = {
  examDate: VALIDATION_RULES.EXAM_DATE,
  score: VALIDATION_RULES.EXAM_SCORE,
  remarks: VALIDATION_RULES.OPTIONAL_TEXT
}

const recordFormRef = ref()

// 加载所有考试记录
const loadAllExamRecords = async () => {
  if (!props.examPapers || props.examPapers.length === 0) {
    examRecords.value = []
    statistics.value = null
    return
  }

  await executeWithLoading(async () => {
    const allRecords = []
    let totalRecords = 0
    let passedRecords = 0
    let totalScore = 0
    let maxScore = 0

    // 并行获取所有考卷的记录
    const recordPromises = props.examPapers.map(async (examPaper) => {
      try {
        const response = await examRecordAPI.getExamRecords(props.projectId, examPaper.id)
        const records = response.data.data || []
        
        // 为每条记录添加考卷标题
        const recordsWithTitle = records.map(record => ({
          ...record,
          examPaperTitle: examPaper.title,
          examPaperId: examPaper.id
        }))
        
        allRecords.push(...recordsWithTitle)
        
        // 计算统计信息
        records.forEach(record => {
          totalRecords++
          totalScore += parseFloat(record.score || 0)
          if (record.score >= 60) passedRecords++
          if (record.score > maxScore) maxScore = record.score
        })
      } catch (error) {
        console.error(`获取考卷 ${examPaper.title} 的记录失败:`, error)
      }
    })

    await Promise.all(recordPromises)

    // 按考试日期倒序排列
    allRecords.sort((a, b) => new Date(b.examDate) - new Date(a.examDate))
    examRecords.value = allRecords

    // 计算统计信息
    statistics.value = {
      totalRecords,
      passedRecords,
      avgScore: totalRecords > 0 ? (totalScore / totalRecords) : 0,
      maxScore
    }
  }, {
    loadingRef: loading,
    errorMessage: '加载考试记录失败'
  })
}

// 监听考卷变化，重新加载数据
watch(() => props.examPapers, () => {
  loadAllExamRecords()
}, { immediate: true })

onMounted(() => {
  loadAllExamRecords()
})

// 使用工具类中的函数
const formatScore = scoreUtils.formatScore
const getScoreTagType = scoreUtils.getScoreTagType

// 刷新数据
const refreshData = () => {
  emit('refresh')
  loadAllExamRecords()
}

// 跳转到考卷列表
const goToExamList = () => {
  router.push(`/projects/${props.projectId}/exams`)
}

// 跳转到考卷详情
const goToExamDetail = (examId) => {
  router.push(`/projects/${props.projectId}/exams/${examId}/records`)
}

// 跳转到考试记录详情
const goToExamRecords = (examId) => {
  router.push(`/projects/${props.projectId}/exams/${examId}/records`)
}

// 编辑记录
const editRecord = (record) => {
  editingRecord.value = record
  recordForm.value = {
    examDate: record.examDate,
    score: record.score,
    remarks: record.remarks || ''
  }
  showEditDialog.value = true
}

// 保存记录
const saveRecord = async () => {
  if (!recordFormRef.value) return

  try {
    await recordFormRef.value.validate()
  } catch (error) {
    return
  }

  await executeWithLoading(async () => {
    if (editingRecord.value) {
      // 更新记录
      await examRecordAPI.updateExamRecord(
        props.projectId,
        editingRecord.value.examPaperId,
        editingRecord.value.id,
        recordForm.value
      )
      ElMessage.success('考试记录更新成功')
    }

    showEditDialog.value = false
    editingRecord.value = null
    recordForm.value = {
      examDate: '',
      score: null,
      remarks: ''
    }
    
    refreshData()
  }, {
    loadingRef: submitting,
    errorMessage: '保存考试记录失败'
  })
}

// 删除记录
const deleteRecord = async (record) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条考试记录吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await examRecordAPI.deleteExamRecord(
      props.projectId,
      record.examPaperId,
      record.id
    )
    
    ElMessage.success('考试记录删除成功')
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除考试记录失败:', error)
      ElMessage.error('删除考试记录失败')
    }
  }
}
</script>

<style scoped>
.exam-records-card {
  margin-top: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.loading-container,
.empty-container {
  padding: 40px 0;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>
