<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button size="small" @click="showImageUpload">
          <el-icon><UploadFilled /></el-icon>
          插入图片
        </el-button>
        <el-button size="small" @click="showImageCrop">
          <el-icon><Crop /></el-icon>
          截图上传
        </el-button>

      </div>
    </div>

    <div class="editor-content" :style="{ height: height + 'px' }">
      <QuillEditor
        ref="quillEditor"
        v-model:content="content"
        :options="editorOptions"
        content-type="html"
        @update:content="handleContentChange"
        :style="{ height: '100%' }"
      />
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传图片"
      width="600px"
      :close-on-click-modal="false"
      :append-to-body="true"
      class="image-upload-dialog"
    >
      <div class="upload-container">
        <el-upload
          ref="uploadRef"
          :action="uploadImageUrl"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          :show-file-list="false"
          accept="image/*"
          drag
          class="upload-dragger"
        >
          <el-icon class="el-icon--upload" style="font-size: 48px; color: #409eff;"><upload-filled /></el-icon>
          <div class="el-upload__text" style="font-size: 16px; margin-top: 16px;">
            将图片拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip" style="margin-top: 12px; font-size: 14px;">
              只能上传jpg/png文件，且不超过2MB
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 截图上传对话框 -->
    <el-dialog
      v-model="cropDialogVisible"
      title="截图上传"
      width="900px"
      :close-on-click-modal="false"
      :append-to-body="true"
      class="image-crop-dialog"
    >
      <div class="crop-container">
        <div class="crop-upload">
          <el-upload
            ref="cropUpload"
            :action="uploadImageUrl"
            :headers="uploadHeaders"
            :on-change="handleCropImageChange"
            :auto-upload="false"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button type="primary">选择图片</el-button>
            <template #tip>
              <div class="upload-tip">支持 jpg、png、gif 格式，选择图片后可以进行裁剪</div>
            </template>
          </el-upload>
        </div>
        
        <div v-if="cropImageSrc" class="crop-area">
          <div class="crop-image-container">
            <img
              ref="cropImage"
              :src="cropImageSrc"
              alt="待裁剪图片"
              style="max-width: 100%;"
            />
          </div>
          <div class="crop-actions">
            <el-button @click="resetCrop">重置</el-button>
            <el-button type="primary" @click="confirmCrop" :loading="uploading">
              确认裁剪并上传
            </el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="closeCropDialog">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Crop } from '@element-plus/icons-vue'
import { QuillEditor, Quill } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import api from '@/api'
import DOMPurify from 'dompurify'

// 配置自定义字号选项
const Size = Quill.import('formats/size')
Size.whitelist = ['10px', '12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '36px', '48px', '60px', '72px', '96px', '120px']
Quill.register(Size, true)

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 300
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  uploadImageUrl: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const quillEditor = ref(null)
const content = ref(props.modelValue)
const uploadDialogVisible = ref(false)
const cropDialogVisible = ref(false)
const cropImageSrc = ref('')
const cropImageFile = ref(null)
const cropImage = ref(null)
const cropperInstance = ref(null)
const uploading = ref(false)
const uploadRef = ref(null)
const cropUpload = ref(null)


// 编辑器配置
const editorOptions = {
  theme: 'snow',
  placeholder: props.placeholder,
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['10px', '12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '36px', '48px', '60px', '72px', '96px', '120px'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link']
    ]
  }
}

const uploadHeaders = {}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue
  }
})

// 方法
const handleContentChange = (newContent) => {
  const cleanContent = DOMPurify.sanitize(newContent)
  emit('update:modelValue', cleanContent)
}

const showImageUpload = () => {
  uploadDialogVisible.value = true
}

const showImageCrop = () => {
  cropDialogVisible.value = true
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const onUploadSuccess = (response) => {
  if (response.success && response.data?.url) {
    insertImage(response.data.url, response.data.filename)
    ElMessage.success('图片上传成功')
    uploadDialogVisible.value = false
  } else {
    ElMessage.error('图片上传失败')
  }
}

const onUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('图片上传失败')
}

const insertImage = (url, filename) => {
  if (quillEditor.value) {
    const quill = quillEditor.value.getQuill()
    const range = quill.getSelection()
    const index = range ? range.index : quill.getLength()
    
    quill.insertEmbed(index, 'image', url)
    quill.setSelection(index + 1)
  }
}

// 截图相关方法
const handleCropImageChange = (file) => {
  if (file.raw) {
    cropImageFile.value = file.raw
    const reader = new FileReader()
    reader.onload = (e) => {
      cropImageSrc.value = e.target.result
      // 等待图片加载完成后初始化 cropper
      nextTick(() => {
        // 再等一个 tick 确保图片元素已经渲染
        setTimeout(() => {
          initCropper()
        }, 100)
      })
    }
    reader.readAsDataURL(file.raw)
  }
}

// 初始化 cropper
const initCropper = () => {
  if (cropperInstance.value) {
    cropperInstance.value.destroy()
  }

  if (cropImage.value) {
    cropperInstance.value = new Cropper(cropImage.value, {
      aspectRatio: NaN, // 允许自由裁剪
      viewMode: 1, // 限制裁剪框在画布内
      dragMode: 'move',
      autoCropArea: 0.8,
      restore: false,
      guides: true,
      center: true,
      highlight: false,
      cropBoxMovable: true,
      cropBoxResizable: true,
      toggleDragModeOnDblclick: false,
    })
  }
}

const resetCrop = () => {
  if (cropperInstance.value) {
    cropperInstance.value.reset()
  }
}

const confirmCrop = async () => {
  if (!cropperInstance.value) {
    ElMessage.error('请先选择图片')
    return
  }

  uploading.value = true

  // 获取裁剪后的画布
  const canvas = cropperInstance.value.getCroppedCanvas({
    width: 800, // 限制最大宽度
    height: 600, // 限制最大高度
    imageSmoothingEnabled: true,
    imageSmoothingQuality: 'high',
  })

  // 将画布转换为 Blob
  canvas.toBlob(async (blob) => {
    if (!blob) {
      ElMessage.error('图片处理失败')
      uploading.value = false
      return
    }

    // 创建 FormData 并上传
    const formData = new FormData()
    const fileName = `cropped_${Date.now()}.png`
    formData.append('file', blob, fileName)

    try {
      // 使用 axios 上传，它会自动处理代理和请求头
      // 移除 /api 前缀，因为 api 实例已经设置了 baseURL
      const apiPath = props.uploadImageUrl.replace('/api', '')
      const response = await api.post(apiPath, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      const result = response.data
      if (result.success && result.data && result.data.url) {
        insertImage(result.data.url, result.data.filename || fileName)
        closeCropDialog()
        ElMessage.success('图片上传成功')
      } else {
        ElMessage.error(result.message || '图片上传失败')
      }
    } catch (error) {
      console.error('图片上传失败:', error)
      ElMessage.error('图片上传失败: ' + (error.response?.data?.message || error.message))
    } finally {
      uploading.value = false
    }
  }, 'image/png', 0.9) // 使用 PNG 格式，质量 0.9
}

const closeCropDialog = () => {
  cropDialogVisible.value = false
  cropImageSrc.value = ''
  cropImageFile.value = null
  if (cropperInstance.value) {
    cropperInstance.value.destroy()
    cropperInstance.value = null
  }
}

const cancelCrop = () => {
  closeCropDialog()
}

const focus = () => {
  if (quillEditor.value) {
    quillEditor.value.getQuill().focus()
  }
}



// 暴露方法给父组件
defineExpose({
  focus
})

// 生命周期
onMounted(() => {
  // 组件挂载后的初始化逻辑
})

onUnmounted(() => {
  if (cropperInstance.value) {
    cropperInstance.value.destroy()
  }
})
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: visible; /* 改为 visible 以允许下拉菜单显示 */
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.editor-content {
  position: relative;
}

.upload-container {
  text-align: center;
  padding: 20px;
  min-height: 200px;
}

.upload-dragger {
  width: 100% !important;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100% !important;
  height: 200px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
}

.crop-container {
  text-align: center;
}

.crop-upload {
  margin-bottom: 20px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.crop-area {
  margin-top: 20px;
}

.crop-image-container {
  max-height: 500px;
  overflow: hidden;
  margin: 0 auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.crop-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>

<style>
/* Quill编辑器样式覆盖 */
.rich-text-editor .ql-editor {
  min-height: 200px;
  font-size: 14px;
  line-height: 1.6;
}

.rich-text-editor .ql-toolbar {
  border: none;
  border-bottom: 1px solid #dcdfe6;
}

.rich-text-editor .ql-container {
  border: none;
  font-size: 14px;
}

/* 确保工具栏下拉菜单能正确显示 */
.rich-text-editor .ql-toolbar .ql-picker {
  position: relative;
}

.rich-text-editor .ql-toolbar .ql-picker-options {
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  position: absolute;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 图片上传对话框样式 */
.image-upload-dialog.el-dialog {
  min-width: 600px !important;
  min-height: 400px !important;
}

.image-upload-dialog .el-dialog__body {
  padding: 20px !important;
  min-height: 300px !important;
}

.image-crop-dialog.el-dialog {
  min-width: 900px !important;
  min-height: 600px !important;
}

.image-crop-dialog .el-dialog__body {
  padding: 20px !important;
  min-height: 500px !important;
}

/* 自定义字号样式 */
.rich-text-editor .ql-editor .ql-size-10px { font-size: 10px; }
.rich-text-editor .ql-editor .ql-size-12px { font-size: 12px; }
.rich-text-editor .ql-editor .ql-size-14px { font-size: 14px; }
.rich-text-editor .ql-editor .ql-size-16px { font-size: 16px; }
.rich-text-editor .ql-editor .ql-size-18px { font-size: 18px; }
.rich-text-editor .ql-editor .ql-size-20px { font-size: 20px; }
.rich-text-editor .ql-editor .ql-size-24px { font-size: 24px; }
.rich-text-editor .ql-editor .ql-size-28px { font-size: 28px; }
.rich-text-editor .ql-editor .ql-size-32px { font-size: 32px; }
.rich-text-editor .ql-editor .ql-size-36px { font-size: 36px; }
.rich-text-editor .ql-editor .ql-size-48px { font-size: 48px; }
.rich-text-editor .ql-editor .ql-size-60px { font-size: 60px; }
.rich-text-editor .ql-editor .ql-size-72px { font-size: 72px; }
.rich-text-editor .ql-editor .ql-size-96px { font-size: 96px; }
.rich-text-editor .ql-editor .ql-size-120px { font-size: 120px; }

/* 工具栏字号选择器样式 */
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
  content: '10px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
  content: '12px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
  content: '14px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
  content: '16px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
  content: '18px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
  content: '20px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
  content: '24px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="28px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="28px"]::before {
  content: '28px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
  content: '32px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="36px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="36px"]::before {
  content: '36px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="48px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="48px"]::before {
  content: '48px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="60px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="60px"]::before {
  content: '60px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="72px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="72px"]::before {
  content: '72px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="96px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="96px"]::before {
  content: '96px';
}
.rich-text-editor .ql-picker.ql-size .ql-picker-label[data-value="120px"]::before,
.rich-text-editor .ql-picker.ql-size .ql-picker-item[data-value="120px"]::before {
  content: '120px';
}
</style>
