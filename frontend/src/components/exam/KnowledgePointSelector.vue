<template>
  <el-card class="knowledge-card">
    <template #header>
      <div class="card-header">
        <span>知识点选择</span>
        <div class="header-actions">
          <el-button size="small" @click="selectAll">全选</el-button>
          <el-button size="small" @click="clearSelection">清空</el-button>
        </div>
      </div>
    </template>

    <div class="knowledge-selection">
      <div v-if="!knowledgeData || knowledgeData.length === 0" class="no-knowledge">
        <el-empty description="暂无知识点数据">
          <el-button type="primary" @click="$emit('go-to-knowledge')">
            去配置知识点
          </el-button>
        </el-empty>
      </div>

      <div v-else class="knowledge-tree">
        <div class="simple-knowledge-list">
          <div v-for="item in filteredKnowledgeList" :key="item.uid || item.name" class="knowledge-item">
            <el-checkbox
              :model-value="isKnowledgePointSelected(item)"
              @change="(checked) => toggleKnowledgePoint(item, checked)"
            >
              <div class="knowledge-content">
                <div class="knowledge-name">{{ item.name }}</div>
                <div class="knowledge-path">
                  {{ item.fullName || '暂无路径' }}
                </div>
                <div class="knowledge-meta">
                  <StatusTag
                    v-if="item.score"
                    :status="item.score"
                    type="score"
                    size="small"
                  />
                  <span
                    v-if="item.description"
                    class="knowledge-desc"
                    :title="item.description"
                  >
                    {{ item.description }}
                  </span>
                </div>
              </div>
            </el-checkbox>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue'
import StatusTag from '@/components/common/StatusTag.vue'
import { flattenKnowledgeTree } from '@/utils/knowledgeUtils'
import { getLeafKnowledgePointsWithPaths } from '@/services/knowledgePathService'
import { executeWithLoading } from '@/utils/apiUtils'

const props = defineProps({
  projectId: {
    type: Number,
    required: true
  },
  knowledgeData: {
    type: Array,
    default: () => []
  },
  selectedKnowledgePoints: {
    type: Array,
    default: () => []
  },
  scoreFilters: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:selectedKnowledgePoints', 'go-to-knowledge'])

// 后端叶子节点数据
const leafKnowledgePoints = ref([])
const loading = ref(false)

// 扁平化知识点列表 - 优先使用后端数据，回退到前端处理
const flatKnowledgeList = computed(() => {
  // 如果有后端数据，使用后端数据
  if (leafKnowledgePoints.value && leafKnowledgePoints.value.length > 0) {
    return leafKnowledgePoints.value.map(point => ({
      name: point.name,
      fullName: point.path || '', // 使用后端返回的路径
      fullPath: point.path ? `${point.path} > ${point.name}` : point.name,
      description: point.description || '',
      score: point.score || 3,
      uid: point.uid,
      isParent: false
    }))
  }

  // 回退到前端处理
  if (!props.knowledgeData || !Array.isArray(props.knowledgeData)) {
    return []
  }

  // 使用工具函数扁平化知识点树，只包含叶子节点
  return flattenKnowledgeTree(props.knowledgeData, {
    includeParents: false, // 只包含叶子节点
    includeFullPath: true, // 包含完整路径
    defaultScore: 3 // 默认分数
  })
})

// 加载后端叶子节点数据
const loadLeafKnowledgePoints = async () => {
  if (!props.projectId) return

  await executeWithLoading(async () => {
    console.log('🔍 从后端加载带路径的叶子节点数据:', props.projectId)
    const points = await getLeafKnowledgePointsWithPaths(props.projectId)
    leafKnowledgePoints.value = points
    console.log('✅ 成功加载叶子节点数据:', points.length, '个')
  }, {
    loadingRef: loading,
    showMessage: false,
    onError: (error) => {
      console.error('❌ 加载叶子节点数据失败:', error)
      // 失败时清空后端数据，让组件回退到前端处理
      leafKnowledgePoints.value = []
    }
  })
}

// 监听项目ID变化
watch(() => props.projectId, (newProjectId) => {
  if (newProjectId) {
    loadLeafKnowledgePoints()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.projectId) {
    loadLeafKnowledgePoints()
  }
})

// 根据分数筛选器过滤知识点
const filteredKnowledgeList = computed(() => {
  if (props.scoreFilters && props.scoreFilters.length > 0) {
    return flatKnowledgeList.value.filter(item => props.scoreFilters.includes(item.score))
  }
  return flatKnowledgeList.value
})

// 检查知识点是否被选中
const isKnowledgePointSelected = (item) => {
  return props.selectedKnowledgePoints.some(selected =>
    selected.name === item.name && selected.fullPath === item.fullPath
  )
}

// 切换知识点选择状态
const toggleKnowledgePoint = (item, checked) => {
  let newSelection = [...props.selectedKnowledgePoints]

  if (checked) {
    // 添加知识点（包含完整信息）
    const knowledgePoint = {
      name: item.name,
      fullPath: item.fullPath,
      description: item.description || '',
      score: item.score
    }
    newSelection.push(knowledgePoint)
  } else {
    // 移除知识点
    newSelection = newSelection.filter(selected =>
      !(selected.name === item.name && selected.fullPath === item.fullPath)
    )
  }

  emit('update:selectedKnowledgePoints', newSelection)
}

// 全选知识点
const selectAll = () => {
  const allKnowledgePoints = filteredKnowledgeList.value.map(item => ({
    name: item.name,
    fullPath: item.fullPath,
    description: item.description || '',
    score: item.score
  }))
  emit('update:selectedKnowledgePoints', allKnowledgePoints)
}

// 清空选择
const clearSelection = () => {
  emit('update:selectedKnowledgePoints', [])
}
</script>

<style scoped>
.knowledge-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.knowledge-selection {
  max-height: 600px;
  overflow-y: auto;
}

.no-knowledge {
  text-align: center;
  padding: 40px 20px;
}

.simple-knowledge-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.knowledge-item {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
  min-height: 70px;
  height: 100px;
}

.knowledge-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* 修复checkbox布局 */
.knowledge-item :deep(.el-checkbox) {
  width: 100%;
  align-items: flex-start;
}

.knowledge-item :deep(.el-checkbox__input) {
  margin-top: 2px;
}

.knowledge-item :deep(.el-checkbox__label) {
  width: 100%;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 8px;
}

.knowledge-content {
  width: 100%;
  margin-left: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.knowledge-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
}

.knowledge-path {
  font-size: 12px;
  color: #909399;
  font-style: italic;
  line-height: 1.3;
}

.knowledge-meta {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex-wrap: wrap;
}

.knowledge-desc {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
  cursor: help;
}

/* 确保标签样式一致 */
.knowledge-item .el-tag {
  margin-left: 0;
}
</style>
