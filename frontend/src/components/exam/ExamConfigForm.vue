<template>
  <el-card class="config-card">
    <template #header>
      <div class="card-header">
        <span>考卷配置</span>
      </div>
    </template>

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基本信息 -->
      <el-form-item label="考卷标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入考卷标题" />
      </el-form-item>

      <el-form-item label="考卷描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入考卷描述（可选）"
        />
      </el-form-item>

      <el-form-item label="目标等级" prop="targetLevel">
        <el-input
          v-model="form.targetLevel"
          placeholder="如：小学三年级上、大学高数等"
        />
      </el-form-item>

      <el-form-item label="难度等级" prop="difficulty">
        <el-select v-model="form.difficulty" placeholder="请选择难度等级">
          <el-option label="简单" value="easy" />
          <el-option label="中等" value="medium" />
          <el-option label="困难" value="hard" />
          <el-option label="竞赛" value="competition" />
          <el-option label="中考" value="middle_school_exam" />
          <el-option label="高考" value="high_school_exam" />
        </el-select>
      </el-form-item>

      <!-- 快捷选择 -->
      <el-form-item label="快捷选择">
        <div class="score-filters">
          <span class="filter-label">按掌握分数筛选：</span>
          <el-checkbox-group v-model="form.scoreFilters">
            <el-checkbox :label="1">1分</el-checkbox>
            <el-checkbox :label="2">2分</el-checkbox>
            <el-checkbox :label="3">3分</el-checkbox>
            <el-checkbox :label="4">4分</el-checkbox>
            <el-checkbox :label="5">5分</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>

      <el-form-item label="额外要求">
        <el-input
          v-model="form.additionalRequirements"
          type="textarea"
          :rows="3"
          placeholder="请输入额外的考卷要求（可选）"
        />
      </el-form-item>

      <!-- 生成按钮 -->
      <el-form-item>
        <el-button
          type="primary"
          @click="handleGenerate"
          :loading="generating"
          :disabled="!canGenerate"
        >
          <el-icon><MagicStick /></el-icon>
          {{ isRegenerate ? '重新生成考卷' : '生成考卷' }}
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { MagicStick } from '@element-plus/icons-vue'
import { VALIDATION_RULES } from '@/utils/constants'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  generating: {
    type: Boolean,
    default: false
  },
  selectedKnowledgePoints: {
    type: Array,
    default: () => []
  },
  isRegenerate: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'generate'])

const formRef = ref(null)

const form = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单验证规则
const rules = {
  title: VALIDATION_RULES.EXAM_TITLE,
  targetLevel: VALIDATION_RULES.EXAM_TARGET_LEVEL,
  difficulty: VALIDATION_RULES.EXAM_DIFFICULTY
}

// 是否可以生成
const canGenerate = computed(() => {
  return form.value.title?.trim() &&
         form.value.targetLevel?.trim() &&
         form.value.difficulty &&
         props.selectedKnowledgePoints.length > 0 &&
         !props.generating
})

// 生成考卷
const handleGenerate = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      emit('generate')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 暴露表单引用给父组件
defineExpose({
  formRef,
  validate: () => formRef.value?.validate()
})
</script>

<style scoped>
.config-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.score-filters {
  width: 100%;
}

.filter-label {
  display: block;
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
}
</style>
