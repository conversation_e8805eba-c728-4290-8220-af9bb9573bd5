<template>
  <div class="markdown-viewer" v-html="renderedContent"></div>
</template>

<script setup>
import { computed } from 'vue'
import { renderMarkdown } from '@/utils/markdown_renderer.js'

// Props
const props = defineProps({
  content: {
    type: String,
    default: ''
  }
})

// 计算属性
const renderedContent = computed(() => {
  return renderMarkdown(props.content)
})
</script>

<style scoped>
.markdown-viewer {
  line-height: 1.6;
  color: #333;
  font-size: 14px;
}

/* 全局样式，不使用 scoped */
</style>

<style>
.markdown-viewer h1,
.markdown-viewer h2,
.markdown-viewer h3,
.markdown-viewer h4,
.markdown-viewer h5,
.markdown-viewer h6 {
  margin: 1.5em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.4;
}

.markdown-viewer h1 {
  font-size: 2em;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-viewer h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-viewer h3 {
  font-size: 1.25em;
}

.markdown-viewer h4 {
  font-size: 1em;
}

.markdown-viewer h5 {
  font-size: 0.875em;
}

.markdown-viewer h6 {
  font-size: 0.85em;
  color: #666;
}

.markdown-viewer p {
  margin: 0.8em 0;
}

.markdown-viewer strong {
  font-weight: 600;
}

.markdown-viewer em {
  font-style: italic;
}

.markdown-viewer ul,
.markdown-viewer ol {
  margin: 0.8em 0;
  padding-left: 2em;
}

.markdown-viewer li {
  margin: 0.2em 0;
}

.markdown-viewer blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #ddd;
  background: #f9f9f9;
  color: #666;
}

.markdown-viewer pre {
  margin: 1em 0;
  padding: 1em;
  background: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.45;
}

.markdown-viewer code {
  padding: 0.2em 0.4em;
  background: #f6f8fa;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 85%;
}

.markdown-viewer pre code {
  padding: 0;
  background: transparent;
  border-radius: 0;
}

.markdown-viewer table {
  margin: 1em 0;
  border-collapse: collapse;
  width: 100%;
}

.markdown-viewer th,
.markdown-viewer td {
  padding: 0.5em 1em;
  border: 1px solid #ddd;
  text-align: left;
}

.markdown-viewer th {
  background: #f6f8fa;
  font-weight: 600;
}

.markdown-viewer img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0.5em 0;
}

.markdown-viewer a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-viewer a:hover {
  text-decoration: underline;
}

.markdown-viewer .empty-content {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 2em;
}

.markdown-viewer .error-content {
  color: #f56c6c;
  text-align: center;
  padding: 2em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-viewer {
    font-size: 13px;
  }
  
  .markdown-viewer h1 {
    font-size: 1.8em;
  }
  
  .markdown-viewer h2 {
    font-size: 1.4em;
  }
  
  .markdown-viewer h3 {
    font-size: 1.2em;
  }
  
  .markdown-viewer pre {
    padding: 0.8em;
    font-size: 12px;
  }
  
  .markdown-viewer table {
    font-size: 12px;
  }
  
  .markdown-viewer th,
  .markdown-viewer td {
    padding: 0.4em 0.6em;
  }
}
</style>
