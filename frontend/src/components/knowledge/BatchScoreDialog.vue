<template>
  <el-dialog
    v-model="visible"
    title="批量快速评分 - 知识点叶子节点"
    width="80%"
    :close-on-click-modal="false"
    custom-class="standard-dialog large-dialog"
    @close="handleClose"
  >
    <div class="standard-dialog-content">
      <div class="batch-score-container">
        <!-- 操作工具栏 -->
        <div class="score-toolbar">
          <div class="toolbar-left">
            <span class="leaf-count">共找到 {{ leafNodes.length }} 个叶子节点</span>
            <span class="default-score-tip">（未评分节点默认为3分）</span>
          </div>
          <div class="toolbar-right">
            <el-button size="small" @click="clearAllScores" type="info" plain>
              清空所有分数
            </el-button>
          </div>
        </div>

        <!-- 叶子节点列表 -->
        <div class="leaf-nodes-list">
          <div
            v-for="(node, index) in leafNodes"
            :key="node.uid || index"
            class="leaf-node-item"
          >
            <div class="node-info">
              <div class="node-name">{{ node.name }}</div>
              <div
                class="node-description"
                v-if="node.description"
                :title="node.description"
              >
                {{ node.description }}
              </div>
              <div class="node-path">{{ node.path }}</div>
            </div>
            <div class="score-selector">
              <el-radio-group v-model="node.score" @change="onScoreChange(node, $event)">
                <el-radio-button :label="1" class="score-1">1分</el-radio-button>
                <el-radio-button :label="2" class="score-2">2分</el-radio-button>
                <el-radio-button :label="3" class="score-3">3分</el-radio-button>
                <el-radio-button :label="4" class="score-4">4分</el-radio-button>
                <el-radio-button :label="5" class="score-5">5分</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="score-statistics">
          <div class="stat-title">分数分布统计：</div>
          <div class="stat-items">
            <el-tag
              v-for="(count, score) in scoreDistribution"
              :key="score"
              :type="getScoreTagType(parseInt(score))"
              class="stat-tag"
            >
              {{ score }}分: {{ count }}个
            </el-tag>
            <el-tag v-if="unscored > 0" type="info" class="stat-tag">
              未评分: {{ unscored }}个
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="standard-dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleApply"
          :loading="applying"
        >
          应用评分并保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { scoreUtils } from '@/utils/dataUtils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  knowledgeData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'apply'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const leafNodes = ref([])
const applying = ref(false)

// 获取分数标签类型
const getScoreTagType = (score) => {
  return scoreUtils.getScoreTagType(score)
}

// 分数分布统计
const scoreDistribution = computed(() => {
  const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  leafNodes.value.forEach(node => {
    if (node.score !== undefined && node.score !== null) {
      const score = node.score
      if (score >= 1 && score <= 5) {
        distribution[score] = (distribution[score] || 0) + 1
      }
    }
  })
  // 只返回有数据的分数
  const result = {}
  Object.keys(distribution).forEach(score => {
    if (distribution[score] > 0) {
      result[score] = distribution[score]
    }
  })
  return result
})

// 提取叶子节点
const extractLeafNodes = (data) => {
  const nodes = []

  function traverse(items, parentPath = '', isRoot = true) {
    if (!Array.isArray(items)) return

    items.forEach(item => {
      if (item.children && item.children.length > 0) {
        // 非叶子节点，继续遍历
        const currentPath = isRoot ? '' : (parentPath ? `${parentPath} > ${item.name}` : item.name)
        traverse(item.children, currentPath, false)
      } else {
        // 叶子节点
        nodes.push({
          name: item.name,
          description: item.description || '',
          path: parentPath || '',
          score: item.score !== undefined ? item.score : 3,
          uid: item.uid,
          originalItem: item // 保持对原始对象的引用
        })
      }
    })
  }

  traverse(data)
  leafNodes.value = nodes
}

// 未评分节点数量（原始数据中没有score字段的节点）
const unscored = computed(() => {
  return leafNodes.value.filter(node =>
    node.originalItem && (node.originalItem.score === undefined || node.originalItem.score === null)
  ).length
})

// 监听知识点数据变化，提取叶子节点
watch(() => props.knowledgeData, (newData) => {
  extractLeafNodes(newData)
}, { immediate: true })

// 清空所有分数
const clearAllScores = () => {
  leafNodes.value.forEach(node => {
    node.score = undefined
    // 同时更新原始对象
    if (node.originalItem) {
      delete node.originalItem.score
    }
  })
  ElMessage.success('已清空所有分数')
}

// 分数变化处理
const onScoreChange = (node, score) => {
  // 更新原始对象
  if (node.originalItem) {
    node.originalItem.score = score
  }
}

// 应用评分
const handleApply = async () => {
  applying.value = true
  try {
    emit('apply', leafNodes.value)
    ElMessage.success('评分应用成功')
    handleClose()
  } catch (error) {
    console.error('应用评分失败:', error)
    ElMessage.error('应用评分失败')
  } finally {
    applying.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.batch-score-container {
  padding: 20px 24px;
}

.score-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.toolbar-left .leaf-count {
  font-weight: 500;
  color: #606266;
}

.toolbar-left .default-score-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.leaf-nodes-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.leaf-node-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  min-height: 70px;
}

.leaf-node-item:last-child {
  border-bottom: none;
}

.node-info {
  flex: 1;
  margin-right: 20px;
}

.node-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.node-description {
  color: #909399;
  font-size: 12px;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
}

.node-path {
  color: #c0c4cc;
  font-size: 11px;
}

.score-selector {
  flex-shrink: 0;
}

.score-statistics {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-title {
  font-weight: 500;
  margin-bottom: 10px;
  color: #303133;
}

.stat-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.stat-tag {
  margin: 0;
  padding: 4px 8px;
  font-size: 13px;
  line-height: 1.4;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 24px;
  border-radius: 4px;
}

/* 确保3分标签是白色背景 */
.stat-tag:not(.el-tag--danger):not(.el-tag--warning):not(.el-tag--info):not(.el-tag--success) {
  background-color: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  color: #666666 !important;
}

/* 4分标签蓝色样式 */
.stat-tag.el-tag--info {
  background-color: #e6f7ff !important;
  border-color: #91d5ff !important;
  color: #1890ff !important;
}
</style>
