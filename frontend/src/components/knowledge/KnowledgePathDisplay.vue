<template>
  <div class="knowledge-path-display">
    <!-- 单个知识点路径显示 -->
    <div v-if="mode === 'single'" class="single-path">
      <el-tooltip 
        v-if="showTooltip && pathTooltip" 
        :content="pathTooltip" 
        placement="top"
      >
        <span class="path-text">{{ displayPath }}</span>
      </el-tooltip>
      <span v-else class="path-text">{{ displayPath }}</span>
    </div>

    <!-- 批量知识点路径显示 -->
    <div v-else-if="mode === 'batch'" class="batch-paths">
      <div 
        v-for="item in formattedItems" 
        :key="item.uid" 
        class="path-item"
      >
        <div class="item-name">{{ item.name }}</div>
        <el-tooltip 
          v-if="item.pathTooltip && item.pathTooltip !== item.formattedPath" 
          :content="item.pathTooltip" 
          placement="top"
        >
          <div class="item-path">{{ item.formattedPath }}</div>
        </el-tooltip>
        <div v-else class="item-path">{{ item.formattedPath }}</div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载路径中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Loading, Warning } from '@element-plus/icons-vue'
import knowledgePathService from '@/services/knowledgePathService'
import { formatKnowledgePathDisplay, batchFormatKnowledgePaths } from '@/utils/pathUtils'
import { executeWithLoading } from '@/utils/apiUtils'

const props = defineProps({
  // 显示模式：single（单个）、batch（批量）
  mode: {
    type: String,
    default: 'single',
    validator: value => ['single', 'batch'].includes(value)
  },
  // 项目ID
  projectId: {
    type: Number,
    required: true
  },
  // 路径类型：parent（父级路径，用于知识点管理）或 full（完整路径，用于卡片管理）
  pathType: {
    type: String,
    default: 'parent',
    validator: (value) => ['parent', 'full'].includes(value)
  },
  // 单个模式：知识点UID
  uid: {
    type: String,
    default: ''
  },
  // 单个模式：直接传入的路径
  path: {
    type: String,
    default: ''
  },
  // 批量模式：知识点数组
  knowledgePoints: {
    type: Array,
    default: () => []
  },
  // 显示选项
  maxLength: {
    type: Number,
    default: 50
  },
  placeholder: {
    type: String,
    default: '暂无路径'
  },
  showTooltip: {
    type: Boolean,
    default: true
  },
  // 是否自动加载
  autoLoad: {
    type: Boolean,
    default: true
  }
})

const loading = ref(false)
const error = ref('')
const singlePath = ref('')
const batchPaths = ref({})

// 单个路径显示
const displayPath = computed(() => {
  if (props.mode !== 'single') return ''
  
  const path = props.path || singlePath.value
  const formatted = formatKnowledgePathDisplay(path, {
    maxLength: props.maxLength,
    placeholder: props.placeholder,
    showTooltip: props.showTooltip
  })
  
  return typeof formatted === 'string' ? formatted : formatted.display
})

const pathTooltip = computed(() => {
  if (props.mode !== 'single' || !props.showTooltip) return ''
  
  const path = props.path || singlePath.value
  const formatted = formatKnowledgePathDisplay(path, {
    maxLength: props.maxLength,
    placeholder: props.placeholder,
    showTooltip: props.showTooltip
  })
  
  return typeof formatted === 'object' ? formatted.tooltip : ''
})

// 批量路径显示
const formattedItems = computed(() => {
  if (props.mode !== 'batch') return []
  
  return batchFormatKnowledgePaths(props.knowledgePoints, batchPaths.value, {
    maxLength: props.maxLength,
    placeholder: props.placeholder,
    showTooltip: props.showTooltip
  })
})

// 加载单个路径
async function loadSinglePath() {
  // 如果已经有路径，直接使用
  if (props.path) return

  // 如果没有UID，无法加载
  if (!props.uid) return

  await executeWithLoading(async () => {
    // 根据pathType选择不同的API
    if (props.pathType === 'full') {
      // 获取完整路径（用于卡片管理）
      const fullPaths = await knowledgePathService.getKnowledgePointFullPaths(props.projectId)
      singlePath.value = fullPaths[props.uid] || ''
    } else {
      // 获取父级路径（用于知识点管理）
      singlePath.value = await knowledgePathService.getKnowledgePointPath(props.projectId, props.uid)
    }
  }, {
    loadingRef: loading,
    errorMessage: '加载路径失败',
    resetError: false,
    onError: (err) => {
      error.value = err.message || '加载路径失败'
    }
  })
}

// 加载批量路径
async function loadBatchPaths() {
  if (!props.knowledgePoints || props.knowledgePoints.length === 0) return

  await executeWithLoading(async () => {
    const uids = props.knowledgePoints.map(item => item.uid).filter(Boolean)

    if (props.pathType === 'full') {
      // 获取完整路径（用于卡片管理）
      const fullPaths = await knowledgePathService.getKnowledgePointFullPaths(props.projectId)
      batchPaths.value = {}
      uids.forEach(uid => {
        if (fullPaths[uid] !== undefined) {
          batchPaths.value[uid] = fullPaths[uid]
        }
      })
      console.log('✅ 批量完整路径获取完成:', Object.keys(batchPaths.value).length, '/', uids.length)
    } else {
      // 获取父级路径（用于知识点管理）
      batchPaths.value = await knowledgePathService.getBatchKnowledgePointPaths(props.projectId, uids)
      console.log('✅ 批量父级路径获取完成:', Object.keys(batchPaths.value).length, '/', uids.length)
    }
  }, {
    loadingRef: loading,
    errorMessage: '加载路径失败',
    resetError: false,
    onError: (err) => {
      error.value = err.message || '加载路径失败'
    }
  })
}

// 加载路径数据
async function loadPaths() {
  if (!props.autoLoad) return
  
  if (props.mode === 'single') {
    await loadSinglePath()
  } else if (props.mode === 'batch') {
    await loadBatchPaths()
  }
}

// 手动刷新
async function refresh() {
  await loadPaths()
}

// 监听属性变化
watch(() => [props.projectId, props.uid, props.knowledgePoints], loadPaths, { deep: true })

onMounted(() => {
  if (props.autoLoad) {
    loadPaths()
  }
})

// 暴露方法
defineExpose({
  refresh,
  loadPaths
})
</script>

<style scoped>
.knowledge-path-display {
  font-size: 14px;
}

.single-path .path-text {
  color: #606266;
  word-break: break-all;
}

.batch-paths {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.path-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.item-name {
  font-weight: 500;
  color: #303133;
  flex: 0 0 auto;
  margin-right: 12px;
}

.item-path {
  color: #606266;
  text-align: right;
  word-break: break-all;
  flex: 1;
}

.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 12px;
}

.error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f56c6c;
  font-size: 12px;
}

.path-text:hover,
.item-path:hover {
  color: #409eff;
  cursor: pointer;
}
</style>
