<template>
  <div class="knowledge-info-panel">
    <!-- 配置信息 -->
    <el-card class="info-card">
      <template #header>
        <span>配置信息</span>
      </template>
      
      <div v-if="currentConfig" class="config-info">
        <div class="info-item">
          <label>版本号:</label>
          <span>{{ currentConfig.version }}</span>
        </div>
        <div class="info-item">
          <label>创建时间:</label>
          <span>{{ formatDate(currentConfig.createdAt, 'datetime') }}</span>
        </div>
        <div class="info-item">
          <label>创建者:</label>
          <span>{{ currentConfig.createdBy }}</span>
        </div>
        <div class="info-item" v-if="currentConfig.notes">
          <label>说明:</label>
          <span>{{ currentConfig.notes }}</span>
        </div>
      </div>
      <div v-else class="no-config">
        <el-empty description="暂无配置" :image-size="80" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { formatDate } from '@/utils/environment'

defineProps({
  currentConfig: {
    type: Object,
    default: null
  }
})
</script>

<style scoped>
.knowledge-info-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.config-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item span {
  color: #303133;
  text-align: right;
  flex: 1;
}

.no-config {
  text-align: center;
  padding: 20px;
}
</style>
