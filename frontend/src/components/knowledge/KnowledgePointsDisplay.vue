<template>
  <div class="knowledge-points-display">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <span>{{ error }}</span>
    </div>

    <!-- 知识点列表 -->
    <div v-else-if="displayPoints.length > 0" class="points-list">
      <span
        v-for="(point, index) in displayPoints"
        :key="point.uid || index"
        class="point-item"
      >
        <el-tooltip
          v-if="point.path && point.path !== point.name"
          :content="point.path"
          placement="top"
        >
          <span class="point-name">{{ point.name }}</span>
        </el-tooltip>
        <span v-else class="point-name">{{ point.name }}</span>
        <span v-if="index < displayPoints.length - 1" class="separator">、</span>
      </span>
      
      <!-- 显示更多提示 -->
      <span v-if="hasMore" class="more-indicator">
        等{{ totalCount }}个
      </span>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty">
      {{ placeholder }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import knowledgePathService from '@/services/knowledgePathService'
import { executeWithLoading } from '@/utils/apiUtils'

const props = defineProps({
  // 项目ID
  projectId: {
    type: Number,
    required: true
  },
  // 知识点数据（可能是字符串、数组或对象）
  knowledgePoints: {
    type: [String, Array, Object],
    default: () => []
  },
  // 最大显示数量
  maxDisplay: {
    type: Number,
    default: 3
  },
  // 空状态占位符
  placeholder: {
    type: String,
    default: '无'
  },
  // 是否自动加载路径
  autoLoadPaths: {
    type: Boolean,
    default: true
  }
})

const loading = ref(false)
const error = ref('')
const pathMap = ref({})

// 解析知识点数据
const parsedPoints = computed(() => {
  try {
    let points = []

    if (!props.knowledgePoints) {
      return []
    }

    // 如果是字符串，尝试解析JSON
    if (typeof props.knowledgePoints === 'string') {
      try {
        points = JSON.parse(props.knowledgePoints)
      } catch (parseError) {
        // 如果解析失败，可能是简单的字符串
        return [{ name: props.knowledgePoints, uid: null }]
      }
    }
    // 如果已经是数组
    else if (Array.isArray(props.knowledgePoints)) {
      points = props.knowledgePoints
    }
    // 如果是对象，转换为数组
    else if (typeof props.knowledgePoints === 'object') {
      if (props.knowledgePoints.name) {
        return [props.knowledgePoints]
      } else {
        return []
      }
    }

    // 标准化数据格式
    return points.map((point, index) => {
      if (typeof point === 'string') {
        return { name: point, uid: null }
      } else if (typeof point === 'object' && point !== null) {
        return {
          name: point.name || point.title || point.fullPath || point.path || `知识点${index + 1}`,
          uid: point.uid || null,
          path: point.path || point.fullPath || ''
        }
      }
      return { name: String(point), uid: null }
    }).filter(point => point.name && point.name.trim())
  } catch (error) {
    console.warn('解析知识点数据失败:', error, props.knowledgePoints)
    return []
  }
})

// 显示的知识点（带路径信息）
const displayPoints = computed(() => {
  const points = parsedPoints.value.slice(0, props.maxDisplay)
  
  return points.map(point => ({
    ...point,
    path: point.path || pathMap.value[point.uid] || ''
  }))
})

// 是否有更多知识点
const hasMore = computed(() => {
  return parsedPoints.value.length > props.maxDisplay
})

// 总数量
const totalCount = computed(() => {
  return parsedPoints.value.length
})

// 加载知识点路径
const loadPaths = async () => {
  if (!props.autoLoadPaths || !props.projectId) return
  
  const uidsToLoad = parsedPoints.value
    .filter(point => point.uid && !point.path)
    .map(point => point.uid)
  
  if (uidsToLoad.length === 0) return

  await executeWithLoading(async () => {
    const paths = await knowledgePathService.getBatchKnowledgePointPaths(props.projectId, uidsToLoad)
    pathMap.value = { ...pathMap.value, ...paths }
  }, {
    loadingRef: loading,
    errorRef: error,
    errorMessage: '加载路径失败',
    showMessage: false
  })
}

// 监听属性变化
watch(() => [props.projectId, props.knowledgePoints], loadPaths, { deep: true })

onMounted(() => {
  if (props.autoLoadPaths) {
    loadPaths()
  }
})

// 暴露方法
defineExpose({
  loadPaths,
  parsedPoints,
  displayPoints
})
</script>

<style scoped>
.knowledge-points-display {
  font-size: 14px;
  line-height: 1.4;
}

.loading {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
}

.error {
  color: #f56c6c;
  font-size: 12px;
}

.points-list {
  display: inline;
}

.point-item {
  display: inline;
}

.point-name {
  color: #606266;
  cursor: default;
}

.point-name:hover {
  color: #409eff;
}

.separator {
  color: #909399;
  margin: 0 2px;
}

.more-indicator {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
}

.empty {
  color: #c0c4cc;
  font-style: italic;
}
</style>
