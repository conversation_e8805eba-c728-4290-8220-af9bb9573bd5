<template>
  <div class="knowledge-leaf-path-display">
    <!-- 单个叶子节点路径显示 -->
    <div v-if="mode === 'single'" class="single-leaf-path">
      <el-tooltip 
        v-if="showTooltip && pathTooltip" 
        :content="pathTooltip" 
        placement="top"
      >
        <span class="leaf-path-text">{{ displayPath }}</span>
      </el-tooltip>
      <span v-else class="leaf-path-text">{{ displayPath }}</span>
    </div>

    <!-- 批量叶子节点路径显示 -->
    <div v-else-if="mode === 'batch'" class="batch-leaf-paths">
      <div 
        v-for="item in formattedLeafItems" 
        :key="item.uid" 
        class="leaf-path-item"
      >
        <div class="leaf-item-name">{{ item.name }}</div>
        <el-tooltip 
          v-if="item.pathTooltip && item.pathTooltip !== item.formattedPath" 
          :content="item.pathTooltip" 
          placement="top"
        >
          <div class="leaf-item-path">{{ item.formattedPath }}</div>
        </el-tooltip>
        <div v-else class="leaf-item-path">{{ item.formattedPath }}</div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载叶子节点路径中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Loading, Warning } from '@element-plus/icons-vue'
import knowledgePathService from '@/services/knowledgePathService'
import { formatKnowledgePathDisplay } from '@/utils/pathUtils'
import { executeWithLoading } from '@/utils/apiUtils'

const props = defineProps({
  // 显示模式：single（单个）、batch（批量）
  mode: {
    type: String,
    default: 'single',
    validator: value => ['single', 'batch'].includes(value)
  },
  // 项目ID
  projectId: {
    type: Number,
    required: true
  },
  // 单个模式：知识点UID
  uid: {
    type: String,
    default: ''
  },
  // 单个模式：直接传入的路径
  path: {
    type: String,
    default: ''
  },
  // 单个模式：知识点名称（用于查找UID）
  knowledgePointName: {
    type: String,
    default: ''
  },
  // 批量模式：叶子节点数组
  leafNodes: {
    type: Array,
    default: () => []
  },
  // 显示选项
  maxLength: {
    type: Number,
    default: 50
  },
  placeholder: {
    type: String,
    default: '暂无路径'
  },
  showTooltip: {
    type: Boolean,
    default: true
  },
  // 是否自动加载
  autoLoad: {
    type: Boolean,
    default: true
  }
})

const loading = ref(false)
const error = ref('')
const singleLeafPath = ref('')
const leafPathsMap = ref({})

// 单个叶子节点路径显示
const displayPath = computed(() => {
  if (props.mode !== 'single') return ''
  
  const path = props.path || singleLeafPath.value
  const formatted = formatKnowledgePathDisplay(path, {
    maxLength: props.maxLength,
    placeholder: props.placeholder,
    showTooltip: props.showTooltip
  })
  
  return typeof formatted === 'string' ? formatted : formatted.display
})

const pathTooltip = computed(() => {
  if (props.mode !== 'single' || !props.showTooltip) return ''
  
  const path = props.path || singleLeafPath.value
  const formatted = formatKnowledgePathDisplay(path, {
    maxLength: props.maxLength,
    placeholder: props.placeholder,
    showTooltip: props.showTooltip
  })
  
  return typeof formatted === 'object' ? formatted.tooltip : ''
})

// 批量叶子节点路径显示
const formattedLeafItems = computed(() => {
  if (props.mode !== 'batch') return []
  
  return props.leafNodes.map(node => {
    const path = leafPathsMap.value[node.uid] || node.fullPath || ''
    const formattedPath = formatKnowledgePathDisplay(path, {
      maxLength: props.maxLength,
      placeholder: props.placeholder,
      showTooltip: props.showTooltip
    })

    return {
      ...node,
      path,
      formattedPath: typeof formattedPath === 'string' ? formattedPath : formattedPath.display,
      pathTooltip: typeof formattedPath === 'object' ? formattedPath.tooltip : formattedPath
    }
  })
})

// 加载单个叶子节点路径
async function loadSingleLeafPath() {
  // 如果已经有路径，直接使用
  if (props.path) return

  // 如果没有UID也没有知识点名称，无法加载
  if (!props.uid && !props.knowledgePointName) return

  await executeWithLoading(async () => {
    if (props.uid) {
      // 通过UID获取完整路径（叶子节点需要完整路径）
      const fullPaths = await knowledgePathService.getKnowledgePointFullPaths(props.projectId)
      singleLeafPath.value = fullPaths[props.uid] || ''
    } else if (props.knowledgePointName) {
      // 通过知识点名称查找UID，然后获取完整路径
      const uid = await knowledgePathService.findUidByKnowledgePointName(props.projectId, props.knowledgePointName)
      if (uid) {
        const fullPaths = await knowledgePathService.getKnowledgePointFullPaths(props.projectId)
        singleLeafPath.value = fullPaths[uid] || ''
      } else {
        throw new Error(`未找到知识点"${props.knowledgePointName}"对应的UID`)
      }
    }
  }, {
    loadingRef: loading,
    errorRef: error,
    errorMessage: '加载叶子节点路径失败',
    showMessage: false
  })
}

// 加载批量叶子节点路径
async function loadBatchLeafPaths() {
  if (!props.leafNodes || props.leafNodes.length === 0) return

  await executeWithLoading(async () => {
    const uids = props.leafNodes.map(item => item.uid).filter(Boolean)
    
    if (uids.length > 0) {
      // 获取完整路径（叶子节点需要完整路径）
      const fullPaths = await knowledgePathService.getKnowledgePointFullPaths(props.projectId)
      leafPathsMap.value = {}
      uids.forEach(uid => {
        if (fullPaths[uid] !== undefined) {
          leafPathsMap.value[uid] = fullPaths[uid]
        }
      })
      console.log('✅ 批量叶子节点路径获取完成:', Object.keys(leafPathsMap.value).length, '/', uids.length)
    }
  }, {
    loadingRef: loading,
    errorRef: error,
    errorMessage: '加载叶子节点路径失败',
    showMessage: false
  })
}

// 加载路径数据
async function loadPaths() {
  if (!props.autoLoad) return
  
  if (props.mode === 'single') {
    await loadSingleLeafPath()
  } else if (props.mode === 'batch') {
    await loadBatchLeafPaths()
  }
}

// 手动刷新
async function refresh() {
  await loadPaths()
}

// 监听属性变化
watch(() => [props.projectId, props.uid, props.knowledgePointName, props.leafNodes], loadPaths, { deep: true })

onMounted(() => {
  if (props.autoLoad) {
    loadPaths()
  }
})

// 暴露方法
defineExpose({
  refresh,
  loadPaths
})
</script>

<style scoped>
.knowledge-leaf-path-display {
  font-size: 14px;
}

.single-leaf-path .leaf-path-text {
  color: #606266;
  word-break: break-all;
  font-style: italic;
}

.batch-leaf-paths {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.leaf-path-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.leaf-item-name {
  font-weight: 500;
  color: #303133;
}

.leaf-item-path {
  font-size: 12px;
  color: #909399;
  font-style: italic;
  cursor: help;
}

.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 12px;
}

.error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f56c6c;
  font-size: 12px;
}
</style>
