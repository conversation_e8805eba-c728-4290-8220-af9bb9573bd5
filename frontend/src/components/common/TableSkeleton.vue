<template>
  <div class="table-skeleton">
    <!-- 表格头部骨架 -->
    <div class="skeleton-header">
      <el-skeleton :rows="1" animated>
        <template #template>
          <div class="header-skeleton">
            <el-skeleton-item variant="text" style="width: 200px; height: 32px;" />
            <div class="header-actions">
              <el-skeleton-item variant="button" style="width: 80px; height: 32px;" />
              <el-skeleton-item variant="button" style="width: 80px; height: 32px;" />
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <!-- 表格内容骨架 -->
    <div class="skeleton-table">
      <el-skeleton :rows="rows" animated>
        <template #template>
          <div v-for="i in rows" :key="i" class="table-row-skeleton">
            <el-skeleton-item 
              v-for="(width, index) in columnWidths" 
              :key="index"
              variant="text" 
              :style="{ width: width, height: '20px', marginRight: '16px' }" 
            />
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  rows: {
    type: Number,
    default: 5
  },
  columnWidths: {
    type: Array,
    default: () => ['120px', '200px', '150px', '100px', '120px']
  }
})
</script>

<style scoped>
.table-skeleton {
  padding: 20px;
}

.skeleton-header {
  margin-bottom: 20px;
}

.header-skeleton {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.skeleton-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-row-skeleton {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.table-row-skeleton:last-child {
  border-bottom: none;
}
</style>
