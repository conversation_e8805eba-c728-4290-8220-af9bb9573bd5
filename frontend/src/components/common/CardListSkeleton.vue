<template>
  <div class="card-list-skeleton">
    <!-- 页面头部骨架 -->
    <div class="skeleton-header" v-if="showHeader">
      <el-skeleton :rows="1" animated>
        <template #template>
          <div class="header-skeleton">
            <div class="header-left">
              <el-skeleton-item variant="button" style="width: 40px; height: 32px;" />
              <el-skeleton-item variant="text" style="width: 200px; height: 28px;" />
            </div>
            <div class="header-actions">
              <el-skeleton-item variant="button" style="width: 80px; height: 32px;" />
              <el-skeleton-item variant="button" style="width: 100px; height: 32px;" />
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <!-- 搜索栏骨架 -->
    <div class="skeleton-search" v-if="showSearch">
      <el-skeleton :rows="1" animated>
        <template #template>
          <div class="search-skeleton">
            <el-skeleton-item variant="text" style="width: 300px; height: 32px;" />
            <el-skeleton-item variant="button" style="width: 80px; height: 32px;" />
          </div>
        </template>
      </el-skeleton>
    </div>

    <!-- 卡片列表骨架 -->
    <div class="skeleton-cards">
      <div class="cards-grid">
        <div v-for="i in cardCount" :key="i" class="card-skeleton">
          <el-skeleton :rows="4" animated>
            <template #template>
              <div class="card-content">
                <div class="card-header">
                  <el-skeleton-item variant="text" style="width: 60%; height: 20px;" />
                  <el-skeleton-item variant="text" style="width: 80px; height: 16px;" />
                </div>
                <el-skeleton-item variant="text" style="width: 100%; height: 16px; margin: 8px 0;" />
                <el-skeleton-item variant="text" style="width: 80%; height: 16px; margin: 8px 0;" />
                <div class="card-footer">
                  <el-skeleton-item variant="text" style="width: 100px; height: 14px;" />
                  <div class="card-actions">
                    <el-skeleton-item variant="button" style="width: 60px; height: 24px;" />
                    <el-skeleton-item variant="button" style="width: 60px; height: 24px;" />
                  </div>
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  cardCount: {
    type: Number,
    default: 6
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showSearch: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped>
.card-list-skeleton {
  padding: 20px;
}

.skeleton-header {
  margin-bottom: 20px;
}

.header-skeleton {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.skeleton-search {
  margin-bottom: 20px;
}

.search-skeleton {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-cards {
  margin-top: 20px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.card-skeleton {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  background: white;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.card-actions {
  display: flex;
  gap: 8px;
}
</style>
