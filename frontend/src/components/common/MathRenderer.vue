<template>
  <div class="math-renderer" ref="contentRef" v-html="renderedContent"></div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import katex from 'katex'
import 'katex/dist/katex.min.css'

const props = defineProps({
  content: {
    type: String,
    required: true
  }
})

const contentRef = ref(null)

const renderedContent = computed(() => {
  if (!props.content) return ''
  
  let content = props.content
  
  // 渲染行内数学公式 $...$
  content = content.replace(/\$([^$]+)\$/g, (match, formula) => {
    try {
      return katex.renderToString(formula, { 
        throwOnError: false,
        displayMode: false
      })
    } catch (error) {
      console.warn('KaTeX render error:', error)
      return match
    }
  })
  
  // 渲染块级数学公式 $$...$$
  content = content.replace(/\$\$([^$]+)\$\$/g, (match, formula) => {
    try {
      return katex.renderToString(formula, { 
        throwOnError: false,
        displayMode: true
      })
    } catch (error) {
      console.warn('KaTeX render error:', error)
      return match
    }
  })
  
  return content
})

onMounted(() => {
  nextTick(() => {
    // 确保数学公式正确渲染
    if (contentRef.value) {
      const mathElements = contentRef.value.querySelectorAll('.katex')
      mathElements.forEach(el => {
        el.style.fontSize = '1.1em'
      })
    }
  })
})
</script>

<style scoped>
.math-renderer {
  line-height: 1.6;
}

.math-renderer :deep(.katex) {
  font-size: 1.1em;
}

.math-renderer :deep(.katex-display) {
  margin: 16px 0;
  text-align: center;
}

.math-renderer :deep(h3) {
  color: #303133;
  font-size: 18px;
  margin: 24px 0 12px 0;
}

.math-renderer :deep(h4) {
  color: #606266;
  font-size: 16px;
  margin: 16px 0 8px 0;
}

.math-renderer :deep(p) {
  color: #606266;
  margin: 8px 0;
}

.math-renderer :deep(ul), 
.math-renderer :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.math-renderer :deep(li) {
  margin: 4px 0;
  color: #606266;
}

.math-renderer :deep(strong) {
  color: #303133;
  font-weight: 600;
}
</style> 