<template>
  <div class="unified-knowledge-selector">
    <!-- 单选下拉模式 -->
    <div v-if="mode === 'single'" class="single-selector">
      <el-select
        v-model="singleValue"
        :placeholder="placeholder"
        filterable
        clearable
        :loading="loading"
        @change="handleSingleChange"
        style="width: 100%"
      >
        <el-option
          v-for="point in leafPoints"
          :key="point.uid"
          :label="point.name"
          :value="point.uid"
        >
          <div class="option-content">
            <div class="option-name">{{ point.name }}</div>
            <div class="option-path">{{ point.path }}</div>
          </div>
        </el-option>
      </el-select>
    </div>

    <!-- 多选列表模式 -->
    <el-card v-else-if="mode === 'multiple'" class="multiple-selector">
      <template #header>
        <div class="card-header">
          <span>{{ title || '知识点选择' }}</span>
          <div class="header-actions">
            <el-button size="small" @click="selectAll">全选</el-button>
            <el-button size="small" @click="clearSelection">清空</el-button>
          </div>
        </div>
      </template>

      <div class="knowledge-selection">
        <div v-if="!knowledgeData || knowledgeData.length === 0" class="no-knowledge">
          <el-empty description="暂无知识点数据">
            <el-button type="primary" @click="$emit('go-to-knowledge')">
              去配置知识点
            </el-button>
          </el-empty>
        </div>

        <div v-else class="knowledge-list">
          <div v-for="item in filteredKnowledgeList" :key="item.uid || item.name" class="knowledge-item">
            <el-checkbox
              :model-value="isKnowledgePointSelected(item)"
              @change="(checked) => toggleKnowledgePoint(item, checked)"
            >
              <div class="knowledge-content">
                <div class="knowledge-name">{{ item.name }}</div>
                <KnowledgeLeafPathDisplay
                  v-if="item.uid && showPaths"
                  mode="single"
                  :project-id="projectId"
                  :uid="item.uid"
                  :max-length="50"
                  class="knowledge-path"
                />
                <div v-else-if="showPaths" class="knowledge-path">{{ item.fullName || item.path || '' }}</div>
                <div v-if="showMeta" class="knowledge-meta">
                  <StatusTag
                    v-if="item.score && showScores"
                    :status="item.score"
                    type="score"
                    size="small"
                  />
                  <span
                    v-if="item.description && showDescriptions"
                    class="knowledge-desc"
                    :title="item.description"
                  >
                    {{ item.description }}
                  </span>
                </div>
              </div>
            </el-checkbox>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 树形选择模式 -->
    <div v-else-if="mode === 'tree'" class="tree-selector">
      <KnowledgeTreeSelector
        :data="knowledgeData"
        :selected="treeSelectedKeys"
        :show-scores="showScores"
        @update:selected="handleTreeChange"
        @selection-change="handleTreeSelectionChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import StatusTag from './StatusTag.vue'
import KnowledgeLeafPathDisplay from '../knowledge/KnowledgeLeafPathDisplay.vue'
import KnowledgeTreeSelector from '../KnowledgeTreeSelector.vue'
import { flattenKnowledgeTree } from '@/utils/knowledgeUtils'
import { getLeafKnowledgePointsWithPaths } from '@/services/knowledgePathService'
import { executeWithLoading } from '@/utils/apiUtils'

const props = defineProps({
  // 通用属性
  mode: {
    type: String,
    default: 'single', // 'single' | 'multiple' | 'tree'
    validator: (value) => ['single', 'multiple', 'tree'].includes(value)
  },
  projectId: {
    type: [String, Number],
    required: true
  },
  
  // 单选模式属性
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择知识点'
  },
  
  // 多选模式属性
  selectedKnowledgePoints: {
    type: Array,
    default: () => []
  },
  knowledgeData: {
    type: Array,
    default: () => []
  },
  scoreFilters: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: ''
  },
  
  // 显示选项
  showPaths: {
    type: Boolean,
    default: true
  },
  showScores: {
    type: Boolean,
    default: true
  },
  showDescriptions: {
    type: Boolean,
    default: true
  },
  showMeta: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits([
  'update:modelValue',
  'change',
  'update:selectedKnowledgePoints',
  'go-to-knowledge',
  'selection-change'
])

// 状态
const loading = ref(false)
const leafPoints = ref([])
const singleValue = ref(props.modelValue)

// 计算属性
const flatKnowledgeList = computed(() => {
  if (!props.knowledgeData || !Array.isArray(props.knowledgeData)) {
    return []
  }
  return flattenKnowledgeTree(props.knowledgeData, {
    includeParents: false,
    includeFullPath: true,
    defaultScore: 3
  })
})

const filteredKnowledgeList = computed(() => {
  if (props.scoreFilters && props.scoreFilters.length > 0) {
    return flatKnowledgeList.value.filter(item => props.scoreFilters.includes(item.score))
  }
  return flatKnowledgeList.value
})

const treeSelectedKeys = computed(() => {
  if (props.mode === 'tree') {
    return props.selectedKnowledgePoints.map(item => item.uid || item.name)
  }
  return []
})

// 方法
const loadKnowledgePoints = async () => {
  if (!props.projectId || props.mode !== 'single') return

  await executeWithLoading(async () => {
    console.log('🔍 加载知识点叶子节点（单选模式）:', props.projectId)

    // 使用统一的知识点路径服务
    const leafPointsData = await getLeafKnowledgePointsWithPaths(props.projectId)
    leafPoints.value = leafPointsData || []

    console.log('✅ 知识点叶子节点加载成功:', leafPoints.value.length)
  }, {
    loadingRef: loading,
    errorMessage: '加载知识点失败',
    onError: () => {
      leafPoints.value = []
    }
  })
}

const handleSingleChange = (value) => {
  singleValue.value = value
  const selectedPoint = leafPoints.value.find(point => point.uid === value)
  emit('update:modelValue', value)
  emit('change', selectedPoint)
}

const isKnowledgePointSelected = (item) => {
  return props.selectedKnowledgePoints.some(selected =>
    selected.name === item.name && selected.fullPath === item.fullPath
  )
}

const toggleKnowledgePoint = (item, checked) => {
  let newSelection = [...props.selectedKnowledgePoints]
  
  if (checked) {
    const knowledgePoint = {
      name: item.name,
      fullPath: item.fullPath,
      description: item.description || '',
      score: item.score,
      uid: item.uid
    }
    newSelection.push(knowledgePoint)
  } else {
    newSelection = newSelection.filter(selected =>
      !(selected.name === item.name && selected.fullPath === item.fullPath)
    )
  }
  
  emit('update:selectedKnowledgePoints', newSelection)
  emit('selection-change', newSelection)
}

const selectAll = () => {
  const allKnowledgePoints = filteredKnowledgeList.value.map(item => ({
    name: item.name,
    fullPath: item.fullPath,
    description: item.description || '',
    score: item.score,
    uid: item.uid
  }))
  emit('update:selectedKnowledgePoints', allKnowledgePoints)
  emit('selection-change', allKnowledgePoints)
}

const clearSelection = () => {
  emit('update:selectedKnowledgePoints', [])
  emit('selection-change', [])
}

const handleTreeChange = (selectedKeys) => {
  emit('update:selectedKnowledgePoints', selectedKeys)
}

const handleTreeSelectionChange = (selectedKeys) => {
  emit('selection-change', selectedKeys)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  singleValue.value = newValue
})

watch(() => props.projectId, () => {
  if (props.mode === 'single') {
    loadKnowledgePoints()
  }
}, { immediate: true })

onMounted(() => {
  if (props.mode === 'single') {
    loadKnowledgePoints()
  }
})
</script>

<style scoped>
.unified-knowledge-selector {
  width: 100%;
}

/* 单选模式样式 */
.single-selector {
  width: 100%;
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.option-name {
  font-weight: 500;
  color: #303133;
}

.option-path {
  font-size: 12px;
  color: #909399;
}

/* 多选模式样式 */
.multiple-selector {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.knowledge-selection {
  max-height: 600px;
  overflow-y: auto;
}

.no-knowledge {
  text-align: center;
  padding: 40px 20px;
}

.knowledge-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.knowledge-item {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
  min-height: 70px;
  height: auto;
}

.knowledge-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.knowledge-item :deep(.el-checkbox) {
  width: 100%;
  align-items: flex-start;
}

.knowledge-item :deep(.el-checkbox__input) {
  margin-top: 2px;
}

.knowledge-item :deep(.el-checkbox__label) {
  width: 100%;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 8px;
}

.knowledge-content {
  width: 100%;
  margin-left: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.knowledge-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
}

.knowledge-path {
  font-size: 12px;
  color: #909399;
  font-style: italic;
  line-height: 1.3;
}

.knowledge-meta {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex-wrap: wrap;
}

.knowledge-desc {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300px;
  cursor: help;
}

.knowledge-item .el-tag {
  margin-left: 0;
}

/* 树形模式样式 */
.tree-selector {
  width: 100%;
}
</style>
