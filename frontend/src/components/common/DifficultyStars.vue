<template>
  <div class="difficulty-stars" :data-size="size">
    <span
      v-for="star in 5"
      :key="star"
      :class="['star', { 'filled': star <= difficulty }]"
    >
      ⭐
    </span>
    <span v-if="showLabel" class="difficulty-label">
      {{ difficultyLabel }}
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { DIFFICULTY_LEVELS } from '@/data/quizzes.js';

const props = defineProps({
  difficulty: {
    type: Number,
    required: true,
    validator: (value) => value >= 1 && value <= 5
  },
  showLabel: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'normal', // normal, small, large
    validator: (value) => ['small', 'normal', 'large'].includes(value)
  }
});

const difficultyLabel = computed(() => {
  return DIFFICULTY_LEVELS[props.difficulty]?.label || '';
});
</script>

<style scoped>
.difficulty-stars {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.star {
  font-size: 16px;
  transition: opacity 0.2s ease;
}

.star.filled {
  opacity: 1;
}

.star:not(.filled) {
  opacity: 0.3;
}

.difficulty-label {
  margin-left: 8px;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

/* 尺寸变体 */
.difficulty-stars[data-size="small"] .star {
  font-size: 12px;
}

.difficulty-stars[data-size="small"] .difficulty-label {
  font-size: 0.8rem;
  margin-left: 6px;
}

.difficulty-stars[data-size="large"] .star {
  font-size: 20px;
}

.difficulty-stars[data-size="large"] .difficulty-label {
  font-size: 1rem;
  margin-left: 10px;
}
</style> 