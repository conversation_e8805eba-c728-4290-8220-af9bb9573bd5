<template>
  <div class="loading-wrapper" v-loading="loading" :element-loading-text="loadingText">
    <div v-if="error" class="error-container">
      <el-result
        icon="error"
        :title="errorTitle"
        :sub-title="errorMessage"
      >
        <template #extra>
          <el-button type="primary" @click="handleRetry" v-if="showRetry">
            重试
          </el-button>
          <slot name="error-actions"></slot>
        </template>
      </el-result>
    </div>
    
    <div v-else-if="empty && !loading" class="empty-container">
      <el-empty :description="emptyDescription">
        <slot name="empty-actions"></slot>
      </el-empty>
    </div>
    
    <div v-else class="content-container">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '加载中...'
  },
  error: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  errorTitle: {
    type: String,
    default: '加载失败'
  },
  errorMessage: {
    type: String,
    default: '请稍后重试'
  },
  empty: {
    type: Boolean,
    default: false
  },
  emptyDescription: {
    type: String,
    default: '暂无数据'
  },
  showRetry: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['retry'])

const handleRetry = () => {
  emit('retry')
}
</script>

<style scoped>
.loading-wrapper {
  min-height: 200px;
  position: relative;
}

.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.content-container {
  width: 100%;
}
</style>
