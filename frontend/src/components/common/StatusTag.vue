<template>
  <el-tag :type="tagType" :size="size" :effect="effect">
    {{ displayText }}
  </el-tag>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  status: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'status', // 'status' | 'difficulty' | 'score'
    validator: (value) => ['status', 'difficulty', 'score'].includes(value)
  },
  size: {
    type: String,
    default: 'small'
  },
  effect: {
    type: String,
    default: 'light'
  }
})

// 状态映射
const statusMappings = {
  status: {
    generated: { type: '', text: '已生成' },
    reviewed: { type: 'success', text: '已审核' },
    published: { type: 'info', text: '已发布' },
    draft: { type: 'info', text: '草稿' },
    active: { type: 'success', text: '活跃' },
    inactive: { type: 'info', text: '非活跃' }
  },
  difficulty: {
    easy: { type: 'success', text: '简单' },
    medium: { type: 'warning', text: '中等' },
    hard: { type: 'danger', text: '困难' },
    competition: { type: 'danger', text: '竞赛' },
    middle_school_exam: { type: 'info', text: '中考' },
    high_school_exam: { type: 'info', text: '高考' }
  },
  score: {
    1: { type: 'danger', text: '1分' },
    2: { type: 'warning', text: '2分' },
    3: { type: '', text: '3分' },
    4: { type: 'info', text: '4分' },
    5: { type: 'success', text: '5分' }
  }
}

const tagType = computed(() => {
  const mapping = statusMappings[props.type]?.[props.status]
  return mapping?.type || ''
})

const displayText = computed(() => {
  const mapping = statusMappings[props.type]?.[props.status]
  return mapping?.text || props.status
})
</script>
