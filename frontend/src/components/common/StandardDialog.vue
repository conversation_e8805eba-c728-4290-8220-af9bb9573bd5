<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :top="top"
    :before-close="handleClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :class="dialogClass"
    :fullscreen="isFullscreen"
    :destroy-on-close="destroyOnClose"
  >
    <!-- 自定义header插槽 -->
    <template #header="{ close, titleId, titleClass }" v-if="$slots.header">
      <slot name="header" :close="close" :titleId="titleId" :titleClass="titleClass" />
    </template>

    <!-- 标准header -->
    <template #header="{ close, titleId, titleClass }" v-else-if="showCustomHeader">
      <div class="standard-dialog-header">
        <h4 :id="titleId" :class="titleClass" class="dialog-title">
          {{ title }}
        </h4>
        <div class="header-actions" v-if="showHeaderActions">
          <el-button
            v-if="enableFullscreen"
            @click="toggleFullscreen"
            circle
            size="small"
            :title="isFullscreen ? '退出全屏' : '全屏'"
          >
            <el-icon>
              <ScaleToOriginal v-if="isFullscreen" />
              <FullScreen v-else />
            </el-icon>
          </el-button>
          <el-button
            @click="handleClose"
            circle
            size="small"
            title="关闭"
          >
            <el-icon>
              <Close />
            </el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <!-- 主要内容区域 -->
    <div class="standard-dialog-content">
      <slot />
    </div>

    <!-- Footer插槽 -->
    <template #footer v-if="$slots.footer || showDefaultFooter">
      <slot name="footer" v-if="$slots.footer" />
      <div class="standard-dialog-footer" v-else-if="showDefaultFooter">
        <el-button @click="handleCancel" :disabled="loading">
          {{ cancelText }}
        </el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="loading"
          v-if="showConfirmButton"
        >
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '90%'
  },
  top: {
    type: String,
    default: '2vh'
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  closeOnPressEscape: {
    type: Boolean,
    default: false
  },
  destroyOnClose: {
    type: Boolean,
    default: true
  },
  customClass: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  // Footer配置
  showDefaultFooter: {
    type: Boolean,
    default: true
  },
  showConfirmButton: {
    type: Boolean,
    default: true
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  // Header配置
  showCustomHeader: {
    type: Boolean,
    default: false
  },
  showHeaderActions: {
    type: Boolean,
    default: false
  },
  enableFullscreen: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'close'])

// 响应式数据
const isFullscreen = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogClass = computed(() => {
  const classes = ['standard-dialog']
  if (props.customClass) {
    classes.push(props.customClass)
  }
  return classes.join(' ')
})

// 方法
const handleClose = () => {
  emit('close')
  visible.value = false
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 暴露方法
defineExpose({
  toggleFullscreen
})
</script>

<style scoped>
/* 标准dialog样式 */
:deep(.standard-dialog) {
  max-width: 1400px;
  max-height: 95vh;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

:deep(.standard-dialog .el-dialog) {
  max-height: 95vh;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
}

:deep(.standard-dialog .el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

:deep(.standard-dialog .el-dialog__body) {
  padding: 0;
  flex: 1;
  overflow: hidden;
  max-height: calc(95vh - 180px); /* 减去头部和底部的空间 */
  display: flex;
  flex-direction: column;
}

:deep(.standard-dialog .el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #ebeef5;
  flex-shrink: 0;
  background: #fff;
}

:deep(.standard-dialog .el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 标准header样式 */
.standard-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 标准内容区域样式 */
.standard-dialog-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  min-height: 0; /* 确保能够收缩 */
}

/* 标准footer样式 */
.standard-dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :deep(.standard-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  :deep(.standard-dialog) {
    width: 98% !important;
    max-height: 98vh;
  }

  :deep(.standard-dialog .el-dialog__body) {
    max-height: calc(98vh - 160px);
  }

  :deep(.standard-dialog .el-dialog__header) {
    padding: 16px 20px 12px;
  }

  :deep(.standard-dialog .el-dialog__footer) {
    padding: 12px 16px;
  }

  .standard-dialog-footer {
    gap: 8px;
  }

  .dialog-title {
    font-size: 16px;
  }
}

@media (max-height: 700px) {
  :deep(.standard-dialog) {
    height: 90vh !important;
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
  }

  :deep(.standard-dialog .el-dialog__body) {
    max-height: calc(90vh - 160px);
  }
}

@media (max-height: 600px) {
  :deep(.standard-dialog) {
    height: 95vh !important;
    margin-top: 2.5vh !important;
    margin-bottom: 2.5vh !important;
  }

  :deep(.standard-dialog .el-dialog__body) {
    max-height: calc(95vh - 140px);
  }
}
</style> 