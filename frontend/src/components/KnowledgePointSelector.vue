<template>
  <div class="knowledge-point-selector">
    <el-select
      v-model="selectedValue"
      :placeholder="placeholder"
      filterable
      clearable
      @change="handleChange"
      style="width: 100%"
    >
      <el-option
        v-for="point in leafPoints"
        :key="point.uid"
        :label="point.name"
        :value="point.uid"
      >
        <div class="option-content">
          <div class="option-name">{{ point.name }}</div>
          <div class="option-path">{{ point.path }}</div>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { executeWithLoading } from '@/utils/apiUtils'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  projectId: {
    type: [String, Number],
    required: true
  },
  placeholder: {
    type: String,
    default: '请选择知识点'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const selectedValue = ref(props.modelValue)
const leafPoints = ref([])
const loading = ref(false)

// 方法
const loadKnowledgePoints = async () => {
  if (!props.projectId) return

  await executeWithLoading(async () => {
    const response = await fetch(`/api/projects/${props.projectId}/knowledge-paths/leaf-points`)
    const result = await response.json()

    if (result.success) {
      leafPoints.value = result.data || []
    } else {
      throw new Error(result.message || '加载知识点失败')
    }
  }, {
    loadingRef: loading,
    errorMessage: '加载知识点失败'
  })
}

const handleChange = (value) => {
  selectedValue.value = value

  // 找到选中的知识点对象
  const selectedPoint = leafPoints.value.find(point => point.uid === value)

  // 发送change事件，传递完整的知识点对象
  emit('update:modelValue', value)
  emit('change', selectedPoint)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
})

watch(() => props.projectId, () => {
  loadKnowledgePoints()
}, { immediate: true })
</script>

<style scoped>
.knowledge-point-selector {
  width: 100%;
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.option-name {
  font-weight: 500;
  color: #303133;
}

.option-path {
  font-size: 12px;
  color: #909399;
}
</style>
