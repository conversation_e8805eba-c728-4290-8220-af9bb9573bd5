<template>
  <div class="difficulty-selector">
    <h2>选择考试难度</h2>
    <div class="options">
      <button @click="selectDifficulty(1)">10以内加减法</button>
      <button @click="selectDifficulty(2)">20以内加减法</button>
      <button @click="selectDifficulty(3)">100以内加减法</button>
      <button @click="selectDifficulty(4)">乘法口诀</button>
      <button @click="selectDifficulty(5)">除法口诀</button>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['start-exam']);

const selectDifficulty = (difficulty) => {
  emit('start-exam', difficulty);
};
</script>

<style scoped>
.difficulty-selector {
  text-align: center;
}

h2 {
  font-size: 22px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 24px;
}

.options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

button {
  width: 100%;
  padding: 20px 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}

button:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

button:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>