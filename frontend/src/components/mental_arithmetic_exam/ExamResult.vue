<template>
  <div class="exam-result-container">
    <h2>考试结果</h2>
    <p>总题目数: {{ result.total }}</p>
    <p>正确数: {{ result.correct }}</p>
    <p>错误数: {{ result.incorrect }}</p>
    <p class="score">{{ percentageScore }} 分</p>
    <div v-if="saveStatus" class="save-status">
      <p v-if="saveStatus === 'saving'" class="saving">
        <i class="el-icon-loading"></i> 正在保存考试记录...
      </p>
      <p v-else-if="saveStatus === 'saved'" class="saved">
        <i class="el-icon-check"></i> 考试记录已保存
      </p>
      <p v-else-if="saveStatus === 'error'" class="error">
        <i class="el-icon-warning"></i> 保存失败，但不影响查看结果
      </p>
    </div>
    <button @click="restart">再试一次</button>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { quizExamRecordsApi, EXAM_CATEGORIES, MATH_SUBCATEGORIES, createExamRecordData } from '@/api/quizExamRecords';

const props = defineProps(['result']);
const emit = defineEmits(['restart']);

const saveStatus = ref(null);

const percentageScore = computed(() => {
  if (props.result.total === 0) {
    return 0;
  }
  return Math.floor((props.result.correct / props.result.total) * 100);
});

// 获取难度对应的子类别和标题
const getDifficultyInfo = (difficulty) => {
  const difficultyMap = {
    1: { subcategory: MATH_SUBCATEGORIES.ADDITION, title: '10以内加减法练习' },
    2: { subcategory: MATH_SUBCATEGORIES.MIXED, title: '20以内加减法练习' },
    3: { subcategory: MATH_SUBCATEGORIES.MIXED, title: '100以内加减法练习' },
    4: { subcategory: MATH_SUBCATEGORIES.MULTIPLICATION, title: '乘法口诀练习' },
    5: { subcategory: MATH_SUBCATEGORIES.DIVISION, title: '除法口诀练习' }
  };
  
  return difficultyMap[difficulty] || { 
    subcategory: MATH_SUBCATEGORIES.MIXED, 
    title: '数学口算练习' 
  };
};

// 保存考试记录
const saveExamRecord = async () => {
  if (!props.result || saveStatus.value === 'saved') return;
  
  console.log('🔍 开始保存数学口算考试记录');
  console.log('📋 考试结果数据:', props.result);
  
  try {
    saveStatus.value = 'saving';
    
    const difficultyInfo = getDifficultyInfo(props.result.difficulty);
    console.log('📊 难度信息:', difficultyInfo);
    
    const examData = createExamRecordData({
      category: EXAM_CATEGORIES.MENTAL_ARITHMETIC,
      subcategory: difficultyInfo.subcategory,
      title: difficultyInfo.title,
      totalQuestions: props.result.total,
      correctAnswers: props.result.correct,
      durationSeconds: props.result.durationSeconds,
      details: {
        difficulty: props.result.difficulty,
        incorrectAnswers: props.result.incorrect,
        examType: 'mental_arithmetic'
      }
    });
    
    console.log('📝 准备保存的考试数据:', examData);
    
    await quizExamRecordsApi.addExamRecord(examData);
    saveStatus.value = 'saved';
    
    console.log('✅ 数学口算考试记录已保存');
  } catch (error) {
    console.error('❌ 保存数学口算考试记录失败:', error);
    console.error('❌ 错误详情:', error.response?.data || error.message);
    saveStatus.value = 'error';
    // 不显示错误消息，避免影响用户体验
  }
};

const restart = () => {
  emit('restart');
};

// 组件挂载时自动保存记录
onMounted(() => {
  saveExamRecord();
});
</script>

<style scoped>
.exam-result-container {
  text-align: center;
}

.exam-result-container h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 24px;
}

.exam-result-container p {
  font-size: 18px;
  margin: 12px 0;
  color: #4b5563;
}

.exam-result-container .score {
  font-size: 48px;
  font-weight: 700;
  color: #8e44ad;
  margin: 20px 0;
}

.exam-result-container button {
  margin-top: 24px;
  padding: 16px 30px;
  font-size: 18px;
  font-weight: 600;
  width: 100%;
  box-sizing: border-box;
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.exam-result-container button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.save-status {
  margin: 16px 0;
  font-size: 14px;
}

.save-status .saving {
  color: #409EFF;
  margin: 0;
}

.save-status .saved {
  color: #67C23A;
  margin: 0;
}

.save-status .error {
  color: #F56C6C;
  margin: 0;
}

.save-status i {
  margin-right: 4px;
}
</style>