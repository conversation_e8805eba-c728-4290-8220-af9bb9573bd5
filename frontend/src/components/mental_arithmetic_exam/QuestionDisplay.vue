<template>
  <div class="question-display-container">
    <div v-if="currentQuestion">
      <div class="question">{{ currentQuestion.text }}</div>
      <div class="options">
        <button
          v-for="(option, index) in currentQuestion.options"
          :key="index"
          @click="selectAnswer(option)"
          :class="{ 'correct': showResult && option === currentQuestion.answer, 'incorrect': showResult && selectedAnswer === option && option !== currentQuestion.answer }"
          :disabled="showResult"
        >
          {{ option }}
        </button>
      </div>
      <div v-if="showResult" class="result-message">
        <span v-if="isCorrect">正确!</span>
        <span v-else>错误. 正确答案是 {{ currentQuestion.answer }}</span>
      </div>
      <button @click="nextQuestion" v-if="showResult" class="next-question-button">下一题</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps(['difficulty']);
const emit = defineEmits(['exam-finished']);

const questions = ref([]);
const currentQuestionIndex = ref(0);
const currentQuestion = ref(null);
const selectedAnswer = ref(null);
const showResult = ref(false);
const isCorrect = ref(false);
const correctCount = ref(0);
const incorrectCount = ref(0);

const generateQuestions = () => {
  const generatedQuestions = [];
  for (let i = 0; i < 20; i++) {
    generatedQuestions.push(generateQuestion(props.difficulty));
  }
  questions.value = generatedQuestions;
  currentQuestion.value = questions.value[0];
};

const generateQuestion = (difficulty) => {
  let num1, num2, operator, answer, maxRange;

  switch (difficulty) {
    case 1: // 10以内加减法
      maxRange = 10;
      operator = Math.random() < 0.5 ? '+' : '-';
      if (operator === '+') {
        // 确保加数和被加数都不为0，且和不超过10
        num1 = Math.floor(Math.random() * 9) + 1; // 1-9
        num2 = Math.floor(Math.random() * Math.min(9, maxRange - num1)) + 1; // 1到(10-num1)
      } else {
        // 确保被减数和减数都不为0，且被减数大于等于减数
        num1 = Math.floor(Math.random() * 10) + 1; // 1-10
        num2 = Math.floor(Math.random() * num1) + 1; // 1到num1
      }
      break;
    case 2: // 20以内加减法
      maxRange = 20;
      operator = Math.random() < 0.5 ? '+' : '-';
      if (operator === '+') {
        num1 = Math.floor(Math.random() * 19) + 1; // 1-19
        num2 = Math.floor(Math.random() * Math.min(19, maxRange - num1)) + 1; // 1到(20-num1)
      } else {
        num1 = Math.floor(Math.random() * 20) + 1; // 1-20
        num2 = Math.floor(Math.random() * num1) + 1; // 1到num1
      }
      break;
    case 3: // 100以内加减法
      maxRange = 100;
      operator = Math.random() < 0.5 ? '+' : '-';
      if (operator === '+') {
        num1 = Math.floor(Math.random() * 99) + 1; // 1-99
        num2 = Math.floor(Math.random() * Math.min(99, maxRange - num1)) + 1; // 1到(100-num1)
      } else {
        num1 = Math.floor(Math.random() * 100) + 1; // 1-100
        num2 = Math.floor(Math.random() * num1) + 1; // 1到num1
      }
      break;
    case 4: // 乘法口诀
      maxRange = 81; // 9x9最大值
      operator = '*';
      num1 = Math.floor(Math.random() * 9) + 1;
      num2 = Math.floor(Math.random() * 9) + 1;
      break;
    case 5: // 除法口诀
      maxRange = 9; // 商最大为9
      operator = '÷';
      num2 = Math.floor(Math.random() * 9) + 1;
      answer = Math.floor(Math.random() * 9) + 1;
      num1 = num2 * answer;
      break;
  }

  // 安全的计算方法，不使用 eval
  if (operator !== '÷') {
    switch (operator) {
      case '+':
        answer = num1 + num2;
        break;
      case '-':
        answer = num1 - num2;
        break;
      case '*':
        answer = num1 * num2;
        break;
      default:
        answer = 0;
    }
  }

  const options = generateOptions(answer, maxRange);

  return {
    text: `${num1} ${operator} ${num2} = ?`,
    answer,
    options,
  };
};

const generateOptions = (answer, maxRange) => {
  const options = new Set([answer]);
  
  while (options.size < 4) {
    // 根据答案的大小调整偏移范围
    let offsetRange;
    if (answer <= 10) {
      offsetRange = 3; // 小数字使用较小偏移
    } else if (answer <= 20) {
      offsetRange = 5;
    } else if (answer <= 50) {
      offsetRange = 8;
    } else {
      offsetRange = 10;
    }
    
    const randomOffset = Math.floor(Math.random() * (offsetRange * 2 + 1)) - offsetRange;
    const incorrectOption = answer + randomOffset;
    
    // 确保选项在合理范围内：不为负数，不超过考试范围，不等于正确答案
    if (incorrectOption >= 0 && incorrectOption <= maxRange && incorrectOption !== answer) {
      options.add(incorrectOption);
    }
  }
  
  return Array.from(options).sort(() => Math.random() - 0.5);
};

const selectAnswer = (option) => {
  selectedAnswer.value = option;
  isCorrect.value = option === currentQuestion.value.answer;
  if (isCorrect.value) {
    correctCount.value++;
  } else {
    incorrectCount.value++;
  }
  showResult.value = true;
};

const nextQuestion = () => {
  if (currentQuestionIndex.value < questions.value.length - 1) {
    currentQuestionIndex.value++;
    currentQuestion.value = questions.value[currentQuestionIndex.value];
    showResult.value = false;
    selectedAnswer.value = null;
  } else {
    emit('exam-finished', { correct: correctCount.value, incorrect: incorrectCount.value, total: questions.value.length });
  }
};

onMounted(() => {
  generateQuestions();
});

</script>

<style scoped>
.question-display-container {
  text-align: center;
}

.question {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 30px;
  color: #1f2937;
}

.options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.options button {
  width: 100%;
  padding: 20px 10px;
  font-size: 22px;
  font-weight: 600;
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 16px;
  background-color: #f0f0f0;
  color: #333;
  transition: all 0.3s ease;
}

.options button:disabled {
  cursor: not-allowed;
}

.result-message {
  margin-top: 20px;
  font-size: 22px;
  font-weight: 600;
}

.correct {
  background-color: #2ecc71;
  color: white;
  border-color: #27ae60;
}

.incorrect {
  background-color: #e74c3c;
  color: white;
  border-color: #c0392b;
}

.next-question-button {
  margin-top: 20px;
  padding: 16px 30px;
  font-size: 18px;
  font-weight: 600;
  width: 100%;
  box-sizing: border-box;
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, #8e44ad 0%, #3498db 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.next-question-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}
</style>