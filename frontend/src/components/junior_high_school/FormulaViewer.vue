<template>
  <div class="formula-viewer-container" v-html="renderedContent"></div>
</template>

<script setup>
import { computed } from 'vue';
import MarkdownIt from 'markdown-it';
import mk from 'md-it-katex';
import 'katex/dist/katex.min.css';

const props = defineProps({
  content: {
    type: String,
    required: true,
  },
});

const md = new MarkdownIt({
  html: true,
});
md.use(mk, {
  throwOnError: false,
  errorColor: '#cc0000',
  strict: false
});

const renderedContent = computed(() => {
  return md.render(props.content);
});
</script>

<style scoped>
.formula-viewer-container {
  padding: 20px;
  line-height: 1.8;
}

.formula-viewer-container :deep(h2) {
  font-size: 22px;
  font-weight: 700;
  margin-top: 24px;
  margin-bottom: 16px;
  border-bottom: 2px solid #eee;
  padding-bottom: 8px;
}

.formula-viewer-container :deep(ul) {
  padding-left: 20px;
}

.formula-viewer-container :deep(li) {
  margin-bottom: 12px;
}

.formula-viewer-container :deep(strong) {
  color: #8e44ad;
}
</style>