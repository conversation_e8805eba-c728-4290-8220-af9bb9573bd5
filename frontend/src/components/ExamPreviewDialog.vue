<template>
  <el-dialog
    ref="dialogRef"
    v-model="dialogVisible"
    :title="examTitle || '考卷预览'"
    width="80%"
    top="5vh"
    append-to-body
    class="standard-dialog large-dialog"
  >
    <div class="standard-dialog-content">
      <div class="preview-container">
        <!-- 工具栏 -->
        <div class="preview-toolbar">
          <div class="toolbar-left">
            <el-button-group>
              <el-button
                :type="viewMode === 'preview' ? 'primary' : ''"
                @click="viewMode = 'preview'"
                size="small"
              >
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button
                :type="viewMode === 'source' ? 'primary' : ''"
                @click="viewMode = 'source'"
                size="small"
              >
                <el-icon><Document /></el-icon>
                源码
              </el-button>
            </el-button-group>
          </div>
          <div class="toolbar-right">
            <el-button size="small" @click="copyContent">
              <el-icon><DocumentCopy /></el-icon>
              复制
            </el-button>
            <el-button size="small" @click="downloadMarkdown">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button size="small" @click="toggleFullscreen">
              <el-icon><FullScreen v-if="!isFullscreen" /><Aim v-else /></el-icon>
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </el-button>
            <el-button size="small" type="primary" @click="printExam">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="preview-content">
          <!-- 预览模式 -->
          <div v-if="viewMode === 'preview'" class="preview-mode">
            <div class="exam-content" v-html="renderedMarkdown"></div>
          </div>

          <!-- 源码模式 -->
          <div v-else class="source-mode">
            <el-input
              :value="examContent"
              type="textarea"
              :rows="25"
              readonly
              class="source-textarea"
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="standard-dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  View, Document, DocumentCopy, Download, Printer, FullScreen, Aim
} from '@element-plus/icons-vue'
// marked 和 markedKatex 使用动态导入
import 'katex/dist/katex.min.css'
import { processSvgInMarkdown, containsSvg, preprocessMathDelimiters } from '@/utils/svgToImage'
import { marked } from 'marked'
import markedKatex from 'marked-katex-extension'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  examContent: {
    type: String,
    default: ''
  },
  examTitle: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const viewMode = ref('preview') // 'preview' | 'source'
const isFullscreen = ref(false) // 全屏状态
const dialogRef = ref(null) // 对话框引用



// 渲染后的Markdown内容
const renderedMarkdown = ref('')

// 处理内容的异步函数
const processAndRenderContent = async () => {
  if (!props.examContent) {
    renderedMarkdown.value = ''
    return
  }

  try {
    console.log('🚀 预览对话框开始综合处理...')
    let content = props.examContent

    // 先预处理数学公式分隔符
    console.log('🔢 预处理数学公式分隔符...')
    content = preprocessMathDelimiters(content)
    console.log('🔄 数学公式分隔符处理后的内容:', content)

    // 再处理SVG
    if (containsSvg(content)) {
      console.log('🎨 检测到SVG，开始处理...')
      content = await processSvgInMarkdown(content)
      console.log('🔄 SVG处理后的内容:', content)
    }

    // 使用marked解析markdown
    const result = marked.parse(content)
    console.log('✅ 预览对话框最终渲染结果:', result)

    renderedMarkdown.value = result
  } catch (error) {
    console.error('预览对话框渲染失败:', error)
    renderedMarkdown.value = `<pre>${props.examContent}</pre>`
  }
}

// 删除旧的SVG处理函数，使用新的综合处理函数



// 监听对话框显示状态，重置视图模式并处理内容
watch(dialogVisible, async (visible) => {
  if (visible) {
    viewMode.value = 'preview'
    isFullscreen.value = false
    // 处理内容
    await processAndRenderContent()
  } else {
    // 对话框关闭时清理内容
    renderedMarkdown.value = ''
  }
})

// 监听考卷内容变化，重新处理内容
watch(() => props.examContent, async () => {
  if (dialogVisible.value) {
    await processAndRenderContent()
  }
})

// 复制内容
const copyContent = async () => {
  if (!props.examContent) {
    ElMessage.warning('没有可复制的内容')
    return
  }

  try {
    await navigator.clipboard.writeText(props.examContent)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = props.examContent
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('内容已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 下载Markdown文件
const downloadMarkdown = () => {
  if (!props.examContent) {
    ElMessage.warning('没有可下载的内容')
    return
  }

  try {
    const blob = new Blob([props.examContent], { type: 'text/markdown;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    
    link.href = url
    link.download = `${props.examTitle || '考卷'}.md`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('文件下载失败')
  }
}

// 切换全屏
const toggleFullscreen = async () => {
  try {
    if (!isFullscreen.value) {
      // 进入全屏 - 对整个文档进行全屏
      const element = document.documentElement

      if (element.requestFullscreen) {
        await element.requestFullscreen()
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen()
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen()
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen()
      } else {
        throw new Error('浏览器不支持全屏API')
      }

      isFullscreen.value = true
      ElMessage.success('已进入全屏模式')
    } else {
      // 退出全屏
      if (document.exitFullscreen) {
        await document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen()
      }

      isFullscreen.value = false
      ElMessage.success('已退出全屏模式')
    }
  } catch (error) {
    console.error('全屏切换失败:', error)
    ElMessage.error('全屏切换失败: ' + error.message)
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  const isCurrentlyFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  )

  if (isFullscreen.value !== isCurrentlyFullscreen) {
    isFullscreen.value = isCurrentlyFullscreen
  }
}

// 打印考卷
const printExam = () => {
  if (!props.examContent) {
    ElMessage.warning('没有可打印的内容')
    return
  }

  try {
    // 创建打印窗口
    const printWindow = window.open('', '_blank')
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${props.examTitle || '考卷'}</title>
        <meta charset="utf-8">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css">
        <style>
          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
          }

          .exam-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
          }

          .exam-header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
            font-weight: bold;
          }

          .exam-info {
            font-size: 14px;
            color: #666;
          }

          h1, h2, h3, h4, h5, h6 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 10px;
          }

          h1 {
            font-size: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
          }

          h2 {
            font-size: 18px;
          }

          h3 {
            font-size: 16px;
          }

          p {
            margin-bottom: 10px;
          }

          ol, ul {
            margin-bottom: 15px;
            padding-left: 25px;
          }

          li {
            margin-bottom: 5px;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }

          table, th, td {
            border: 1px solid #ddd;
          }

          th, td {
            padding: 8px;
            text-align: left;
          }

          th {
            background-color: #f5f5f5;
            font-weight: bold;
          }

          blockquote {
            border-left: 4px solid #ddd;
            margin: 0 0 15px 0;
            padding: 10px 15px;
            background-color: #f9f9f9;
          }

          code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
          }

          pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin-bottom: 15px;
          }

          pre code {
            background: none;
            padding: 0;
          }

          .answer-space {
            border-bottom: 1px solid #333;
            min-height: 30px;
            margin: 10px 0;
          }

          img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px auto;
          }

          @media print {
            body {
              margin: 0;
              font-size: 12pt;
            }

            .no-print {
              display: none;
            }

            h1 {
              page-break-after: avoid;
            }

            .page-break {
              page-break-before: always;
            }

            img {
              page-break-inside: avoid;
              max-width: 100%;
            }
          }
        </style>
      </head>
      <body>
        <div class="exam-header">
          <h1>${props.examTitle || '考卷'}</h1>
          <div class="exam-info">
            <p>打印时间：${new Date().toLocaleString()}</p>
          </div>
        </div>
        <div class="exam-content">
          ${renderedMarkdown.value}
        </div>
      </body>
      </html>
    `

    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()

    // 等待内容加载完成后打印
    setTimeout(() => {
      printWindow.print()
    }, 500)
    
    ElMessage.success('打印预览已打开')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败: ' + error.message)
  }
}

// 初始化marked
const initializeMarked = () => {
  marked.use(markedKatex({
    throwOnError: false,
    displayMode: false,
    output: 'html'
  }))
}

// 在组件挂载时初始化
onMounted(() => {
  initializeMarked()
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
})

// 组件卸载时移除全屏状态监听
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
})
</script>

<style scoped>
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  min-height: 0;
}

.preview-mode {
  height: 100%;
}

.exam-content {
  background: white;
  padding: 30px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 100%;
}

.source-mode {
  height: 100%;
}

.source-textarea {
  height: 100%;
}

.source-textarea :deep(.el-textarea__inner) {
  height: 500px !important;
  resize: none;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

/* Markdown内容样式 */
.exam-content :deep(h1),
.exam-content :deep(h2),
.exam-content :deep(h3),
.exam-content :deep(h4),
.exam-content :deep(h5),
.exam-content :deep(h6) {
  color: #303133;
  margin-top: 24px;
  margin-bottom: 12px;
  font-weight: 600;
}

.exam-content :deep(h1) {
  font-size: 24px;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 12px;
  text-align: center;
}

.exam-content :deep(h2) {
  font-size: 20px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.exam-content :deep(h3) {
  font-size: 18px;
}

.exam-content :deep(h4) {
  font-size: 16px;
}

.exam-content :deep(p) {
  margin-bottom: 12px;
  line-height: 1.7;
  color: #606266;
}

.exam-content :deep(ol),
.exam-content :deep(ul) {
  margin-bottom: 16px;
  padding-left: 24px;
}

.exam-content :deep(li) {
  margin-bottom: 6px;
  line-height: 1.6;
}

.exam-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.exam-content :deep(th),
.exam-content :deep(td) {
  padding: 12px;
  text-align: left;
  border: 1px solid #e4e7ed;
}

.exam-content :deep(th) {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.exam-content :deep(blockquote) {
  border-left: 4px solid #409eff;
  margin: 16px 0;
  padding: 12px 16px;
  background-color: #f0f9ff;
  color: #606266;
}

.exam-content :deep(code) {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #e74c3c;
}

.exam-content :deep(pre) {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
}

.exam-content :deep(pre code) {
  background: none;
  padding: 0;
  color: #303133;
}

.exam-content :deep(hr) {
  border: none;
  border-top: 1px solid #e4e7ed;
  margin: 24px 0;
}

/* 全屏模式样式 */
:fullscreen .exam-preview-dialog {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
}

:fullscreen .exam-preview-dialog .el-dialog {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  border-radius: 0 !important;
  max-width: none !important;
  top: 0 !important;
}

:fullscreen .exam-preview-dialog .preview-container {
  height: calc(100vh - 120px) !important;
}

:fullscreen .exam-preview-dialog .preview-toolbar {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

:fullscreen .exam-preview-dialog .dialog-footer {
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 全屏时隐藏其他元素 */
:fullscreen .exam-preview-dialog ~ * {
  display: none !important;
}


</style>
