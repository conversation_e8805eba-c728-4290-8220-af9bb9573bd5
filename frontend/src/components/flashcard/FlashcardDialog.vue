<template>
  <el-dialog
    v-model="dialogVisible"
    width="70%"
    :fullscreen="isFullscreen"
    :before-close="handleClose"
    center
    :close-on-click-modal="false"
    :show-close="false"
    class="standard-dialog large-dialog"
    :destroy-on-close="true"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="dialog-header">
        <h4 :id="titleId" :class="titleClass" class="dialog-title">
          {{ isEdit ? '编辑卡片' : '创建卡片' }}
        </h4>
        <div class="header-actions">
          <el-button
            @click="toggleFullscreen"
            circle
            size="small"
            :title="isFullscreen ? '退出全屏' : '全屏'"
          >
            <el-icon>
              <ScaleToOriginal v-if="isFullscreen" />
              <FullScreen v-else />
            </el-icon>
          </el-button>
          <el-button
            @click="handleClose"
            circle
            size="small"
            title="关闭"
          >
            <el-icon>
              <Close />
            </el-icon>
          </el-button>
        </div>
      </div>
    </template>
    <div class="standard-dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="left"
        class="flashcard-form"
      >
      <!-- 一行布局：标题、知识点、难度等级 -->
      <div class="form-row">
        <el-form-item label="标题" prop="title" class="form-item-title">
          <el-input
            v-model="form.title"
            placeholder="请输入卡片标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="知识点" prop="knowledgePointId" class="form-item-knowledge">
          <KnowledgePointSelector
            v-model="form.knowledgePointId"
            :project-id="projectId"
            placeholder="请选择知识点"
            @change="onKnowledgePointChange"
          />
        </el-form-item>

        <el-form-item label="难度" prop="difficulty" class="form-item-difficulty">
          <el-select v-model="form.difficulty" placeholder="选择难度">
            <el-option :value="1" label="简单">
              <span class="difficulty-option difficulty-1">简单</span>
            </el-option>
            <el-option :value="2" label="容易">
              <span class="difficulty-option difficulty-2">容易</span>
            </el-option>
            <el-option :value="3" label="中等">
              <span class="difficulty-option difficulty-3">中等</span>
            </el-option>
            <el-option :value="4" label="困难">
              <span class="difficulty-option difficulty-4">困难</span>
            </el-option>
            <el-option :value="5" label="极难">
              <span class="difficulty-option difficulty-5">极难</span>
            </el-option>
          </el-select>
        </el-form-item>
      </div>

      <!-- 正面内容编辑和预览 -->
      <el-form-item label="正面内容" prop="frontContent" class="form-item-split">
        <div class="split-container">
          <div class="editor-panel">
            <RichTextEditor
              v-model="form.frontContent"
              :height="280"
              :upload-image-url="`/api/flashcards/${projectId}/upload-image`"
              placeholder="请输入卡片正面内容，支持富文本格式和图片上传..."
              class="split-editor"
            />
          </div>
          <div class="preview-panel">
            <div class="panel-header">
              预览
              <div class="preview-actions">
                <el-button
                  size="small"
                  link
                  @click="speakPreviewContent('front')"
                  :disabled="!form.frontContent || isSpeaking"
                >
                  <el-icon>
                    <Loading v-if="isSpeaking && currentSpeakingPanel === 'front'" />
                    <Microphone v-else />
                  </el-icon>
                  {{ isSpeaking && currentSpeakingPanel === 'front' ? '朗读中...' : '朗读' }}
                </el-button>
              </div>
            </div>
            <div class="preview-card" @click="handlePreviewClick($event, 'front')">
              <RichTextViewer :content="form.frontContent || '暂无内容'" />
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 背面内容编辑和预览 -->
      <el-form-item label="背面内容（可选）" prop="backContent" class="form-item-split">
        <div class="split-container">
          <div class="editor-panel">
            <RichTextEditor
              v-model="form.backContent"
              :height="280"
              :upload-image-url="`/api/flashcards/${projectId}/upload-image`"
              placeholder="请输入卡片背面内容（可选），支持富文本格式和图片上传..."
              class="split-editor"
            />
          </div>
          <div class="preview-panel">
            <div class="panel-header">
              预览
              <div class="preview-actions">
                <el-button
                  size="small"
                  link
                  @click="speakPreviewContent('back')"
                  :disabled="!form.backContent || isSpeaking"
                >
                  <el-icon>
                    <Loading v-if="isSpeaking && currentSpeakingPanel === 'back'" />
                    <Microphone v-else />
                  </el-icon>
                  {{ isSpeaking && currentSpeakingPanel === 'back' ? '朗读中...' : '朗读' }}
                </el-button>
              </div>
            </div>
            <div class="preview-card" @click="handlePreviewClick($event, 'back')">
              <RichTextViewer :content="form.backContent || '暂无内容'" />
            </div>
          </div>
        </div>
      </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="standard-dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="!isEdit"
          type="success"
          @click="handleSubmitAndContinue"
          :loading="loading"
        >
          创建并继续
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Microphone, Loading, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import RichTextEditor from '@/components/RichTextEditor.vue'
import RichTextViewer from '@/components/RichTextViewer.vue'
import KnowledgePointSelector from '@/components/KnowledgePointSelector.vue'
import { flashcardAPI } from '@/api/flashcard'
import {
  isSpeechSynthesisSupported,
  speakText,
  stopSpeech,
  isSpeaking as checkSpeaking,
  isTextReadable,
  getTextSpeechInfo,
  extractTextFromHtml
} from '@/utils/speech'
import { VALIDATION_RULES } from '@/utils/constants'
import { executeWithLoading } from '@/utils/apiUtils'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  projectId: {
    type: Number,
    required: true
  },
  flashcard: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const loading = ref(false)
const isFullscreen = ref(false)
const isSpeaking = ref(false)
const currentSpeakingPanel = ref(null) // 'front' 或 'back'

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.flashcard)

// 表单数据
const form = reactive({
  title: '',
  knowledgePointId: '',
  knowledgePointName: '',
  knowledgePointPath: '',
  frontContent: '',
  backContent: '',
  difficulty: 1
})

// 表单验证规则
const rules = {
  title: VALIDATION_RULES.CARD_TITLE,
  knowledgePointId: VALIDATION_RULES.KNOWLEDGE_POINT,
  frontContent: VALIDATION_RULES.CARD_FRONT_CONTENT,
  difficulty: VALIDATION_RULES.CARD_DIFFICULTY
}

// 监听对话框显示状态
watch(dialogVisible, async (visible) => {
  if (visible) {
    resetForm()
    if (props.flashcard) {
      fillForm(props.flashcard)
    }
  } else {
    // 对话框关闭时停止朗读并重置状态
    if (isSpeaking.value) {
      console.log('🛑 对话框关闭，停止朗读')
      stopSpeech()
      resetSpeechState()
    }
  }
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    title: '',
    knowledgePointId: '',
    knowledgePointName: '',
    knowledgePointPath: '',
    frontContent: '',
    backContent: '',
    difficulty: 1
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const fillForm = (flashcard) => {
  Object.assign(form, {
    title: flashcard.title,
    knowledgePointId: flashcard.knowledgePointId,
    knowledgePointName: flashcard.knowledgePointName,
    knowledgePointPath: flashcard.knowledgePointPath,
    frontContent: flashcard.frontContent,
    backContent: flashcard.backContent,
    difficulty: flashcard.difficulty
  })
}

const onKnowledgePointChange = (selectedPoint) => {
  if (selectedPoint) {
    form.knowledgePointName = selectedPoint.name
    form.knowledgePointPath = selectedPoint.path || ''
  } else {
    form.knowledgePointName = ''
    form.knowledgePointPath = ''
  }
}

const handleSubmit = async (continueAfterSubmit = false) => {
  try {
    await formRef.value.validate()

    const data = {
      projectId: props.projectId,
      knowledgePointId: form.knowledgePointId,
      knowledgePointName: form.knowledgePointName,
      knowledgePointPath: form.knowledgePointPath,
      title: form.title,
      frontContent: form.frontContent,
      backContent: form.backContent,
      difficulty: form.difficulty
    }

    await executeWithLoading(async () => {
      if (isEdit.value) {
        await flashcardAPI.updateFlashcard(props.flashcard.id, props.projectId, data)
        ElMessage.success('卡片更新成功')
      } else {
        await flashcardAPI.createFlashcard(data)
        ElMessage.success('卡片创建成功')
      }
    }, {
      loadingRef: loading,
      errorMessage: isEdit.value ? '更新卡片失败' : '创建卡片失败'
    })

    emit('success', { continueAfterSubmit })

    // 如果是"创建并继续"模式，清空表单但保持对话框打开
    if (continueAfterSubmit && !isEdit.value) {
      // 保存当前的知识点信息，以便用户继续在同一知识点下创建卡片
      const currentKnowledgePoint = {
        knowledgePointId: form.knowledgePointId,
        knowledgePointName: form.knowledgePointName,
        knowledgePointPath: form.knowledgePointPath
      }

      // 重置表单
      resetForm()

      // 恢复知识点选择（用户体验优化）
      Object.assign(form, currentKnowledgePoint)

      // 清除表单验证状态
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    }

  } catch (error) {
    if (error !== 'validation failed') {
      console.error('提交卡片失败:', error)
    }
  }
}

const handleSubmitAndContinue = async () => {
  await handleSubmit(true)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const handleClose = () => {
  // 停止朗读
  if (isSpeaking.value) {
    stopSpeech()
    resetSpeechState()
  }

  dialogVisible.value = false
  isFullscreen.value = false
}

// 语音朗读相关方法
const speakPreviewContent = async (panel) => {
  try {
    // 检查浏览器支持
    if (!isSpeechSynthesisSupported()) {
      ElMessage.error('您的浏览器不支持语音合成功能')
      return
    }

    // 如果正在朗读，则停止
    if (isSpeaking.value) {
      console.log('🛑 停止当前朗读')
      stopSpeech()
      resetSpeechState()
      return
    }

    // 获取要朗读的内容
    const content = panel === 'front' ? form.frontContent : form.backContent
    if (!content || content.trim() === '' || content === '暂无内容') {
      ElMessage.warning('没有可朗读的内容')
      return
    }

    // 提取纯文本
    const text = extractTextFromHtml(content)
    if (!text || !text.trim()) {
      ElMessage.warning('没有可朗读的文本内容')
      return
    }

    // 检查文本是否可读
    if (!isTextReadable(text)) {
      ElMessage.warning('该文本暂不支持朗读功能')
      return
    }

    // 获取文本信息并显示提示
    const speechInfo = getTextSpeechInfo(text)
    const languageMap = {
      'english': '英文',
      'chinese': '中文',
      'mixed': '中英文混合'
    }
    const languageText = languageMap[speechInfo.language] || '未知语言'
    console.log(`🔊 开始朗读${languageText}内容:`, text.substring(0, 50) + '...')

    // 设置朗读状态
    isSpeaking.value = true
    currentSpeakingPanel.value = panel

    // 使用自定义的语音播放方法，避免依赖外部状态管理
    await playTextWithCustomControl(text, panel)

  } catch (error) {
    console.error('❌ 朗读失败:', error)

    // 检查是否是中断错误（用户主动停止）
    if (error.message && error.message.includes('interrupted')) {
      console.log('🛑 朗读被用户中断')
    } else {
      ElMessage.error(`朗读失败: ${error.message || '未知错误'}`)
    }
  } finally {
    // 确保状态重置
    console.log('🔄 finally块重置朗读状态')
    resetSpeechState()
  }
}

// 自定义语音播放控制方法
const playTextWithCustomControl = (text, panel) => {
  return new Promise((resolve, reject) => {
    // 停止任何现有的语音
    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel()
    }

    // 短暂延迟确保之前的语音完全停止
    setTimeout(() => {
      const utterance = new SpeechSynthesisUtterance(text)

      // 设置语音参数
      utterance.rate = 0.9
      utterance.pitch = 1.0
      utterance.volume = 1.0
      utterance.lang = 'zh-CN'

      // 尝试选择合适的语音
      const voices = window.speechSynthesis.getVoices()
      const chineseVoice = voices.find(voice =>
        voice.lang.includes('zh') || voice.lang.includes('Chinese')
      )
      if (chineseVoice) {
        utterance.voice = chineseVoice
      }

      // 设置事件监听器
      utterance.onstart = () => {
        console.log('🔊 语音开始播放')
      }

      utterance.onend = () => {
        console.log('✅ 语音播放完成')
        ElMessage.success('朗读完成')
        // 延迟重置状态，确保UI更新
        setTimeout(() => {
          resetSpeechState()
          resolve()
        }, 100)
      }

      utterance.onerror = (event) => {
        console.error('❌ 语音播放错误:', event.error)
        if (event.error === 'interrupted') {
          console.log('🛑 语音被中断')
          resolve()
        } else {
          reject(new Error(`语音播放失败: ${event.error}`))
        }
        setTimeout(() => {
          resetSpeechState()
        }, 100)
      }

      // 开始播放
      console.log('🚀 开始语音合成播放')
      window.speechSynthesis.speak(utterance)

    }, 200) // 给足够的时间让之前的语音停止
  })
}

const handlePreviewClick = async (event, panel) => {
  // 防止频繁点击
  const now = Date.now()
  if (now - (handlePreviewClick.lastClickTime || 0) < 500) {
    return
  }
  handlePreviewClick.lastClickTime = now

  // 如果点击的是文本内容，尝试朗读
  const target = event.target
  if (target && target.textContent && target.textContent.trim()) {
    await speakPreviewContent(panel)
  }
}

const resetSpeechState = () => {
  console.log('🔄 重置朗读状态 - 当前状态:', {
    isSpeaking: isSpeaking.value,
    currentSpeakingPanel: currentSpeakingPanel.value
  })

  // 重置状态
  isSpeaking.value = false
  currentSpeakingPanel.value = null

  console.log('✅ 朗读状态已重置')
}
</script>

<style scoped>
/* 弹窗样式已在全局样式中定义，这里只定义组件内部样式 */

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 44px; /* 进一步减少高度 */
  padding: 0 16px; /* 保持左右padding */
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  margin: 0; /* 确保没有外边距 */
  box-sizing: border-box;
}

.dialog-title {
  margin: 0;
  padding: 0;
  font-size: 16px; /* 减小字体 */
  font-weight: 600;
  color: #303133;
  line-height: 44px; /* 与header高度一致 */
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 移除了 form-section 相关样式以节省高度 */

.difficulty-option {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.difficulty-option.difficulty-1 { background-color: #E8F5E8; color: #4CAF50; }
.difficulty-option.difficulty-2 { background-color: #E3F2FD; color: #2196F3; }
.difficulty-option.difficulty-3 { background-color: #F3E5F5; color: #9C27B0; }
.difficulty-option.difficulty-4 { background-color: #FFF3E0; color: #FF9800; }
.difficulty-option.difficulty-5 { background-color: #FFEBEE; color: #F44336; }

/* 一行表单布局 */
.form-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr;
  gap: 16px;
  align-items: start;
}

/* 表单项标签统一样式 */
.form-item-title,
.form-item-knowledge,
.form-item-difficulty,
.form-item-split {
  :deep(.el-form-item__label) {
    width: 60px !important;
    text-align: right;
    padding-right: 6px;
  }

  :deep(.el-form-item__content) {
    margin-left: 66px !important;
  }
}

/* 响应式：小屏幕时改为垂直布局 */
@media (max-width: 1024px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* 小屏幕时表单项样式保持一致，无需重复定义 */
}

.split-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 16px;
  width: 100%;
}

.editor-panel {
  display: flex;
  flex-direction: column;
}

.preview-panel {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  width: 300px;
  aspect-ratio: 3/4;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background: #f9fafb;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-actions {
  display: flex;
  gap: 4px;
}

.preview-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
  height: auto;
  min-height: auto;
}

.split-editor {
  width: 100% !important;
  flex: 1;
}

.preview-card {
  padding: 12px;
  flex: 1;
  overflow-y: auto;
  background: white;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.preview-card:hover {
  background-color: rgba(64, 158, 255, 0.05);
}

.preview-card::after {
  content: '💬 点击朗读内容';
  position: absolute;
  bottom: 8px;
  right: 8px;
  font-size: 11px;
  color: #9ca3af;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.preview-card:hover::after {
  opacity: 1;
}

/* 响应式设计 - 小屏幕时改为上下布局 */
@media (max-width: 1024px) {
  .split-container {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .preview-panel {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    aspect-ratio: 3/4;
  }
}

/* 响应式设计已在全局样式中定义 */



.dialog-footer {
  text-align: right;
}

/* 全局样式覆盖，确保弹窗样式生效 */
</style>

<style>
/* 卡片表单容器样式 */
.flashcard-form {
  padding: 20px 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .flashcard-dialog.el-dialog {
    width: 85% !important;
  }
}

@media (max-width: 768px) {
  .flashcard-dialog.el-dialog {
    width: 95% !important;
    height: 85vh !important;
    margin-top: 7.5vh !important;
    margin-bottom: 7.5vh !important;
  }
}

@media (max-height: 700px) {
  .flashcard-dialog.el-dialog {
    height: 90vh !important;
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
  }
}

@media (max-height: 600px) {
  .flashcard-dialog.el-dialog {
    height: 95vh !important;
    margin-top: 2.5vh !important;
    margin-bottom: 2.5vh !important;
  }
}

/* 确保富文本编辑器的下拉菜单在对话框中正确显示 */
.flashcard-dialog .rich-text-editor .ql-picker-options {
  z-index: 2000 !important; /* 提高z-index确保在对话框之上 */
  position: absolute !important;
  background: white !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}
</style>
