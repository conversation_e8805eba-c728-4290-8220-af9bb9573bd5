<template>
  <el-dialog
    v-model="dialogVisible"
    title="学习卡片"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :modal="true"
    modal-class="study-modal"
  >
    <div class="study-container" v-if="flashcard">
      <!-- 学习区域 -->
      <div class="study-area">
        <!-- 正面内容 -->
        <div v-if="!showBack" class="card-face front" :class="`difficulty-${flashcard.difficulty}`">
          <div class="face-header">
            <span class="face-title">正面</span>
            <div class="face-actions">
              <el-button
                v-if="hasBackContent"
                size="small"
                @click="showBack = true"
                type="primary"
              >
                查看答案
                <span class="shortcut-hint">(空格)</span>
              </el-button>
            </div>
          </div>
          <div class="face-content">
            <RichTextViewer :content="flashcard.frontContent" />
          </div>
          <div v-if="hasBackContent" class="keyboard-tips front-tips">
            <span>💡 按空格键查看答案</span>
          </div>
        </div>

        <!-- 背面内容 -->
        <div v-else-if="hasBackContent" class="card-face back" :class="`difficulty-${flashcard.difficulty}`">
          <div class="face-header">
            <span class="face-title">背面</span>
            <div class="face-actions">
              <el-button size="small" @click="speakContent('back')" :icon="Microphone" circle />
              <el-button size="small" @click="showBack = false" type="default">
                返回正面
              </el-button>
            </div>
          </div>
          <div class="face-content">
            <RichTextViewer :content="flashcard.backContent" />
          </div>
        </div>
      </div>

      <!-- 学习反馈 -->
      <div v-if="showBack || !hasBackContent" class="feedback-area">
        <div class="feedback-title">学习反馈</div>

        <div class="feedback-buttons">
          <el-button
            type="success"
            size="large"
            @click="submitResult(true)"
            :icon="Check"
          >
            答对了
            <span class="shortcut-hint">(1/Y)</span>
          </el-button>
          <el-button
            type="danger"
            size="large"
            @click="submitResult(false)"
            :icon="Close"
          >
            答错了
            <span class="shortcut-hint">(2/N)</span>
          </el-button>
          <el-button
            type="info"
            size="large"
            @click="skipCard"
            :icon="Right"
            plain
          >
            跳过
            <span class="shortcut-hint">(3/S/空格)</span>
          </el-button>
        </div>

        <div class="keyboard-tips">
          <span>💡 快捷键：1/Y=答对，2/N=答错，3/S/空格=跳过</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Microphone, Check, Close, Right } from '@element-plus/icons-vue'
import RichTextViewer from '@/components/RichTextViewer.vue'
import { flashcardAPI } from '@/api/flashcard'
import { quizExamRecordsApi, EXAM_CATEGORIES, createExamRecordData } from '@/api/quizExamRecords'
import { speakHtmlContent, isSpeechSynthesisSupported } from '@/utils/speech'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  flashcard: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'study-complete'])

// 响应式数据
const loading = ref(false)
const showBack = ref(false)
const startTime = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 判断是否有背面内容
const hasBackContent = computed(() => {
  return props.flashcard?.backContent &&
         props.flashcard.backContent.trim() !== '' &&
         props.flashcard.backContent !== '<p></p>' &&
         props.flashcard.backContent !== '<p><br></p>'
})

// 学习反馈数据
const feedback = reactive({
  isCorrect: null
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    resetStudy()
    startTime.value = Date.now()
    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeydown)
  } else {
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeydown)
  }
})

// 方法
const resetStudy = () => {
  showBack.value = false
  feedback.isCorrect = null
}

const submitResult = (isCorrect) => {
  // 立即显示反馈并跳转到下一张卡片
  ElMessage.success(isCorrect ? '答对了！' : '答错了，继续加油！')
  emit('study-complete')

  // 异步保存学习记录，不阻塞用户操作
  const data = {
    flashcardId: props.flashcard.id,
    projectId: props.flashcard.projectId,
    isCorrect: isCorrect
  }

  flashcardAPI.recordStudyResult(data)
    .then(() => {
      console.log('✅ 学习记录保存成功')
    })
    .catch((error) => {
      console.error('❌ 保存学习记录失败:', error)
      // 静默失败，不影响用户体验
      // 可以考虑在后台重试或者显示不显眼的提示
    })
}

const skipCard = () => {
  ElMessage.info('已跳过当前卡片')
  emit('study-complete')
}

const handleKeydown = (event) => {
  // 如果没有背面内容，直接在正面响应学习反馈
  if (!hasBackContent.value) {
    switch (event.key) {
      case '1':
      case 'y':
      case 'Y':
        event.preventDefault()
        submitResult(true)
        break
      case '2':
      case 'n':
      case 'N':
        event.preventDefault()
        submitResult(false)
        break
      case '3':
      case 's':
      case 'S':
      case ' ': // 空格键
        event.preventDefault()
        skipCard()
        break
    }
    return
  }

  // 有背面内容的情况下，在正面时空格键跳转到背面
  if (!showBack.value) {
    if (event.key === ' ') {
      event.preventDefault()
      showBack.value = true
    }
    return
  }

  // 在背面时响应学习反馈键盘事件
  switch (event.key) {
    case '1':
    case 'y':
    case 'Y':
      event.preventDefault()
      submitResult(true)
      break
    case '2':
    case 'n':
    case 'N':
      event.preventDefault()
      submitResult(false)
      break
    case '3':
    case 's':
    case 'S':
    case ' ': // 空格键
      event.preventDefault()
      skipCard()
      break
  }
}

const speakContent = async (side) => {
  try {
    if (!isSpeechSynthesisSupported()) {
      ElMessage.error('您的浏览器不支持语音合成功能')
      return
    }

    const content = side === 'front' ? props.flashcard.frontContent : props.flashcard.backContent

    if (!content) {
      ElMessage.warning('没有可朗读的内容')
      return
    }

    await speakHtmlContent(content)

  } catch (error) {
    console.error('朗读失败:', error)
    // 只有非中断错误才显示错误消息
    if (!error.message.includes('interrupted')) {
      ElMessage.error(error.message || '朗读失败')
    }
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

// 组件销毁时清理事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.study-container {
  padding: 0;
}

.study-area {
  margin-bottom: 20px;
}

.card-face {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  min-height: 200px;
}

/* 难度配色方案 */
.card-face.difficulty-1 {
  border-color: #4CAF50;
  box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.2);
}

.card-face.difficulty-2 {
  border-color: #2196F3;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.2);
}

.card-face.difficulty-3 {
  border-color: #9C27B0;
  box-shadow: 0 0 0 1px rgba(156, 39, 176, 0.2);
}

.card-face.difficulty-4 {
  border-color: #FF9800;
  box-shadow: 0 0 0 1px rgba(255, 152, 0, 0.2);
}

.card-face.difficulty-5 {
  border-color: #F44336;
  box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.2);
}

.face-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

/* 难度配色的头部背景 */
.card-face.difficulty-1 .face-header {
  background: rgba(76, 175, 80, 0.1);
  border-bottom-color: rgba(76, 175, 80, 0.3);
}

.card-face.difficulty-2 .face-header {
  background: rgba(33, 150, 243, 0.1);
  border-bottom-color: rgba(33, 150, 243, 0.3);
}

.card-face.difficulty-3 .face-header {
  background: rgba(156, 39, 176, 0.1);
  border-bottom-color: rgba(156, 39, 176, 0.3);
}

.card-face.difficulty-4 .face-header {
  background: rgba(255, 152, 0, 0.1);
  border-bottom-color: rgba(255, 152, 0, 0.3);
}

.card-face.difficulty-5 .face-header {
  background: rgba(244, 67, 54, 0.1);
  border-bottom-color: rgba(244, 67, 54, 0.3);
}

.face-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.face-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.face-content {
  padding: 20px;
  font-size: 16px;
  line-height: 1.6;
  min-height: 150px;
}

.feedback-area {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.feedback-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}



.dialog-footer {
  text-align: right;
}

/* 学习模式黑色背景 */
:deep(.study-modal) {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

.feedback-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.feedback-buttons .el-button {
  min-width: 100px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

/* 跳过按钮样式调整 */
.feedback-buttons .el-button--info.is-plain {
  border-color: #909399;
  color: #909399;
}

.feedback-buttons .el-button--info.is-plain:hover {
  background-color: #909399;
  border-color: #909399;
  color: #ffffff;
}

/* 快捷键提示样式 */
.shortcut-hint {
  font-size: 12px;
  opacity: 0.7;
  margin-left: 4px;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.8);
}

.keyboard-tips {
  text-align: center;
  margin-top: 12px;
  font-size: 12px;
  color: #6b7280;
  opacity: 0.8;
}

.front-tips {
  padding: 8px 16px;
  background: rgba(59, 130, 246, 0.1);
  border-top: 1px solid rgba(59, 130, 246, 0.2);
  margin-top: 0;
  color: #3b82f6;
  opacity: 1;
}
</style>
