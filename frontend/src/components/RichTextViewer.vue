<template>
  <div class="rich-text-viewer">
    <div
      v-if="content"
      class="content"
      v-html="sanitizedContent"
      @click="handleContentClick"
    ></div>
    <div v-else class="empty-content">
      暂无内容
    </div>

    <!-- 朗读状态提示 -->
    <div v-if="isSpeaking" class="speech-indicator">
      <el-icon class="speaking-icon"><Microphone /></el-icon>
      正在朗读...
      <el-button size="small" link @click="stopSpeech">停止</el-button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Microphone } from '@element-plus/icons-vue'
import DOMPurify from 'dompurify'
import {
  isSpeechSynthesisSupported,
  speakText as speak,
  stopSpeech as stop,
  isSpeaking as checkSpeaking,
  isTextReadable,
  getTextSpeechInfo,
  detectTextLanguage,
  resetSpeechState as globalResetSpeech
} from '@/utils/speech'

// Props
const props = defineProps({
  content: {
    type: String,
    default: ''
  }
})

// 响应式数据
const isSpeaking = ref(false)
const currentSpeakingElement = ref(null)
const lastClickTime = ref(0)

// 计算属性
const sanitizedContent = computed(() => {
  if (!props.content) {
    return ''
  }

  try {
    // 使用 DOMPurify 清理 HTML，防止 XSS 攻击
    return DOMPurify.sanitize(props.content, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'strong', 'em', 'u', 's', 'del', 'ins',
        'ul', 'ol', 'li',
        'blockquote', 'pre', 'code',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'a', 'img',
        'div', 'span',
        'sub', 'sup'
      ],
      ALLOWED_ATTR: [
        'href', 'src', 'alt', 'title', 'class', 'id',
        'target', 'rel', 'style', 'data-text'
      ],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
    })
  } catch (error) {
    console.error('富文本内容渲染失败:', error)
    return '<p class="error-content">内容渲染失败</p>'
  }
})

// 方法
const handleContentClick = (event) => {
  const target = event.target
  const now = Date.now()

  // 防抖：避免快速连续点击
  if (now - lastClickTime.value < 500) {
    return
  }
  lastClickTime.value = now

  // 检查是否点击了标记为朗读的文本
  if (target.classList.contains('tts-word')) {
    event.preventDefault()
    speakText(target)
    return
  }

  // 检查是否点击了其他文本元素（支持通用文本朗读）
  if (target.textContent && target.textContent.trim()) {
    const text = target.textContent.trim()

    // 只对英文文本进行朗读
    if (isEnglishText(text) && text.length > 0) {
      event.preventDefault()
      speakGeneralText(text, target)
    }
  }
}

const speakText = async (element) => {
  try {
    // 检查浏览器支持
    if (!isSpeechSynthesisSupported()) {
      ElMessage.error('您的浏览器不支持语音合成功能')
      return
    }

    // 停止当前朗读
    if (isSpeaking.value) {
      stopSpeech()
      return
    }

    const text = element.getAttribute('data-text') || element.textContent

    if (!text || !text.trim()) {
      ElMessage.warning('没有可朗读的文本')
      return
    }

    // 检查文本是否可以朗读
    if (!isTextReadable(text)) {
      ElMessage.warning('该文本暂不支持朗读功能')
      return
    }

    // 获取文本朗读信息
    const speechInfo = getTextSpeechInfo(text)
    console.log('📊 文本朗读信息:', speechInfo)

    // 显示朗读提示
    const languageMap = {
      'english': '英文',
      'chinese': '中文',
      'mixed': '中英文混合'
    }
    const languageText = languageMap[speechInfo.language] || '未知语言'
    ElMessage.info(`开始朗读${languageText}文本...`)

    // 设置朗读状态
    isSpeaking.value = true
    currentSpeakingElement.value = element

    // 添加朗读中的样式
    element.classList.add('speaking')

    // 开始朗读
    await speak(text.trim(), {
      rate: 0.9,
      pitch: 1.0,
      volume: 1.0
    })

  } catch (error) {
    console.error('朗读失败:', error)
    // 只有非中断错误才显示错误消息
    if (!error.message.includes('interrupted')) {
      ElMessage.error(`朗读失败: ${error.message}`)
    }
  } finally {
    // 重置状态
    resetSpeechState()
  }
}

const speakGeneralText = async (text, element) => {
  try {
    // 检查浏览器支持
    if (!isSpeechSynthesisSupported()) {
      ElMessage.error('您的浏览器不支持语音合成功能')
      return
    }

    // 停止当前朗读
    if (isSpeaking.value) {
      stopSpeech()
      return
    }

    // 检查文本是否可以朗读
    if (!isTextReadable(text)) {
      ElMessage.warning('该文本暂不支持朗读功能')
      return
    }

    // 获取文本朗读信息并显示提示
    const speechInfo = getTextSpeechInfo(text)
    const languageMap = {
      'english': '英文',
      'chinese': '中文',
      'mixed': '中英文混合'
    }
    const languageText = languageMap[speechInfo.language] || '未知语言'
    ElMessage.info(`开始朗读${languageText}文本...`)

    // 设置朗读状态
    isSpeaking.value = true
    currentSpeakingElement.value = element

    // 添加朗读中的样式
    element.classList.add('speaking-general')

    // 开始朗读
    await speak(text, {
      rate: 0.9,
      pitch: 1.0,
      volume: 1.0
    })

  } catch (error) {
    console.error('朗读失败:', error)
    // 只有非中断错误才显示错误消息
    if (!error.message.includes('interrupted')) {
      ElMessage.error(`朗读失败: ${error.message}`)
    }
  } finally {
    // 重置状态
    resetSpeechState()
  }
}

const stopSpeech = () => {
  stop()
  resetSpeechState()
}

const resetSpeechState = () => {
  isSpeaking.value = false

  if (currentSpeakingElement.value) {
    currentSpeakingElement.value.classList.remove('speaking')
    currentSpeakingElement.value.classList.remove('speaking-general')
    currentSpeakingElement.value = null
  }
}

// 生命周期
onMounted(() => {
  // 检查浏览器支持
  if (!isSpeechSynthesisSupported()) {
    console.warn('当前浏览器不支持Web Speech API')
  }
})

onUnmounted(() => {
  // 组件销毁时停止朗读并重置状态
  if (isSpeaking.value) {
    globalResetSpeech()
  }
})
</script>

<style scoped>
.rich-text-viewer {
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.content {
  line-height: 1.6;
  color: #333;
}

.empty-content {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.error-content {
  color: #f56c6c;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

/* 朗读状态指示器 */
.speech-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #409eff;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.speaking-icon {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
</style>

<style>
/* 富文本内容样式 */
.rich-text-viewer .content h1,
.rich-text-viewer .content h2,
.rich-text-viewer .content h3,
.rich-text-viewer .content h4,
.rich-text-viewer .content h5,
.rich-text-viewer .content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.4;
}

.rich-text-viewer .content h1 {
  font-size: 24px;
  color: #1f2937;
}

.rich-text-viewer .content h2 {
  font-size: 20px;
  color: #374151;
}

.rich-text-viewer .content h3 {
  font-size: 18px;
  color: #4b5563;
}

.rich-text-viewer .content h4,
.rich-text-viewer .content h5,
.rich-text-viewer .content h6 {
  font-size: 16px;
  color: #6b7280;
}

.rich-text-viewer .content p {
  margin: 8px 0;
  line-height: 1.6;
}

.rich-text-viewer .content strong {
  font-weight: 600;
}

.rich-text-viewer .content em {
  font-style: italic;
}

.rich-text-viewer .content u {
  text-decoration: underline;
}

.rich-text-viewer .content s {
  text-decoration: line-through;
}

.rich-text-viewer .content ul,
.rich-text-viewer .content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.rich-text-viewer .content li {
  margin: 4px 0;
  line-height: 1.6;
}

.rich-text-viewer .content blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  background: #f9fafb;
  border-left: 4px solid #d1d5db;
  color: #6b7280;
  font-style: italic;
}

.rich-text-viewer .content pre {
  margin: 16px 0;
  padding: 12px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
}

.rich-text-viewer .content code {
  padding: 2px 4px;
  background: #f3f4f6;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.rich-text-viewer .content pre code {
  padding: 0;
  background: transparent;
}

.rich-text-viewer .content table {
  width: 100%;
  margin: 16px 0;
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
}

.rich-text-viewer .content th,
.rich-text-viewer .content td {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  text-align: left;
}

.rich-text-viewer .content th {
  background: #f9fafb;
  font-weight: 600;
}

.rich-text-viewer .content a {
  color: #3b82f6;
  text-decoration: none;
}

.rich-text-viewer .content a:hover {
  text-decoration: underline;
}

.rich-text-viewer .content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

.rich-text-viewer .content sub {
  font-size: 0.8em;
  vertical-align: sub;
}

.rich-text-viewer .content sup {
  font-size: 0.8em;
  vertical-align: super;
}

/* 朗读文本样式 */
.rich-text-viewer .content .tts-word {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1px solid #2196f3;
  border-radius: 4px;
  padding: 2px 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
  margin: 0 2px;
}

.rich-text-viewer .content .tts-word:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.rich-text-viewer .content .tts-word:active {
  transform: translateY(0);
}

/* 朗读中的样式 */
.rich-text-viewer .content .tts-word.speaking {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
  border-color: #ff9800;
  animation: speaking-pulse 1s infinite;
}

@keyframes speaking-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 152, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
  }
}

/* 朗读文本的提示图标 */
.rich-text-viewer .content .tts-word::after {
  content: '🔊';
  font-size: 12px;
  margin-left: 4px;
  opacity: 0.7;
}

/* 通用文本朗读样式 */
.rich-text-viewer .content .speaking-general {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-radius: 4px;
  padding: 2px 4px;
  animation: speaking-pulse-general 1.5s infinite;
  position: relative;
}

@keyframes speaking-pulse-general {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* 为英文文本添加可点击提示 */
.rich-text-viewer .content p,
.rich-text-viewer .content span,
.rich-text-viewer .content div {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.rich-text-viewer .content p:hover,
.rich-text-viewer .content span:hover,
.rich-text-viewer .content div:hover {
  background-color: rgba(33, 150, 243, 0.05);
  border-radius: 2px;
}

/* 自定义字号样式 */
.rich-text-viewer .content .ql-size-10px { font-size: 10px; }
.rich-text-viewer .content .ql-size-12px { font-size: 12px; }
.rich-text-viewer .content .ql-size-14px { font-size: 14px; }
.rich-text-viewer .content .ql-size-16px { font-size: 16px; }
.rich-text-viewer .content .ql-size-18px { font-size: 18px; }
.rich-text-viewer .content .ql-size-20px { font-size: 20px; }
.rich-text-viewer .content .ql-size-24px { font-size: 24px; }
.rich-text-viewer .content .ql-size-28px { font-size: 28px; }
.rich-text-viewer .content .ql-size-32px { font-size: 32px; }
.rich-text-viewer .content .ql-size-36px { font-size: 36px; }
.rich-text-viewer .content .ql-size-48px { font-size: 48px; }
.rich-text-viewer .content .ql-size-60px { font-size: 60px; }
.rich-text-viewer .content .ql-size-72px { font-size: 72px; }
.rich-text-viewer .content .ql-size-96px { font-size: 96px; }
.rich-text-viewer .content .ql-size-120px { font-size: 120px; }
</style>
