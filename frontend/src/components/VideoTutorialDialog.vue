<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="90%"
    top="5vh"
    :close-on-click-modal="false"
    custom-class="standard-dialog xlarge-dialog"
    @close="handleClose"
  >
    <div class="standard-dialog-content">
      <div class="tutorial-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
        <div class="loading-text">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在生成视频教程，请稍候...
        </div>
      </div>

      <!-- 教程内容 -->
      <div v-else-if="tutorialData" class="tutorial-content">
        <!-- 教程信息 -->
        <div class="tutorial-info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>项目名称：</label>
                <span>{{ projectName || '未知项目' }}</span>
              </div>
              <div class="info-item">
                <label>知识点：</label>
                <span>{{ tutorialData.knowledgePointName }}</span>
              </div>
              <div class="info-item" v-if="tutorialData.knowledgePointDescription">
                <label>描述：</label>
                <span>{{ tutorialData.knowledgePointDescription }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>项目描述：</label>
                <span>{{ projectDescription || '暂无描述' }}</span>
              </div>
              <div class="info-item">
                <label>路径：</label>
                <span>{{ tutorialData.knowledgePointPath }}</span>
              </div>
              <div class="info-item">
                <label>生成时间：</label>
                <span>{{ formatDate(tutorialData.createdAt) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- Markdown内容渲染 -->
        <div class="markdown-container">
          <div class="markdown-toolbar">
            <el-button-group>
              <el-button 
                :type="viewMode === 'preview' ? 'primary' : 'default'"
                @click="viewMode = 'preview'"
                size="small"
              >
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button 
                :type="viewMode === 'source' ? 'primary' : 'default'"
                @click="viewMode = 'source'"
                size="small"
              >
                <el-icon><Document /></el-icon>
                源码
              </el-button>
            </el-button-group>
            
            <el-button @click="copyContent" size="small">
              <el-icon><DocumentCopy /></el-icon>
              复制内容
            </el-button>

            <el-button @click="toggleFullscreen" size="small">
              <el-icon><FullScreen /></el-icon>
              全屏显示
            </el-button>
          </div>

          <!-- 预览模式 -->
          <div
            v-if="viewMode === 'preview'"
            class="markdown-preview"
            v-html="renderedContent"
            @click="handleMarkdownClick"
          ></div>
          
          <!-- 源码模式 -->
          <div v-else class="markdown-source">
            <el-input
              v-model="tutorialData.tutorialContent"
              type="textarea"
              :rows="20"
              readonly
              resize="none"
            />
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <el-result
          icon="error"
          title="生成失败"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="retryGenerate">重试</el-button>
          </template>
        </el-result>
      </div>
    </div>
    </div>

    <template #footer>
      <div class="standard-dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 视频播放对话框 -->
    <el-dialog
      v-model="showVideoDialog"
      title="视频播放"
      width="80%"
      :close-on-click-modal="false"
      custom-class="video-player-dialog"
    >
      <div class="video-container">
        <video
          v-if="currentVideoUrl && isVideoFile(currentVideoUrl)"
          :src="currentVideoUrl"
          controls
          preload="metadata"
          style="width: 100%; max-height: 500px;"
        >
          您的浏览器不支持视频播放。
        </video>

        <iframe
          v-else-if="currentVideoUrl && isEmbeddableVideo(currentVideoUrl)"
          :src="getEmbedUrl(currentVideoUrl)"
          frameborder="0"
          allowfullscreen
          style="width: 100%; height: 500px;"
        ></iframe>

        <div v-else class="video-error">
          <el-result
            icon="warning"
            title="无法播放视频"
            sub-title="不支持的视频格式或链接"
          >
            <template #extra>
              <el-button type="primary" @click="openInNewTab(currentVideoUrl)">
                在新标签页打开
              </el-button>
            </template>
          </el-result>
        </div>
      </div>
    </el-dialog>

    <!-- 全屏显示对话框 -->
    <el-dialog
      v-model="showFullscreen"
      title="全屏查看"
      fullscreen
      :close-on-click-modal="false"
      custom-class="fullscreen-markdown-dialog"
    >
      <div class="fullscreen-content">
        <div
          class="fullscreen-markdown-preview"
          v-html="renderedContent"
          @click="handleMarkdownClick"
        ></div>
      </div>

      <template #footer>
        <div class="fullscreen-footer">
          <el-button @click="copyContent">
            <el-icon><DocumentCopy /></el-icon>
            复制内容
          </el-button>
          <el-button @click="showFullscreen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Loading, View, Document, DocumentCopy, FullScreen
} from '@element-plus/icons-vue'
import { videoTutorialAPI } from '@/api'
import { marked } from 'marked'
import markedKatex from 'marked-katex-extension'
import 'katex/dist/katex.min.css'
import { preprocessMathDelimiters } from '@/utils/svgToImage'
import { executeWithLoading } from '@/utils/apiUtils'
import MarkdownViewer from '@/components/MarkdownViewer.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  projectId: {
    type: Number,
    required: true
  },
  knowledgePoint: {
    type: Object,
    default: null
  },
  projectName: {
    type: String,
    default: ''
  },
  projectDescription: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'saved'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const tutorialData = ref(null)
const error = ref('')
const viewMode = ref('preview')
const showVideoDialog = ref(false)
const currentVideoUrl = ref('')
const showFullscreen = ref(false)

// 计算属性
const dialogTitle = computed(() => {
  if (props.knowledgePoint) {
    return `视频教程 - ${props.knowledgePoint.name}`
  }
  return '视频教程'
})

const renderedContent = computed(() => {
  if (!tutorialData.value?.tutorialContent) return ''
  try {
    // 预处理数学公式分隔符
    let content = preprocessMathDelimiters(tutorialData.value.tutorialContent)

    // 配置marked选项
    marked.setOptions({
      breaks: true,
      gfm: true
    })

    // 使用KaTeX扩展
    marked.use(markedKatex({
      throwOnError: false,
      strict: false,
      output: 'html',
      nonStandard: true
    }))

    // 渲染Markdown内容
    let html = marked.parse(content)

    // 后处理：为所有链接添加target="_blank"
    html = html.replace(/<a\s+href="([^"]*)"([^>]*)>/g, '<a href="$1"$2 target="_blank" rel="noopener noreferrer">')

    return html
  } catch (e) {
    console.error('Markdown渲染失败:', e)
    return '<p>内容渲染失败</p>'
  }
})

// 监听对话框显示
watch(visible, (newVisible) => {
  if (newVisible && props.knowledgePoint) {
    generateTutorial()
  } else if (!newVisible) {
    resetState()
  }
}, { immediate: false })

// 生成教程
const generateTutorial = async () => {
  if (!props.knowledgePoint || !props.projectId) return

  // 如果已经有现有教程，直接显示
  if (props.knowledgePoint.existingTutorial) {
    console.log('📋 显示已存在的视频教程:', props.knowledgePoint.existingTutorial)
    tutorialData.value = props.knowledgePoint.existingTutorial
    loading.value = false
    error.value = ''
    return
  }

  await executeWithLoading(async () => {
    console.log('🎬 开始生成视频教程:', props.knowledgePoint.name)

    const request = {
      knowledgePointName: props.knowledgePoint.name,
      knowledgePointPath: props.knowledgePoint.fullPath || props.knowledgePoint.name,
      knowledgePointDescription: props.knowledgePoint.description || ''
    }

    const response = await videoTutorialAPI.generateVideoTutorial(props.projectId, request)

    if (response.data.success) {
      tutorialData.value = response.data.data.data
      ElMessage.success('视频教程生成成功！')
    } else {
      error.value = response.data.data.errorMessage || '生成失败'
      throw new Error(response.data.data.errorMessage || '生成失败')
    }
  }, {
    loadingRef: loading,
    errorMessage: '视频教程生成失败',
    resetError: false,
    onError: (err) => {
      error.value = err.response?.data?.message || err.message || '生成失败'
    }
  })
}

// 重试生成
const retryGenerate = () => {
  generateTutorial()
}

// 复制内容
const copyContent = async () => {
  if (!tutorialData.value?.tutorialContent) return

  try {
    await navigator.clipboard.writeText(tutorialData.value.tutorialContent)
    ElMessage.success('内容已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  }
}

// 切换全屏显示
const toggleFullscreen = () => {
  showFullscreen.value = true
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 重置状态
const resetState = () => {
  loading.value = false
  tutorialData.value = null
  error.value = ''
  viewMode.value = 'preview'
  showFullscreen.value = false
}

// 处理Markdown中的链接点击
const handleMarkdownClick = (event) => {
  const target = event.target

  // 检查是否点击的是链接
  if (target.tagName === 'A') {
    event.preventDefault()

    // 获取href属性值
    const url = target.getAttribute('href') || target.href

    console.log('点击链接:', url) // 调试日志

    if (url && url !== '#' && !url.startsWith('javascript:')) {
      // 检查是否是视频链接
      if (isVideoLink(url)) {
        console.log('识别为视频链接，弹窗播放') // 调试日志
        // 弹窗播放视频
        currentVideoUrl.value = url
        showVideoDialog.value = true
      } else {
        console.log('识别为普通链接，新标签页打开') // 调试日志
        // 在新标签页打开普通链接
        openInNewTab(url)
      }
    }
  }
}

// 检查是否是视频链接
const isVideoLink = (url) => {
  const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv']
  const videoSites = ['youtube.com', 'youtu.be', 'vimeo.com', 'bilibili.com', 'qq.com']

  // 检查文件扩展名
  const hasVideoExtension = videoExtensions.some(ext =>
    url.toLowerCase().includes(ext)
  )

  // 检查视频网站
  const isVideoSite = videoSites.some(site =>
    url.toLowerCase().includes(site)
  )

  return hasVideoExtension || isVideoSite
}

// 检查是否是视频文件
const isVideoFile = (url) => {
  const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv']
  return videoExtensions.some(ext => url.toLowerCase().includes(ext))
}

// 检查是否是可嵌入的视频
const isEmbeddableVideo = (url) => {
  return url.includes('youtube.com') || url.includes('youtu.be') ||
         url.includes('vimeo.com') || url.includes('bilibili.com')
}

// 获取嵌入式播放URL
const getEmbedUrl = (url) => {
  if (url.includes('youtube.com/watch?v=')) {
    const videoId = url.split('v=')[1].split('&')[0]
    return `https://www.youtube.com/embed/${videoId}`
  } else if (url.includes('youtu.be/')) {
    const videoId = url.split('youtu.be/')[1].split('?')[0]
    return `https://www.youtube.com/embed/${videoId}`
  } else if (url.includes('vimeo.com/')) {
    const videoId = url.split('vimeo.com/')[1].split('?')[0]
    return `https://player.vimeo.com/video/${videoId}`
  } else if (url.includes('bilibili.com/video/')) {
    // B站视频需要特殊处理
    const bvid = url.match(/BV[a-zA-Z0-9]+/)?.[0]
    if (bvid) {
      return `https://player.bilibili.com/player.html?bvid=${bvid}&high_quality=1`
    }
  }

  return url
}

// 在新标签页打开链接
const openInNewTab = (url) => {
  window.open(url, '_blank', 'noopener,noreferrer')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  showVideoDialog.value = false
  currentVideoUrl.value = ''
  showFullscreen.value = false
}
</script>

<style scoped>
.video-tutorial-dialog {
  --el-dialog-content-font-size: 14px;
}

:deep(.video-tutorial-dialog) {
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:deep(.video-tutorial-dialog .el-dialog__body) {
  flex: 1;
  overflow: hidden;
  padding: 20px;
}

:deep(.video-tutorial-dialog .el-dialog__footer) {
  flex-shrink: 0;
  padding: 15px 20px;
  border-top: 1px solid var(--el-border-color-light);
}

.tutorial-container {
  padding: 20px 24px;
  min-height: 400px;
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
}

.loading-text {
  margin-top: 20px;
  color: var(--el-text-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tutorial-info {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 80px;
  flex-shrink: 0;
}

.info-item span {
  color: var(--el-text-color-primary);
  flex: 1;
}

.markdown-container {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.markdown-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
}

.markdown-preview {
  padding: 20px;
  max-height: 50vh;
  overflow-y: auto;
  line-height: 1.6;
}

.markdown-preview :deep(h1),
.markdown-preview :deep(h2),
.markdown-preview :deep(h3) {
  color: var(--el-text-color-primary);
  margin-top: 24px;
  margin-bottom: 16px;
}

.markdown-preview :deep(h1) {
  font-size: 24px;
  border-bottom: 1px solid var(--el-border-color);
  padding-bottom: 8px;
}

.markdown-preview :deep(h2) {
  font-size: 20px;
}

.markdown-preview :deep(h3) {
  font-size: 16px;
}

.markdown-preview :deep(p) {
  margin-bottom: 16px;
  color: var(--el-text-color-regular);
}

.markdown-preview :deep(ul),
.markdown-preview :deep(ol) {
  margin-bottom: 16px;
  padding-left: 24px;
}

.markdown-preview :deep(li) {
  margin-bottom: 4px;
  color: var(--el-text-color-regular);
}

.markdown-preview :deep(code) {
  background: var(--el-bg-color-page);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.markdown-preview :deep(pre) {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin-bottom: 16px;
}

.markdown-preview :deep(blockquote) {
  border-left: 4px solid var(--el-color-primary);
  padding-left: 16px;
  margin: 16px 0;
  color: var(--el-text-color-secondary);
}

.markdown-source {
  padding: 16px;
}

.error-container {
  padding: 40px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 视频播放对话框样式 */
:deep(.video-player-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
}

.video-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.video-error {
  width: 100%;
  text-align: center;
}

/* Markdown链接样式增强 */
.markdown-preview :deep(a) {
  color: var(--el-color-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s;
  cursor: pointer;
}

.markdown-preview :deep(a:hover) {
  border-bottom-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  padding: 2px 4px;
  border-radius: 3px;
}

/* 视频链接特殊样式 */
.markdown-preview :deep(a[href*="youtube.com"]),
.markdown-preview :deep(a[href*="youtu.be"]),
.markdown-preview :deep(a[href*="vimeo.com"]),
.markdown-preview :deep(a[href*="bilibili.com"]),
.markdown-preview :deep(a[href$=".mp4"]),
.markdown-preview :deep(a[href$=".webm"]),
.markdown-preview :deep(a[href$=".ogg"]) {
  position: relative;
  padding-left: 20px;
}

.markdown-preview :deep(a[href*="youtube.com"]:before),
.markdown-preview :deep(a[href*="youtu.be"]:before),
.markdown-preview :deep(a[href*="vimeo.com"]:before),
.markdown-preview :deep(a[href*="bilibili.com"]:before),
.markdown-preview :deep(a[href$=".mp4"]:before),
.markdown-preview :deep(a[href$=".webm"]:before),
.markdown-preview :deep(a[href$=".ogg"]:before) {
  content: "▶";
  position: absolute;
  left: 4px;
  color: var(--el-color-primary);
  font-size: 12px;
}

/* 全屏显示对话框样式 */
:deep(.fullscreen-markdown-dialog) {
  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .el-dialog__body {
    padding: 0;
    height: calc(100vh - 120px);
    overflow: hidden;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color-page);
  }
}

.fullscreen-content {
  height: 100%;
  overflow: hidden;
}

.fullscreen-markdown-preview {
  height: 100%;
  padding: 30px;
  overflow-y: auto;
  line-height: 1.8;
  font-size: 16px;
  background: var(--el-bg-color);
}

.fullscreen-markdown-preview :deep(h1),
.fullscreen-markdown-preview :deep(h2),
.fullscreen-markdown-preview :deep(h3) {
  color: var(--el-text-color-primary);
  margin-top: 32px;
  margin-bottom: 20px;
}

.fullscreen-markdown-preview :deep(h1) {
  font-size: 32px;
  border-bottom: 2px solid var(--el-border-color);
  padding-bottom: 12px;
}

.fullscreen-markdown-preview :deep(h2) {
  font-size: 26px;
}

.fullscreen-markdown-preview :deep(h3) {
  font-size: 20px;
}

.fullscreen-markdown-preview :deep(p) {
  margin-bottom: 20px;
  color: var(--el-text-color-regular);
}

.fullscreen-markdown-preview :deep(ul),
.fullscreen-markdown-preview :deep(ol) {
  margin-bottom: 20px;
  padding-left: 30px;
}

.fullscreen-markdown-preview :deep(li) {
  margin-bottom: 8px;
  color: var(--el-text-color-regular);
}

.fullscreen-markdown-preview :deep(code) {
  background: var(--el-bg-color-page);
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.fullscreen-markdown-preview :deep(pre) {
  background: var(--el-bg-color-page);
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin-bottom: 20px;
}

.fullscreen-markdown-preview :deep(blockquote) {
  border-left: 4px solid var(--el-color-primary);
  padding-left: 20px;
  margin: 20px 0;
  color: var(--el-text-color-secondary);
}

.fullscreen-markdown-preview :deep(a) {
  color: var(--el-color-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s;
  cursor: pointer;
}

.fullscreen-markdown-preview :deep(a:hover) {
  border-bottom-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  padding: 2px 4px;
  border-radius: 3px;
}

.fullscreen-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
