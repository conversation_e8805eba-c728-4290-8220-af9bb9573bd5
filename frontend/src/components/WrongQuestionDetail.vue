<template>
  <div class="wrong-question-detail">
    <!-- 导航按钮 -->
    <div class="navigation-bar" v-if="showNavigation">
      <el-button
        :disabled="currentIndex <= 0 || totalCount <= 1"
        @click="goToPrevious"
        icon="ArrowLeft"
        size="small"
      >
        上一题
      </el-button>
      <span class="question-counter">
        {{ currentIndex + 1 }} / {{ totalCount }}
      </span>
      <el-button
        :disabled="currentIndex >= totalCount - 1 || totalCount <= 1"
        @click="goToNext"
        icon="ArrowRight"
        size="small"
      >
        下一题
      </el-button>
    </div>

    <div class="detail-header">
      <h2>{{ question.knowledgePointName }}</h2>
      <p class="knowledge-path">{{ question.knowledgePointPath }}</p>
      <div class="meta-info">
        <span class="meta-item">
          <i class="el-icon-time"></i>
          录入时间：{{ formatDate(question.createdAt) }}
        </span>
        <span class="meta-item" v-if="question.updatedAt !== question.createdAt">
          <i class="el-icon-edit"></i>
          更新时间：{{ formatDate(question.updatedAt) }}
        </span>
      </div>

      <!-- 复习操作按钮 -->
      <div class="review-actions" v-if="reviewId">
        <el-button
          type="success"
          size="small"
          :loading="markingCompleted"
          @click="markReviewCompleted"
        >
          <i class="el-icon-check"></i>
          今日已读
        </el-button>
      </div>
    </div>

    <div class="detail-content">
      <!-- 错题内容 -->
      <div class="content-section">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          错题内容
        </h3>
        <div class="content-body">
          <markdown-viewer :content="question.contentMarkdown" />
        </div>
      </div>

      <!-- 可收起的内容区域 -->
      <div class="collapsible-sections" v-if="question.wrongReasonMarkdown || question.correctSolutionMarkdown">
        <el-collapse v-model="activeCollapse">
          <!-- 做错原因说明 -->
          <el-collapse-item
            name="wrongReason"
            v-if="question.wrongReasonMarkdown"
          >
            <template #title>
              <div class="collapse-title">
                <i class="el-icon-warning"></i>
                做错原因说明
              </div>
            </template>
            <div class="collapse-content">
              <markdown-viewer :content="question.wrongReasonMarkdown" />
            </div>
          </el-collapse-item>

          <!-- 正确解题说明 -->
          <el-collapse-item
            name="correctSolution"
            v-if="question.correctSolutionMarkdown"
          >
            <template #title>
              <div class="collapse-title">
                <i class="el-icon-check"></i>
                正确解题说明
              </div>
            </template>
            <div class="collapse-content">
              <markdown-viewer :content="question.correctSolutionMarkdown" />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import MarkdownViewer from '@/components/MarkdownViewer.vue'
import { reviewAPI } from '@/api'

// Props
const props = defineProps({
  question: {
    type: Object,
    required: true
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  totalCount: {
    type: Number,
    default: 1
  },
  showNavigation: {
    type: Boolean,
    default: false
  },
  reviewId: {
    type: Number,
    default: null
  }
})

// Emits
const emit = defineEmits(['previous', 'next', 'reviewCompleted'])

// 响应式数据
const activeCollapse = ref([]) // 默认都收起
const markingCompleted = ref(false)

// 方法
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const goToPrevious = () => {
  emit('previous')
}

const goToNext = () => {
  emit('next')
}

// 标记复习完成
const markReviewCompleted = async () => {
  if (!props.reviewId) {
    ElMessage.error('复习记录ID不存在')
    return
  }

  try {
    markingCompleted.value = true
    await reviewAPI.markReviewCompleted(props.question.projectId, props.reviewId)
    ElMessage.success('复习完成！')
    emit('reviewCompleted', props.reviewId)
  } catch (error) {
    console.error('标记复习完成失败:', error)
    ElMessage.error('标记复习完成失败')
  } finally {
    markingCompleted.value = false
  }
}
</script>

<style scoped>
.wrong-question-detail {
  padding: 20px;
}

/* 导航栏样式 */
.navigation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.question-counter {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.detail-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #ebeef5;
}

.review-actions {
  margin-top: 15px;
  text-align: right;
}

.detail-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.knowledge-path {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 16px;
}

.meta-info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 14px;
}

.meta-item i {
  margin-right: 4px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.content-section {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  margin: 0;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 8px;
  font-size: 20px;
}

.section-title:nth-of-type(1) i {
  color: #409eff;
}

.content-body {
  padding: 20px;
  background: #fff;
  min-height: 100px;
}

/* 可收起区域样式 */
.collapsible-sections {
  margin-top: 10px;
}

.collapse-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.collapse-title i {
  margin-right: 8px;
  font-size: 20px;
}

.el-collapse-item:nth-child(1) .collapse-title i {
  color: #e6a23c;
}

.el-collapse-item:nth-child(2) .collapse-title i {
  color: #67c23a;
}

.collapse-content {
  padding: 20px;
  background: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wrong-question-detail {
    padding: 16px;
  }

  .detail-header h2 {
    font-size: 20px;
  }

  .knowledge-path {
    font-size: 14px;
  }

  .meta-info {
    flex-direction: column;
    gap: 8px;
  }

  .section-title {
    font-size: 16px;
    padding: 12px 16px;
  }

  .content-body {
    padding: 16px;
  }

  .collapse-title {
    font-size: 16px;
  }

  .collapse-content {
    padding: 16px;
  }
}
</style>
