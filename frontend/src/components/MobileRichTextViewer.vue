<template>
  <div class="mobile-rich-text-viewer">
    <div
      v-if="content"
      class="content"
      v-html="sanitizedContent"
      @click="handleContentClick"
    ></div>
    <div v-else class="empty-content">
      暂无内容
    </div>

    <!-- 朗读状态提示 -->
    <div v-if="isSpeaking" class="speech-indicator">
      <span class="speaking-icon">🔊</span>
      正在朗读...
      <button @click="stopSpeech" class="stop-btn">停止</button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import DOMPurify from 'dompurify'
import {
  isSpeechSynthesisSupported,
  speakText as speak,
  stopSpeech as stop,
  isSpeaking as checkSpeaking,
  isEnglishText,
  isTextReadable
} from '@/utils/speech'

// Props
const props = defineProps({
  content: {
    type: String,
    default: ''
  }
})

// 响应式数据
const isSpeaking = ref(false)
const currentSpeakingElement = ref(null)
const lastClickTime = ref(0)

// 计算属性
const sanitizedContent = computed(() => {
  if (!props.content) {
    return ''
  }

  try {
    // 使用 DOMPurify 清理 HTML，防止 XSS 攻击
    return DOMPurify.sanitize(props.content, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'strong', 'em', 'u', 's', 'del', 'ins',
        'ul', 'ol', 'li',
        'blockquote', 'pre', 'code',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'a', 'img',
        'div', 'span',
        'sub', 'sup'
      ],
      ALLOWED_ATTR: [
        'href', 'src', 'alt', 'title', 'class', 'id',
        'target', 'rel', 'style', 'data-text'
      ],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
    })
  } catch (error) {
    console.error('富文本内容渲染失败:', error)
    return '<p class="error-content">内容渲染失败</p>'
  }
})

// 方法
const handleContentClick = (event) => {
  const target = event.target

  // 检查是否点击了图片
  if (target.tagName === 'IMG') {
    event.preventDefault()
    showImageFullscreen(target.src)
    return
  }

  // 检查是否点击了标记为朗读的文本
  if (target.classList.contains('tts-word')) {
    event.preventDefault()
    speakText(target)
    return
  }

  // 检查是否点击了其他文本元素（支持通用文本朗读）
  if (target.textContent && target.textContent.trim()) {
    const text = target.textContent.trim()

    // 支持中英文混合朗读
    if (isTextReadable(text) && text.length > 0) {
      event.preventDefault()
      speakGeneralText(text, target)
    }
  }
}

const showImageFullscreen = (src) => {
  // 创建全屏图片查看器
  const overlay = document.createElement('div')
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    cursor: pointer;
  `

  const fullImg = document.createElement('img')
  fullImg.src = src
  fullImg.style.cssText = `
    max-width: 95%;
    max-height: 95%;
    object-fit: contain;
    border-radius: 8px;
  `

  overlay.appendChild(fullImg)
  document.body.appendChild(overlay)

  // 点击关闭
  overlay.addEventListener('click', () => {
    document.body.removeChild(overlay)
  })
}

const speakText = async (element) => {
  if (!isSpeechSynthesisSupported()) {
    alert('您的浏览器不支持语音功能')
    return
  }

  // 防止重复点击
  const now = Date.now()
  if (now - lastClickTime.value < 500) {
    return
  }
  lastClickTime.value = now

  try {
    // 停止当前朗读
    if (isSpeaking.value) {
      stopSpeech()
      return
    }

    // 获取要朗读的文本
    const text = element.getAttribute('data-text') || element.textContent.trim()
    if (!text) return

    // 设置朗读状态
    isSpeaking.value = true
    currentSpeakingElement.value = element
    element.classList.add('speaking')

    // 开始朗读
    await speak(text)

    // 朗读完成后重置状态
    resetSpeechState()
  } catch (error) {
    console.error('朗读失败:', error)
    resetSpeechState()
  }
}

const speakGeneralText = async (text, element) => {
  if (!isSpeechSynthesisSupported()) {
    return
  }

  // 防止重复点击
  const now = Date.now()
  if (now - lastClickTime.value < 500) {
    return
  }
  lastClickTime.value = now

  try {
    // 停止当前朗读
    if (isSpeaking.value) {
      stopSpeech()
      return
    }

    // 设置朗读状态
    isSpeaking.value = true
    currentSpeakingElement.value = element
    element.classList.add('speaking-general')

    // 开始朗读
    await speak(text)

    // 朗读完成后重置状态
    resetSpeechState()
  } catch (error) {
    console.error('朗读失败:', error)
    resetSpeechState()
  }
}

const stopSpeech = () => {
  try {
    stop()
    resetSpeechState()
  } catch (error) {
    console.error('停止朗读失败:', error)
    resetSpeechState()
  }
}

const resetSpeechState = () => {
  isSpeaking.value = false

  if (currentSpeakingElement.value) {
    currentSpeakingElement.value.classList.remove('speaking')
    currentSpeakingElement.value.classList.remove('speaking-general')
    currentSpeakingElement.value = null
  }
}

// 生命周期
onMounted(() => {
  // 检查浏览器支持
  if (!isSpeechSynthesisSupported()) {
    console.warn('当前浏览器不支持Web Speech API')
  }
})

onUnmounted(() => {
  // 组件销毁时停止朗读
  if (isSpeaking.value) {
    stopSpeech()
  }
})
</script>

<style scoped>
.mobile-rich-text-viewer {
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.content {
  line-height: 1.6;
  color: #333;
  font-size: 16px; /* 移动端适合的字体大小 */
}

.empty-content {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
  font-size: 14px;
}

.error-content {
  color: #ff4d4f;
  text-align: center;
  padding: 20px;
  font-size: 14px;
}

/* 朗读状态提示 */
.speech-indicator {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
}

.speaking-icon {
  font-size: 16px;
}

.stop-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer;
}

.stop-btn:active {
  background: rgba(255, 255, 255, 0.3);
}
</style>

<!-- 全局样式，专门为移动端优化 -->
<style>
.mobile-rich-text-viewer .content h1,
.mobile-rich-text-viewer .content h2,
.mobile-rich-text-viewer .content h3,
.mobile-rich-text-viewer .content h4,
.mobile-rich-text-viewer .content h5,
.mobile-rich-text-viewer .content h6 {
  margin: 1.2em 0 0.6em 0;
  font-weight: 600;
  line-height: 1.3;
}

.mobile-rich-text-viewer .content h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.3em;
}

.mobile-rich-text-viewer .content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.mobile-rich-text-viewer .content h3 {
  font-size: 1.3em;
}

.mobile-rich-text-viewer .content h4 {
  font-size: 1.1em;
}

.mobile-rich-text-viewer .content h5 {
  font-size: 1em;
}

.mobile-rich-text-viewer .content h6 {
  font-size: 0.9em;
  color: #666;
}

.mobile-rich-text-viewer .content p {
  margin: 0.8em 0;
  line-height: 1.6;
}

.mobile-rich-text-viewer .content ul,
.mobile-rich-text-viewer .content ol {
  margin: 0.8em 0;
  padding-left: 1.5em;
}

.mobile-rich-text-viewer .content li {
  margin: 0.3em 0;
  line-height: 1.5;
}

.mobile-rich-text-viewer .content blockquote {
  margin: 1em 0;
  padding: 0.8em 1em;
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  border-radius: 0 4px 4px 0;
  font-style: italic;
}

.mobile-rich-text-viewer .content pre {
  margin: 1em 0;
  padding: 1em;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.4;
}

.mobile-rich-text-viewer .content code {
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.mobile-rich-text-viewer .content pre code {
  padding: 0;
  background: transparent;
  border-radius: 0;
}

.mobile-rich-text-viewer .content table {
  margin: 1em 0;
  border-collapse: collapse;
  width: 100%;
  font-size: 14px;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.mobile-rich-text-viewer .content th,
.mobile-rich-text-viewer .content td {
  padding: 0.5em 0.8em;
  border: 1px solid #ddd;
  text-align: left;
}

.mobile-rich-text-viewer .content th {
  background: #f6f8fa;
  font-weight: 600;
}

/* 移动端图片优化 */
.mobile-rich-text-viewer .content img {
  max-width: 100% !important;
  max-height: 300px !important; /* 限制最大高度，确保在手机上能看完整 */
  width: auto !important;
  height: auto !important;
  border-radius: 8px;
  margin: 8px 0;
  display: block;
  object-fit: contain !important;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-rich-text-viewer .content img:active {
  transform: scale(0.98);
}

.mobile-rich-text-viewer .content a {
  color: #007bff;
  text-decoration: none;
  word-break: break-all; /* 长链接换行 */
}

.mobile-rich-text-viewer .content a:hover {
  text-decoration: underline;
}

.mobile-rich-text-viewer .content sub {
  font-size: 0.8em;
  vertical-align: sub;
}

.mobile-rich-text-viewer .content sup {
  font-size: 0.8em;
  vertical-align: super;
}

/* 朗读文本样式 - 移动端优化 */
.mobile-rich-text-viewer .content .tts-word {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1px solid #2196f3;
  border-radius: 6px;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
  margin: 0 2px;
  font-size: 16px; /* 移动端适合的字体大小 */
}

.mobile-rich-text-viewer .content .tts-word:active {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  transform: scale(0.95);
}

.mobile-rich-text-viewer .content .speaking {
  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  border-color: #4caf50;
  color: white;
  animation: pulse 1.5s infinite;
}

.mobile-rich-text-viewer .content .speaking-general {
  background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);
  border-color: #ff9800;
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* 移动端媒体查询优化 */
@media (max-width: 480px) {
  .mobile-rich-text-viewer .content {
    font-size: 15px;
  }
  
  .mobile-rich-text-viewer .content h1 {
    font-size: 1.6em;
  }
  
  .mobile-rich-text-viewer .content h2 {
    font-size: 1.4em;
  }
  
  .mobile-rich-text-viewer .content h3 {
    font-size: 1.2em;
  }
  
  .mobile-rich-text-viewer .content img {
    max-height: 250px !important;
    margin: 6px 0;
    border-radius: 6px;
  }
  
  .mobile-rich-text-viewer .content table {
    font-size: 13px;
  }
  
  .mobile-rich-text-viewer .content th,
  .mobile-rich-text-viewer .content td {
    padding: 0.4em 0.6em;
  }
  
  .mobile-rich-text-viewer .content pre {
    font-size: 13px;
    padding: 0.8em;
  }
  
  .mobile-rich-text-viewer .content .tts-word {
    font-size: 15px;
    padding: 3px 6px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .mobile-rich-text-viewer .content {
    font-size: 14px;
  }
  
  .mobile-rich-text-viewer .content img {
    max-height: 200px !important;
    margin: 4px 0;
    border-radius: 4px;
  }
  
  .mobile-rich-text-viewer .content table {
    font-size: 12px;
  }
  
  .mobile-rich-text-viewer .content pre {
    font-size: 12px;
    padding: 0.6em;
  }
  
  .mobile-rich-text-viewer .content .tts-word {
    font-size: 14px;
    padding: 2px 4px;
  }
}

/* 自定义字号样式 - 移动端优化 */
.mobile-rich-text-viewer .content .ql-size-10px { font-size: 10px; }
.mobile-rich-text-viewer .content .ql-size-12px { font-size: 12px; }
.mobile-rich-text-viewer .content .ql-size-14px { font-size: 14px; }
.mobile-rich-text-viewer .content .ql-size-16px { font-size: 16px; }
.mobile-rich-text-viewer .content .ql-size-18px { font-size: 18px; }
.mobile-rich-text-viewer .content .ql-size-20px { font-size: 20px; }
.mobile-rich-text-viewer .content .ql-size-24px { font-size: 24px; }
.mobile-rich-text-viewer .content .ql-size-28px { font-size: 28px; }
.mobile-rich-text-viewer .content .ql-size-32px { font-size: 32px; }
.mobile-rich-text-viewer .content .ql-size-36px { font-size: 36px; }
.mobile-rich-text-viewer .content .ql-size-48px { font-size: 48px; }
.mobile-rich-text-viewer .content .ql-size-60px { font-size: 60px; }
.mobile-rich-text-viewer .content .ql-size-72px { font-size: 72px; }
.mobile-rich-text-viewer .content .ql-size-96px { font-size: 96px; }
.mobile-rich-text-viewer .content .ql-size-120px { font-size: 120px; }
</style>
