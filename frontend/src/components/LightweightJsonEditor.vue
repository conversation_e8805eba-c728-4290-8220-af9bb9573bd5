<template>
  <div class="lightweight-json-editor">
    <!-- 编辑器工具栏 -->
    <div class="editor-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <span class="status-indicator" :class="{ 'valid': isValid, 'invalid': !isValid }">
          <el-icon><CircleCheck v-if="isValid" /><CircleClose v-else /></el-icon>
          {{ isValid ? 'JSON 格式正确' : 'JSON 格式错误' }}
        </span>
      </div>
    </div>

    <!-- JSON 编辑器 -->
    <div class="editor-container" :class="{ 'dark-theme': isDark }">
      <div class="line-numbers" v-if="showLineNumbers">
        <div 
          v-for="n in lineCount" 
          :key="n" 
          class="line-number"
          :class="{ 'error-line': errorLine === n }"
        >
          {{ n }}
        </div>
      </div>
      
      <textarea
        ref="textareaRef"
        v-model="internalValue"
        class="json-textarea"
        :class="{
          'with-line-numbers': showLineNumbers,
          'error': !isValid
        }"
        :placeholder="placeholder"
        :readonly="readonly"
        @input="handleInput"
        @scroll="handleScroll"
        @keydown="handleKeydown"
        spellcheck="false"
      />
      
      <!-- 错误提示 -->
      <div v-if="!isValid && errorMessage" class="error-message">
        <el-icon><Warning /></el-icon>
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  CircleCheck, 
  CircleClose, 
  DocumentCopy, 
  Refresh, 
  Warning 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: [String, Number],
    default: 400
  },
  theme: {
    type: String,
    default: 'light', // 'light' | 'dark'
    validator: (value) => ['light', 'dark'].includes(value)
  },
  readonly: {
    type: Boolean,
    default: false
  },
  showToolbar: {
    type: Boolean,
    default: true
  },
  showLineNumbers: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: String,
    default: '请输入 JSON 内容...'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// Refs
const textareaRef = ref(null)

// Reactive data
const internalValue = ref(props.modelValue)
const isValid = ref(true)
const errorMessage = ref('')
const errorLine = ref(null)
const isDark = ref(props.theme === 'dark')

// Computed
const lineCount = computed(() => {
  return internalValue.value.split('\n').length
})

const editorHeight = computed(() => {
  return typeof props.height === 'number' ? `${props.height}px` : props.height
})

// Watch props changes
watch(() => props.modelValue, (newValue) => {
  console.log('📝 LightweightJsonEditor: modelValue changed', {
    newValue: newValue && newValue.substring(0, 100) + (newValue && newValue.length > 100 ? '...' : ''),
    currentValue: internalValue.value && internalValue.value.substring(0, 100) + (internalValue.value && internalValue.value.length > 100 ? '...' : ''),
    different: newValue !== internalValue.value
  })
  if (newValue !== internalValue.value) {
    internalValue.value = newValue
    validateJson()
    console.log('✅ LightweightJsonEditor: internalValue updated')
  }
}, { immediate: true })

watch(() => props.theme, (newTheme) => {
  isDark.value = newTheme === 'dark'
})

// Methods
const validateJson = () => {
  if (!internalValue.value.trim()) {
    isValid.value = true
    errorMessage.value = ''
    errorLine.value = null
    emit('validate', { valid: true })
    return true
  }

  try {
    JSON.parse(internalValue.value)
    isValid.value = true
    errorMessage.value = ''
    errorLine.value = null
    emit('validate', { valid: true })
    return true
  } catch (error) {
    isValid.value = false
    errorMessage.value = error.message
    
    // 尝试解析错误行号
    const match = error.message.match(/line (\d+)/i)
    if (match) {
      errorLine.value = parseInt(match[1])
    } else {
      errorLine.value = null
    }
    
    emit('validate', { valid: false, error: error.message })
    return false
  }
}

const formatJson = () => {
  if (!isValid.value) {
    ElMessage.warning('JSON 格式错误，无法格式化')
    return
  }

  try {
    const parsed = JSON.parse(internalValue.value)
    internalValue.value = JSON.stringify(parsed, null, 2)
    emit('update:modelValue', internalValue.value)
    ElMessage.success('JSON 格式化完成')
  } catch (error) {
    ElMessage.error('格式化失败: ' + error.message)
  }
}

const toggleTheme = () => {
  isDark.value = !isDark.value
  ElMessage.success(`已切换到${isDark.value ? '深色' : '浅色'}主题`)
}

const handleInput = () => {
  emit('update:modelValue', internalValue.value)
  emit('change', internalValue.value)
  
  // 延迟验证，避免频繁验证
  clearTimeout(handleInput.timer)
  handleInput.timer = setTimeout(() => {
    validateJson()
  }, 500)
}

const handleScroll = () => {
  // 同步行号滚动
  const lineNumbers = document.querySelector('.line-numbers')
  if (lineNumbers && textareaRef.value) {
    lineNumbers.scrollTop = textareaRef.value.scrollTop
  }
}

const handleKeydown = (event) => {
  // Tab 键插入空格
  if (event.key === 'Tab') {
    event.preventDefault()
    const start = event.target.selectionStart
    const end = event.target.selectionEnd
    const spaces = '  ' // 2个空格
    
    internalValue.value = 
      internalValue.value.substring(0, start) + 
      spaces + 
      internalValue.value.substring(end)
    
    nextTick(() => {
      event.target.selectionStart = event.target.selectionEnd = start + spaces.length
    })
  }
  
  // Ctrl+S 保存（可以由父组件监听）
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    // 触发保存事件，由父组件处理
    emit('save')
  }
}

// 公开方法
const focus = () => {
  textareaRef.value?.focus()
}

const getSelection = () => {
  if (!textareaRef.value) return null
  return {
    start: textareaRef.value.selectionStart,
    end: textareaRef.value.selectionEnd
  }
}

const setSelection = (start, end) => {
  if (!textareaRef.value) return
  textareaRef.value.selectionStart = start
  textareaRef.value.selectionEnd = end
}

// 暴露方法给父组件
defineExpose({
  validateJson,
  formatJson,
  focus,
  getSelection,
  setSelection
})

// 初始化
onMounted(() => {
  validateJson()
})
</script>

<style scoped>
.lightweight-json-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-size: 12px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.status-indicator.valid {
  color: #67c23a;
}

.status-indicator.invalid {
  color: #f56c6c;
}

.editor-container {
  position: relative;
  display: flex;
  height: v-bind(editorHeight);
  background-color: #ffffff;
}

.editor-container.dark-theme {
  background-color: #1e1e1e;
}

.line-numbers {
  width: 50px;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 8px 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #6c757d;
  overflow: hidden;
  user-select: none;
}

.dark-theme .line-numbers {
  background-color: #252526;
  border-right-color: #3e3e42;
  color: #858585;
}

.line-number {
  text-align: right;
  padding-right: 8px;
  min-height: 18px;
}

.line-number.error-line {
  background-color: #fef0f0;
  color: #f56c6c;
}

.dark-theme .line-number.error-line {
  background-color: #2d1b1b;
  color: #f56c6c;
}

.json-textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  background-color: transparent;
  color: #2c3e50;
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
}

.json-textarea.with-line-numbers {
  padding-left: 8px;
}

.json-textarea.error {
  background-color: #fef0f0;
}

.dark-theme .json-textarea {
  color: #d4d4d4;
}

.dark-theme .json-textarea.error {
  background-color: #2d1b1b;
}

.error-message {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  color: #f56c6c;
  display: flex;
  align-items: center;
  gap: 4px;
}

.dark-theme .error-message {
  background-color: #2d1b1b;
  border-color: #5c2626;
}

/* 滚动条样式 */
.json-textarea::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-textarea::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.json-textarea::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.json-textarea::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark-theme .json-textarea::-webkit-scrollbar-track {
  background: #2d2d30;
}

.dark-theme .json-textarea::-webkit-scrollbar-thumb {
  background: #424245;
}

.dark-theme .json-textarea::-webkit-scrollbar-thumb:hover {
  background: #4f4f55;
}
</style>
