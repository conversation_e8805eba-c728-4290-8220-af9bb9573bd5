<template>
  <div class="review-config-form">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span class="title">
            <el-icon><Setting /></el-icon>
            复习周期配置
          </span>
          <div class="header-actions">
            <el-button 
              type="info" 
              size="small" 
              @click="resetToDefault"
              :loading="resetting"
            >
              <el-icon><RefreshLeft /></el-icon>
              恢复默认
            </el-button>
          </div>
        </div>
      </template>

      <div class="config-content">
        <div class="description">
          <el-alert
            title="配置说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>根据科学记忆法（艾宾浩斯遗忘曲线），设置错题的复习间隔天数。</p>
              <p>建议配置：第1天、第3天、第7天、第15天、第30天、第60天后复习。</p>
            </template>
          </el-alert>
        </div>

        <el-form 
          ref="configForm" 
          :model="formData" 
          :rules="rules" 
          label-width="120px"
          class="config-form"
        >
          <el-form-item label="复习间隔" prop="reviewIntervals">
            <div class="intervals-container">
              <div class="intervals-list">
                <div 
                  v-for="(interval, index) in formData.reviewIntervals" 
                  :key="index"
                  class="interval-item"
                >
                  <span class="interval-label">第{{ index + 1 }}次复习：</span>
                  <el-input-number
                    v-model="formData.reviewIntervals[index]"
                    :min="1"
                    :max="365"
                    size="small"
                    class="interval-input"
                    @change="validateIntervals"
                  />
                  <span class="interval-unit">天后</span>
                  <el-button
                    v-if="formData.reviewIntervals.length > 1"
                    type="danger"
                    size="small"
                    text
                    @click="removeInterval(index)"
                    class="remove-btn"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <div class="intervals-actions">
                <el-button
                  v-if="formData.reviewIntervals.length < 20"
                  type="primary"
                  size="small"
                  @click="addInterval"
                  class="add-btn"
                >
                  <el-icon><Plus /></el-icon>
                  添加复习间隔
                </el-button>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="预览效果">
            <div class="preview-container">
              <div class="preview-timeline">
                <div 
                  v-for="(interval, index) in sortedIntervals" 
                  :key="index"
                  class="timeline-item"
                >
                  <div class="timeline-dot"></div>
                  <div class="timeline-content">
                    <div class="timeline-title">第{{ index + 1 }}次复习</div>
                    <div class="timeline-desc">创建后第{{ interval }}天</div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>

        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSave"
            :loading="saving"
          >
            保存配置
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, RefreshLeft, Plus, Delete } from '@element-plus/icons-vue'
import { reviewAPI } from '@/api'
import { executeWithLoading } from '@/utils/apiUtils'
import { createFormState, safeSubmit } from '@/utils/formUtils'

// Props
const props = defineProps({
  projectId: {
    type: Number,
    required: true
  }
})

// Emits
const emit = defineEmits(['saved', 'cancel'])

// 响应式数据
const configForm = ref(null)
const saving = ref(false)
const resetting = ref(false)
const formData = ref({
  reviewIntervals: [1, 3, 7, 15, 30, 60] // 默认配置
})

// 表单验证规则
const rules = {
  reviewIntervals: [
    { 
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('至少需要设置一个复习间隔'))
          return
        }
        
        // 检查是否有重复值
        const uniqueValues = [...new Set(value)]
        if (uniqueValues.length !== value.length) {
          callback(new Error('复习间隔不能重复'))
          return
        }
        
        // 检查是否有无效值
        for (let i = 0; i < value.length; i++) {
          if (!value[i] || value[i] <= 0) {
            callback(new Error('复习间隔必须大于0'))
            return
          }
        }
        
        callback()
      }, 
      trigger: 'change' 
    }
  ]
}

// 计算属性
const sortedIntervals = computed(() => {
  return [...formData.value.reviewIntervals].sort((a, b) => a - b)
})

// 方法
const loadConfiguration = async () => {
  await executeWithLoading(async () => {
    const response = await reviewAPI.getReviewConfiguration(props.projectId)
    if (response.data.data && response.data.data.reviewIntervals) {
      formData.value.reviewIntervals = [...response.data.data.reviewIntervals]
    }
  }, {
    errorMessage: '加载复习配置失败',
    showMessage: false
  })
}

const addInterval = () => {
  const lastInterval = Math.max(...formData.value.reviewIntervals)
  const newInterval = lastInterval + 7 // 默认增加7天
  formData.value.reviewIntervals.push(newInterval)
  validateIntervals()
}

const removeInterval = (index) => {
  formData.value.reviewIntervals.splice(index, 1)
  validateIntervals()
}

const validateIntervals = async () => {
  await nextTick()
  if (configForm.value) {
    configForm.value.validateField('reviewIntervals')
  }
}

const resetToDefault = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复为默认配置吗？这将覆盖当前的自定义配置。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await executeWithLoading(async () => {
      await reviewAPI.resetReviewConfiguration(props.projectId)
      // 重新加载配置
      await loadConfiguration()
    }, {
      loadingRef: resetting,
      errorMessage: '重置配置失败',
      onSuccess: () => {
        ElMessage.success('已恢复为默认配置')
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置配置失败:', error)
    }
  }
}

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await configForm.value.validate()
    if (!valid) return

    const configData = {
      projectId: props.projectId,
      reviewIntervals: sortedIntervals.value
    }

    await executeWithLoading(async () => {
      console.log('🚀 保存复习配置数据:', configData)
      await reviewAPI.updateReviewConfiguration(props.projectId, configData)
    }, {
      loadingRef: saving,
      errorMessage: '保存复习配置失败',
      onSuccess: () => {
        ElMessage.success('复习配置保存成功')
        emit('saved', configData)
      }
    })
  } catch (error) {
    console.error('保存复习配置失败:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 生命周期
onMounted(() => {
  loadConfiguration()
})
</script>

<style scoped>
.review-config-form {
  max-width: 800px;
  margin: 0 auto;
}

.config-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-content {
  padding: 0;
}

.description {
  margin-bottom: 24px;
}

.config-form {
  margin-bottom: 24px;
}

.intervals-container {
  width: 100%;
}

.intervals-list {
  margin-bottom: 16px;
}

.interval-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.interval-label {
  min-width: 100px;
  font-weight: 500;
  color: #606266;
}

.interval-input {
  width: 120px;
}

.interval-unit {
  color: #909399;
  font-size: 14px;
}

.remove-btn {
  margin-left: auto;
}

.intervals-actions {
  text-align: center;
}

.add-btn {
  border-style: dashed;
}

.preview-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.preview-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 8px;
  top: 24px;
  width: 2px;
  height: 20px;
  background: #e4e7ed;
}

.timeline-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #409eff;
  margin-right: 16px;
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.timeline-desc {
  font-size: 14px;
  color: #909399;
}

.form-actions {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin-left: 12px;
}

@media (max-width: 768px) {
  .interval-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .interval-label {
    min-width: auto;
  }
  
  .remove-btn {
    margin-left: 0;
    align-self: flex-end;
  }
}
</style>
