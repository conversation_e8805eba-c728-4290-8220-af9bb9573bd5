/**
 * 路由工具类
 * 提供路由参数验证、导航等常用功能
 */
import { isValidId, validateAndConvertId } from './validation'
import { computed } from 'vue'

/**
 * 创建安全的路由参数计算属性
 * @param {Object} route - Vue路由对象
 * @param {string} paramName - 参数名称
 * @param {Object} options - 配置选项
 * @returns {ComputedRef} 计算属性
 */
export function createSafeRouteParam(route, paramName, options = {}) {
  const {
    required = true,
    logError = true,
    fallback = null
  } = options

  return computed(() => {
    const value = route.params[paramName]
    
    if (!required && (!value || value === '')) {
      return fallback
    }
    
    if (paramName.toLowerCase().includes('id')) {
      const validId = validateAndConvertId(value, paramName)
      if (validId === null) {
        if (logError) {
          console.error(`❌ 无效的${paramName}:`, value)
        }
        return fallback
      }
      return validId
    }
    
    if (!value || value === '') {
      if (logError && required) {
        console.error(`❌ 缺少必需的路由参数${paramName}`)
      }
      return fallback
    }
    
    return value
  })
}

/**
 * 批量创建路由参数计算属性
 * @param {Object} route - Vue路由对象
 * @param {Array|Object} paramConfig - 参数配置
 * @returns {Object} 参数计算属性对象
 */
export function createRouteParams(route, paramConfig) {
  const params = {}
  
  if (Array.isArray(paramConfig)) {
    // 数组格式：['projectId', 'examId']
    paramConfig.forEach(paramName => {
      params[paramName] = createSafeRouteParam(route, paramName)
    })
  } else {
    // 对象格式：{ projectId: { required: true }, examId: { required: false } }
    Object.entries(paramConfig).forEach(([paramName, options]) => {
      params[paramName] = createSafeRouteParam(route, paramName, options)
    })
  }
  
  return params
}

/**
 * 验证所有必需的路由参数
 * @param {Object} params - 路由参数对象
 * @param {Array} requiredParams - 必需参数列表
 * @returns {Object} 验证结果
 */
export function validateRouteParams(params, requiredParams = []) {
  const errors = []
  const validParams = {}
  
  for (const paramName of requiredParams) {
    const paramValue = params[paramName] && params[paramName].value || params[paramName]
    
    if (paramValue === null || paramValue === undefined) {
      errors.push(`缺少必需的参数: ${paramName}`)
    } else {
      validParams[paramName] = paramValue
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
    validParams
  }
}

/**
 * 项目相关路由导航工具
 */
export class ProjectNavigator {
  constructor(router, projectId) {
    this.router = router
    this.projectId = projectId
  }

  /**
   * 返回项目详情页
   */
  goToDetail() {
    this.router.push(`/projects/${this.projectId}`)
  }

  /**
   * 前往知识点管理页
   */
  goToKnowledge() {
    this.router.push(`/projects/${this.projectId}/knowledge`)
  }

  /**
   * 前往考试列表页
   */
  goToExams() {
    this.router.push(`/projects/${this.projectId}/exams`)
  }

  /**
   * 前往卡片管理页
   */
  goToFlashcards() {
    this.router.push(`/projects/${this.projectId}/flashcards`)
  }

  /**
   * 前往错题管理页
   */
  goToWrongQuestions() {
    this.router.push(`/projects/${this.projectId}/wrong-questions`)
  }

  /**
   * 前往考试分析页
   */
  goToExamAnalysis() {
    this.router.push(`/projects/${this.projectId}/exam-analysis`)
  }

  /**
   * 前往复习配置页
   */
  goToReviewConfig() {
    this.router.push(`/projects/${this.projectId}/review-configuration`)
  }

  /**
   * 前往考试记录页
   */
  goToExamRecords(examId) {
    this.router.push(`/projects/${this.projectId}/exams/${examId}/records`)
  }

  /**
   * 前往移动端学习页
   */
  goToMobileStudy() {
    this.router.push(`/mobile/study/${this.projectId}`)
  }
}

/**
 * 创建项目导航器
 * @param {Object} router - Vue路由器
 * @param {number|ComputedRef} projectId - 项目ID
 * @returns {ProjectNavigator} 项目导航器实例
 */
export function createProjectNavigator(router, projectId) {
  const id = typeof projectId === 'object' ? projectId.value : projectId
  return new ProjectNavigator(router, id)
}

/**
 * 安全的路由跳转
 * @param {Object} router - Vue路由器
 * @param {string} path - 目标路径
 * @param {Object} options - 跳转选项
 */
export async function safeNavigate(router, path, options = {}) {
  const {
    replace = false,
    params = {},
    query = {},
    onError = null
  } = options

  try {
    const route = {
      path,
      params,
      query
    }

    if (replace) {
      await router.replace(route)
    } else {
      await router.push(route)
    }
  } catch (error) {
    console.error('❌ 路由跳转失败:', error)
    if (onError && typeof onError === 'function') {
      onError(error)
    }
  }
} 