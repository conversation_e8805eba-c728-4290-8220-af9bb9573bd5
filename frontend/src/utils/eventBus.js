import { ref } from 'vue'

/**
 * 简单的事件总线实现
 * 用于组件间通信，特别是缓存刷新通知
 */
class EventBus {
  constructor() {
    this.events = new Map()
  }

  // 监听事件
  on(event, callback) {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event).push(callback)
  }

  // 移除事件监听
  off(event, callback) {
    if (!this.events.has(event)) return
    
    const callbacks = this.events.get(event)
    const index = callbacks.indexOf(callback)
    if (index > -1) {
      callbacks.splice(index, 1)
    }
  }

  // 触发事件
  emit(event, ...args) {
    if (!this.events.has(event)) return
    
    const callbacks = this.events.get(event)
    callbacks.forEach(callback => {
      try {
        callback(...args)
      } catch (error) {
        console.error('EventBus callback error:', error)
      }
    })
  }

  // 清除所有事件监听
  clear() {
    this.events.clear()
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus()

// 预定义的事件类型
export const EVENT_TYPES = {
  PROJECT_UPDATED: 'project:updated',
  PROJECT_CREATED: 'project:created', 
  PROJECT_DELETED: 'project:deleted',
  CACHE_REFRESH: 'cache:refresh'
}

/**
 * 项目数据变更通知 Composable
 */
export function useProjectEvents() {
  // 通知项目列表刷新
  const notifyProjectListRefresh = () => {
    console.log('📢 通知项目列表刷新')
    eventBus.emit(EVENT_TYPES.PROJECT_UPDATED)
    eventBus.emit(EVENT_TYPES.CACHE_REFRESH, 'projects')
  }

  // 通知项目创建
  const notifyProjectCreated = (project) => {
    console.log('📢 通知项目创建:', project?.name)
    eventBus.emit(EVENT_TYPES.PROJECT_CREATED, project)
    eventBus.emit(EVENT_TYPES.CACHE_REFRESH, 'projects')
  }

  // 通知项目删除
  const notifyProjectDeleted = (projectId) => {
    console.log('📢 通知项目删除:', projectId)
    eventBus.emit(EVENT_TYPES.PROJECT_DELETED, projectId)
    eventBus.emit(EVENT_TYPES.CACHE_REFRESH, 'projects')
  }

  // 监听项目列表刷新事件
  const onProjectListRefresh = (callback) => {
    eventBus.on(EVENT_TYPES.PROJECT_UPDATED, callback)
    eventBus.on(EVENT_TYPES.PROJECT_CREATED, callback)
    eventBus.on(EVENT_TYPES.PROJECT_DELETED, callback)
    
    // 返回清理函数
    return () => {
      eventBus.off(EVENT_TYPES.PROJECT_UPDATED, callback)
      eventBus.off(EVENT_TYPES.PROJECT_CREATED, callback)
      eventBus.off(EVENT_TYPES.PROJECT_DELETED, callback)
    }
  }

  return {
    notifyProjectListRefresh,
    notifyProjectCreated,
    notifyProjectDeleted,
    onProjectListRefresh
  }
}
