/**
 * 知识点相关工具函数
 */

/**
 * 扁平化知识点树结构
 * @param {Array} nodes - 知识点树节点
 * @param {Object} options - 配置选项
 * @returns {Array} 扁平化后的知识点列表
 */
export function flattenKnowledgeTree(nodes, options = {}) {
  const {
    includeParents = false, // 是否包含父节点
    includeFullPath = true, // 是否包含完整路径
    defaultScore = 3 // 默认分数
  } = options

  const result = []

  function traverse(items, parentPath = '', isRoot = true) {
    if (!Array.isArray(items)) return

    items.forEach(item => {
      // 构建当前节点的路径
      // 如果是根节点，不包含在路径中；否则将当前节点名称添加到父路径
      const currentPath = isRoot ? '' : (parentPath ? `${parentPath} > ${item.name}` : item.name)

      if (item.children && item.children.length > 0) {
        // 非叶子节点
        if (includeParents) {
          result.push({
            name: item.name,
            fullName: parentPath || '',
            fullPath: currentPath,
            description: item.description || '',
            score: item.score !== undefined ? item.score : defaultScore,
            uid: item.uid,
            isParent: true,
            children: item.children
          })
        }

        // 继续遍历子节点，传递当前路径
        traverse(item.children, currentPath, false)
      } else {
        // 叶子节点
        // fullName: 父级路径，不包含自己的名称
        // fullPath: 完整路径，包含自己的名称
        const leafParentPath = parentPath || ''
        const leafFullPath = currentPath || item.name

        result.push({
          name: item.name,
          fullName: includeFullPath ? leafParentPath : '',
          fullPath: includeFullPath ? leafFullPath : item.name,
          description: item.description || '',
          score: item.score !== undefined ? item.score : defaultScore,
          uid: item.uid,
          isParent: false
        })
      }
    })
  }

  traverse(nodes)
  return result
}

/**
 * 提取知识点叶子节点
 * @param {Array} nodes - 知识点树节点
 * @returns {Array} 叶子节点列表
 */
export function extractLeafNodes(nodes) {
  return flattenKnowledgeTree(nodes, { includeParents: false })
}

/**
 * 根据分数筛选知识点
 * @param {Array} knowledgePoints - 知识点列表
 * @param {Array} scoreFilters - 分数筛选器
 * @returns {Array} 筛选后的知识点列表
 */
export function filterKnowledgeByScore(knowledgePoints, scoreFilters) {
  if (!scoreFilters || scoreFilters.length === 0) {
    return knowledgePoints
  }
  
  return knowledgePoints.filter(point => 
    scoreFilters.includes(point.score)
  )
}

/**
 * 计算知识点分数分布
 * @param {Array} knowledgePoints - 知识点列表
 * @returns {Object} 分数分布统计
 */
export function calculateScoreDistribution(knowledgePoints) {
  const distribution = {}
  let unscored = 0
  
  knowledgePoints.forEach(point => {
    if (point.score !== undefined && point.score !== null) {
      const score = point.score
      distribution[score] = (distribution[score] || 0) + 1
    } else {
      unscored++
    }
  })
  
  return { distribution, unscored }
}

/**
 * 批量设置知识点分数
 * @param {Array} knowledgePoints - 知识点列表
 * @param {number} score - 要设置的分数
 * @returns {Array} 更新后的知识点列表
 */
export function batchSetScore(knowledgePoints, score) {
  return knowledgePoints.map(point => ({
    ...point,
    score
  }))
}

/**
 * 清空知识点分数
 * @param {Array} knowledgePoints - 知识点列表
 * @returns {Array} 更新后的知识点列表
 */
export function clearAllScores(knowledgePoints) {
  return knowledgePoints.map(point => {
    const { score, ...rest } = point
    return rest
  })
}

/**
 * 验证知识点数据结构
 * @param {*} data - 要验证的数据
 * @returns {Object} 验证结果
 */
export function validateKnowledgeData(data) {
  const errors = []
  
  if (!Array.isArray(data)) {
    errors.push('知识点数据必须是数组格式')
    return { valid: false, errors }
  }
  
  function validateNode(node, path = '') {
    const currentPath = path ? `${path}.${node.name}` : node.name
    
    if (!node.name || typeof node.name !== 'string') {
      errors.push(`节点 ${currentPath} 缺少有效的名称`)
    }
    
    if (node.score !== undefined && (typeof node.score !== 'number' || node.score < 1 || node.score > 5)) {
      errors.push(`节点 ${currentPath} 的分数必须是1-5之间的数字`)
    }
    
    if (node.children) {
      if (!Array.isArray(node.children)) {
        errors.push(`节点 ${currentPath} 的children必须是数组`)
      } else {
        node.children.forEach(child => validateNode(child, currentPath))
      }
    }
  }
  
  data.forEach(node => validateNode(node))
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// 路径相关函数已迁移到 @/utils/pathUtils.js
// 为了保持向后兼容，这里重新导出
import {
  generateKnowledgePath,
  formatKnowledgePathDisplay,
  batchFormatKnowledgePaths
} from './pathUtils'

export {
  generateKnowledgePath,
  formatKnowledgePathDisplay,
  batchFormatKnowledgePaths
}

/**
 * 搜索知识点
 * @param {Array} knowledgePoints - 知识点列表
 * @param {string} keyword - 搜索关键词
 * @param {Object} options - 搜索选项
 * @returns {Array} 搜索结果
 */
export function searchKnowledgePoints(knowledgePoints, keyword, options = {}) {
  const {
    searchFields = ['name', 'description', 'fullPath'],
    caseSensitive = false
  } = options
  
  if (!keyword || !keyword.trim()) {
    return knowledgePoints
  }
  
  const searchTerm = caseSensitive ? keyword.trim() : keyword.trim().toLowerCase()
  
  return knowledgePoints.filter(point => {
    return searchFields.some(field => {
      const value = point[field]
      if (!value) return false
      
      const searchValue = caseSensitive ? value : value.toLowerCase()
      return searchValue.includes(searchTerm)
    })
  })
}

/**
 * 导出知识点为JSON格式
 * @param {Array} knowledgePoints - 知识点数据
 * @param {Object} options - 导出选项
 * @returns {string} JSON字符串
 */
export function exportKnowledgeToJson(knowledgePoints, options = {}) {
  const {
    indent = 2,
    includeMetadata = false
  } = options
  
  let exportData = knowledgePoints
  
  if (!includeMetadata) {
    // 移除元数据，只保留核心字段
    exportData = knowledgePoints.map(point => {
      const { uid, isParent, fullName, fullPath, ...core } = point
      return core
    })
  }
  
  return JSON.stringify(exportData, null, indent)
}

/**
 * 从JSON导入知识点
 * @param {string} jsonString - JSON字符串
 * @returns {Object} 导入结果
 */
export function importKnowledgeFromJson(jsonString) {
  try {
    const data = JSON.parse(jsonString)
    const validation = validateKnowledgeData(data)
    
    if (!validation.valid) {
      return {
        success: false,
        errors: validation.errors,
        data: null
      }
    }
    
    return {
      success: true,
      errors: [],
      data
    }
  } catch (error) {
    return {
      success: false,
      errors: [`JSON解析失败: ${error.message}`],
      data: null
    }
  }
}
