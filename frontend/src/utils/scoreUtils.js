/**
 * 评分标签工具函数
 * 统一全系统的评分标签样式和颜色
 */

/**
 * 获取分数标签类型
 * @param {number} score - 分数值 (1-5)
 * @returns {string} Element Plus 标签类型
 */
export function getScoreTagType(score) {
  if (score === undefined || score === null) return ''
  
  switch (score) {
    case 1: return 'danger'    // 红色
    case 2: return 'warning'   // 黄色
    case 3: return ''          // 白色（默认类型，通过CSS自定义为白色）
    case 4: return 'info'      // 蓝色
    case 5: return 'success'   // 绿色
    default: return ''         // 默认白色
  }
}

/**
 * 获取分数对应的颜色值
 * @param {number} score - 分数值 (1-5)
 * @returns {object} 包含颜色信息的对象
 */
export function getScoreColors(score) {
  switch (score) {
    case 1:
      return {
        color: '#ff4d4f',      // 红色文字
        backgroundColor: '#fff2f0',
        borderColor: '#ffccc7'
      }
    case 2:
      return {
        color: '#faad14',      // 黄色文字
        backgroundColor: '#fffbe6',
        borderColor: '#ffe58f'
      }
    case 3:
      return {
        color: '#666666',      // 灰色文字
        backgroundColor: '#ffffff', // 白色背景
        borderColor: '#d9d9d9'
      }
    case 4:
      return {
        color: '#1890ff',      // 蓝色文字
        backgroundColor: '#e6f7ff',
        borderColor: '#91d5ff'
      }
    case 5:
      return {
        color: '#52c41a',      // 绿色文字
        backgroundColor: '#f6ffed',
        borderColor: '#b7eb8f'
      }
    default:
      return {
        color: '#666666',      // 默认灰色文字
        backgroundColor: '#ffffff', // 白色背景
        borderColor: '#d9d9d9'
      }
  }
}

/**
 * 获取分数对应的图标
 * @param {number} score - 分数值 (1-5)
 * @returns {string} 对应的图标
 */
export function getScoreIcon(score) {
  switch (score) {
    case 1: return '🔴'  // 红色圆圈
    case 2: return '🟡'  // 黄色圆圈
    case 3: return '⚪'  // 白色圆圈
    case 4: return '🔵'  // 蓝色圆圈
    case 5: return '🟢'  // 绿色圆圈
    default: return '⚪' // 默认白色圆圈
  }
}

/**
 * 生成统一的评分标签CSS类名
 * @param {number} score - 分数值 (1-5)
 * @returns {string} CSS类名
 */
export function getScoreClassName(score) {
  if (score === undefined || score === null) return 'score-tag-default'
  return `score-tag-${score}`
}
