/**
 * 安卓设备兼容性工具
 */
import { 
  initImportMetaPolyfill, 
  needsImportMetaPolyfill,
  testImportMetaPolyfill,
  getImportMeta
} from './importMetaPolyfill';

/**
 * 检测是否为安卓设备
 */
export const isAndroid = () => {
  return /Android/i.test(navigator.userAgent);
};

/**
 * 检测安卓版本
 */
export const getAndroidVersion = () => {
  const match = navigator.userAgent.match(/Android\s([0-9\.]*)/);
  return match ? parseFloat(match[1]) : null;
};

/**
 * 检测是否为旧版本安卓（7.0以下）
 */
export const isOldAndroid = () => {
  const version = getAndroidVersion();
  return version !== null && version < 7.0;
};

/**
 * 检测WebView版本
 */
export const getWebViewVersion = () => {
  const match = navigator.userAgent.match(/Chrome\/([0-9\.]*)/);
  return match ? parseFloat(match[1]) : null;
};

/**
 * 检测是否为低版本WebView
 */
export const isOldWebView = () => {
  const version = getWebViewVersion();
  return version !== null && version < 79;
};

/**
 * 检测ES模块支持
 */
export const supportsESModules = () => {
  try {
    // 检测基本的ES模块特性
    return 'noModule' in HTMLScriptElement.prototype;
  } catch {
    return false;
  }
};

/**
 * 检测动态import支持
 */
export const supportsDynamicImport = () => {
  try {
    new Function('return import("")');
    return true;
  } catch {
    return false;
  }
};

/**
 * 检测import.meta支持
 */
export const supportsImportMeta = () => {
  try {
    new Function('return import.meta');
    return true;
  } catch {
    return false;
  }
};

/**
 * 综合ES模块兼容性检测
 */
export const getESModuleCompatibility = () => {
  return {
    basicSupport: supportsESModules(),
    dynamicImport: supportsDynamicImport(),
    importMeta: supportsImportMeta(),
    needsImportMetaPolyfill: needsImportMetaPolyfill(),
    isFullySupported: supportsESModules() && supportsDynamicImport() && supportsImportMeta()
  };
};

/**
 * 检测WebView是否需要Legacy支持
 */
export const needsLegacySupport = () => {
  if (!isAndroid()) {
    // 非安卓环境也可能需要import.meta polyfill
    return needsImportMetaPolyfill();
  }
  
  const esModuleCompat = getESModuleCompatibility();
  const isOldVersion = isOldAndroid() || isOldWebView();
  
  return isOldVersion || !esModuleCompat.isFullySupported;
};

/**
 * 动态加载polyfill
 */
export const loadPolyfills = async () => {
  console.log('🔄 开始加载polyfill...');
  
  // 首先加载import.meta polyfill（这是最关键的）
  if (needsImportMetaPolyfill()) {
    console.log('🔧 加载import.meta polyfill...');
    const polyfillResult = initImportMetaPolyfill();
    
    if (polyfillResult) {
      // 验证polyfill是否工作
      setTimeout(() => {
        const testResult = testImportMetaPolyfill();
        console.log('🧪 import.meta polyfill测试结果:', testResult);
      }, 100);
    }
  } else {
    console.log('✅ 原生支持import.meta，无需polyfill');
  }

  // 如果不是现代浏览器，加载其他polyfills
  if (!needsLegacySupport()) {
    console.log('✅ 现代浏览器，仅需import.meta polyfill');
    return;
  }

  console.log('🔄 加载其他必要的polyfill...');
  
  // Promise polyfill
  if (!window.Promise) {
    try {
      await import('es6-promise/auto');
      console.log('✅ Promise polyfill加载成功');
    } catch (e) {
      console.warn('Promise polyfill加载失败:', e);
    }
  }
  
  // Array.from polyfill
  if (!Array.from) {
    try {
      await import('core-js/features/array/from');
      console.log('✅ Array.from polyfill加载成功');
    } catch (e) {
      console.warn('Array.from polyfill加载失败:', e);
    }
  }
  
  // Object.assign polyfill
  if (!Object.assign) {
    try {
      await import('core-js/features/object/assign');
      console.log('✅ Object.assign polyfill加载成功');
    } catch (e) {
      console.warn('Object.assign polyfill加载失败:', e);
    }
  }
  
  // fetch polyfill for old WebView
  if (!window.fetch) {
    try {
      await import('whatwg-fetch');
      console.log('✅ fetch polyfill加载成功');
    } catch (e) {
      console.warn('fetch polyfill加载失败:', e);
    }
  }
  
  console.log('✅ Polyfills加载完成');
};

/**
 * 生成设备兼容性报告
 */
export const getCompatibilityReport = () => {
  const report = {
    isAndroid: isAndroid(),
    androidVersion: getAndroidVersion(),
    isOldAndroid: isOldAndroid(),
    webViewVersion: getWebViewVersion(),
    isOldWebView: isOldWebView(),
    esModuleSupport: getESModuleCompatibility(),
    needsLegacy: needsLegacySupport(),
    needsImportMetaPolyfill: needsImportMetaPolyfill(),
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
      ratio: window.devicePixelRatio
    },
    supportedFeatures: {
      touchEvents: 'ontouchstart' in window,
      transformSupport: 'transform' in document.body.style,
      webkitTransform: 'webkitTransform' in document.body.style,
      flexbox: CSS.supports && CSS.supports('display', 'flex'),
      grid: CSS.supports && CSS.supports('display', 'grid'),
      fetch: typeof fetch !== 'undefined',
      promise: typeof Promise !== 'undefined',
      localStorage: typeof localStorage !== 'undefined',
      sessionStorage: typeof sessionStorage !== 'undefined'
    },
    // 添加import.meta相关信息
    importMetaInfo: {
      needsPolyfill: needsImportMetaPolyfill(),
      hasPolyfill: !!window.__vite_import_meta__,
      canAccessImportMeta: (() => {
        try {
          const meta = getImportMeta();
          return !!(meta && meta.env);
        } catch {
          return false;
        }
      })()
    }
  };

  console.log('📱 设备兼容性报告:', report);
  return report;
};

/**
 * 应用安卓兼容性优化
 */
export const applyAndroidOptimizations = () => {
  // 为所有设备添加特殊CSS类（不仅限于安卓）
  if (isAndroid()) {
    document.body.classList.add('android-device');
  }
  
  if (isOldAndroid()) {
    document.body.classList.add('old-android');
  }
  
  if (isOldWebView()) {
    document.body.classList.add('old-webview');
  }

  if (needsLegacySupport()) {
    document.body.classList.add('needs-legacy-support');
  }

  if (needsImportMetaPolyfill()) {
    document.body.classList.add('needs-import-meta-polyfill');
  }

  // 为WebView环境添加特殊标识
  if (location.protocol === 'file:') {
    document.body.classList.add('file-protocol');
  }

  // 禁用某些可能导致问题的动画
  if (isOldWebView() || needsLegacySupport()) {
    const style = document.createElement('style');
    style.id = 'webview-compatibility-styles';
    style.textContent = `
      .old-webview *,
      .needs-legacy-support * {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
      }
      
      /* 禁用复杂的CSS特性 */
      .old-webview .complex-animation,
      .needs-legacy-support .complex-animation {
        animation: none !important;
        transition: none !important;
      }
      
      /* 优化滚动性能 */
      .old-webview,
      .needs-legacy-support {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: auto !important;
      }
      
      /* 为需要polyfill的环境添加特殊样式 */
      .needs-import-meta-polyfill {
        /* 可以添加一些调试样式 */
      }
      
      .file-protocol {
        /* file协议特殊样式 */
      }
    `;
    document.head.appendChild(style);
  }

  // 添加WebView特定的meta标签
  if (isAndroid()) {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
      );
    }
  }

  console.log('✅ 已应用兼容性优化');
};

/**
 * WebView环境初始化
 */
export const initWebViewEnvironment = async () => {
  console.log('🚀 开始WebView环境初始化...');
  
  // 生成兼容性报告
  const report = getCompatibilityReport();
  
  // 设置全局错误处理
  setupErrorReporting();
  
  // 加载必要的polyfill
  await loadPolyfills();
  
  // 应用优化
  applyAndroidOptimizations();
  
  // 最终验证
  if (needsImportMetaPolyfill()) {
    setTimeout(() => {
      const testResult = testImportMetaPolyfill();
      if (testResult.canAccess && testResult.envAccess) {
        console.log('✅ import.meta polyfill验证成功');
      } else {
        console.warn('⚠️ import.meta polyfill可能存在问题:', testResult);
      }
    }, 200);
  }
  
  console.log('✅ WebView环境初始化完成');
  return report;
};

/**
 * 错误捕获和报告
 */
export const setupErrorReporting = () => {
  window.addEventListener('error', (event) => {
    const errorInfo = {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error,
      compatibility: {
        isAndroid: isAndroid(),
        needsLegacy: needsLegacySupport(),
        needsImportMetaPolyfill: needsImportMetaPolyfill(),
        esModuleSupport: getESModuleCompatibility(),
        hasImportMetaPolyfill: !!window.__vite_import_meta__
      }
    };
    
    console.error('🚨 全局错误:', errorInfo);
    
    // 如果是import.meta相关错误，尝试修复
    if (event.message && event.message.includes('import.meta')) {
      console.warn('🔧 检测到import.meta相关错误，尝试重新初始化polyfill');
      setTimeout(() => {
        initImportMetaPolyfill();
      }, 100);
    }
  });

  window.addEventListener('unhandledrejection', (event) => {
    const errorInfo = {
      reason: event.reason,
      compatibility: {
        isAndroid: isAndroid(),
        needsLegacy: needsLegacySupport(),
        needsImportMetaPolyfill: needsImportMetaPolyfill(),
        esModuleSupport: getESModuleCompatibility(),
        hasImportMetaPolyfill: !!window.__vite_import_meta__
      }
    };
    
    console.error('🚨 未处理的Promise拒绝:', errorInfo);
  });
}; 