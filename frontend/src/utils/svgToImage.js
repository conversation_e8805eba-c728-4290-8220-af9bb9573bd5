/**
 * SVG转图片工具函数
 * 将SVG代码转换为Base64编码的图片
 */

/**
 * 预处理数学公式分隔符，将 \( \) 转换为 $ $，将 \[ \] 转换为 $$ $$
 * 同时清理LaTeX命令后的多余空格
 * @param {string} content - 包含数学公式的内容
 * @returns {string} 处理后的内容
 */
export function preprocessMathDelimiters(content) {
  if (!content) return content

  // 首先清理代码块前的空格
  content = cleanCodeBlockIndentation(content)

  // 将 \( ... \) 转换为 $ ... $（行内公式）
  content = content.replace(/\\\((.*?)\\\)/g, (match, formula) => {
    return '$' + cleanLatexSpacing(formula.trim()) + '$'
  })

  // 将 \[ ... \] 转换为 $$ ... $$（块级公式）
  content = content.replace(/\\\[([\s\S]*?)\\\]/g, (match, formula) => {
    return '$$' + cleanLatexSpacing(formula.trim()) + '$$'
  })

  // 也处理已有的 $ $ 和 $$ $$ 分隔符中的内容
  content = content.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
    return '$$' + cleanLatexSpacing(formula.trim()) + '$$'
  })

  content = content.replace(/\$([^$]*?)\$/g, (match, formula) => {
    return '$' + cleanLatexSpacing(formula.trim()) + '$'
  })

  return content
}

/**
 * 清理代码块前的缩进空格
 * @param {string} content - 包含代码块的内容
 * @returns {string} 清理后的内容
 */
function cleanCodeBlockIndentation(content) {
  if (!content) return content

  // 匹配带有前导空格的代码块并移除前导空格
  content = content.replace(/^(\s+)(```(?:html|svg)?)/gim, '$2')

  // 也处理代码块结束标记前的空格
  content = content.replace(/^(\s+)(```\s*)$/gim, '$2')

  return content
}

/**
 * 清理LaTeX公式中的多余空格
 * @param {string} formula - LaTeX公式内容
 * @returns {string} 清理后的公式
 */
function cleanLatexSpacing(formula) {
  if (!formula) return formula

  // 清理常见LaTeX命令后的多余空格
  const latexCommands = [
    'quad', 'qquad', 'enspace', 'space',
    'frac', 'sqrt', 'sin', 'cos', 'tan', 'log', 'ln',
    'sum', 'int', 'prod', 'lim', 'max', 'min',
    'alpha', 'beta', 'gamma', 'delta', 'epsilon', 'theta', 'lambda', 'mu', 'pi', 'sigma', 'phi', 'omega',
    'left', 'right', 'begin', 'end'
  ]

  // 为每个命令清理后面的多余空格
  latexCommands.forEach(cmd => {
    // 匹配 \command 后跟一个或多个空格的情况
    const regex = new RegExp(`\\\\${cmd}\\s+`, 'g')
    formula = formula.replace(regex, `\\${cmd} `)
  })

  // 特别处理一些不需要后续空格的命令
  const noSpaceCommands = ['quad', 'qquad', 'enspace']
  noSpaceCommands.forEach(cmd => {
    const regex = new RegExp(`\\\\${cmd}\\s+`, 'g')
    formula = formula.replace(regex, `\\${cmd}`)
  })

  return formula
}

/**
 * 将SVG字符串转换为Base64编码的PNG图片
 * @param {string} svgString - SVG代码字符串
 * @param {number} scale - 缩放比例，默认为2（提高清晰度）
 * @returns {Promise<string>} Base64编码的图片数据URL
 */
export function svgToBase64Image(svgString, scale = 2) {
  return new Promise((resolve, reject) => {
    try {
      // 创建一个临时的SVG元素来获取尺寸
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = svgString
      const svgElement = tempDiv.querySelector('svg')

      if (!svgElement) {
        reject(new Error('无效的SVG代码'))
        return
      }

      // 获取SVG的宽高
      let width = parseInt(svgElement.getAttribute('width')) || 100
      let height = parseInt(svgElement.getAttribute('height')) || 100

      // 如果SVG没有设置宽高，尝试从viewBox获取
      if (!svgElement.getAttribute('width') && !svgElement.getAttribute('height')) {
        const viewBox = svgElement.getAttribute('viewBox')
        if (viewBox) {
          const [, , vbWidth, vbHeight] = viewBox.split(' ').map(Number)
          width = vbWidth || 100
          height = vbHeight || 100
        }
      }

      // 确保SVG有正确的命名空间
      if (!svgElement.getAttribute('xmlns')) {
        svgElement.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
      }

      // 使用更简单的方法：直接将SVG转换为data URL
      const svgData = new XMLSerializer().serializeToString(svgElement)
      const svgDataUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)))

      // 创建Image对象
      const img = new Image()
      img.onload = function() {
        try {
          // 创建Canvas
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          // 设置Canvas尺寸（应用缩放）
          canvas.width = width * scale
          canvas.height = height * scale

          // 设置白色背景
          ctx.fillStyle = 'white'
          ctx.fillRect(0, 0, canvas.width, canvas.height)

          // 绘制SVG到Canvas
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

          // 转换为Base64
          const dataURL = canvas.toDataURL('image/png', 1.0)

          resolve(dataURL)
        } catch (error) {
          reject(error)
        }
      }

      img.onerror = function() {
        reject(new Error('SVG图片加载失败'))
      }

      // 设置图片源
      img.src = svgDataUrl

    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 处理Markdown内容中的SVG代码，将其转换为图片
 * @param {string} markdownContent - 包含SVG的Markdown内容
 * @returns {Promise<string>} 处理后的Markdown内容
 */
export async function processSvgInMarkdown(markdownContent) {
  if (!markdownContent) return markdownContent

  console.log('🔍 开始处理Markdown中的SVG内容...')

  // 匹配完整的HTML代码块，支持前导空格
  const svgRegex = /^(\s*)```html\s*\n([\s\S]*?)\n\s*```/gim
  let processedContent = markdownContent
  const matches = []

  // 收集所有匹配
  let match
  while ((match = svgRegex.exec(markdownContent)) !== null) {
    if (match[2].includes('<svg') && match[2].includes('</svg>')) {
      matches.push({
        fullMatch: match[0],
        codeContent: match[2],
        indentation: match[1] // 保存前导空格
      })
    }
  }

  console.log(`📊 找到 ${matches.length} 个包含SVG的代码块`)

  // 处理每个匹配
  for (const svgMatch of matches) {
    try {
      // 提取SVG标签
      const svgTagMatch = svgMatch.codeContent.match(/<svg[^>]*>[\s\S]*?<\/svg>/i)
      if (svgTagMatch) {
        const svgString = svgTagMatch[0]

        // 转换为图片
        const imageDataUrl = await svgToBase64Image(svgString)

        // 提取图片说明
        const captionMatch = svgMatch.codeContent.match(/（([^）]+)）/)
        const caption = captionMatch ? captionMatch[1] : '图形'

        // 替换为图片
        const replacement = `![${caption}](${imageDataUrl})\n\n*${caption}*`

        console.log(`🔄 替换SVG为图片: ${caption}`)
        processedContent = processedContent.replace(svgMatch.fullMatch, replacement)
      }
    } catch (error) {
      console.error('❌ 处理SVG失败:', error)
    }
  }

  console.log(`📊 找到 ${matches.length} 个包含SVG的代码块`)

  // 处理每个SVG代码块
  for (const svgMatch of matches) {
    try {
      // 提取SVG标签
      const svgTagMatch = svgMatch.codeContent.match(/<svg[^>]*>[\s\S]*?<\/svg>/i)
      if (svgTagMatch) {
        const svgString = svgTagMatch[0]

        // 转换为图片
        const imageDataUrl = await svgToBase64Image(svgString)

        // 提取图片说明（在SVG后面的中文括号内容）
        const captionMatch = svgMatch.codeContent.match(/（([^）]+)）/)
        const caption = captionMatch ? captionMatch[1] : '图形'

        // 替换为Markdown图片语法，确保图片能正确显示
        let replacement = `![${caption}](${imageDataUrl})`

        // 如果有说明文字，添加为图片下方的文本
        if (caption && caption !== '图形') {
          replacement += `\n\n*${caption}*`
        }

        console.log(`🔄 替换SVG为图片: ${caption}`)
        console.log(`📝 原始匹配内容:`, svgMatch.fullMatch)
        console.log(`📝 替换内容:`, replacement)

        // 确保替换成功
        const beforeLength = processedContent.length
        processedContent = processedContent.replace(svgMatch.fullMatch, replacement)
        const afterLength = processedContent.length

        console.log(`✅ 替换完成，长度变化: ${beforeLength} -> ${afterLength}`)

        // 验证替换是否成功
        if (processedContent.includes(svgMatch.fullMatch)) {
          console.warn('⚠️ 警告：原始内容仍然存在，替换可能失败')
        }
      }
    } catch (error) {
      console.error('❌ 处理SVG失败:', error)
      // 如果转换失败，保留原始SVG代码
    }
  }

  console.log('✅ SVG处理完成')
  return processedContent
}

/**
 * 批量处理多个SVG
 * @param {Array<string>} svgStrings - SVG字符串数组
 * @param {number} scale - 缩放比例
 * @returns {Promise<Array<string>>} Base64图片数组
 */
export async function batchConvertSvgToImages(svgStrings, scale = 2) {
  const promises = svgStrings.map(svg => svgToBase64Image(svg, scale))
  return Promise.all(promises)
}

/**
 * 检查字符串是否包含SVG代码
 * @param {string} content - 要检查的内容
 * @returns {boolean} 是否包含SVG
 */
export function containsSvg(content) {
  if (!content) return false

  // 检查是否有包含SVG的代码块，支持前导空格
  const codeBlockRegex = /^(\s*)```\s*(?:html|svg)?\s*\n([\s\S]*?)\n\s*```/gim
  let match

  while ((match = codeBlockRegex.exec(content)) !== null) {
    const codeContent = match[2]
    if (codeContent.includes('<svg') && codeContent.includes('</svg>')) {
      return true
    }
  }

  // 如果代码块检查失败，直接检查是否包含SVG标签
  return content.includes('<svg') && content.includes('</svg>')
}

export default {
  svgToBase64Image,
  processSvgInMarkdown,
  batchConvertSvgToImages,
  containsSvg
}
