/**
 * 数据处理工具类
 * 提供常用的数据格式化、排序、筛选等功能
 */

/**
 * 分数相关工具
 */
export const scoreUtils = {
  /**
   * 格式化分数显示
   */
  formatScore: (score) => {
    if (score == null || score === '') return '0'
    return Number(score).toFixed(1)
  },

  /**
   * 格式化百分比
   */
  formatPercent: (percent) => {
    if (percent == null) return '0%'
    return Number(percent).toFixed(1) + '%'
  },

  /**
   * 获取分数标签类型
   */
  getScoreTagType: (score) => {
    if (score >= 90) return 'success'
    if (score >= 80) return 'primary'
    if (score >= 60) return 'warning'
    return 'danger'
  },

  /**
   * 获取分数等级
   */
  getScoreGrade: (score) => {
    if (score >= 95) return '优秀'
    if (score >= 85) return '良好'
    if (score >= 75) return '中等'
    if (score >= 60) return '及格'
    return '不及格'
  },

  /**
   * 计算平均分
   */
  calculateAverage: (scores) => {
    if (!Array.isArray(scores) || scores.length === 0) return 0
    const validScores = scores.filter(s => typeof s === 'number' && !isNaN(s))
    if (validScores.length === 0) return 0
    return validScores.reduce((sum, score) => sum + score, 0) / validScores.length
  }
}

/**
 * 难度相关工具
 */
export const difficultyUtils = {
  /**
   * 难度映射
   */
  difficultyMap: {
    1: '简单',
    2: '容易', 
    3: '中等',
    4: '困难',
    5: '极难'
  },

  /**
   * 获取难度文本
   */
  getDifficultyText: (difficulty) => {
    return difficultyUtils.difficultyMap[difficulty] || '未知'
  },

  /**
   * 获取难度标签类型
   */
  getDifficultyTagType: (difficulty) => {
    const typeMap = {
      1: 'success',
      2: 'primary',
      3: 'warning',
      4: 'danger',
      5: 'danger'
    }
    return typeMap[difficulty] || 'info'
  },

  /**
   * 获取难度颜色
   */
  getDifficultyColor: (difficulty) => {
    const colorMap = {
      1: '#67C23A',
      2: '#409EFF',
      3: '#E6A23C',
      4: '#F56C6C',
      5: '#F56C6C'
    }
    return colorMap[difficulty] || '#909399'
  }
}

/**
 * 数组处理工具
 */
export const arrayUtils = {
  /**
   * 安全的数组获取
   */
  safeArray: (data) => {
    return Array.isArray(data) ? data : []
  },

  /**
   * 数组去重
   */
  unique: (array, key = null) => {
    if (!Array.isArray(array)) return []
    
    if (key) {
      const seen = new Set()
      return array.filter(item => {
        const value = item[key]
        if (seen.has(value)) {
          return false
        }
        seen.add(value)
        return true
      })
    }
    
    return [...new Set(array)]
  },

  /**
   * 数组分组
   */
  groupBy: (array, key) => {
    if (!Array.isArray(array)) return {}
    
    return array.reduce((groups, item) => {
      const group = typeof key === 'function' ? key(item) : item[key]
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(item)
      return groups
    }, {})
  },

  /**
   * 安全排序
   */
  safeSortBy: (array, key, order = 'asc') => {
    if (!Array.isArray(array)) return []
    
    return [...array].sort((a, b) => {
      let aVal = typeof key === 'function' ? key(a) : a[key]
      let bVal = typeof key === 'function' ? key(b) : b[key]
      
      // 处理 null/undefined
      if (aVal == null && bVal == null) return 0
      if (aVal == null) return order === 'asc' ? 1 : -1
      if (bVal == null) return order === 'asc' ? -1 : 1
      
      // 数字比较
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return order === 'asc' ? aVal - bVal : bVal - aVal
      }
      
      // 字符串比较
      aVal = String(aVal).toLowerCase()
      bVal = String(bVal).toLowerCase()
      
      if (order === 'asc') {
        return aVal.localeCompare(bVal)
      } else {
        return bVal.localeCompare(aVal)
      }
    })
  },

  /**
   * 分页处理
   */
  paginate: (array, page = 1, pageSize = 10) => {
    if (!Array.isArray(array)) return { data: [], total: 0, page, pageSize }
    
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    
    return {
      data: array.slice(startIndex, endIndex),
      total: array.length,
      page,
      pageSize,
      totalPages: Math.ceil(array.length / pageSize)
    }
  }
}

/**
 * 搜索和筛选工具
 */
export const filterUtils = {
  /**
   * 文本搜索
   */
  textSearch: (items, keyword, searchFields = []) => {
    if (!keyword || !Array.isArray(items)) return items
    
    const lowerKeyword = keyword.toLowerCase()
    
    return items.filter(item => {
      if (searchFields.length === 0) {
        // 搜索所有字符串字段
        return Object.values(item).some(value => 
          typeof value === 'string' && value.toLowerCase().includes(lowerKeyword)
        )
      }
      
      // 搜索指定字段
      return searchFields.some(field => {
        const value = item[field]
        return typeof value === 'string' && value.toLowerCase().includes(lowerKeyword)
      })
    })
  },

  /**
   * 多条件筛选
   */
  multiFilter: (items, filters = {}) => {
    if (!Array.isArray(items)) return []
    
    return items.filter(item => {
      return Object.keys(filters).every(key => {
        const filterValue = filters[key]
        const itemValue = item[key]
        
        // 跳过空值筛选条件
        if (filterValue == null || filterValue === '') return true
        
        // 数组类型（多选）
        if (Array.isArray(filterValue)) {
          return filterValue.length === 0 || filterValue.includes(itemValue)
        }
        
        // 对象类型（范围筛选）
        if (typeof filterValue === 'object' && filterValue.min != null && filterValue.max != null) {
          return itemValue >= filterValue.min && itemValue <= filterValue.max
        }
        
        // 普通相等比较
        return itemValue === filterValue
      })
    })
  }
}

/**
 * 统计工具
 */
export const statsUtils = {
  /**
   * 计算基本统计信息
   */
  calculateStats: (array, valueKey = null) => {
    if (!Array.isArray(array) || array.length === 0) {
      return { count: 0, sum: 0, avg: 0, min: 0, max: 0 }
    }
    
    const values = valueKey 
      ? array.map(item => item[valueKey]).filter(v => typeof v === 'number' && !isNaN(v))
      : array.filter(v => typeof v === 'number' && !isNaN(v))
    
    if (values.length === 0) {
      return { count: array.length, sum: 0, avg: 0, min: 0, max: 0 }
    }
    
    const sum = values.reduce((a, b) => a + b, 0)
    const avg = sum / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)
    
    return { count: array.length, sum, avg, min, max }
  },

  /**
   * 计算正确率
   */
  calculateAccuracy: (correct, total) => {
    if (total === 0) return 0
    return Math.round((correct / total) * 100 * 10) / 10
  },

  /**
   * 计算及格率
   */
  calculatePassRate: (scores, passScore = 60) => {
    if (!Array.isArray(scores) || scores.length === 0) return 0
    const passCount = scores.filter(score => score >= passScore).length
    return Math.round((passCount / scores.length) * 100 * 10) / 10
  }
}

/**
 * 数据转换工具
 */
export const transformUtils = {
  /**
   * 扁平化数组
   */
  flatten: (array, depth = 1) => {
    if (!Array.isArray(array)) return []
    return depth > 0 ? array.reduce((acc, val) => 
      acc.concat(Array.isArray(val) ? transformUtils.flatten(val, depth - 1) : val), []
    ) : array.slice()
  },

  /**
   * 树形数据扁平化
   */
  flattenTree: (tree, childrenKey = 'children', parentKey = 'parentId') => {
    const result = []
    
    function traverse(nodes, parentId = null) {
      if (!Array.isArray(nodes)) return
      
      nodes.forEach(node => {
        const flatNode = { ...node }
        if (parentKey) {
          flatNode[parentKey] = parentId
        }
        
        // 移除children字段避免循环引用
        delete flatNode[childrenKey]
        result.push(flatNode)
        
        // 递归处理子节点
        if (node[childrenKey] && Array.isArray(node[childrenKey])) {
          traverse(node[childrenKey], node.id || node.uid)
        }
      })
    }
    
    traverse(tree)
    return result
  },

  /**
   * 列表转树形结构
   */
  listToTree: (list, { idKey = 'id', parentKey = 'parentId', childrenKey = 'children' } = {}) => {
    if (!Array.isArray(list)) return []
    
    const map = {}
    const roots = []
    
    // 创建映射
    list.forEach(item => {
      map[item[idKey]] = { ...item, [childrenKey]: [] }
    })
    
    // 构建树形结构
    list.forEach(item => {
      const node = map[item[idKey]]
      const parentId = item[parentKey]
      
      if (parentId && map[parentId]) {
        map[parentId][childrenKey].push(node)
      } else {
        roots.push(node)
      }
    })
    
    return roots
  }
} 