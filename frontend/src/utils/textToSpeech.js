/**
 * 文本朗读工具类
 * 基于Web Speech API实现
 */
class TextToSpeech {
  constructor() {
    this.synth = window.speechSynthesis
    this.currentUtterance = null
    this.isSupported = 'speechSynthesis' in window
    this.voices = []
    this.defaultSettings = {
      rate: 1.0,      // 语速 (0.1 - 10)
      pitch: 1.0,     // 音调 (0 - 2)
      volume: 1.0,    // 音量 (0 - 1)
      lang: 'en-US'   // 默认语言
    }
    
    // 初始化语音列表
    this.loadVoices()
    
    // 监听语音列表变化（某些浏览器需要异步加载）
    if (this.synth.onvoiceschanged !== undefined) {
      this.synth.onvoiceschanged = () => {
        this.loadVoices()
      }
    }
  }



  /**
   * 加载可用的语音列表
   */
  loadVoices() {
    this.voices = this.synth.getVoices()
  }

  /**
   * 获取指定语言的语音
   * @param {string} lang - 语言代码，如 'en-US', 'zh-CN'
   * @returns {SpeechSynthesisVoice|null}
   */
  getVoiceByLang(lang) {
    // 优先选择指定语言的语音
    let voice = this.voices.find(v => v.lang === lang)
    
    // 如果没找到精确匹配，尝试匹配语言前缀
    if (!voice) {
      const langPrefix = lang.split('-')[0]
      voice = this.voices.find(v => v.lang.startsWith(langPrefix))
    }
    
    return voice || null
  }

  /**
   * 检测文本语言
   * @param {string} text - 要检测的文本
   * @returns {string} - 语言代码
   */
  detectLanguage(text) {
    // 简单的语言检测逻辑
    const chineseRegex = /[\u4e00-\u9fff]/
    const englishRegex = /^[a-zA-Z\s\.,!?;:'"()-]+$/
    
    if (chineseRegex.test(text)) {
      return 'zh-CN'
    } else if (englishRegex.test(text.trim())) {
      return 'en-US'
    }
    
    // 默认返回英文
    return 'en-US'
  }

  /**
   * 朗读文本
   * @param {string} text - 要朗读的文本
   * @param {Object} options - 朗读选项
   * @returns {Promise}
   */
  speak(text, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('浏览器不支持语音合成功能'))
        return
      }

      if (!text || text.trim() === '') {
        reject(new Error('朗读文本不能为空'))
        return
      }

      // 停止当前朗读
      this.stop()

      // 合并设置
      const settings = { ...this.defaultSettings, ...options }
      
      // 自动检测语言（如果未指定）
      if (!options.lang) {
        settings.lang = this.detectLanguage(text)
      }

      // 创建语音合成实例
      this.currentUtterance = new SpeechSynthesisUtterance(text)
      
      // 设置语音参数
      this.currentUtterance.rate = settings.rate
      this.currentUtterance.pitch = settings.pitch
      this.currentUtterance.volume = settings.volume
      this.currentUtterance.lang = settings.lang

      // 尝试设置对应语言的语音
      const voice = this.getVoiceByLang(settings.lang)
      if (voice) {
        this.currentUtterance.voice = voice
      }

      // 设置事件监听器
      this.currentUtterance.onstart = () => {
        console.log('开始朗读:', text)
      }

      this.currentUtterance.onend = () => {
        console.log('朗读结束:', text)
        this.currentUtterance = null
        resolve()
      }

      this.currentUtterance.onerror = (event) => {
        console.error('朗读出错:', event.error)
        this.currentUtterance = null
        reject(new Error(`朗读失败: ${event.error}`))
      }

      // 开始朗读
      this.synth.speak(this.currentUtterance)
    })
  }

  /**
   * 停止朗读
   */
  stop() {
    if (this.synth.speaking) {
      this.synth.cancel()
    }
    this.currentUtterance = null
  }

  /**
   * 暂停朗读
   */
  pause() {
    if (this.synth.speaking && !this.synth.paused) {
      this.synth.pause()
    }
  }

  /**
   * 恢复朗读
   */
  resume() {
    if (this.synth.paused) {
      this.synth.resume()
    }
  }

  /**
   * 检查是否正在朗读
   */
  isSpeaking() {
    return this.synth.speaking
  }

  /**
   * 检查是否已暂停
   */
  isPaused() {
    return this.synth.paused
  }

  /**
   * 获取可用语音列表
   */
  getVoices() {
    return this.voices
  }

  /**
   * 设置默认配置
   */
  setDefaultSettings(settings) {
    this.defaultSettings = { ...this.defaultSettings, ...settings }
  }
}

// 创建单例实例
const textToSpeech = new TextToSpeech()

export default textToSpeech
