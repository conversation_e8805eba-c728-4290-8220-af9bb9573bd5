// 环境检测工具

/**
 * 检查是否在Web浏览器中运行
 */
export function isWeb() {
  return true // 只支持Web模式
}

/**
 * 获取当前平台
 */
export function getPlatform() {
  return navigator.platform
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment() {
  return import.meta.env.DEV
}

/**
 * 检查是否为生产环境
 */
export function isProduction() {
  return !isDevelopment()
}

/**
 * 获取应用版本
 */
export async function getAppVersion() {
  return import.meta.env.VITE_APP_VERSION || '1.0.0'
}

/**
 * 显示消息框（Web版本使用浏览器原生API）
 */
export async function showMessageBox(options) {
  // Web环境处理
  const message = options.message + (options.detail ? '\n\n' + options.detail : '')
  if (options.type === 'question') {
    return { response: confirm(message) ? 0 : 1 }
  } else {
    alert(message)
    return { response: 0 }
  }
}

/**
 * 显示文件打开对话框（Web版本使用input元素）
 */
export async function showOpenDialog(options) {
  return new Promise((resolve) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = options?.properties?.includes('multiSelections') || false

    if (options?.filters) {
      const extensions = options.filters.flatMap(filter => filter.extensions)
      input.accept = extensions.map(ext => `.${ext}`).join(',')
    }

    input.onchange = (e) => {
      const files = Array.from(e.target.files || [])
      resolve({
        canceled: files.length === 0,
        filePaths: files.map(file => file.path || file.name)
      })
    }

    input.click()
  })
}

/**
 * 显示文件保存对话框（Web版本不支持，使用下载）
 */
export async function showSaveDialog(options) {
  console.warn('Web版本不支持文件保存对话框，请使用下载功能')
  return { canceled: true, filePath: '' }
}

/**
 * 监听菜单操作（Web版本不支持）
 */
export function onMenuAction(callback) {
  console.warn('Web版本不支持菜单操作监听')
}

/**
 * 移除事件监听器（Web版本不支持）
 */
export function removeAllListeners(channel) {
  console.warn('Web版本不支持事件监听器移除')
}

/**
 * 获取环境信息
 */
export function getEnvironmentInfo() {
  return {
    isWeb: isWeb(),
    platform: getPlatform(),
    isDevelopment: isDevelopment(),
    isProduction: isProduction(),
    userAgent: navigator.userAgent
  }
}

/**
 * 格式化日期字符串
 * @param {string} dateString - 日期字符串，格式如 "2025-06-04 19:38:42"
 * @param {string} type - 格式类型：'date'(仅日期), 'datetime'(日期时间), 'time'(仅时间)
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(dateString, type = 'datetime') {
  if (!dateString) return '无效日期'

  try {
    // 处理后端返回的日期格式 "2025-06-04 19:38:42"
    // 确保日期字符串可以被正确解析
    let date
    if (typeof dateString === 'string' && dateString.includes(' ')) {
      // 如果是 "YYYY-MM-DD HH:mm:ss" 格式，直接创建Date对象
      date = new Date(dateString.replace(' ', 'T'))
    } else {
      date = new Date(dateString)
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '无效日期'
    }

    if (type === 'time') {
      return date.toLocaleTimeString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    } else if (type === 'datetime') {
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    } else {
      // type === 'date'
      return date.toLocaleDateString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  } catch (error) {
    console.error('日期格式化错误:', error, '原始日期:', dateString)
    return '日期格式错误'
  }
}

/**
 * 格式化日期时间字符串（便捷函数）
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(dateString) {
  return formatDate(dateString, 'datetime')
}

// 导出环境常量
export const ENV = {
  IS_WEB: isWeb(),
  IS_DEV: isDevelopment(),
  IS_PROD: isProduction(),
  PLATFORM: getPlatform()
}
