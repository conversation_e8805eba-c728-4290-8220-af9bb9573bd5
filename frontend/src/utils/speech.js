/**
 * 语音合成工具函数
 */

// 检查浏览器是否支持语音合成
export const isSpeechSynthesisSupported = () => {
  return 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window
}

// 获取可用的语音列表
export const getAvailableVoices = () => {
  if (!isSpeechSynthesisSupported()) {
    return []
  }
  return speechSynthesis.getVoices()
}

// 获取英文语音
export const getEnglishVoice = () => {
  const voices = getAvailableVoices()
  // 优先选择高质量的英文语音
  return voices.find(voice =>
    voice.lang.startsWith('en') && (voice.name.includes('Google') || voice.name.includes('Microsoft'))
  ) || voices.find(voice =>
    voice.lang.startsWith('en') && voice.name.includes('Natural')
  ) || voices.find(voice =>
    voice.lang.startsWith('en')
  ) || voices[0]
}

// 获取中文语音
export const getChineseVoice = () => {
  const voices = getAvailableVoices()
  // 优先选择高质量的中文语音
  return voices.find(voice =>
    (voice.lang.startsWith('zh') || voice.lang.includes('Chinese')) &&
    (voice.name.includes('Google') || voice.name.includes('Microsoft'))
  ) || voices.find(voice =>
    (voice.lang.startsWith('zh') || voice.lang.includes('Chinese')) &&
    voice.name.includes('Natural')
  ) || voices.find(voice =>
    voice.lang.startsWith('zh') || voice.lang.includes('Chinese')
  ) || voices.find(voice =>
    voice.lang.includes('cn') || voice.lang.includes('CN')
  )
}

// 根据语言获取最佳语音
export const getBestVoiceForLanguage = (lang) => {
  const voices = getAvailableVoices()

  // 根据语言代码选择语音
  const langCode = lang.toLowerCase()

  if (langCode.startsWith('en')) {
    return getEnglishVoice()
  } else if (langCode.startsWith('zh') || langCode.includes('chinese')) {
    return getChineseVoice()
  }

  // 通用语言匹配
  return voices.find(voice =>
    voice.lang.toLowerCase().startsWith(langCode)
  ) || voices[0]
}

// 语音播放状态管理
let currentUtterance = null
let isPlaying = false

// 停止当前播放
export const stopSpeech = () => {
  try {
    if (speechSynthesis.speaking || speechSynthesis.pending) {
      speechSynthesis.cancel()
    }

    // 清理状态
    if (currentUtterance) {
      currentUtterance = null
    }
    isPlaying = false

    console.log('🛑 语音播放已停止')
  } catch (error) {
    console.warn('停止语音播放时出错:', error)
    // 强制重置状态
    currentUtterance = null
    isPlaying = false
  }
}

// 播放单个文本段落
const speakSingleText = (text, language, options = {}) => {
  return new Promise((resolve, reject) => {
    // 创建语音合成实例
    const utterance = new SpeechSynthesisUtterance(text.trim())

    // 根据语言选择最佳语音
    let voice = options.voice
    if (!voice) {
      if (language === 'chinese') {
        voice = getChineseVoice()
        utterance.lang = 'zh-CN'
      } else if (language === 'english') {
        voice = getEnglishVoice()
        utterance.lang = 'en-US'
      } else {
        voice = getBestVoiceForLanguage(options.lang || 'en-US')
      }
    }

    if (voice) {
      utterance.voice = voice
    }

    // 设置语音参数
    utterance.rate = options.rate || 0.9
    utterance.pitch = options.pitch || 1
    utterance.volume = options.volume || 1

    // 设置事件监听
    utterance.onstart = () => {
      console.log(`🔊 开始播放 [${language}]:`, text)
    }

    utterance.onend = () => {
      console.log(`✅ 播放完成 [${language}]:`, text)
      resolve()
    }

    utterance.onerror = (event) => {
      if (event.error === 'interrupted') {
        console.log('🛑 语音播放被中断（正常停止）')
        resolve()
      } else {
        console.error('❌ 播放失败:', event.error)
        reject(new Error(`语音播放失败: ${event.error}`))
      }
    }

    // 开始播放
    currentUtterance = utterance
    speechSynthesis.speak(utterance)
  })
}

// 播放文本（支持中英文混合）
export const speakText = (text, options = {}) => {
  return new Promise(async (resolve, reject) => {
    if (!isSpeechSynthesisSupported()) {
      reject(new Error('浏览器不支持语音合成'))
      return
    }

    if (!text || !text.trim()) {
      reject(new Error('文本内容为空'))
      return
    }

    // 停止当前播放
    stopSpeech()

    try {
      // 短暂延迟，确保之前的语音完全停止
      await new Promise(resolve => setTimeout(resolve, 100))

      isPlaying = true

      // 检测文本语言类型
      const language = detectTextLanguage(text)
      console.log('🔍 检测到文本语言:', language)

      if (language === 'mixed') {
        // 处理混合语言文本
        const segments = splitMixedText(text)
        console.log('📝 分割文本段落:', segments)

        for (const segment of segments) {
          if (!isPlaying) break // 如果被停止，退出循环

          if (segment.text.trim() && segment.language !== 'other') {
            await speakSingleText(segment.text, segment.language, options)
            // 段落间短暂停顿
            await new Promise(resolve => setTimeout(resolve, 200))
          }
        }
      } else {
        // 单一语言文本
        await speakSingleText(text, language, options)
      }

      resolve()
    } catch (error) {
      reject(error)
    } finally {
      isPlaying = false
      currentUtterance = null
    }
  })
}

// 检查是否正在播放
export const isSpeaking = () => {
  return isPlaying || speechSynthesis.speaking || speechSynthesis.pending
}

// 强制重置语音状态（用于异常情况）
export const resetSpeechState = () => {
  try {
    stopSpeech()
    // 额外的清理
    setTimeout(() => {
      if (speechSynthesis.speaking || speechSynthesis.pending) {
        speechSynthesis.cancel()
      }
    }, 100)
  } catch (error) {
    console.warn('重置语音状态时出错:', error)
  }
}

// 暂停播放
export const pauseSpeech = () => {
  if (speechSynthesis.speaking && !speechSynthesis.paused) {
    speechSynthesis.pause()
  }
}

// 恢复播放
export const resumeSpeech = () => {
  if (speechSynthesis.paused) {
    speechSynthesis.resume()
  }
}

// 检查文本是否为英文
export const isEnglishText = (text) => {
  if (!text) return false
  const englishRegex = /^[a-zA-Z\s\.,!?;:'"()\-0-9]+$/
  return englishRegex.test(text.trim())
}

// 检查文本是否为中文
export const isChineseText = (text) => {
  if (!text) return false
  const chineseRegex = /[\u4e00-\u9fff]/
  return chineseRegex.test(text)
}

// 检测文本的主要语言
export const detectTextLanguage = (text) => {
  if (!text || !text.trim()) return 'unknown'

  const cleanText = text.trim()
  const chineseChars = (cleanText.match(/[\u4e00-\u9fff]/g) || []).length
  const englishChars = (cleanText.match(/[a-zA-Z]/g) || []).length
  const totalChars = cleanText.length

  // 计算各语言字符占比
  const chineseRatio = chineseChars / totalChars
  const englishRatio = englishChars / totalChars

  // 判断主要语言
  if (chineseRatio > 0.3) {
    return englishRatio > 0.2 ? 'mixed' : 'chinese'
  } else if (englishRatio > 0.5) {
    return 'english'
  } else if (isEnglishText(cleanText)) {
    return 'english'
  }

  return 'mixed'
}

// 分割混合语言文本
export const splitMixedText = (text) => {
  if (!text || !text.trim()) return []

  const segments = []
  let currentSegment = ''
  let currentLang = null

  // 按字符分析
  for (let i = 0; i < text.length; i++) {
    const char = text[i]
    let charLang = 'other'

    if (/[\u4e00-\u9fff]/.test(char)) {
      charLang = 'chinese'
    } else if (/[a-zA-Z]/.test(char)) {
      charLang = 'english'
    } else if (/[\s\.,!?;:'"()\-0-9]/.test(char)) {
      charLang = currentLang || 'other' // 标点符号跟随当前语言
    }

    // 如果语言改变，保存当前段落
    if (currentLang && charLang !== currentLang && charLang !== 'other') {
      if (currentSegment.trim()) {
        segments.push({
          text: currentSegment.trim(),
          language: currentLang
        })
      }
      currentSegment = char
      currentLang = charLang
    } else {
      currentSegment += char
      if (!currentLang && charLang !== 'other') {
        currentLang = charLang
      }
    }
  }

  // 添加最后一个段落
  if (currentSegment.trim()) {
    segments.push({
      text: currentSegment.trim(),
      language: currentLang || 'other'
    })
  }

  return segments
}

// 从HTML中提取纯文本
export const extractTextFromHtml = (html) => {
  if (!html) return ''
  
  // 创建临时DOM元素
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html
  
  // 提取文本内容
  return tempDiv.textContent || tempDiv.innerText || ''
}

// 播放HTML内容中的文本（支持中英文混合）
export const speakHtmlContent = async (html, options = {}) => {
  const text = extractTextFromHtml(html)

  if (!text || !text.trim()) {
    throw new Error('没有可朗读的文本内容')
  }

  return speakText(text, options)
}

// 检查文本是否可以朗读（移除英文限制）
export const isTextReadable = (text) => {
  if (!text || !text.trim()) return false

  const language = detectTextLanguage(text)
  return ['english', 'chinese', 'mixed'].includes(language)
}

// 获取文本朗读信息
export const getTextSpeechInfo = (text) => {
  if (!text || !text.trim()) {
    return {
      readable: false,
      language: 'unknown',
      segments: []
    }
  }

  const language = detectTextLanguage(text)
  const readable = ['english', 'chinese', 'mixed'].includes(language)

  let segments = []
  if (language === 'mixed') {
    segments = splitMixedText(text)
  } else {
    segments = [{ text: text.trim(), language }]
  }

  return {
    readable,
    language,
    segments,
    estimatedDuration: Math.ceil(text.length / 10) // 粗略估算朗读时长（秒）
  }
}
