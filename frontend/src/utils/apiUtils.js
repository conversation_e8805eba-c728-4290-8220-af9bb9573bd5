/**
 * API调用工具类
 * 提供统一的API调用、错误处理和加载状态管理
 */
import { ElMessage } from 'element-plus'

/**
 * 执行API调用，自动处理加载状态和错误
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 配置选项
 * @returns {Promise} API调用结果
 */
export async function executeWithLoading(apiCall, options = {}) {
  const {
    loadingRef = null,           // loading状态的ref
    errorRef = null,             // error状态的ref
    showMessage = true,          // 是否显示错误消息
    errorMessage = '操作失败',    // 默认错误消息
    resetError = true,           // 是否重置错误状态
    logError = true,             // 是否记录错误日志
    silent = false               // 静默模式
  } = options

  if (loadingRef) loadingRef.value = true
  if (resetError && errorRef) errorRef.value = null

  try {
    const result = await apiCall()
    return result
  } catch (error) {
    if (logError && !silent) {
      console.error(`❌ ${errorMessage}:`, error)
    }
    
    if (errorRef) errorRef.value = error
    
    if (showMessage && !silent) {
      const message = error.response && error.response.data && error.response.data.message || error.message || errorMessage
      ElMessage.error(message)
    }
    
    throw error
  } finally {
    if (loadingRef) loadingRef.value = false
  }
}

/**
 * 格式化API响应
 * @param {Object} response - API响应
 * @returns {Object} 格式化后的响应
 */
export function formatApiResponse(response) {
  if (!response) {
    return {
      success: false,
      data: null,
      message: '响应为空'
    }
  }

  if (response.data && typeof response.data === 'object') {
    return {
      success: response.data.success !== false,
      data: response.data.data || response.data,
      message: response.data.message || '操作成功'
    }
  }

  return {
    success: true,
    data: response,
    message: '操作成功'
  }
}

/**
 * 重试请求
 * @param {Function} requestFn - 请求函数
 * @param {Object} options - 选项
 * @returns {Promise} 请求结果
 */
export async function retryRequest(requestFn, options = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    shouldRetry = () => true
  } = options

  let lastError
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries || !shouldRetry(error)) {
        throw error
      }
      
      if (retryDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }
  }
  
  throw lastError
}

/**
 * 创建请求配置
 * @param {Object} options - 配置选项
 * @returns {Object} 请求配置
 */
export function createRequestConfig(options = {}) {
  const {
    timeout = 30000,
    headers = {},
    withCredentials = true
  } = options

  return {
    timeout,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    },
    withCredentials
  }
}

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @returns {Object} 错误信息
 */
export function handleApiError(error) {
  if (error.code === 'NETWORK_ERROR') {
    return {
      type: 'network',
      message: '网络连接失败',
      code: error.code
    }
  }
  
  if (error.response?.status === 401) {
    return {
      type: 'auth',
      message: '认证失败',
      code: 401
    }
  }
  
  return {
    type: 'unknown',
    message: error.message || '未知错误',
    code: error.code || 'UNKNOWN'
  }
}

/**
 * 并行执行多个API调用
 * @param {Array} apiCalls - API调用函数数组
 * @param {Object} options - 配置选项
 * @returns {Promise<Array>} 所有API调用结果
 */
export async function executeParallel(apiCalls, options = {}) {
  const {
    loadingRef = null,
    showMessage = true,
    logError = true
  } = options

  if (loadingRef) loadingRef.value = true

  try {
    const results = await Promise.allSettled(apiCalls.map(call => call()))
    
    // 检查是否有失败的调用
    const failures = results.filter(result => result.status === 'rejected')
    if (failures.length > 0 && logError) {
      console.warn(`⚠️ ${failures.length} 个API调用失败:`, failures.map(f => f.reason))
    }
    
    return results
  } catch (error) {
    if (logError) {
      console.error('❌ 并行API调用失败:', error)
    }
    
    if (showMessage) {
      ElMessage.error('数据加载失败')
    }
    
    throw error
  } finally {
    if (loadingRef) loadingRef.value = false
  }
}

/**
 * 创建安全的API调用函数
 * @param {Function} apiFunction - 原始API函数
 * @param {Object} defaultOptions - 默认配置选项
 * @returns {Function} 安全的API调用函数
 */
export function createSafeApiCall(apiFunction, defaultOptions = {}) {
  return async (params, options = {}) => {
    const mergedOptions = { ...defaultOptions, ...options }
    return executeWithLoading(() => apiFunction(params), mergedOptions)
  }
}

/**
 * 数据获取工具类
 */
export class DataFetcher {
  constructor(loadingRef, errorRef) {
    this.loadingRef = loadingRef
    this.errorRef = errorRef
  }

  /**
   * 执行单个API调用
   */
  async fetch(apiCall, options = {}) {
    return executeWithLoading(apiCall, {
      loadingRef: this.loadingRef,
      errorRef: this.errorRef,
      ...options
    })
  }

  /**
   * 并行执行多个API调用
   */
  async fetchParallel(apiCalls, options = {}) {
    return executeParallel(apiCalls, {
      loadingRef: this.loadingRef,
      ...options
    })
  }

  /**
   * 刷新数据
   */
  async refresh(refreshFunctions, options = {}) {
    return this.fetchParallel(refreshFunctions, {
      showMessage: false,
      ...options
    })
  }
} 