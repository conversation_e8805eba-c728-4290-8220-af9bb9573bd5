/**
 * 网络工具函数
 */

// 获取本机局域网IP地址
export const getLocalNetworkIP = async () => {
  try {
    // 方法1: 使用WebRTC获取本地IP
    const ip = await getIPViaWebRTC()
    if (ip && isPrivateIP(ip)) {
      return ip
    }
  } catch (error) {
    console.warn('WebRTC获取IP失败:', error)
  }

  try {
    // 方法2: 通过网络请求推断IP（需要后端支持）
    const ip = await getIPViaAPI()
    if (ip && isPrivateIP(ip)) {
      return ip
    }
  } catch (error) {
    console.warn('API获取IP失败:', error)
  }

  // 方法3: 返回null，让用户手动输入
  return null
}

// 使用WebRTC获取本地IP地址
const getIPViaWebRTC = () => {
  return new Promise((resolve, reject) => {
    const rtc = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    })

    let localIP = null

    rtc.onicecandidate = (event) => {
      if (event.candidate) {
        const candidate = event.candidate.candidate
        const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/)
        
        if (ipMatch && isPrivateIP(ipMatch[1])) {
          localIP = ipMatch[1]
          rtc.close()
          resolve(localIP)
        }
      }
    }

    rtc.createDataChannel('test')
    rtc.createOffer()
      .then(offer => rtc.setLocalDescription(offer))
      .catch(reject)

    // 超时处理
    setTimeout(() => {
      rtc.close()
      if (!localIP) {
        reject(new Error('WebRTC获取IP超时'))
      }
    }, 3000)
  })
}

// 通过API获取IP（需要后端支持）
const getIPViaAPI = async () => {
  try {
    // 这里可以调用后端API获取客户端IP
    // const response = await fetch('/api/client-ip')
    // const data = await response.json()
    // return data.ip
    
    // 暂时返回null，表示不支持
    return null
  } catch (error) {
    throw new Error('API获取IP失败')
  }
}

// 判断是否为私有IP地址
export const isPrivateIP = (ip) => {
  if (!ip) return false
  
  const parts = ip.split('.').map(Number)
  if (parts.length !== 4) return false
  
  // 检查是否为私有IP范围
  // 10.0.0.0 - **************
  if (parts[0] === 10) return true
  
  // ********** - **************
  if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true
  
  // *********** - ***************
  if (parts[0] === 192 && parts[1] === 168) return true
  
  return false
}

// 生成可访问的URL
export const generateAccessibleUrl = async () => {
  const currentUrl = window.location
  const hostname = currentUrl.hostname
  const port = currentUrl.port
  const protocol = currentUrl.protocol
  
  // 如果已经是公网IP或域名，直接使用
  if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
    return currentUrl.origin
  }
  
  // 尝试获取局域网IP
  const localIP = await getLocalNetworkIP()
  
  if (localIP) {
    const portStr = port ? `:${port}` : ''
    return `${protocol}//${localIP}${portStr}`
  }
  
  // 如果无法获取IP，返回需要手动替换的模板
  const portStr = port ? `:${port}` : ''
  return `${protocol}//[请替换为您的局域网IP]${portStr}`
}

// 验证IP地址格式
export const isValidIP = (ip) => {
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/
  
  if (!ipRegex.test(ip)) return false
  
  const parts = ip.split('.').map(Number)
  return parts.every(part => part >= 0 && part <= 255)
}

// 获取常见的局域网IP段提示
export const getCommonIPRanges = () => {
  return [
    { range: '192.168.1.x', description: '最常见的家用路由器IP段' },
    { range: '192.168.0.x', description: '常见的家用路由器IP段' },
    { range: '10.0.0.x', description: '企业网络常用IP段' },
    { range: '172.16.x.x - 172.31.x.x', description: '企业内网IP段' }
  ]
}

// 生成IP检测指导
export const getIPDetectionGuide = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (userAgent.includes('windows')) {
    return {
      os: 'Windows',
      steps: [
        '按 Win + R 打开运行对话框',
        '输入 cmd 并按回车',
        '在命令提示符中输入: ipconfig',
        '查找 "以太网适配器" 或 "无线局域网适配器" 下的 "IPv4 地址"'
      ]
    }
  } else if (userAgent.includes('mac')) {
    return {
      os: 'macOS',
      steps: [
        '点击苹果菜单 → 系统偏好设置',
        '点击 "网络"',
        '选择当前连接的网络（WiFi或以太网）',
        '查看右侧显示的 IP 地址'
      ]
    }
  } else if (userAgent.includes('linux')) {
    return {
      os: 'Linux',
      steps: [
        '打开终端',
        '输入命令: ip addr show 或 ifconfig',
        '查找网络接口（如 eth0, wlan0）下的 inet 地址',
        '选择以 192.168、10.0 或 172.16-31 开头的地址'
      ]
    }
  } else {
    return {
      os: '通用',
      steps: [
        '打开系统的网络设置',
        '查看当前连接的网络详情',
        '找到本机的IP地址',
        '确保是以 192.168、10.0 或 172.16-31 开头的局域网地址'
      ]
    }
  }
}

// 测试IP连通性（通过尝试加载资源）
export const testIPConnectivity = async (ip, port) => {
  return new Promise((resolve) => {
    const testUrl = `http://${ip}${port ? `:${port}` : ''}/favicon.ico`
    const img = new Image()
    
    const timeout = setTimeout(() => {
      resolve(false)
    }, 3000)
    
    img.onload = () => {
      clearTimeout(timeout)
      resolve(true)
    }
    
    img.onerror = () => {
      clearTimeout(timeout)
      resolve(false)
    }
    
    img.src = testUrl
  })
}
