import { marked } from 'marked';
import DOMPurify from 'dompurify';
import katex from 'katex';
import 'katex/dist/katex.min.css';

const katexRules = {
  '\\\\': '\\\\\\\\',
  '\\ge': '\\\\ge',
  '\\le': '\\\\le',
  '\\ne': '\\\\ne',
  '\\pm': '\\\\pm',
  '\\times': '\\\\times',
  '\\div': '\\\\div',
};

function applyKatexRules(content) {
  let processedContent = content;
  for (const [key, value] of Object.entries(katexRules)) {
    processedContent = processedContent.replace(new RegExp(key, 'g'), value);
  }
  return processedContent;
}

function renderKatex(text) {
  return text.replace(/\$\$(.*?)\$\$/g, (match, expression) => {
    try {
      return katex.renderToString(expression, { throwOnError: false, displayMode: true });
    } catch (e) {
      return match;
    }
  }).replace(/\$(.*?)\$/g, (match, expression) => {
    try {
      return katex.renderToString(expression, { throwOnError: false, displayMode: false });
    } catch (e) {
      return match;
    }
  });
}

marked.setOptions({
  breaks: true,
  gfm: true,
  sanitize: false,
});

export function renderMarkdown(content) {
  if (!content) {
    return '<p class="empty-content">暂无内容</p>';
  }

  try {
    let processedContent = applyKatexRules(content);
    processedContent = renderKatex(processedContent);
    const html = marked(processedContent);

    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'strong', 'em', 'u', 's', 'del',
        'ul', 'ol', 'li',
        'blockquote', 'pre', 'code',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'a', 'img',
        'div', 'span', 'katex', 'math', 'semantics', 'mrow', 'mo', 'mi', 'mn', 'msup', 'msub', 'mfrac', 'msqrt', 'mroot', 'annotation', 'mstyle'
      ],
      ALLOWED_ATTR: [
        'href', 'src', 'alt', 'title', 'class', 'id',
        'target', 'rel', 'encoding'
      ]
    });
  } catch (error) {
    console.error('Markdown 渲染失败:', error);
    return '<p class="error-content">内容渲染失败</p>';
  }
}