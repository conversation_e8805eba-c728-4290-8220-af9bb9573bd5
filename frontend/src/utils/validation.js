/**
 * 参数验证工具函数
 */

/**
 * 验证ID参数是否有效
 * @param {any} id - 要验证的ID
 * @param {string} paramName - 参数名称（用于错误信息）
 * @returns {boolean} 是否有效
 */
export function isValidId(id, paramName = 'ID') {
  // 检查是否为null、undefined、空字符串
  if (id === null || id === undefined || id === '') {
    console.warn(`⚠️  ${paramName} 为空:`, id)
    return false
  }
  
  // 检查是否为字符串 "null" 或 "undefined"
  if (typeof id === 'string' && (id === 'null' || id === 'undefined')) {
    console.warn(`⚠️  ${paramName} 为字符串空值:`, id)
    return false
  }
  
  // 转换为数字并检查是否为有效的正整数
  const numId = Number(id)
  if (isNaN(numId) || numId <= 0 || !Number.isInteger(numId)) {
    console.warn(`⚠️  ${paramName} 不是有效的正整数:`, id, '-> 转换后:', numId)
    return false
  }
  
  return true
}

/**
 * 验证并转换ID参数
 * @param {any} id - 要验证的ID
 * @param {string} paramName - 参数名称（用于错误信息）
 * @returns {number|null} 转换后的数字ID，无效时返回null
 */
export function validateAndConvertId(id, paramName = 'ID') {
  if (!isValidId(id, paramName)) {
    return null
  }
  return Number(id)
}

/**
 * 验证路由参数
 * @param {Object} params - 路由参数对象
 * @param {Array<string>} requiredParams - 必需的参数名列表
 * @returns {Object} 验证结果 { valid: boolean, errors: Array<string>, validParams: Object }
 */
export function validateRouteParams(params, requiredParams = []) {
  const errors = []
  const validParams = {}
  
  for (const paramName of requiredParams) {
    const paramValue = params[paramName]
    
    if (paramName.toLowerCase().includes('id')) {
      // ID类型参数的特殊验证
      const validId = validateAndConvertId(paramValue, paramName)
      if (validId === null) {
        errors.push(`参数 ${paramName} 无效: ${paramValue}`)
      } else {
        validParams[paramName] = validId
      }
    } else {
      // 其他参数的基本验证
      if (paramValue === null || paramValue === undefined || paramValue === '') {
        errors.push(`参数 ${paramName} 不能为空`)
      } else {
        validParams[paramName] = paramValue
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
    validParams
  }
}

/**
 * 验证API调用参数
 * @param {Object} apiParams - API参数对象
 * @param {Object} rules - 验证规则
 * @returns {Object} 验证结果
 */
export function validateApiParams(apiParams, rules = {}) {
  const errors = []
  const validParams = {}
  
  for (const [paramName, paramValue] of Object.entries(apiParams)) {
    const rule = rules[paramName] || {}
    
    // 检查必需参数
    if (rule.required && (paramValue === null || paramValue === undefined || paramValue === '')) {
      errors.push(`参数 ${paramName} 是必需的`)
      continue
    }
    
    // 跳过非必需的空参数
    if (!rule.required && (paramValue === null || paramValue === undefined || paramValue === '')) {
      continue
    }
    
    // ID类型验证
    if (rule.type === 'id') {
      const validId = validateAndConvertId(paramValue, paramName)
      if (validId === null) {
        errors.push(`参数 ${paramName} 必须是有效的ID: ${paramValue}`)
      } else {
        validParams[paramName] = validId
      }
      continue
    }
    
    // 数字类型验证
    if (rule.type === 'number') {
      const num = Number(paramValue)
      if (isNaN(num)) {
        errors.push(`参数 ${paramName} 必须是数字: ${paramValue}`)
      } else {
        validParams[paramName] = num
      }
      continue
    }
    
    // 字符串类型验证
    if (rule.type === 'string') {
      if (typeof paramValue !== 'string') {
        errors.push(`参数 ${paramName} 必须是字符串: ${paramValue}`)
      } else {
        validParams[paramName] = paramValue.trim()
      }
      continue
    }
    
    // 默认直接使用原值
    validParams[paramName] = paramValue
  }
  
  return {
    valid: errors.length === 0,
    errors,
    validParams
  }
}

/**
 * 创建安全的API调用函数
 * @param {Function} apiFunction - 原始API函数
 * @param {Object} paramRules - 参数验证规则
 * @returns {Function} 包装后的安全API函数
 */
export function createSafeApiCall(apiFunction, paramRules = {}) {
  return async (...args) => {
    // 构建参数对象
    const paramNames = Object.keys(paramRules)
    const apiParams = {}
    
    paramNames.forEach((name, index) => {
      if (index < args.length) {
        apiParams[name] = args[index]
      }
    })
    
    // 验证参数
    const validation = validateApiParams(apiParams, paramRules)
    
    if (!validation.valid) {
      const error = new Error(`API参数验证失败: ${validation.errors.join(', ')}`)
      error.validationErrors = validation.errors
      throw error
    }
    
    // 使用验证后的参数调用API
    const validArgs = paramNames.map(name => validation.validParams[name])
    return await apiFunction(...validArgs)
  }
}

/**
 * 检查对象是否为空或无效
 * @param {any} obj - 要检查的对象
 * @returns {boolean} 是否为空或无效
 */
export function isEmpty(obj) {
  if (obj === null || obj === undefined) return true
  if (typeof obj === 'string') return obj.trim() === '' || obj === 'null' || obj === 'undefined'
  if (typeof obj === 'number') return isNaN(obj)
  if (Array.isArray(obj)) return obj.length === 0
  if (typeof obj === 'object') return Object.keys(obj).length === 0
  return false
}

/**
 * 安全地获取嵌套对象属性
 * @param {Object} obj - 源对象
 * @param {string} path - 属性路径，如 'a.b.c'
 * @param {any} defaultValue - 默认值
 * @returns {any} 属性值或默认值
 */
export function safeGet(obj, path, defaultValue = null) {
  if (!obj || typeof obj !== 'object') return defaultValue
  
  const keys = path.split('.')
  let current = obj
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return defaultValue
    }
    current = current[key]
  }
  
  return current
}
