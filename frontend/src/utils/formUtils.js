/**
 * 表单处理工具类
 * 提供常用的表单验证、重置、提交等功能
 */
import { ref, reactive } from 'vue'

/**
 * 创建表单状态管理
 * @param {Object} initialData - 初始表单数据
 * @param {Object} rules - 验证规则
 * @returns {Object} 表单状态管理对象
 */
export function createFormState(initialData = {}, rules = {}) {
  const formData = reactive({ ...initialData })
  const formRef = ref(null)
  const submitting = ref(false)
  const validating = ref(false)

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.keys(initialData).forEach(key => {
      formData[key] = initialData[key]
    })
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  /**
   * 清空表单
   */
  const clearForm = () => {
    Object.keys(formData).forEach(key => {
      if (Array.isArray(formData[key])) {
        formData[key] = []
      } else if (typeof formData[key] === 'object' && formData[key] !== null) {
        formData[key] = {}
      } else {
        formData[key] = ''
      }
    })
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }

  /**
   * 验证表单
   */
  const validateForm = async () => {
    if (!formRef.value) return false
    
    validating.value = true
    try {
      await formRef.value.validate()
      return true
    } catch (error) {
      console.warn('表单验证失败:', error)
      return false
    } finally {
      validating.value = false
    }
  }

  /**
   * 设置表单数据
   */
  const setFormData = (data) => {
    Object.keys(data).forEach(key => {
      if (key in formData) {
        formData[key] = data[key]
      }
    })
  }

  /**
   * 获取表单数据副本
   */
  const getFormData = () => {
    return JSON.parse(JSON.stringify(formData))
  }

  return {
    formData,
    formRef,
    submitting,
    validating,
    rules,
    resetForm,
    clearForm,
    validateForm,
    setFormData,
    getFormData
  }
}

/**
 * 安全的表单提交
 * @param {Function} submitFn - 提交函数
 * @param {Object} formState - 表单状态对象
 * @param {Object} options - 提交选项
 */
export async function safeSubmit(submitFn, formState, options = {}) {
  const {
    validateFirst = true,
    resetAfterSuccess = false,
    clearAfterSuccess = false,
    showSuccessMessage = true,
    showErrorMessage = true,
    onSuccess = null,
    onError = null
  } = options

  if (validateFirst) {
    const isValid = await formState.validateForm()
    if (!isValid) {
      return false
    }
  }

  formState.submitting.value = true
  
  try {
    const result = await submitFn(formState.getFormData())
    
    if (resetAfterSuccess) {
      formState.resetForm()
    } else if (clearAfterSuccess) {
      formState.clearForm()
    }

    if (onSuccess && typeof onSuccess === 'function') {
      onSuccess(result)
    }

    return result
  } catch (error) {
    console.error('表单提交失败:', error)
    
    if (onError && typeof onError === 'function') {
      onError(error)
    }
    
    throw error
  } finally {
    formState.submitting.value = false
  }
}

/**
 * 常用验证规则
 */
export const commonRules = {
  required: { required: true, message: '此字段为必填项', trigger: 'blur' },
  email: { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  url: { type: 'url', message: '请输入正确的URL', trigger: 'blur' },
  
  minLength: (min) => ({ min, message: `长度不能少于${min}个字符`, trigger: 'blur' }),
  maxLength: (max) => ({ max, message: `长度不能超过${max}个字符`, trigger: 'blur' }),
  
  numberRange: (min, max) => ({
    type: 'number',
    min,
    max,
    message: `数值必须在${min}-${max}之间`,
    trigger: 'blur'
  }),
  
  score: {
    type: 'number',
    min: 0,
    max: 100,
    message: '分数必须在0-100之间',
    trigger: 'blur'
  },
  
  positiveInteger: {
    type: 'number',
    min: 1,
    message: '必须是正整数',
    trigger: 'blur'
  },

  date: {
    type: 'date',
    required: true,
    message: '请选择日期',
    trigger: 'change'
  }
}

/**
 * 创建动态规则
 * @param {string} field - 字段名
 * @param {Array} ruleTypes - 规则类型数组
 * @returns {Array} 验证规则数组
 */
export function createRules(field, ruleTypes = []) {
  const rules = []
  
  ruleTypes.forEach(ruleType => {
    if (typeof ruleType === 'string') {
      if (commonRules[ruleType]) {
        rules.push(commonRules[ruleType])
      }
    } else if (typeof ruleType === 'object') {
      rules.push(ruleType)
    }
  })
  
  return rules
}

/**
 * 表单数据差异检测
 * @param {Object} original - 原始数据
 * @param {Object} current - 当前数据
 * @returns {Object} 差异对象
 */
export function detectFormChanges(original, current) {
  const changes = {}
  const hasChanges = Object.keys(current).some(key => {
    if (current[key] !== original[key]) {
      changes[key] = {
        from: original[key],
        to: current[key]
      }
      return true
    }
    return false
  })
  
  return { hasChanges, changes }
}

/**
 * 表单数据格式化
 */
export const formatters = {
  /**
   * 移除空白字符
   */
  trim: (value) => typeof value === 'string' ? value.trim() : value,
  
  /**
   * 转换为数字
   */
  toNumber: (value) => {
    const num = Number(value)
    return isNaN(num) ? value : num
  },
  
  /**
   * 格式化日期
   */
  formatDate: (value, format = 'YYYY-MM-DD') => {
    if (!value) return ''
    const date = new Date(value)
    return date.toISOString().split('T')[0]
  },
  
  /**
   * 移除HTML标签
   */
  stripHtml: (value) => {
    if (typeof value !== 'string') return value
    return value.replace(/<[^>]*>/g, '')
  }
}

/**
 * 批量格式化表单数据
 * @param {Object} data - 表单数据
 * @param {Object} formatConfig - 格式化配置
 * @returns {Object} 格式化后的数据
 */
export function formatFormData(data, formatConfig = {}) {
  const formatted = { ...data }
  
  Object.keys(formatConfig).forEach(field => {
    if (field in formatted) {
      const formatter = formatConfig[field]
      if (typeof formatter === 'function') {
        formatted[field] = formatter(formatted[field])
      } else if (typeof formatter === 'string' && formatters[formatter]) {
        formatted[field] = formatters[formatter](formatted[field])
      }
    }
  })
  
  return formatted
} 