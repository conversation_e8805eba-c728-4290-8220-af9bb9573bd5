/**
 * 知识点路径工具函数 - 重构版本
 * 专门处理知识点路径的格式化、显示和计算
 */

/**
 * 路径分隔符常量
 */
export const PATH_SEPARATOR = ' > '

/**
 * 默认配置
 */
export const DEFAULT_PATH_CONFIG = {
  maxLength: 50,
  placeholder: '暂无路径',
  showTooltip: false,
  separator: PATH_SEPARATOR
}

/**
 * 格式化知识点路径显示
 * @param {string} path - 路径字符串
 * @param {Object} options - 配置选项
 * @returns {string|Object} 格式化后的路径字符串或包含display和tooltip的对象
 */
export function formatKnowledgePathDisplay(path, options = {}) {
  const config = { ...DEFAULT_PATH_CONFIG, ...options }
  const { maxLength, placeholder, showTooltip } = config

  if (!path || path.trim() === '') {
    return placeholder
  }

  const trimmedPath = path.trim()

  if (trimmedPath.length <= maxLength) {
    return trimmedPath
  }

  if (showTooltip) {
    return {
      display: trimmedPath.substring(0, maxLength - 3) + '...',
      tooltip: trimmedPath
    }
  }

  return trimmedPath.substring(0, maxLength - 3) + '...'
}

/**
 * 批量格式化知识点路径
 * @param {Array} knowledgePoints - 知识点数组
 * @param {Object} pathMap - 路径映射对象 {uid: path}
 * @param {Object} options - 配置选项
 * @returns {Array} 带格式化路径的知识点数组
 */
export function batchFormatKnowledgePaths(knowledgePoints, pathMap, options = {}) {
  if (!Array.isArray(knowledgePoints) || !pathMap) {
    return knowledgePoints || []
  }

  return knowledgePoints.map(point => {
    const path = pathMap[point.uid] || ''
    const formattedPath = formatKnowledgePathDisplay(path, options)

    return {
      ...point,
      path,
      formattedPath: typeof formattedPath === 'string' ? formattedPath : formattedPath.display,
      pathTooltip: typeof formattedPath === 'object' ? formattedPath.tooltip : formattedPath
    }
  })
}

/**
 * 生成知识点路径字符串
 * @param {Array} pathArray - 路径数组
 * @param {Object} options - 配置选项
 * @returns {string} 路径字符串
 */
export function generateKnowledgePath(pathArray, options = {}) {
  const config = { ...DEFAULT_PATH_CONFIG, ...options }
  const { separator, excludeRoot = true, excludeSelf = false } = config

  if (!Array.isArray(pathArray) || pathArray.length === 0) {
    return ''
  }

  let path = [...pathArray]

  if (excludeRoot && path.length > 0) {
    path = path.slice(1)
  }

  if (excludeSelf && path.length > 0) {
    path = path.slice(0, -1)
  }

  return path.join(separator)
}

/**
 * 解析路径字符串为数组
 * @param {string} pathString - 路径字符串
 * @param {string} separator - 分隔符
 * @returns {Array} 路径数组
 */
export function parsePathString(pathString, separator = PATH_SEPARATOR) {
  if (!pathString || typeof pathString !== 'string') {
    return []
  }

  return pathString.split(separator).map(part => part.trim()).filter(Boolean)
}

/**
 * 获取路径的父级路径
 * @param {string} fullPath - 完整路径
 * @param {string} separator - 分隔符
 * @returns {string} 父级路径
 */
export function getParentPath(fullPath, separator = PATH_SEPARATOR) {
  if (!fullPath) return ''
  
  const pathArray = parsePathString(fullPath, separator)
  if (pathArray.length <= 1) return ''
  
  return pathArray.slice(0, -1).join(separator)
}

/**
 * 获取路径的最后一级（叶子节点名称）
 * @param {string} fullPath - 完整路径
 * @param {string} separator - 分隔符
 * @returns {string} 叶子节点名称
 */
export function getLeafName(fullPath, separator = PATH_SEPARATOR) {
  if (!fullPath) return ''
  
  const pathArray = parsePathString(fullPath, separator)
  return pathArray[pathArray.length - 1] || ''
}

/**
 * 计算两个路径的公共前缀
 * @param {string} path1 - 路径1
 * @param {string} path2 - 路径2
 * @param {string} separator - 分隔符
 * @returns {string} 公共前缀路径
 */
export function getCommonPathPrefix(path1, path2, separator = PATH_SEPARATOR) {
  if (!path1 || !path2) return ''
  
  const array1 = parsePathString(path1, separator)
  const array2 = parsePathString(path2, separator)
  
  const commonParts = []
  const minLength = Math.min(array1.length, array2.length)
  
  for (let i = 0; i < minLength; i++) {
    if (array1[i] === array2[i]) {
      commonParts.push(array1[i])
    } else {
      break
    }
  }
  
  return commonParts.join(separator)
}

/**
 * 检查路径是否为另一个路径的子路径
 * @param {string} childPath - 子路径
 * @param {string} parentPath - 父路径
 * @param {string} separator - 分隔符
 * @returns {boolean} 是否为子路径
 */
export function isSubPath(childPath, parentPath, separator = PATH_SEPARATOR) {
  if (!childPath || !parentPath) return false
  
  const childArray = parsePathString(childPath, separator)
  const parentArray = parsePathString(parentPath, separator)
  
  if (childArray.length <= parentArray.length) return false
  
  for (let i = 0; i < parentArray.length; i++) {
    if (childArray[i] !== parentArray[i]) {
      return false
    }
  }
  
  return true
}

/**
 * 路径深度计算
 * @param {string} path - 路径字符串
 * @param {string} separator - 分隔符
 * @returns {number} 路径深度
 */
export function getPathDepth(path, separator = PATH_SEPARATOR) {
  if (!path) return 0
  return parsePathString(path, separator).length
}

/**
 * 路径美化显示（智能省略中间部分）
 * @param {string} path - 路径字符串
 * @param {number} maxLength - 最大长度
 * @param {string} separator - 分隔符
 * @returns {Object} 包含display和tooltip的对象
 */
export function beautifyPathDisplay(path, maxLength = 50, separator = PATH_SEPARATOR) {
  if (!path || path.length <= maxLength) {
    return {
      display: path || '',
      tooltip: path || ''
    }
  }

  const pathArray = parsePathString(path, separator)

  if (pathArray.length <= 2) {
    // 如果只有1-2级，直接截断
    return {
      display: path.substring(0, maxLength - 3) + '...',
      tooltip: path
    }
  }

  // 保留首尾，省略中间
  const first = pathArray[0]
  const last = pathArray[pathArray.length - 1]
  const abbreviated = `${first}...${last}`

  if (abbreviated.length <= maxLength) {
    return {
      display: abbreviated,
      tooltip: path
    }
  }

  // 如果还是太长，只保留最后一级
  return {
    display: last.length <= maxLength - 3 ? `...${last}` : last.substring(0, maxLength - 3) + '...',
    tooltip: path
  }
}

/**
 * 路径验证
 * @param {string} path - 路径字符串
 * @param {Object} options - 验证选项
 * @returns {Object} 验证结果
 */
export function validatePath(path, options = {}) {
  const { 
    minDepth = 0, 
    maxDepth = 10, 
    allowEmpty = true,
    separator = PATH_SEPARATOR 
  } = options

  const errors = []

  if (!path) {
    if (!allowEmpty) {
      errors.push('路径不能为空')
    }
    return { valid: errors.length === 0, errors }
  }

  if (typeof path !== 'string') {
    errors.push('路径必须是字符串')
    return { valid: false, errors }
  }

  const depth = getPathDepth(path, separator)
  
  if (depth < minDepth) {
    errors.push(`路径深度不能小于${minDepth}`)
  }
  
  if (depth > maxDepth) {
    errors.push(`路径深度不能大于${maxDepth}`)
  }

  const pathArray = parsePathString(path, separator)

  // 检查原始路径是否包含空的部分（parsePathString已经过滤了空部分）
  const originalParts = path.split(separator).map(part => part.trim())
  const hasEmptyParts = originalParts.some(part => !part)

  if (hasEmptyParts) {
    errors.push('路径中不能包含空的部分')
  }

  return {
    valid: errors.length === 0,
    errors,
    depth,
    parts: pathArray
  }
}
