/**
 * 移动端测试辅助工具
 */

/**
 * 模拟移动设备用户代理
 */
export const mobileUserAgents = {
  // 安卓设备
  'android-7-chrome-51': 'Mozilla/5.0 (Linux; Android 7.0; SM-G930V Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.90 Mobile Safari/537.36',
  'android-6-chrome-44': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.157 Mobile Safari/537.36',
  'android-5-webview': 'Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/******** Mobile Safari/537.36',
  'android-modern': 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
  
  // iOS设备 (对比测试)
  'ios-safari': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
};

/**
 * 设置用户代理
 */
export const setUserAgent = (userAgent) => {
  // 注意：这种方法只对某些检测有效，完整测试还是需要真实设备
  Object.defineProperty(navigator, 'userAgent', {
    get: function () { 
      return userAgent;
    },
    configurable: true
  });
  
  console.log('🔧 已设置User Agent:', userAgent);
  
  // 触发页面重新加载以应用新的User Agent
  if (confirm('需要刷新页面以应用新的设备模拟。是否继续？')) {
    window.location.reload();
  }
};

/**
 * 创建测试面板
 */
export const createMobileTestPanel = () => {
  // 检查是否已存在测试面板
  if (document.getElementById('mobile-test-panel')) {
    return;
  }

  const panel = document.createElement('div');
  panel.id = 'mobile-test-panel';
  panel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px;
    border-radius: 8px;
    z-index: 10000;
    font-family: monospace;
    font-size: 12px;
    max-width: 300px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `;

  const title = document.createElement('h3');
  title.textContent = '📱 移动端测试工具';
  title.style.cssText = 'margin: 0 0 10px 0; font-size: 14px;';
  panel.appendChild(title);

  // 当前设备信息
  const deviceInfo = document.createElement('div');
  deviceInfo.style.cssText = 'margin-bottom: 15px; padding: 8px; background: rgba(255, 255, 255, 0.1); border-radius: 4px;';
  deviceInfo.innerHTML = `
    <strong>当前设备信息:</strong><br>
    User Agent: ${navigator.userAgent.substring(0, 60)}...<br>
    触摸支持: ${'ontouchstart' in window ? '✅' : '❌'}<br>
    屏幕尺寸: ${window.innerWidth}x${window.innerHeight}<br>
    像素比: ${window.devicePixelRatio}
  `;
  panel.appendChild(deviceInfo);

  // 快速测试按钮
  const testButtons = document.createElement('div');
  testButtons.innerHTML = `
    <div style="margin-bottom: 10px;">
      <strong>快速测试:</strong>
    </div>
    <button onclick="testTouch()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">触摸测试</button>
    <button onclick="testZoom()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">缩放测试</button>
    <button onclick="clearConsole()" style="margin: 2px; padding: 5px 8px; font-size: 11px;">清空控制台</button>
  `;
  panel.appendChild(testButtons);

  // 设备切换
  const deviceSwitcher = document.createElement('div');
  deviceSwitcher.style.cssText = 'margin-top: 15px;';
  deviceSwitcher.innerHTML = `
    <div style="margin-bottom: 8px;"><strong>设备模拟:</strong></div>
    <select id="device-selector" style="width: 100%; padding: 4px; margin-bottom: 8px;">
      <option value="">选择设备</option>
      <option value="android-7-chrome-51">Android 7.0 Chrome 51 (旧)</option>
      <option value="android-6-chrome-44">Android 6.0 Chrome 44 (旧)</option>
      <option value="android-5-webview">Android 5.0 WebView (很旧)</option>
      <option value="android-modern">Android 11 Chrome 91 (新)</option>
      <option value="ios-safari">iOS Safari (对比)</option>
    </select>
    <button onclick="applyDevice()" style="width: 100%; padding: 5px;">应用设备</button>
  `;
  panel.appendChild(deviceSwitcher);

  // 关闭按钮
  const closeBtn = document.createElement('button');
  closeBtn.textContent = '✕';
  closeBtn.style.cssText = `
    position: absolute;
    top: 5px;
    right: 5px;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
  `;
  closeBtn.onclick = () => panel.remove();
  panel.appendChild(closeBtn);

  document.body.appendChild(panel);

  // 全局测试函数
  window.testTouch = () => {
    console.log('🧪 触摸测试开始');
    console.log('- 触摸事件支持:', 'ontouchstart' in window);
    console.log('- 指针事件支持:', 'onpointerdown' in window);
    console.log('- 多点触控支持:', navigator.maxTouchPoints || 'unknown');
    
    // 模拟触摸事件
    const img = document.querySelector('.quiz-image');
    if (img) {
      console.log('- 找到测试图片，模拟触摸...');
      const touchEvent = new TouchEvent('touchstart', {
        touches: [{
          clientX: img.offsetLeft + img.offsetWidth / 2,
          clientY: img.offsetTop + img.offsetHeight / 2
        }]
      });
      img.dispatchEvent(touchEvent);
    }
  };

  window.testZoom = () => {
    console.log('🔍 缩放测试开始');
    const img = document.querySelector('.quiz-image');
    if (img) {
      console.log('- 找到测试图片，执行点击缩放...');
      img.click();
    } else {
      console.log('- 未找到测试图片，请先进入趣味考试页面');
    }
  };

  window.clearConsole = () => {
    console.clear();
    console.log('📱 控制台已清空，开始新的测试...');
  };

  window.applyDevice = () => {
    const selector = document.getElementById('device-selector');
    const deviceKey = selector.value;
    if (deviceKey && mobileUserAgents[deviceKey]) {
      setUserAgent(mobileUserAgents[deviceKey]);
    }
  };

  console.log('📱 移动端测试面板已创建');
};

/**
 * 检测移动端兼容性问题
 */
export const checkMobileCompatibility = () => {
  const issues = [];
  
  // 检查CSS支持
  if (!CSS.supports('display', 'flex')) {
    issues.push('不支持Flexbox布局');
  }
  
  if (!CSS.supports('transform', 'scale(1.5)')) {
    issues.push('不支持CSS Transform');
  }
  
  // 检查JavaScript API
  if (!window.TouchEvent) {
    issues.push('不支持TouchEvent API');
  }
  
  if (!window.requestAnimationFrame) {
    issues.push('不支持requestAnimationFrame');
  }
  
  // 检查WebView版本
  const userAgent = navigator.userAgent;
  const chromeMatch = userAgent.match(/Chrome\/([0-9]+)/);
  if (chromeMatch) {
    const chromeVersion = parseInt(chromeMatch[1]);
    if (chromeVersion < 79) {
      issues.push(`Chrome版本过低: ${chromeVersion} (建议79+)`);
    }
  }
  
  console.log('🔍 兼容性检查结果:');
  if (issues.length === 0) {
    console.log('✅ 未发现兼容性问题');
  } else {
    console.log('⚠️ 发现以下问题:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }
  
  return issues;
};

/**
 * 自动初始化测试工具（开发环境）
 */
export const initMobileTestingInDev = () => {
  if (import.meta.env.DEV) {
    console.log('🛠️ 开发环境：初始化移动端测试工具');
    
    // 添加快捷键
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+M 打开移动测试面板
      if (e.ctrlKey && e.shiftKey && e.key === 'M') {
        e.preventDefault();
        createMobileTestPanel();
      }
      
      // Ctrl+Shift+C 检查兼容性
      if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        checkMobileCompatibility();
      }
    });
    
    console.log('快捷键：');
    console.log('  Ctrl+Shift+M: 打开移动测试面板');
    console.log('  Ctrl+Shift+C: 检查移动兼容性');
  }
}; 