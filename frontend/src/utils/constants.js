/**
 * 应用常量定义
 */

// 状态映射
export const STATUS_MAPPINGS = {
  // 考卷状态
  EXAM_STATUS: {
    GENERATED: 'generated',
    REVIEWED: 'reviewed',
    PUBLISHED: 'published'
  },
  
  // 项目状态
  PROJECT_STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    DRAFT: 'draft'
  },
  
  // 难度等级
  DIFFICULTY: {
    EASY: 'easy',
    MEDIUM: 'medium',
    HARD: 'hard',
    COMPETITION: 'competition',
    MIDDLE_SCHOOL_EXAM: 'middle_school_exam',
    HIGH_SCHOOL_EXAM: 'high_school_exam'
  },
  
  // 分数等级
  SCORE_LEVELS: {
    LEVEL_1: 1,
    LEVEL_2: 2,
    LEVEL_3: 3,
    LEVEL_4: 4,
    LEVEL_5: 5
  }
}

// 状态文本映射
export const STATUS_TEXT = {
  // 考卷状态
  [STATUS_MAPPINGS.EXAM_STATUS.GENERATED]: '已生成',
  [STATUS_MAPPINGS.EXAM_STATUS.REVIEWED]: '已审核',
  [STATUS_MAPPINGS.EXAM_STATUS.PUBLISHED]: '已发布',
  
  // 项目状态
  [STATUS_MAPPINGS.PROJECT_STATUS.ACTIVE]: '活跃',
  [STATUS_MAPPINGS.PROJECT_STATUS.INACTIVE]: '非活跃',
  [STATUS_MAPPINGS.PROJECT_STATUS.DRAFT]: '草稿',
  
  // 难度等级
  [STATUS_MAPPINGS.DIFFICULTY.EASY]: '简单',
  [STATUS_MAPPINGS.DIFFICULTY.MEDIUM]: '中等',
  [STATUS_MAPPINGS.DIFFICULTY.HARD]: '困难',
  [STATUS_MAPPINGS.DIFFICULTY.COMPETITION]: '竞赛',
  [STATUS_MAPPINGS.DIFFICULTY.MIDDLE_SCHOOL_EXAM]: '中考',
  [STATUS_MAPPINGS.DIFFICULTY.HIGH_SCHOOL_EXAM]: '高考'
}

// 标签类型映射
export const TAG_TYPES = {
  // 考卷状态
  [STATUS_MAPPINGS.EXAM_STATUS.GENERATED]: '',
  [STATUS_MAPPINGS.EXAM_STATUS.REVIEWED]: 'success',
  [STATUS_MAPPINGS.EXAM_STATUS.PUBLISHED]: 'info',
  
  // 项目状态
  [STATUS_MAPPINGS.PROJECT_STATUS.ACTIVE]: 'success',
  [STATUS_MAPPINGS.PROJECT_STATUS.INACTIVE]: 'info',
  [STATUS_MAPPINGS.PROJECT_STATUS.DRAFT]: 'warning',
  
  // 难度等级
  [STATUS_MAPPINGS.DIFFICULTY.EASY]: 'success',
  [STATUS_MAPPINGS.DIFFICULTY.MEDIUM]: 'warning',
  [STATUS_MAPPINGS.DIFFICULTY.HARD]: 'danger',
  [STATUS_MAPPINGS.DIFFICULTY.COMPETITION]: 'danger',
  [STATUS_MAPPINGS.DIFFICULTY.MIDDLE_SCHOOL_EXAM]: 'info',
  [STATUS_MAPPINGS.DIFFICULTY.HIGH_SCHOOL_EXAM]: 'info',
  
  // 分数等级
  [STATUS_MAPPINGS.SCORE_LEVELS.LEVEL_1]: 'danger',
  [STATUS_MAPPINGS.SCORE_LEVELS.LEVEL_2]: 'warning',
  [STATUS_MAPPINGS.SCORE_LEVELS.LEVEL_3]: '',
  [STATUS_MAPPINGS.SCORE_LEVELS.LEVEL_4]: 'info',
  [STATUS_MAPPINGS.SCORE_LEVELS.LEVEL_5]: 'success'
}

// 表单验证规则
export const VALIDATION_RULES = {
  // 项目相关
  PROJECT_NAME: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  
  PROJECT_DESCRIPTION: [
    { max: 500, message: '项目描述不能超过 500 个字符', trigger: 'blur' }
  ],
  
  // 考卷相关
  EXAM_TITLE: [
    { required: true, message: '请输入考卷标题', trigger: 'blur' },
    { max: 200, message: '标题长度不能超过200字符', trigger: 'blur' }
  ],
  
  EXAM_TARGET_LEVEL: [
    { required: true, message: '请输入目标等级', trigger: 'blur' },
    { max: 100, message: '目标等级长度不能超过100字符', trigger: 'blur' }
  ],
  
  EXAM_DIFFICULTY: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],

  // 考试记录相关
  EXAM_DATE: [
    { required: true, message: '请选择考试日期', trigger: 'change' }
  ],
  
  EXAM_SCORE: [
    { required: true, message: '请输入考试成绩', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '成绩必须在0-100之间', trigger: 'blur' }
  ],

  // 卡片相关
  CARD_TITLE: [
    { required: true, message: '请输入卡片标题', trigger: 'blur' },
    { max: 100, message: '标题长度不能超过100字符', trigger: 'blur' }
  ],

  CARD_FRONT_CONTENT: [
    { required: true, message: '请输入正面内容', trigger: 'blur' }
  ],

  CARD_DIFFICULTY: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],

  // 知识点相关
  KNOWLEDGE_POINT: [
    { required: true, message: '请选择知识点', trigger: 'change' }
  ],

  // 错题相关
  WRONG_QUESTION_CONTENT: [
    { required: true, message: '请输入错题内容', trigger: 'blur' }
  ],

  // 通用规则
  REQUIRED: [
    { required: true, message: '此字段为必填项', trigger: 'blur' }
  ],

  OPTIONAL_TEXT: [
    { max: 500, message: '内容长度不能超过500字符', trigger: 'blur' }
  ]
}

// 应用配置 - 简化配置，使用固定值
const getAppConfig = () => {
  return {
    // 编辑器配置
    EDITOR_HEIGHT: 600,
    EDITOR_THEME: 'dark',

    // 文件上传配置
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: ['.md', '.txt', '.json'],

    // AI生成配置
    AI_RETRY_COUNT: 3,
    AI_TIMEOUT: 60000,

    // 请求超时时间
    REQUEST_TIMEOUT: 180000,

    // 缓存配置
    ENABLE_CACHE: true,
    CACHE_PREFIX: 'kpe_'
  }
}

// 默认配置
export const DEFAULT_CONFIG = {
  // 分页配置
  PAGE_SIZE: 20,

  // 应用配置
  ...getAppConfig()
}

// 路由路径
export const ROUTES = {
  HOME: '/',
  ABOUT: '/about',
  PROJECT_LIST: '/',
  PROJECT_FORM: '/projects/new',
  PROJECT_EDIT: (id) => `/projects/${id}/edit`,
  KNOWLEDGE_MANAGEMENT: (id) => `/projects/${id}/knowledge`,
  EXAM_GENERATION: (id) => `/projects/${id}/exams/generate`,
  EXAM_LIST: (id) => `/projects/${id}/exams`,
  EXAM_RECORDS: (id, examId) => `/projects/${id}/exams/${examId}/records`,
  EXAM_ANALYSIS: (id) => `/projects/${id}/analysis`
}

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器错误，请稍后重试',
  VALIDATION_ERROR: '请检查输入内容',
  PERMISSION_ERROR: '权限不足',
  NOT_FOUND_ERROR: '请求的资源不存在'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  CREATE_SUCCESS: '创建成功',
  UPLOAD_SUCCESS: '上传成功',
  DOWNLOAD_SUCCESS: '下载成功'
}
