/**
 * 对话框工具类
 * 提供统一的确认对话框、提示对话框等常用功能
 */
import { ElMessageBox, ElMessage } from 'element-plus'

/**
 * 确认删除对话框
 * @param {string} itemName - 要删除的项目名称
 * @param {string} itemType - 项目类型（如：错题、卡片、记录等）
 * @param {Object} options - 额外配置选项
 */
export async function confirmDelete(itemName = '', itemType = '项目', options = {}) {
  const {
    title = '确认删除',
    customMessage = null,
    dangerousAction = false
  } = options

  const message = customMessage || 
    `确定要删除${itemType}${itemName ? `"${itemName}"` : ''}吗？${dangerousAction ? '\n\n此操作无法撤销！' : ''}`

  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning',
    ...options
  })
}

/**
 * 确认重置对话框
 * @param {string} itemName - 要重置的项目名称
 * @param {string} resetType - 重置类型（如：正确率、配置等）
 * @param {Object} options - 额外配置选项
 */
export async function confirmReset(itemName = '', resetType = '数据', options = {}) {
  const {
    title = '确认重置',
    customMessage = null,
    warningText = '此操作将清零相关统计数据，无法撤销。'
  } = options

  const message = customMessage || 
    `确定要重置${itemName ? `"${itemName}"的` : ''}${resetType}吗？\n\n${warningText}`

  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确认重置',
    cancelButtonText: '取消',
    type: 'warning',
    ...options
  })
}

/**
 * 确认保存对话框
 * @param {string} action - 操作类型（如：保存、提交等）
 * @param {Object} options - 额外配置选项
 */
export async function confirmSave(action = '保存', options = {}) {
  const {
    title = `确认${action}`,
    message = `确定要${action}当前更改吗？`,
    type = 'info'
  } = options

  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type,
    ...options
  })
}

/**
 * 确认离开页面对话框
 * @param {Object} options - 额外配置选项
 */
export async function confirmLeave(options = {}) {
  const {
    title = '确认离开',
    message = '您有未保存的更改，确定要离开此页面吗？',
    hasUnsavedChanges = true
  } = options

  if (!hasUnsavedChanges) {
    return true
  }

  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定离开',
    cancelButtonText: '继续编辑',
    type: 'warning',
    ...options
  })
}

/**
 * 成功操作提示
 * @param {string} action - 操作类型
 * @param {string} itemType - 项目类型
 * @param {Object} options - 额外配置选项
 */
export function showSuccess(action = '操作', itemType = '', options = {}) {
  const {
    customMessage = null,
    duration = 3000
  } = options

  const message = customMessage || `${itemType}${action}成功！`
  
  ElMessage.success({
    message,
    duration,
    ...options
  })
}

/**
 * 错误操作提示
 * @param {string} action - 操作类型
 * @param {Error|string} error - 错误信息
 * @param {Object} options - 额外配置选项
 */
export function showError(action = '操作', error = null, options = {}) {
  const {
    customMessage = null,
    duration = 3000,
    showDetail = false
  } = options

  let message = customMessage
  
  if (!message) {
    const baseMessage = `${action}失败`
    if (error && showDetail) {
      const errorMsg = error.response?.data?.message || error.message || '未知错误'
      message = `${baseMessage}: ${errorMsg}`
    } else {
      message = baseMessage
    }
  }
  
  ElMessage.error({
    message,
    duration,
    ...options
  })
}

/**
 * 批量操作确认对话框
 * @param {number} count - 选中项目数量
 * @param {string} action - 操作类型
 * @param {string} itemType - 项目类型
 * @param {Object} options - 额外配置选项
 */
export async function confirmBatchAction(count, action = '删除', itemType = '项目', options = {}) {
  const {
    title = `批量${action}`,
    warningText = '此操作无法撤销！'
  } = options

  const message = `确定要${action} ${count} 个${itemType}吗？\n\n${warningText}`

  return ElMessageBox.confirm(message, title, {
    confirmButtonText: `确定${action}`,
    cancelButtonText: '取消',
    type: 'warning',
    ...options
  })
}

/**
 * 输入对话框
 * @param {string} title - 对话框标题
 * @param {string} placeholder - 输入框占位符
 * @param {Object} options - 额外配置选项
 */
export async function showInputDialog(title, placeholder = '请输入', options = {}) {
  const {
    inputType = 'text',
    inputValidator = null,
    inputErrorMessage = '输入格式不正确'
  } = options

  return ElMessageBox.prompt(title, '输入', {
    inputPlaceholder: placeholder,
    inputType,
    inputValidator,
    inputErrorMessage,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    ...options
  })
} 