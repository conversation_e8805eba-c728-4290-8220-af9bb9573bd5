/**
 * import.meta polyfill for WebView compatibility
 */

export const needsImportMetaPolyfill = () => {
  try {
    new Function('return import.meta');
    return false;
  } catch (error) {
    return true;
  }
};

const getEnvironmentInfo = () => {
  const isFileProtocol = location.protocol === 'file:';
  const baseUrl = isFileProtocol ? './' : '/';
  
  return {
    url: window.location.href,
    env: {
      MODE: 'webview',
      DEV: false,
      PROD: true,
      BASE_URL: baseUrl,
      VITE_WEBVIEW_BUILD: 'true',
      SSR: false,
      LEGACY: true
    },
    resolve: (path) => {
      if (path.startsWith('./') || path.startsWith('../')) {
        return new URL(path, window.location.href).href;
      }
      if (path.startsWith('/')) {
        return new URL(path, window.location.origin).href;
      }
      return path;
    }
  };
};

export const createImportMetaPolyfill = () => {
  if (needsImportMetaPolyfill()) {
    console.log('🔧 加载import.meta polyfill...');
    
    const importMeta = getEnvironmentInfo();
    window.__vite_import_meta__ = importMeta;
    window.__import_meta_polyfill__ = importMeta;

    const originalEval = window.eval;
    window.eval = function(code) {
      if (typeof code === 'string' && code.includes('import.meta')) {
        console.debug('🔧 eval中检测到import.meta使用');
        code = code.replace(/import\.meta/g, 'window.__vite_import_meta__');
      }
      return originalEval.call(this, code);
    };

    const originalFunction = window.Function;
    window.Function = function(...args) {
      if (args.length > 0) {
        let code = args[args.length - 1];
        if (typeof code === 'string' && code.includes('import.meta')) {
          console.debug('🔧 Function构造函数中检测到import.meta使用');
          code = code.replace(/import\.meta/g, 'window.__vite_import_meta__');
          args[args.length - 1] = code;
        }
      }
      return originalFunction.apply(this, args);
    };

    console.log('✅ import.meta polyfill已加载');
    return importMeta;
  }
  
  return null;
};

export const initImportMetaPolyfill = () => {
  if (needsImportMetaPolyfill()) {
    console.log('⚠️ 检测到需要import.meta polyfill');
    const polyfill = createImportMetaPolyfill();
    return polyfill;
  }
  
  console.log('✅ 环境原生支持import.meta，无需polyfill');
  return null;
};

export const getImportMeta = () => {
  // 避免直接使用import关键字，通过动态检查
  try {
    const importObj = window.import || (new Function('try { return import; } catch(e) { return undefined; }'))();
    if (importObj && importObj.meta) {
      return importObj.meta;
    }
  } catch (e) {
    // 使用polyfill版本
  }
  
  return window.__vite_import_meta__ || window.__import_meta_polyfill__ || getEnvironmentInfo();
};

export const testImportMetaPolyfill = () => {
  console.log('🧪 测试import.meta polyfill功能...');
  
  const testResults = {
    needsPolyfill: needsImportMetaPolyfill(),
    hasGlobalPolyfill: !!window.__vite_import_meta__,
    canAccess: false,
    envAccess: false,
    urlAccess: false
  };
  
  try {
    const meta = getImportMeta();
    testResults.canAccess = !!meta;
    testResults.envAccess = !!(meta && meta.env);
    testResults.urlAccess = !!(meta && meta.url);
    
    console.log('📊 import.meta测试结果:', testResults);
    console.log('📋 import.meta内容:', meta);
    
    return testResults;
  } catch (error) {
    console.error('❌ import.meta测试失败:', error);
    testResults.error = error.message;
    return testResults;
  }
}; 