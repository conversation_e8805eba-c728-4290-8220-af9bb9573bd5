/**
 * 文字转图片工具
 * 使用Canvas生成带文字的图片
 */

/**
 * 生成城市名称图片
 * @param {Object} cityData - 城市数据
 * @param {string} cityData.city - 城市名称
 * @param {string} cityData.province - 省份
 * @param {string} cityData.plate_code - 车牌代码
 * @param {Object} options - 可选配置
 */
export function generateCityImage(cityData, options = {}) {
  const {
    width = 400,
    height = 300,
    backgroundColor = '#f8f9fa',
    primaryColor = '#2c3e50',
    secondaryColor = '#7f8c8d',
    accentColor = '#3498db',
    fontSize = 48,
    fontFamily = 'PingFang SC, Microsoft YaHei, sans-serif'
  } = options;

  // 创建Canvas元素
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  // 设置背景
  ctx.fillStyle = backgroundColor;
  ctx.fillRect(0, 0, width, height);

  // 添加边框
  ctx.strokeStyle = '#dee2e6';
  ctx.lineWidth = 2;
  ctx.strokeRect(1, 1, width - 2, height - 2);

  // 设置字体
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // 绘制城市名称（支持自动换行）
  ctx.fillStyle = primaryColor;
  ctx.font = `bold ${fontSize}px ${fontFamily}`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  
  // 自动换行处理
  const cityName = cityData.city;
  const maxWidth = width - 40; // 留边距
  const lineHeight = fontSize * 1.2;
  
  // 检查是否需要换行
  const textWidth = ctx.measureText(cityName).width;
  if (textWidth > maxWidth) {
    // 需要换行，寻找合适的分割点
    const words = [];
    
    // 如果城市名包含特定字符，优先在这些位置分割
    if (cityName.includes('自治州') || cityName.includes('自治区') || cityName.includes('自治县')) {
      const parts = cityName.split(/(?=自治)/);
      words.push(...parts);
    } else if (cityName.includes('族') && cityName.length > 6) {
      const parts = cityName.split(/(?=族)/);
      words.push(...parts);
    } else {
      // 按长度分割
      const mid = Math.ceil(cityName.length / 2);
      words.push(cityName.substring(0, mid));
      words.push(cityName.substring(mid));
    }
    
    // 绘制多行文字
    const totalHeight = words.length * lineHeight;
    const startY = height / 2 - totalHeight / 2 + lineHeight / 2;
    
    words.forEach((word, index) => {
      if (word.trim()) {
        ctx.fillText(word, width / 2, startY + index * lineHeight);
      }
    });
  } else {
    // 单行显示
    ctx.fillText(cityName, width / 2, height / 2);
  }

  // 添加装饰元素
  drawDecorationElements(ctx, width, height, accentColor);

  // 转换为Data URL
  return canvas.toDataURL('image/png');
}

/**
 * 绘制装饰元素
 */
function drawDecorationElements(ctx, width, height, color) {
  ctx.fillStyle = color;
  ctx.globalAlpha = 0.1;

  // 左下角装饰
  ctx.beginPath();
  ctx.moveTo(0, height);
  ctx.lineTo(60, height);
  ctx.lineTo(0, height - 60);
  ctx.closePath();
  ctx.fill();

  // 右上角装饰
  ctx.beginPath();
  ctx.moveTo(width, 0);
  ctx.lineTo(width - 60, 0);
  ctx.lineTo(width, 60);
  ctx.closePath();
  ctx.fill();

  ctx.globalAlpha = 1.0;
}

/**
 * 生成多个城市图片并缓存
 * @param {Array} cityList - 城市列表
 * @param {Object} options - 可选配置
 */
export function generateCityImages(cityList, options = {}) {
  const imageMap = new Map();
  
  cityList.forEach(city => {
    const imageDataUrl = generateCityImage(city, options);
    imageMap.set(city.city, imageDataUrl);
  });
  
  return imageMap;
}

/**
 * 批量生成城市图片并保存到本地存储
 * @param {Array} cityList - 城市列表
 * @param {string} storageKey - 存储键名
 */
export function cacheCityImages(cityList, storageKey = 'cityImages') {
  try {
    const imageMap = generateCityImages(cityList);
    const imageData = {};
    
    imageMap.forEach((dataUrl, cityName) => {
      imageData[cityName] = dataUrl;
    });
    
    localStorage.setItem(storageKey, JSON.stringify(imageData));
    console.log(`✅ 成功缓存 ${cityList.length} 个城市图片`);
    
    return imageData;
  } catch (error) {
    console.error('❌ 缓存城市图片失败:', error);
    return null;
  }
}

/**
 * 从本地存储加载城市图片
 * @param {string} storageKey - 存储键名
 */
export function loadCachedCityImages(storageKey = 'cityImages') {
  try {
    const cachedData = localStorage.getItem(storageKey);
    if (cachedData) {
      return JSON.parse(cachedData);
    }
    return null;
  } catch (error) {
    console.error('❌ 加载缓存城市图片失败:', error);
    return null;
  }
}

/**
 * 清除缓存的城市图片
 * @param {string} storageKey - 存储键名
 */
export function clearCachedCityImages(storageKey = 'cityImages') {
  try {
    localStorage.removeItem(storageKey);
    console.log('✅ 清除城市图片缓存成功');
  } catch (error) {
    console.error('❌ 清除城市图片缓存失败:', error);
  }
}

export default {
  generateCityImage,
  generateCityImages,
  cacheCityImages,
  loadCachedCityImages,
  clearCachedCityImages
}; 