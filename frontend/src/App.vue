<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 顶部导航 -->
      <el-header class="header" v-if="showHeader">
        <div class="header-content">
          <div class="logo">
            <el-icon><Document /></el-icon>
            <span>KPE - 知脉精练</span>
          </div>
          <div class="nav-menu">
            <el-menu
              :default-active="$route.path"
              mode="horizontal"
              router
              background-color="transparent"
              text-color="#fff"
              active-text-color="#409EFF"
              :ellipsis="false"
            >
              <el-menu-item index="/">项目管理</el-menu-item>
              <el-menu-item index="/courses">学习课程</el-menu-item>
              <el-menu-item index="/quiz-exam-records">考试记录</el-menu-item>
              <el-menu-item index="/video-tutorials">视频教程</el-menu-item>
              <el-menu-item index="/m">移动端</el-menu-item>
              <el-menu-item index="/about">关于</el-menu-item>
            </el-menu>
          </div>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { Document } from '@element-plus/icons-vue';

const route = useRoute();
const showHeader = computed(() => {
  const mobilePaths = ['/m', '/quiz-list', '/quiz/', '/intelligence-challenge'];
  return !mobilePaths.some(path => route.path.startsWith(path));
});
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  height: 60px;
  line-height: 60px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.logo .el-icon {
  margin-right: 8px;
  font-size: 24px;
}



.main-content {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 全局样式 */
:global(body) {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

:global(#app) {
  height: 100vh;
}
</style>
