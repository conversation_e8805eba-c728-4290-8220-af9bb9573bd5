import { OPTION_TYPES } from '@/config/quizConfig.js';

/**
 * 选项策略抽象基类
 */
export class OptionStrategy {
  constructor(config = {}) {
    this.config = config;
  }

  /**
   * 生成选项
   * @param {Object} item - 当前题目项
   * @param {Array} dataArray - 数据数组
   * @param {number} optionCount - 选项数量
   * @returns {Array} 选项数组
   */
  generateOptions(item, dataArray, optionCount) {
    throw new Error('generateOptions method must be implemented');
  }

  /**
   * 获取选项值
   * @param {Object} item - 数据项
   * @returns {string|Object} 选项值
   */
  getOptionValue(item) {
    throw new Error('getOptionValue method must be implemented');
  }

  /**
   * 获取正确答案
   * @param {Object} item - 数据项
   * @returns {string|Object} 正确答案
   */
  getCorrectAnswer(item) {
    throw new Error('getCorrectAnswer method must be implemented');
  }
}

/**
 * 文字选项策略
 */
export class TextOptionStrategy extends OptionStrategy {
  constructor(config = {}) {
    super(config);
    this.valueField = config.valueField || 'name';
  }

  generateOptions(item, dataArray, optionCount) {
    const options = new Set([this.getOptionValue(item)]);
    const allValues = dataArray.map(d => this.getOptionValue(d));
    
    while (options.size < optionCount && options.size < allValues.length) {
      const randomIndex = Math.floor(Math.random() * allValues.length);
      const randomValue = allValues[randomIndex];
      if (randomValue !== this.getOptionValue(item)) {
        options.add(randomValue);
      }
    }

    return Array.from(options);
  }

  getOptionValue(item) {
    return item[this.valueField];
  }

  getCorrectAnswer(item) {
    return this.getOptionValue(item);
  }
}

/**
 * 基于分类的文字选项策略
 */
export class CategoryBasedTextStrategy extends TextOptionStrategy {
  constructor(config = {}) {
    super(config);
    this.categoryField = config.categoryField || 'category';
  }

  generateOptions(item, dataArray, optionCount) {
    const options = new Set([this.getOptionValue(item)]);
    const categoryKey = item[this.categoryField] || item.continent;
    
    if (categoryKey) {
      // 优先从同类别中选择选项
      const sameCategory = dataArray.filter(other => 
        (other[this.categoryField] === categoryKey || other.continent === categoryKey) && 
        this.getOptionValue(other) !== this.getOptionValue(item)
      );
      
      while (options.size < optionCount && options.size < sameCategory.length + 1) {
        const randomIndex = Math.floor(Math.random() * sameCategory.length);
        const randomItem = sameCategory[randomIndex];
        if (randomItem) {
          options.add(this.getOptionValue(randomItem));
        }
      }
    }
    
    // 如果同类别选项不够，从所有数据中补充
    if (options.size < optionCount) {
      const allValues = dataArray.map(d => this.getOptionValue(d));
      while (options.size < optionCount && options.size < allValues.length) {
        const randomIndex = Math.floor(Math.random() * allValues.length);
        const randomValue = allValues[randomIndex];
        if (randomValue !== this.getOptionValue(item)) {
          options.add(randomValue);
        }
      }
    }

    return Array.from(options);
  }
}

/**
 * 图片选项策略
 */
export class ImageOptionStrategy extends OptionStrategy {
  constructor(config = {}) {
    super(config);
    this.imageField = config.imageField || 'fileName';
    this.nameField = config.nameField || 'name';
  }

  generateOptions(item, dataArray, optionCount) {
    const options = new Set([this.getOptionValue(item)]);
    
    while (options.size < optionCount && options.size < dataArray.length) {
      const randomIndex = Math.floor(Math.random() * dataArray.length);
      const randomItem = dataArray[randomIndex];
      if (randomItem && this.getOptionValue(randomItem) !== this.getOptionValue(item)) {
        options.add(this.getOptionValue(randomItem));
      }
    }

    return Array.from(options);
  }

  getOptionValue(item) {
    return {
      image: item[this.imageField],
      name: item[this.nameField],
      value: item[this.nameField]
    };
  }

  getCorrectAnswer(item) {
    return item[this.nameField];
  }
}



/**
 * 选项策略工厂
 */
export class OptionStrategyFactory {
  static create(type, config = {}) {
    switch (type) {
      case OPTION_TYPES.TEXT:
        return config.useCategory ? 
          new CategoryBasedTextStrategy(config) : 
          new TextOptionStrategy(config);
      case OPTION_TYPES.IMAGE:
        return new ImageOptionStrategy(config);
      default:
        throw new Error(`Unknown option type: ${type}`);
    }
  }
}

/**
 * 工具函数：打乱数组
 */
export function shuffleArray(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
} 