{"name": "kpe-frontend", "version": "1.0.0", "description": "KPE Frontend - Vue3 + Element Plus", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "build:webview": "vite build --mode webview", "preview": "vite preview", "preview:webview": "vite preview --mode webview", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@monaco-editor/loader": "^1.5.0", "@vueup/vue-quill": "^1.2.0", "axios": "^1.6.2", "core-js": "^3.43.0", "cropperjs": "1.6.2", "dompurify": "^3.2.6", "element-plus": "^2.4.4", "es6-promise": "^4.2.8", "katex": "^0.16.22", "markdown-it": "^14.1.0", "marked": "^15.0.12", "marked-katex-extension": "^5.1.4", "md-it-katex": "^0.0.3", "monaco-editor": "^0.52.2", "pinia": "^2.1.7", "regenerator-runtime": "^0.14.1", "simple-mind-map": "^0.10.6", "vue": "^3.4.0", "vue-router": "^4.2.5", "whatwg-fetch": "^3.6.20"}, "devDependencies": {"@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^4.5.2", "@vue/test-utils": "^2.4.6", "jsdom": "^26.1.0", "terser": "^5.43.1", "vite": "^5.0.10", "vitest": "^3.2.3"}}