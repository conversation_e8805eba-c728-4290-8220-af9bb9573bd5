---
alwaysApply: true
---
# AI 助手工作指南（前端部分）

## 必须遵守的规则
### 前端
- **包管理器**: pnpm代替npm
- **技术栈**: Vue 3 + Vite + Element Plus
- **后端API配置**: 只需配置 `VITE_API_HOST` 和 `VITE_API_PORT` 两个参数
- **前端代理**: Vite自动将 `/api` 请求代理到 `http://{VITE_API_HOST}:{VITE_API_PORT}`
- **核心Composables**: @/composables
- **工具函数**:  @/utils
- **业务服务**:  @/services
- **路径工具**:  @/utils/pathUtils
- **测试框架**: Vitest + @vue/test-utils + jsdom
- **测试目录**: src/test/

## 严格禁止事项 