/**
 * 课程页面统一JavaScript功能
 */

// 初始化页面
document.addEventListener("DOMContentLoaded", function() {
    // 渲染数学公式
    initMathRendering();
    
    // 初始化其他功能
    initPageFeatures();
    
    // 初始化快速导航
    initQuickNavigation();
});

/**
 * 初始化数学公式渲染
 */
function initMathRendering() {
    // 等待KaTeX加载完成
    if (typeof renderMathInElement !== 'undefined') {
        try {
            renderMathInElement(document.body, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ],
                throwOnError: false,
                ignoredTags: ["script", "noscript", "style", "textarea", "pre", "code"],
                ignoredClasses: ["no-katex"]
            });
            console.log('KaTeX渲染完成');
        } catch (error) {
            console.error('KaTeX渲染失败:', error);
        }
    } else {
        // 如果KaTeX还没加载，延迟重试
        setTimeout(initMathRendering, 100);
    }
}

/**
 * 切换答案显示
 * @param {string} answerId - 答案元素的ID
 * @param {HTMLElement} button - 触发按钮元素
 */
function toggleAnswer(answerId, button) {
    const answer = document.getElementById(answerId);
    if (!answer) return;
    
    if (answer.classList.contains('show')) {
        answer.classList.remove('show');
        button.textContent = '查看答案';
    } else {
        answer.classList.add('show');
        button.textContent = '隐藏答案';
        
        // 重新渲染答案中的数学公式
        if (typeof renderMathInElement !== 'undefined') {
            renderMathInElement(answer, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ],
                throwOnError: false
            });
        }
    }
}

/**
 * 初始化页面功能
 */
function initPageFeatures() {
    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // 添加返回顶部功能
    addBackToTopButton();
}

/**
 * 添加返回顶部按钮
 */
function addBackToTopButton() {
    // 创建返回顶部按钮
    const backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '↑';
    backToTopBtn.className = 'back-to-top';
    backToTopBtn.title = '返回顶部';
    backToTopBtn.style.cssText = `
        position: fixed;
        bottom: 80px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #409eff;
        color: white;
        border: none;
        font-size: 20px;
        cursor: pointer;
        display: none;
        z-index: 999;
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(backToTopBtn);
    
    // 监听滚动事件
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    // 点击返回顶部
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // 鼠标悬停效果
    backToTopBtn.addEventListener('mouseenter', function() {
        this.style.background = '#337ecc';
        this.style.transform = 'scale(1.1)';
    });
    
    backToTopBtn.addEventListener('mouseleave', function() {
        this.style.background = '#409eff';
        this.style.transform = 'scale(1)';
    });
}

/**
 * 显示/隐藏所有答案
 * @param {boolean} show - 是否显示答案
 */
function toggleAllAnswers(show) {
    const answers = document.querySelectorAll('.question-answer');
    const buttons = document.querySelectorAll('.answer-btn');
    
    answers.forEach(answer => {
        if (show) {
            answer.classList.add('show');
        } else {
            answer.classList.remove('show');
        }
    });
    
    buttons.forEach(button => {
        button.textContent = show ? '隐藏答案' : '查看答案';
    });
    
    // 如果显示答案，重新渲染数学公式
    if (show && typeof renderMathInElement !== 'undefined') {
        answers.forEach(answer => {
            renderMathInElement(answer, {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false}
                ],
                throwOnError: false
            });
        });
    }
}

/**
 * 初始化快速导航
 */
function initQuickNavigation() {
    createQuickNav();
    updateNavOnScroll();
}

/**
 * 创建快速导航
 */
function createQuickNav() {
    // 创建导航容器
    const quickNav = document.createElement('div');
    quickNav.className = 'quick-nav';
    quickNav.id = 'quickNav';
    
    // 创建导航标题
    const navTitle = document.createElement('div');
    navTitle.className = 'quick-nav-title';
    navTitle.textContent = '快速导航';
    quickNav.appendChild(navTitle);
    
    // 获取所有主要section
    const sections = document.querySelectorAll('.section');
    
    sections.forEach((section, index) => {
        const sectionHeader = section.querySelector('.section-header');
        if (sectionHeader) {
            // 为section添加ID
            const sectionId = `section-${index}`;
            section.id = sectionId;
            
            // 创建主导航项
            const navItem = document.createElement('a');
            navItem.className = 'nav-item nav-chapter';
            navItem.href = `#${sectionId}`;
            
            // 提取图标和文本
            let icon = '📄';
            let text = sectionHeader.textContent;
            if (text.includes('📚')) {
                icon = '📚';
                text = text.replace(/📚\s*/, '');
            } else if (text.includes('📝')) {
                icon = '📝';
                text = text.replace(/📝\s*/, '');
            }
            
            navItem.innerHTML = `<span class="nav-icon">${icon}</span>${text}`;
            
            navItem.addEventListener('click', function(e) {
                e.preventDefault();
                scrollToSection(section);
                updateActiveNav(navItem);
            });
            
            quickNav.appendChild(navItem);
            
            // 如果是知识要点section，添加子章节导航
            if (sectionHeader.textContent.includes('📚') || sectionHeader.textContent.includes('知识')) {
                const h3Elements = section.querySelectorAll('h3');
                h3Elements.forEach((h3, h3Index) => {
                    // 为h3添加ID
                    const h3Id = `chapter-${index}-${h3Index}`;
                    h3.id = h3Id;
                    
                    // 创建子导航项
                    const subNavItem = document.createElement('a');
                    subNavItem.className = 'nav-item';
                    subNavItem.href = `#${h3Id}`;
                    subNavItem.innerHTML = `<span class="nav-icon">•</span>${h3.textContent}`;
                    subNavItem.style.paddingLeft = '20px';
                    subNavItem.style.fontSize = '12px';
                    
                    subNavItem.addEventListener('click', function(e) {
                        e.preventDefault();
                        scrollToSection(h3);
                        updateActiveNav(subNavItem);
                    });
                    
                    quickNav.appendChild(subNavItem);
                });
            }
        }
    });
    
    // 创建移动端切换按钮
    const navToggle = document.createElement('button');
    navToggle.className = 'nav-toggle';
    navToggle.innerHTML = '≡';
    navToggle.title = '切换导航';
    
    navToggle.addEventListener('click', function() {
        quickNav.classList.toggle('collapsed');
        this.innerHTML = quickNav.classList.contains('collapsed') ? '≡' : '×';
    });
    
    // 添加到页面
    document.body.appendChild(quickNav);
    document.body.appendChild(navToggle);
    
    // 移动端默认收起
    if (window.innerWidth <= 768) {
        quickNav.classList.add('collapsed');
    }
}

/**
 * 滚动到指定section
 * @param {HTMLElement} element - 目标元素
 */
function scrollToSection(element) {
    const offsetTop = element.offsetTop - 20; // 留一些空间
    window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
    });
}

/**
 * 更新激活的导航项
 * @param {HTMLElement} activeItem - 激活的导航项
 */
function updateActiveNav(activeItem) {
    // 移除所有激活状态
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 添加激活状态
    activeItem.classList.add('active');
}

/**
 * 监听滚动更新导航状态
 */
function updateNavOnScroll() {
    let ticking = false;
    
    function updateNav() {
        const scrollTop = window.pageYOffset;
        const sections = document.querySelectorAll('.section, h3[id^="chapter-"]');
        
        let currentSection = null;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionBottom = sectionTop + section.offsetHeight;
            
            if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
                currentSection = section;
            }
        });
        
        if (currentSection) {
            const targetNav = document.querySelector(`a[href="#${currentSection.id}"]`);
            if (targetNav) {
                updateActiveNav(targetNav);
            }
        }
        
        ticking = false;
    }
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateNav);
            ticking = true;
        }
    });
}

/**
 * 响应式处理
 */
window.addEventListener('resize', function() {
    const quickNav = document.getElementById('quickNav');
    const navToggle = document.querySelector('.nav-toggle');
    
    if (window.innerWidth <= 768) {
        if (!quickNav.classList.contains('collapsed')) {
            quickNav.classList.add('collapsed');
            navToggle.innerHTML = '≡';
        }
    } else {
        quickNav.classList.remove('collapsed');
        navToggle.innerHTML = '×';
    }
});

// 导出函数供全局使用
window.toggleAnswer = toggleAnswer;
window.toggleAllAnswers = toggleAllAnswers; 