/* 课程页面统一样式 */
body {
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f7fa;
}

.header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #333;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border: 1px solid #dee2e6;
}

.header h1 {
    margin: 0;
    font-size: 28px;
    color: #2c3e50;
}

.header p {
    margin: 8px 0 0 0;
    color: #6c757d;
    font-size: 16px;
}

.section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    padding: 16px 24px;
    font-size: 20px;
    font-weight: bold;
}

.section-content {
    padding: 24px;
}

h3 {
    color: #303133;
    font-size: 18px;
    margin: 24px 0 12px 0;
    border-left: 4px solid #409eff;
    padding-left: 12px;
}

h4 {
    color: #606266;
    font-size: 16px;
    margin: 16px 0 8px 0;
}

p {
    margin: 8px 0;
    color: #606266;
}

strong {
    color: #303133;
    font-weight: 600;
}

ul, ol {
    margin: 8px 0;
    padding-left: 24px;
}

li {
    margin: 4px 0;
    color: #606266;
}

.katex-display {
    margin: 16px 0;
}

.katex {
    font-size: 1.1em;
}

/* 数学公式容器样式 */
.katex-display > .katex {
    display: block;
    text-align: center;
}

/* 修复KaTeX在移动端的显示 */
@media (max-width: 768px) {
    .katex {
        font-size: 1em;
    }
    
    .katex-display {
        overflow-x: auto;
        overflow-y: hidden;
    }
}

/* 题目样式 */
.question-item {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
}

.question-header {
    background: #f5f7fa;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.question-number {
    font-weight: bold;
    color: #303133;
}

.question-source {
    color: #909399;
    font-size: 12px;
}

.knowledge-point {
    background: #e8f4fd;
    color: #409eff;
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 12px;
    border: 1px solid #b3d8ff;
    margin-left: 8px;
}

.question-content {
    padding: 16px;
}

.question-text {
    margin-bottom: 16px;
    line-height: 1.6;
}

.question-answer {
    background: #f0f9ff;
    padding: 16px;
    border-radius: 6px;
    margin: 16px 0;
    border-left: 4px solid #409eff;
    display: none;
}

.question-answer.show {
    display: block;
}

.question-answer h4 {
    color: #303133;
    font-size: 14px;
    margin: 0 0 8px 0;
}

.answer-btn {
    background: #409eff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.answer-btn:hover {
    background: #337ecc;
}

/* 快速导航样式 */
.quick-nav {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 16px 8px;
    z-index: 1000;
    min-width: 120px;
    max-height: 80vh;
    overflow-y: auto;
}

.quick-nav-title {
    font-size: 14px;
    font-weight: bold;
    color: #303133;
    text-align: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
}

.nav-item {
    display: block;
    padding: 8px 12px;
    margin: 4px 0;
    color: #606266;
    text-decoration: none;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-item:hover {
    background: #f0f9ff;
    color: #409eff;
    transform: translateX(-2px);
}

.nav-item.active {
    background: #409eff;
    color: white;
}

.nav-item .nav-icon {
    margin-right: 6px;
    font-size: 14px;
    display: inline-block;
    width: 16px;
    text-align: center;
}

/* 章节导航 */
.nav-chapter {
    font-weight: 600;
    color: #303133;
    background: #f8f9fa;
    border-left: 3px solid #409eff;
}

.nav-chapter:hover {
    background: #e8f4fd;
    color: #337ecc;
}

/* 导航收起按钮 */
.nav-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #409eff;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    z-index: 1001;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: none;
}

.nav-toggle:hover {
    background: #337ecc;
    transform: scale(1.1);
}

.quick-nav.collapsed {
    transform: translateY(-50%) translateX(100%);
    opacity: 0;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .header {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .section-content {
        padding: 16px;
    }
    
    h3 {
        font-size: 16px;
    }
    
    h4 {
        font-size: 14px;
    }
    
    /* 移动端导航调整 */
    .quick-nav {
        position: fixed;
        top: auto;
        bottom: 20px;
        right: 20px;
        transform: none;
        max-height: 60vh;
        min-width: 100px;
    }
    
    .nav-toggle {
        display: block;
    }
    
    .quick-nav.collapsed {
        transform: translateX(100%);
    }
    
    .nav-item {
        font-size: 12px;
        padding: 6px 8px;
    }
    
    .quick-nav-title {
        font-size: 12px;
        margin-bottom: 8px;
    }
}

/* 滚动条样式 */
.quick-nav::-webkit-scrollbar {
    width: 4px;
}

.quick-nav::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.quick-nav::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.quick-nav::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
} 