<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上海幼升小数学真题 - 数学专项</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css">
    <link rel="stylesheet" href="../../assets/course-styles.css">
</head>
<body>
    <div class="header">
        <h1>上海幼升小数学真题</h1>
        <p>上海幼升小数学真题，包含数的认识、简单运算、图形识别、逻辑推理等内容</p>
    </div>

    <!-- 知识要点和公式 -->
    <div class="section">
        <div class="section-header">📚 知识要点与基础概念</div>
        <div class="section-content">
            <h3>数学基础知识</h3>
            <h4>1. 数的认识</h4>
            <p><strong>数字0-20</strong>：能正确认识、读写0-20的数字</p>
            <p><strong>数的大小比较</strong>：会比较20以内数的大小，使用>、<、=符号</p>
            <p><strong>数的组成</strong>：理解10以内数的组成，如8=5+3=6+2等</p>
            
            <h4>2. 简单运算</h4>
            <p><strong>10以内加法</strong>：熟练计算10以内的加法，如3+4=7</p>
            <p><strong>10以内减法</strong>：熟练计算10以内的减法，如8-3=5</p>
            <p><strong>20以内不进位加法</strong>：如12+3=15</p>
            <p><strong>20以内不退位减法</strong>：如15-2=13</p>
            
            <h4>3. 图形认识</h4>
            <p><strong>基本图形</strong>：圆形、正方形、长方形、三角形</p>
            <p><strong>立体图形</strong>：正方体、长方体、球体、圆柱体</p>
            <p><strong>图形特征</strong>：边数、角数、对称性等</p>
            
            <h4>4. 逻辑推理</h4>
            <p><strong>找规律</strong>：数字规律、图形规律、颜色规律</p>
            <p><strong>分类</strong>：按形状、颜色、大小等属性分类</p>
            <p><strong>排序</strong>：按大小、长短、高矮等排序</p>
            
            <h4>5. 应用题</h4>
            <p><strong>简单应用</strong>：结合实际生活的简单应用题</p>
            <p><strong>看图列式</strong>：根据图画内容列出算式</p>
        </div>
    </div>

    <!-- 幼升小真题 -->
    <div class="section">
        <div class="section-header">📝 上海幼升小数学真题</div>
        <div class="section-content">
            <!-- 批量操作按钮 -->
            <div style="margin-bottom: 20px; text-align: center;">
                <button class="answer-btn" onclick="toggleAllAnswers(true)" style="margin-right: 10px;">显示所有答案</button>
                <button class="answer-btn" onclick="toggleAllAnswers(false)">隐藏所有答案</button>
            </div>
            
            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 1</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：数的认识与比较】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>在下面的数字中，找出最大的数和最小的数：</p>
                        <p style="font-size: 24px; text-align: center;">8、3、15、6、12</p>
                    </div>
                    <div class="question-answer" id="answer1">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>最大的数是：<strong>15</strong></p>
                        <p>最小的数是：<strong>3</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>比较这些数的大小：3 < 6 < 8 < 12 < 15</p>
                        <p>所以15最大，3最小</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer1', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 2</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：10以内加减法】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>小明有5颗糖，妈妈又给了他3颗糖，小明一共有几颗糖？</p>
                        <p>如果小明吃掉了2颗糖，现在还剩几颗糖？</p>
                    </div>
                    <div class="question-answer" id="answer2">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>1. 小明一共有：5 + 3 = <strong>8颗糖</strong></p>
                        <p>2. 吃掉2颗后剩下：8 - 2 = <strong>6颗糖</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>这是一道连续的加减法应用题，要分步骤计算</p>
                        <p>第一步：原有的糖 + 新得到的糖 = 总糖数</p>
                        <p>第二步：总糖数 - 吃掉的糖 = 剩余糖数</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer2', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 3</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：图形认识】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>观察下面的图形，回答问题：</p>
                        <p>○ △ □ ○ △ □ ○ △ ？</p>
                        <p>问号处应该填什么图形？</p>
                    </div>
                    <div class="question-answer" id="answer3">
                        <h4>答案解析：</h4>
                        <p><strong>答案：□（正方形）</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>这是一个图形规律题，图形按照"圆形、三角形、正方形"的顺序重复出现</p>
                        <p>规律：○ △ □ | ○ △ □ | ○ △ □</p>
                        <p>每三个图形为一组，重复出现</p>
                        <p>第9个位置正好是第3组的第3个，应该是正方形</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer3', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 4</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：数的组成】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>数字9可以分成哪两个数字？请写出所有可能的组合。</p>
                        <p>例如：9 = 1 + 8</p>
                    </div>
                    <div class="question-answer" id="answer4">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>9的所有组成方式：</p>
                        <ul>
                            <li>9 = 0 + 9</li>
                            <li>9 = 1 + 8</li>
                            <li>9 = 2 + 7</li>
                            <li>9 = 3 + 6</li>
                            <li>9 = 4 + 5</li>
                            <li>9 = 5 + 4</li>
                            <li>9 = 6 + 3</li>
                            <li>9 = 7 + 2</li>
                            <li>9 = 8 + 1</li>
                            <li>9 = 9 + 0</li>
                        </ul>
                        <p><strong>解析：</strong></p>
                        <p>数的组成是加法的基础，掌握10以内数的组成很重要</p>
                        <p>如果不考虑顺序，9有5种不同的组成方式</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer4', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 5</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：比较大小】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>在空格里填上">"、"<"或"="：</p>
                        <p>① 7 ○ 9</p>
                        <p>② 15 ○ 13</p>
                        <p>③ 8 + 2 ○ 5 + 5</p>
                        <p>④ 12 - 3 ○ 6 + 2</p>
                    </div>
                    <div class="question-answer" id="answer5">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>① 7 <strong><</strong> 9</p>
                        <p>② 15 <strong>></strong> 13</p>
                        <p>③ 8 + 2 <strong>=</strong> 5 + 5（10 = 10）</p>
                        <p>④ 12 - 3 <strong>></strong> 6 + 2（9 > 8）</p>
                        <p><strong>解析：</strong></p>
                        <p>• 前两题直接比较数的大小</p>
                        <p>• 后两题需要先计算结果再比较</p>
                        <p>• 记住：大于号">"的开口朝向大的数</p>
                        <p>• 小于号"<"的尖端指向小的数</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer5', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 6</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：找规律】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>观察下面的数字规律，填写空缺的数字：</p>
                        <p style="font-size: 20px; text-align: center;">2, 4, 6, 8, __, 12, __, 16</p>
                    </div>
                    <div class="question-answer" id="answer6">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>2, 4, 6, 8, <strong>10</strong>, 12, <strong>14</strong>, 16</p>
                        <p><strong>解析：</strong></p>
                        <p>这是一个等差数列，每个数都比前一个数大2</p>
                        <p>规律：每次加2</p>
                        <p>2 → 4 → 6 → 8 → 10 → 12 → 14 → 16</p>
                        <p>这些都是2的倍数，也叫偶数</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer6', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 7</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：立体图形】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>下面哪些物品的形状是球体？（可以多选）</p>
                        <p>A. 皮球　　B. 魔方　　C. 地球仪　　D. 书本　　E. 乒乓球</p>
                    </div>
                    <div class="question-answer" id="answer7">
                        <h4>答案解析：</h4>
                        <p><strong>答案：A、C、E</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>A. 皮球 - ✓ 球体（圆球形状）</p>
                        <p>B. 魔方 - ✗ 正方体（有6个面）</p>
                        <p>C. 地球仪 - ✓ 球体（圆球形状）</p>
                        <p>D. 书本 - ✗ 长方体（有长宽高）</p>
                        <p>E. 乒乓球 - ✓ 球体（圆球形状）</p>
                        <p><strong>知识点：</strong></p>
                        <p>球体的特征：表面是曲面，没有顶点和边，从任何方向看都是圆形</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer7', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 8</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：钟表认识】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>小华每天早上7点起床，晚上9点睡觉。</p>
                        <p>请问：小华一天清醒的时间是多少小时？</p>
                    </div>
                    <div class="question-answer" id="answer8">
                        <h4>答案解析：</h4>
                        <p><strong>答案：14小时</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>从早上7点到晚上9点，可以这样计算：</p>
                        <p>方法一：</p>
                        <p>7点到12点：12 - 7 = 5小时</p>
                        <p>12点到21点（晚上9点）：21 - 12 = 9小时</p>
                        <p>总计：5 + 9 = 14小时</p>
                        <p>方法二：</p>
                        <p>21点 - 7点 = 14小时</p>
                        <p><strong>知识点：</strong></p>
                        <p>时间计算，24小时制中晚上9点是21点</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer8', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 9</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：分类统计】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>幼儿园有以下玩具：</p>
                        <p>红球3个，蓝球2个，红积木4块，蓝积木5块，绿积木1块</p>
                        <p>请回答：</p>
                        <p>① 球类玩具一共有几个？</p>
                        <p>② 积木类玩具一共有几块？</p>
                        <p>③ 红色玩具一共有几个？</p>
                    </div>
                    <div class="question-answer" id="answer9">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>① 球类玩具：3 + 2 = <strong>5个</strong></p>
                        <p>② 积木类玩具：4 + 5 + 1 = <strong>10块</strong></p>
                        <p>③ 红色玩具：3 + 4 = <strong>7个</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>这是分类统计题，需要按照不同的标准进行分类：</p>
                        <p>• 按玩具类型分：球类、积木类</p>
                        <p>• 按颜色分：红色、蓝色、绿色</p>
                        <p>分类统计能训练逻辑思维和归纳能力</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer9', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 10</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：逻辑推理】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>小动物排队：</p>
                        <p>小兔排在小猫前面，小狗排在小兔前面，小鸟排在最后。</p>
                        <p>请问它们的排队顺序是怎样的？</p>
                    </div>
                    <div class="question-answer" id="answer10">
                        <h4>答案解析：</h4>
                        <p><strong>答案：小狗 → 小兔 → 小猫 → 小鸟</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>根据题目条件分析：</p>
                        <p>1. 小兔排在小猫前面：小兔在前，小猫在后</p>
                        <p>2. 小狗排在小兔前面：小狗在前，小兔在后</p>
                        <p>3. 小鸟排在最后：小鸟在队伍最后</p>
                        <p>综合这些条件：</p>
                        <p>小狗最前 → 小兔中间 → 小猫次后 → 小鸟最后</p>
                        <p><strong>思维训练：</strong></p>
                        <p>这类题目训练逻辑推理和条件综合分析能力</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer10', this)">查看答案</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入KaTeX和统一的JS -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/contrib/auto-render.min.js"></script>
    <script src="../../assets/course-scripts.js"></script>
</body>
</html> 