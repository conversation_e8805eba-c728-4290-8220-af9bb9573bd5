<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上海幼升小科学常识真题 - 科学常识专项</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css">
    <link rel="stylesheet" href="../../assets/course-styles.css">
</head>
<body>
    <div class="header">
        <h1>上海幼升小科学常识真题</h1>
        <p>上海幼升小科学常识真题，包含生活常识、自然现象、动植物知识、安全知识等内容</p>
    </div>

    <!-- 知识要点 -->
    <div class="section">
        <div class="section-header">📚 知识要点与基础概念</div>
        <div class="section-content">
            <h3>科学常识基础知识</h3>
            <h4>1. 生活常识</h4>
            <p><strong>日常用品</strong>：了解家庭常用物品的用途和特点</p>
            <p><strong>交通工具</strong>：认识各种交通工具及其用途</p>
            <p><strong>职业认知</strong>：了解常见职业及其工作内容</p>
            <p><strong>节日文化</strong>：了解重要传统节日的特点</p>
            
            <h4>2. 自然现象</h4>
            <p><strong>天气变化</strong>：晴天、雨天、雪天、风等天气现象</p>
            <p><strong>季节特征</strong>：春夏秋冬四季的典型特征</p>
            <p><strong>日夜变化</strong>：太阳升起和落下的规律</p>
            <p><strong>水的变化</strong>：水结冰、融化、蒸发等现象</p>
            
            <h4>3. 动植物知识</h4>
            <p><strong>动物分类</strong>：哺乳动物、鸟类、鱼类、昆虫等</p>
            <p><strong>动物习性</strong>：不同动物的生活习惯和特征</p>
            <p><strong>植物组成</strong>：根、茎、叶、花、果实等部分</p>
            <p><strong>植物生长</strong>：种子发芽的基本条件</p>
            
            <h4>4. 安全知识</h4>
            <p><strong>交通安全</strong>：过马路、乘车的安全常识</p>
            <p><strong>用电安全</strong>：不摸插座、不玩电器等</p>
            <p><strong>防火安全</strong>：不玩火、火灾逃生常识</p>
            <p><strong>饮食安全</strong>：不吃变质食物、注意饮食卫生</p>
            
            <h4>5. 环保知识</h4>
            <p><strong>垃圾分类</strong>：可回收垃圾和不可回收垃圾</p>
            <p><strong>节约用水</strong>：随手关水龙头、不浪费水</p>
            <p><strong>爱护环境</strong>：不乱扔垃圾、爱护花草树木</p>
        </div>
    </div>

    <!-- 幼升小科学常识真题 -->
    <div class="section">
        <div class="section-header">📝 上海幼升小科学常识真题</div>
        <div class="section-content">
            <!-- 批量操作按钮 -->
            <div style="margin-bottom: 20px; text-align: center;">
                <button class="answer-btn" onclick="toggleAllAnswers(true)" style="margin-right: 10px;">显示所有答案</button>
                <button class="answer-btn" onclick="toggleAllAnswers(false)">隐藏所有答案</button>
            </div>
            
            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 1</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：季节特征】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>一年有四个季节，请说出这四个季节的名称，并说说每个季节的特点。</p>
                    </div>
                    <div class="question-answer" id="answer1">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p><strong>春季：</strong>天气转暖，花草树木发芽开花，燕子飞回来</p>
                        <p><strong>夏季：</strong>天气炎热，树叶茂盛，可以游泳，吃西瓜</p>
                        <p><strong>秋季：</strong>天气凉爽，叶子变黄掉落，果实成熟</p>
                        <p><strong>冬季：</strong>天气寒冷，会下雪，树木光秃秃的</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer1', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 2</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：动物分类】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>将下面的动物分类：小鸡、金鱼、蝴蝶、小猫、鸽子、鲤鱼</p>
                        <p>请按照它们的生活环境分类。</p>
                    </div>
                    <div class="question-answer" id="answer2">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p><strong>陆地动物：</strong>小鸡、小猫、蝴蝶</p>
                        <p><strong>水中动物：</strong>金鱼、鲤鱼</p>
                        <p><strong>天空动物：</strong>鸽子</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer2', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 3</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：植物生长】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>种子发芽需要什么条件？</p>
                        <p>A. 只要有水就可以 B. 需要水分、温度和空气 C. 只要有阳光就可以</p>
                    </div>
                    <div class="question-answer" id="answer3">
                        <h4>答案解析：</h4>
                        <p><strong>答案：B. 需要水分、温度和空气</strong></p>
                        <p>种子发芽需要适当的水分、温度和空气，三个条件缺一不可。</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer3', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 4</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：交通安全】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>过马路时应该注意什么？请说出3个安全要点。</p>
                    </div>
                    <div class="question-answer" id="answer4">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>1. 红灯停，绿灯行</p>
                        <p>2. 走人行横道（斑马线）</p>
                        <p>3. 过马路前左右看车</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer4', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 5</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：天气现象】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>下雨是怎么形成的？</p>
                        <p>A. 天上的神仙在洒水 B. 云朵里的水珠太重了掉下来 C. 太阳把水变成了雨</p>
                    </div>
                    <div class="question-answer" id="answer5">
                        <h4>答案解析：</h4>
                        <p><strong>答案：B. 云朵里的水珠太重了掉下来</strong></p>
                        <p>水蒸气升到高空变成云，云里的水珠越来越大，太重就掉下来形成雨。</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer5', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 6</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：垃圾分类】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>将下面的垃圾正确分类：苹果核、废纸、塑料瓶、剩饭、易拉罐、菜叶</p>
                        <p>分为：可回收垃圾、厨余垃圾</p>
                    </div>
                    <div class="question-answer" id="answer6">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p><strong>可回收垃圾：</strong>废纸、塑料瓶、易拉罐</p>
                        <p><strong>厨余垃圾：</strong>苹果核、剩饭、菜叶</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer6', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 7</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：人体器官】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>我们的五官是指哪些器官？它们各有什么作用？</p>
                    </div>
                    <div class="question-answer" id="answer7">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>眼睛（看）、耳朵（听）、鼻子（闻）、嘴巴（尝）、舌头（品味）</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer7', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 8</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：职业认知】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>医生、老师、警察、消防员的主要工作是什么？</p>
                    </div>
                    <div class="question-answer" id="answer8">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>医生：给病人看病治病</p>
                        <p>老师：教学生知识</p>
                        <p>警察：维护治安保护人民</p>
                        <p>消防员：扑灭火灾救援人员</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer8', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 9</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：节约用水】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>我们应该怎样节约用水？请说出3个方法。</p>
                    </div>
                    <div class="question-answer" id="answer9">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>1. 随手关水龙头</p>
                        <p>2. 洗菜水浇花</p>
                        <p>3. 洗澡时间不要太长</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer9', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 10</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：食物营养】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>为了身体健康，我们应该怎样合理饮食？</p>
                    </div>
                    <div class="question-answer" id="answer10">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>1. 营养均衡，多吃蔬菜水果</p>
                        <p>2. 三餐规律，按时吃饭</p>
                        <p>3. 少吃零食和垃圾食品</p>
                        <p>4. 多喝白开水</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer10', this)">查看答案</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入KaTeX和统一的JS -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/contrib/auto-render.min.js"></script>
    <script src="../../assets/course-scripts.js"></script>
</body>
</html>
