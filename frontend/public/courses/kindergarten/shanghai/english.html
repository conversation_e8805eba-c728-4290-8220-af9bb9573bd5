<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上海幼升小英语真题 - 英语专项</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css">
    <link rel="stylesheet" href="../../assets/course-styles.css">
</head>
<body>
    <div class="header">
        <h1>上海幼升小英语真题</h1>
        <p>上海幼升小英语真题，包含字母认识、简单单词、基础对话、听力理解等内容</p>
    </div>

    <!-- 知识要点 -->
    <div class="section">
        <div class="section-header">📚 知识要点与基础概念</div>
        <div class="section-content">
            <h3>英语基础知识</h3>
            <h4>1. 字母认识</h4>
            <p><strong>26个英文字母</strong>：能认识和书写大小写字母A-Z, a-z</p>
            <p><strong>字母顺序</strong>：按字母表顺序排列字母</p>
            <p><strong>元音字母</strong>：A, E, I, O, U</p>
            
            <h4>2. 基础单词</h4>
            <p><strong>颜色</strong>：red, blue, green, yellow, black, white</p>
            <p><strong>数字</strong>：one, two, three, four, five, six, seven, eight, nine, ten</p>
            <p><strong>动物</strong>：cat, dog, bird, fish, tiger, elephant</p>
            <p><strong>家庭</strong>：father, mother, brother, sister, baby</p>
            <p><strong>身体部位</strong>：head, eye, nose, mouth, hand, foot</p>
            
            <h4>3. 基础对话</h4>
            <p><strong>问候语</strong>：Hello, Hi, Good morning, Good afternoon</p>
            <p><strong>自我介绍</strong>：My name is..., I am... years old</p>
            <p><strong>礼貌用语</strong>：Please, Thank you, You're welcome</p>
            
            <h4>4. 简单句型</h4>
            <p><strong>This is...</strong>：This is a cat.</p>
            <p><strong>I like...</strong>：I like apples.</p>
            <p><strong>What's this?</strong>：What's this? It's a book.</p>
            
            <h4>5. 发音基础</h4>
            <p><strong>简单音标</strong>：基本元音和辅音的发音</p>
            <p><strong>单词拼读</strong>：简单的拼读技巧</p>
        </div>
    </div>

    <!-- 幼升小英语真题 -->
    <div class="section">
        <div class="section-header">📝 上海幼升小英语真题</div>
        <div class="section-content">
            <!-- 批量操作按钮 -->
            <div style="margin-bottom: 20px; text-align: center;">
                <button class="answer-btn" onclick="toggleAllAnswers(true)" style="margin-right: 10px;">显示所有答案</button>
                <button class="answer-btn" onclick="toggleAllAnswers(false)">隐藏所有答案</button>
            </div>
            
            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 1</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：字母认识】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>请按字母表顺序排列下面的字母：</p>
                        <p style="font-size: 24px; text-align: center;">F, C, A, E, D, B</p>
                    </div>
                    <div class="question-answer" id="answer1">
                        <h4>答案解析：</h4>
                        <p><strong>答案：A, B, C, D, E, F</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>字母表顺序：A B C D E F G H I J K L M N O P Q R S T U V W X Y Z</p>
                        <p>按照字母表顺序，正确排列为：A, B, C, D, E, F</p>
                        <p><strong>学习提示：</strong></p>
                        <p>熟练掌握26个字母的顺序是学习英语的基础</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer1', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 2</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：颜色单词】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>看图选择正确的颜色单词：</p>
                        <p>🍎（红苹果）这是什么颜色？</p>
                        <p>A. blue　　B. red　　C. green　　D. yellow</p>
                    </div>
                    <div class="question-answer" id="answer2">
                        <h4>答案解析：</h4>
                        <p><strong>答案：B. red</strong></p>
                        <p><strong>解析：</strong></p>
                        <p>苹果是红色的，红色的英文是 red</p>
                        <p><strong>常见颜色单词：</strong></p>
                        <p>• red（红色）　• blue（蓝色）</p>
                        <p>• green（绿色）　• yellow（黄色）</p>
                        <p>• black（黑色）　• white（白色）</p>
                        <p>• orange（橙色）　• purple（紫色）</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer2', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 3</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：数字单词】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>将下面的数字与对应的英文单词连线：</p>
                        <p>1　　　　A. five</p>
                        <p>3　　　　B. one</p>
                        <p>5　　　　C. three</p>
                        <p>请写出正确的对应关系。</p>
                    </div>
                    <div class="question-answer" id="answer3">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>1 - B. one</p>
                        <p>3 - C. three</p>
                        <p>5 - A. five</p>
                        <p><strong>解析：</strong></p>
                        <p><strong>1-10的英文数字：</strong></p>
                        <p>one(1), two(2), three(3), four(4), five(5)</p>
                        <p>six(6), seven(7), eight(8), nine(9), ten(10)</p>
                        <p><strong>记忆小技巧：</strong></p>
                        <p>可以通过儿歌或游戏来记忆数字单词</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer3', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 4</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：动物单词】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>看图选择正确的动物单词：</p>
                        <p>🐱 这是什么动物？</p>
                        <p>A. dog　　B. cat　　C. bird　　D. fish</p>
                        <p>🐶 这是什么动物？</p>
                        <p>A. cat　　B. tiger　　C. dog　　D. elephant</p>
                    </div>
                    <div class="question-answer" id="answer4">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>🐱 B. cat（猫）</p>
                        <p>🐶 C. dog（狗）</p>
                        <p><strong>解析：</strong></p>
                        <p><strong>常见动物单词：</strong></p>
                        <p>• cat（猫）　• dog（狗）　• bird（鸟）</p>
                        <p>• fish（鱼）　• tiger（老虎）　• elephant（大象）</p>
                        <p>• monkey（猴子）　• rabbit（兔子）</p>
                        <p><strong>学习建议：</strong></p>
                        <p>可以结合动物的叫声和特征来记忆单词</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer4', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 5</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：问候语】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>选择正确的问候语：</p>
                        <p>早上见到老师，你应该说：</p>
                        <p>A. Good afternoon　　B. Good morning　　C. Good evening</p>
                        <p>当别人说"Thank you"时，你应该回答：</p>
                        <p>A. Hello　　B. Goodbye　　C. You're welcome</p>
                    </div>
                    <div class="question-answer" id="answer5">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>1. B. Good morning（早上好）</p>
                        <p>2. C. You're welcome（不客气）</p>
                        <p><strong>解析：</strong></p>
                        <p><strong>时间问候语：</strong></p>
                        <p>• Good morning（早上好）- 用于上午</p>
                        <p>• Good afternoon（下午好）- 用于下午</p>
                        <p>• Good evening（晚上好）- 用于晚上</p>
                        <p><strong>礼貌用语：</strong></p>
                        <p>• Thank you（谢谢）- You're welcome（不客气）</p>
                        <p>• Please（请）　• Excuse me（打扰一下）</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer5', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 6</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：家庭成员】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>看图选择正确的家庭成员单词：</p>
                        <p>👨 这是我的 ________</p>
                        <p>A. mother　　B. father　　C. sister　　D. brother</p>
                        <p>👩 这是我的 ________</p>
                        <p>A. father　　B. brother　　C. mother　　D. grandfather</p>
                    </div>
                    <div class="question-answer" id="answer6">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>👨 B. father（爸爸）</p>
                        <p>👩 C. mother（妈妈）</p>
                        <p><strong>解析：</strong></p>
                        <p><strong>家庭成员单词：</strong></p>
                        <p>• father（爸爸）　• mother（妈妈）</p>
                        <p>• brother（哥哥/弟弟）　• sister（姐姐/妹妹）</p>
                        <p>• grandfather（爷爷/外公）　• grandmother（奶奶/外婆）</p>
                        <p>• baby（婴儿）</p>
                        <p><strong>实用句型：</strong></p>
                        <p>This is my father/mother.（这是我的爸爸/妈妈）</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer6', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 7</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：身体部位】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>指出下面身体部位的英文单词：</p>
                        <p>眼睛：________　鼻子：________</p>
                        <p>嘴巴：________　手：________</p>
                        <p>选项：eye, nose, mouth, hand</p>
                    </div>
                    <div class="question-answer" id="answer7">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>眼睛：<strong>eye</strong>　鼻子：<strong>nose</strong></p>
                        <p>嘴巴：<strong>mouth</strong>　手：<strong>hand</strong></p>
                        <p><strong>解析：</strong></p>
                        <p><strong>身体部位单词：</strong></p>
                        <p>• head（头）　• eye（眼睛）　• nose（鼻子）</p>
                        <p>• mouth（嘴巴）　• ear（耳朵）</p>
                        <p>• hand（手）　• foot（脚）　• leg（腿）</p>
                        <p><strong>学习活动：</strong></p>
                        <p>可以通过"Head, shoulders, knees and toes"歌曲来学习</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer7', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 8</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：简单对话】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>完成下面的对话：</p>
                        <p>A: Hello! What's your name?</p>
                        <p>B: Hi! My name is ________.</p>
                        <p>A: How old are you?</p>
                        <p>B: I am ________ years old.</p>
                        <p>（假设你叫Tom，6岁）</p>
                    </div>
                    <div class="question-answer" id="answer8">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>B: Hi! My name is <strong>Tom</strong>.</p>
                        <p>B: I am <strong>six</strong> years old.</p>
                        <p><strong>解析：</strong></p>
                        <p><strong>自我介绍句型：</strong></p>
                        <p>• My name is...（我的名字是...）</p>
                        <p>• I am... years old.（我...岁了）</p>
                        <p><strong>常用回答：</strong></p>
                        <p>问名字：What's your name? - My name is...</p>
                        <p>问年龄：How old are you? - I am... years old.</p>
                        <p><strong>年龄表达：</strong></p>
                        <p>five/six/seven years old（5/6/7岁）</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer8', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 9</span>
                    <span class="question-source">2022年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：简单句型】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>选择正确的句子：</p>
                        <p>看到一只猫，你想说"这是一只猫"，应该说：</p>
                        <p>A. This is cat.　　B. This is a cat.　　C. This cat.</p>
                        <p>想表达"我喜欢苹果"，应该说：</p>
                        <p>A. I like apple.　　B. I like apples.　　C. I likes apples.</p>
                    </div>
                    <div class="question-answer" id="answer9">
                        <h4>答案解析：</h4>
                        <p><strong>答案：</strong></p>
                        <p>1. B. This is a cat.（这是一只猫）</p>
                        <p>2. B. I like apples.（我喜欢苹果）</p>
                        <p><strong>解析：</strong></p>
                        <p><strong>This is句型：</strong></p>
                        <p>• This is a + 可数名词单数</p>
                        <p>• This is a cat/dog/book</p>
                        <p><strong>I like句型：</strong></p>
                        <p>• I like + 可数名词复数或不可数名词</p>
                        <p>• I like apples/bananas/milk</p>
                        <p><strong>语法要点：</strong></p>
                        <p>• 可数名词前要加a/an</p>
                        <p>• 喜欢某类事物用复数形式</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer9', this)">查看答案</button>
                </div>
            </div>

            <div class="question-item">
                <div class="question-header">
                    <span class="question-number">题目 10</span>
                    <span class="question-source">2023年上海某小学幼升小面试</span>
                    <span class="knowledge-point">【知识点：元音字母】</span>
                </div>
                <div class="question-content">
                    <div class="question-text">
                        <p>从下面的字母中找出所有的元音字母：</p>
                        <p style="font-size: 24px; text-align: center;">A, B, E, F, I, K, O, P, U, W</p>
                        <p>元音字母有哪些？</p>
                    </div>
                    <div class="question-answer" id="answer10">
                        <h4>答案解析：</h4>
                        <p><strong>答案：A, E, I, O, U</strong></p>
                        <p><strong>解析：</strong></p>
                        <p><strong>英语中的元音字母：</strong></p>
                        <p>A, E, I, O, U（共5个）</p>
                        <p>其余21个字母都是辅音字母</p>
                        <p><strong>元音字母的重要性：</strong></p>
                        <p>• 每个英语单词都至少包含一个元音字母</p>
                        <p>• 元音字母决定音节的划分</p>
                        <p>• 掌握元音有助于单词发音和拼读</p>
                        <p><strong>记忆口诀：</strong></p>
                        <p>"啊诶一哦乌"（A E I O U）</p>
                    </div>
                    <button class="answer-btn" onclick="toggleAnswer('answer10', this)">查看答案</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入KaTeX和统一的JS -->
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/contrib/auto-render.min.js"></script>
    <script src="../../assets/course-scripts.js"></script>
</body>
</html> 