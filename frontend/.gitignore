# KPE Frontend .gitignore (Node.js/Vue3)

# ===== 依赖目录 =====
node_modules/
jspm_packages/

# ===== 构建输出 =====
dist/
build/
.output/
.nuxt/
.next/

# ===== 缓存目录 =====
.cache/
.parcel-cache/
.vite/
.turbo/

# ===== 日志文件 =====
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===== 环境变量文件 =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===== IDE 配置 =====
.idea/
.vscode/
*.swp
*.swo
*~

# ===== 测试覆盖率 =====
coverage/
*.lcov
.nyc_output/

# ===== 临时文件 =====
*.tmp
*.temp
.DS_Store
Thumbs.db

# ===== 包管理器文件 =====
.pnpm-debug.log*
.yarn-integrity

# ===== 运行时文件 =====
*.pid
*.seed
*.pid.lock

# ===== 编辑器配置 =====
.editorconfig.local

# ===== 类型检查 =====
*.tsbuildinfo

# ===== 可选的 npm 缓存目录 =====
.npm

# ===== 可选的 eslint 缓存 =====
.eslintcache

# ===== 可选的 stylelint 缓存 =====
.stylelintcache

# ===== Microbundle 缓存 =====
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===== 可选的 REPL 历史 =====
.node_repl_history

# ===== 输出的 npm 包 =====
*.tgz

# ===== Yarn Integrity 文件 =====
.yarn-integrity

# ===== 环境配置文件 =====
.env
application-local.yml
application-local.properties
application-dev.yml
application-dev.properties
application-prod.yml
application-prod.properties

# ===== parcel-bundler 缓存 =====
.parcel-cache

# ===== Next.js 构建输出 =====
.next

# ===== Nuxt.js 构建/生成输出 =====
.nuxt
dist

# ===== Gatsby 文件 =====
.cache/

# ===== Storybook 构建输出 =====
.out
.storybook-out

# ===== Temporary folders =====
tmp/
temp/

# ===== 本地配置文件 =====
.local
