import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'

export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  // 获取配置值，提供默认值
  const serverHost = env.VITE_SERVER_HOST || '0.0.0.0'
  const serverPort = parseInt(env.VITE_SERVER_PORT) || 5173
  const apiHost = env.VITE_API_HOST || '127.0.0.1'
  const apiPort = parseInt(env.VITE_API_PORT) || 3000
  const apiTarget = `http://${apiHost}:${apiPort}`

  // 检测是否为WebView兼容构建
  const isWebViewBuild = env.VITE_WEBVIEW_BUILD === 'true' || mode === 'webview'

  return {
    plugins: [
      vue(),
      // 添加legacy插件支持，为WebView和老版本浏览器提供兼容性
      legacy({
        targets: [
          'Android >= 4.4', // 支持安卓4.4+
          'iOS >= 9',        // 支持iOS 9+
          'Chrome >= 49',    // 支持Chrome 49+
          'defaults'         // 默认目标浏览器
        ],
        additionalLegacyPolyfills: [
          'regenerator-runtime/runtime' // async/await支持
        ],
        // 始终生成legacy bundle用于WebView
        renderLegacyChunks: true
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./src/test/setup.js']
    },
    server: {
      host: serverHost, // 前端服务器主机
      port: serverPort, // 前端服务器端口
      allowedHosts: true,
      proxy: {
        '/api': {
          target: apiTarget, // 后端API地址: http://host:port
          changeOrigin: true
        }
      }
    },
    optimizeDeps: {
      include: ['monaco-editor', '@monaco-editor/loader']
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      target: 'es2015', // 兼容更老的浏览器
      // WebView兼容性配置
      modulePreload: {
        polyfill: true // 确保模块预加载polyfill启用
      },
      rollupOptions: {
        output: {
          // 为WebView优化的代码分割策略
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            elementPlus: ['element-plus', '@element-plus/icons-vue'],
            monaco: ['monaco-editor', '@monaco-editor/loader']
          },
          // 确保稳定的文件名，便于WebView缓存
          chunkFileNames: isWebViewBuild ? 
            'assets/js/[name]-[hash].js' : 
            'assets/[name]-[hash].js',
          entryFileNames: isWebViewBuild ? 
            'assets/js/[name]-[hash].js' : 
            'assets/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
        }
      }
    },
    esbuild: {
      target: 'es2015'
    },
    base: isWebViewBuild ? './' : '/',
    // 定义全局常量，在构建时替换
    define: {
      __APP_VERSION__: JSON.stringify(env.VITE_APP_VERSION || '1.0.0'),
      __API_TIMEOUT__: parseInt(env.VITE_API_TIMEOUT) || 180000,
      // 添加WebView检测标志
      __IS_WEBVIEW_BUILD__: isWebViewBuild
    }
  }
})
