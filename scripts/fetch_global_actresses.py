import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "global-actresses")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "globalActresses.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 世界各国最具代表的女明星数据 - 按人种分类
GLOBAL_ACTRESSES_DATA = {
    "黄种人": {
        "中国": ["章子怡", "巩俐", "范冰冰", "周迅"],
        "日本": ["石原里美", "新垣结衣", "长泽雅美", "深田恭子"],
        "韩国": ["全智贤", "宋慧乔", "金泰希", "孙艺珍"],
        "泰国": ["平采娜·乐维瑟派布恩", "乌拉萨雅·斯帕邦德"],
        "越南": ["吴青芸"],
        "菲律宾": ["安妮·柯蒂斯", "玛丽安·里维拉"],
        "印度尼西亚": ["阿格妮丝·莫妮卡", "拉伊莎·安德里亚娜"],
        "马来西亚": ["杨紫琼", "梁静茹"],
        "新加坡": ["范文芳", "郑惠玉"],
        "印度": ["艾西瓦娅·雷", "普里扬卡·乔普拉", "卡特莉娜·卡芙", "迪皮卡·帕度柯妮"]
    },
    "白种人": {
        "美国": ["詹妮弗·劳伦斯", "斯嘉丽·约翰逊", "安吉丽娜·朱莉", "梅丽尔·斯特里普", "艾玛·斯通"],
        "英国": ["艾玛·沃特森", "凯特·温斯莱特", "海伦·米伦", "朱迪·丹奇"],
        "法国": ["玛丽昂·歌迪亚", "苏菲·玛索", "伊莎贝尔·于佩尔", "朱丽叶·比诺什"],
        "德国": ["黛安·克鲁格", "弗兰卡·波滕特", "妮娜·霍斯"],
        "意大利": ["莫妮卡·贝鲁奇", "索菲亚·罗兰", "伊莎贝拉·罗西里尼"],
        "西班牙": ["佩内洛普·克鲁兹", "艾尔莎·帕塔奇"],
        "俄罗斯": ["米拉·乔沃维奇", "娜塔莉·沃佳诺娃", "伊琳娜·沙伊克"],
        "澳大利亚": ["妮可·基德曼", "凯特·布兰切特", "娜奥米·沃茨", "玛格特·罗比"],
        "加拿大": ["瑞秋·麦克亚当斯", "艾伦·佩吉", "桑德拉·欧"],
        "瑞典": ["艾丽西亚·维坎德", "诺米·拉佩斯"],
        "丹麦": ["麦斯·米科尔森", "康妮·尼尔森"],
        "荷兰": ["卡里斯·范·侯登", "法米克·詹森"],
        "挪威": ["雷妮·泽尔维格", "阿克塞尔·亨尼"],
        "波兰": ["伊莎贝拉·斯科鲁普科", "阿格涅什卡·格罗乔斯卡"],
        "捷克": ["伊娃·格林", "维罗妮卡·泽曼诺娃"],
        "匈牙利": ["芭芭拉·帕尔文", "维卡·克里克斯"],
        "罗马尼亚": ["亚历山德拉·玛丽亚·拉拉", "莫妮卡·比尔拉德努"],
        "希腊": ["伊莲妮·帕帕斯", "玛丽亚·纳夫普利奥图"],
        "芬兰": ["劳拉·伯恩", "克里斯塔·科索宁"],
        "奥地利": ["克里斯蒂安·贝格", "苏珊娜·沃尔夫"]
    },
    "黑种人": {
        "美国": ["哈莉·贝瑞", "维奥拉·戴维斯", "奥普拉·温弗瑞", "塔拉吉·P·汉森", "露皮塔·尼永奥"],
        "英国": ["娜奥米·哈里斯", "坦迪·牛顿", "列蒂希娅·赖特"],
        "法国": ["奥玛·西", "阿伊萨·迈加"],
        "南非": ["查理兹·塞隆", "特里·费托"],
        "尼日利亚": ["吉纳维芙·纳吉", "奥莫托拉·杰拉德·艾克佩"],
        "埃塞俄比亚": ["莉雅·凯贝德", "露丝·内加"],
        "肯尼亚": ["露皮塔·尼永奥", "瓦里斯·迪里"],
        "加纳": ["伊冯·尼尔森", "妮娅·朗"],
        "塞内加尔": ["艾萨·迈加", "索尼娅·罗兰"],
        "牙买加": ["格蕾丝·琼斯", "娜奥米·坎贝尔"],
        "巴西": ["塔伊斯·阿劳若", "卡米拉·皮坦加"],
        "古巴": ["佐伊·萨尔达娜", "伊娃·门德斯"],
        "多米尼加": ["佐伊·萨尔达娜", "米歇尔·罗德里格斯"],
        "海地": ["加奈儿·梦奈", "维维卡·A·福克斯"],
        "特立尼达和多巴哥": ["妮基·米娜", "海蒂·拉玛"]
    }
}

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_actress(actress_code, actress_name, country, race, save_path):
    """
    使用icrawler获取女明星图片并保存（百度图片搜索）
    """
    # 根据明星姓名构建搜索关键词 - 优先搜索年轻时的照片
    queries = [
        f"{actress_name} 女演员 年轻",
        f"{actress_name} 明星 美丽",
        f"{actress_name} 女星 写真",
        f"{actress_name} actress young",
        f"{actress_name} 演员 肖像",
        f"{actress_name} 明星照片",
        f"{actress_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 百度搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue

        temp_filename = new_files.pop()
        temp_filepath = os.path.join(save_path, temp_filename)

        # 检查文件大小
        file_size = os.path.getsize(temp_filepath)
        if file_size < 5000:  # 小于5KB的图片可能质量不好
            print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
            os.remove(temp_filepath)
            continue

        final_filename = f"{actress_code}.jpg"
        final_filepath = os.path.join(save_path, final_filename)

        # 如果目标文件已存在，删除它
        if os.path.exists(final_filepath):
            os.remove(final_filepath)

        try:
            os.rename(temp_filepath, final_filepath)
            print(f"   ✅ 下载成功: {final_filename}")
            return final_filename
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            continue
    
    print(f"   ⚠️  所有查询都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_global_actresses_js(all_actresses_data):
    """创建globalActresses.js文件"""
    try:
        # 构建新的JS内容
        js_content = "const actressFiles = [\n"
        
        for item in all_actresses_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{item['code']}',\n"
            js_content += f"    name: '{item['name']}',\n"
            js_content += f"    country: '{item['country']}',\n"
            js_content += f"    race: '{item['race']}',\n"
            js_content += f"    fileName: \"{item['fileName']}\"\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const globalActresses = actressFiles.map(item => ({\n"
        js_content += "  ...item,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('global-actresses', item.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ globalActresses.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建globalActresses.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取世界各国最具代表的女明星数据（百度图片搜索）")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    # 开始处理女明星数据
    all_actresses_data = []
    current_code = 1
    
    for race, countries in GLOBAL_ACTRESSES_DATA.items():
        print(f"\n🌍 处理{race}女明星...")
        
        for country, actresses in countries.items():
            print(f"\n  📍 处理{country}女明星...")
            
            for actress_name in actresses:
                actress_code = f"actress_{current_code}"
                print(f"\n[{current_code}] 处理: {actress_name} ({country} - {race})")
                
                # 下载图片
                downloaded_file = fetch_and_download_actress(
                    actress_code, actress_name, country, race, IMAGE_SAVE_PATH
                )
                
                if downloaded_file:
                    local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)
                    
                    # 压缩图片
                    if compress_image(local_file_path):
                        # 上传到OSS
                        oss_key = f"images/global-actresses/{downloaded_file}"
                        if upload_to_oss(bucket, local_file_path, oss_key):
                            all_actresses_data.append({
                                'code': actress_code,
                                'name': actress_name,
                                'country': country,
                                'race': race,
                                'fileName': downloaded_file
                            })
                
                current_code += 1
                time.sleep(1)  # 百度搜索间隔稍长一些避免被限制
    
    print(f"\n📝 处理完成，成功添加了{len(all_actresses_data)}位女明星")
    
    # 创建globalActresses.js文件
    if all_actresses_data:
        create_global_actresses_js(all_actresses_data)
        
        print(f"\n✨ 女明星数据预览：")
        for item in all_actresses_data[:10]:  # 只显示前10个
            print(f"   {item['code']}: {item['name']} ({item['country']} - {item['race']})")
        if len(all_actresses_data) > 10:
            print(f"   ... 还有{len(all_actresses_data)-10}位")
    
    print("\n========================================")
    print("世界各国最具代表的女明星数据获取完成！（百度图片搜索）")
    print("========================================")

if __name__ == "__main__":
    main() 