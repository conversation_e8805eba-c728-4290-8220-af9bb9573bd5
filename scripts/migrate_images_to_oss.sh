#!/bin/bash

# 图片迁移到OSS的完整流程脚本

echo "=========================================="
echo "开始将图片迁移到阿里云OSS"
echo "=========================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到python3，请先安装Python 3"
    exit 1
fi

# 检查oss2依赖
python3 -c "import oss2" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误：未找到oss2库，请先安装：pip install oss2"
    exit 1
fi

echo "步骤1: 更新前端数据文件中的图片URL..."
python3 scripts/update_image_urls.py
if [ $? -ne 0 ]; then
    echo "错误：更新图片URL失败"
    exit 1
fi

echo -e "\n步骤2: 上传图片到阿里云OSS..."
python3 scripts/upload_images_to_oss.py
if [ $? -ne 0 ]; then
    echo "错误：上传图片失败"
    exit 1
fi

echo -e "\n步骤3: 验证OSS图片访问..."
echo "测试访问第一张图片..."
curl -I "https://kpe-system.oss-cn-shanghai.aliyuncs.com/images/animals/animal_1.jpg" | head -1
if [ $? -eq 0 ]; then
    echo "✓ OSS图片访问正常"
else
    echo "✗ OSS图片访问失败"
    exit 1
fi

echo -e "\n=========================================="
echo "图片迁移完成！"
echo "=========================================="
echo "✓ 前端数据文件已更新为OSS链接"
echo "✓ 所有图片已上传到阿里云OSS"
echo "✓ 图片可以正常访问"
echo ""
echo "OSS访问地址: https://kpe-system.oss-cn-shanghai.aliyuncs.com/images/"
echo ""
echo "可选：运行以下命令清理本地图片文件"
echo "python3 scripts/cleanup_local_images.py"
echo "" 