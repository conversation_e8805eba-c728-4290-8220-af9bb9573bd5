import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler, GoogleImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "sports_stars")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "sports_stars.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 世界上最有名的100个体育明星
SPORTS_STARS = [
    # 足球明星 (25个)
    {"name": "梅西", "english_name": "Lionel Messi", "sport": "足球", "country": "阿根廷"},
    {"name": "C罗", "english_name": "Cristiano Ronaldo", "sport": "足球", "country": "葡萄牙"},
    {"name": "内马尔", "english_name": "Neymar", "sport": "足球", "country": "巴西"},
    {"name": "姆巴佩", "english_name": "Kylian Mbappe", "sport": "足球", "country": "法国"},
    {"name": "哈兰德", "english_name": "Erling Haaland", "sport": "足球", "country": "挪威"},
    {"name": "贝利", "english_name": "Pele", "sport": "足球", "country": "巴西"},
    {"name": "马拉多纳", "english_name": "Diego Maradona", "sport": "足球", "country": "阿根廷"},
    {"name": "齐达内", "english_name": "Zinedine Zidane", "sport": "足球", "country": "法国"},
    {"name": "罗纳尔迪尼奥", "english_name": "Ronaldinho", "sport": "足球", "country": "巴西"},
    {"name": "卡卡", "english_name": "Kaka", "sport": "足球", "country": "巴西"},
    {"name": "贝克汉姆", "english_name": "David Beckham", "sport": "足球", "country": "英格兰"},
    {"name": "克鲁伊夫", "english_name": "Johan Cruyff", "sport": "足球", "country": "荷兰"},
    {"name": "普拉蒂尼", "english_name": "Michel Platini", "sport": "足球", "country": "法国"},
    {"name": "巴乔", "english_name": "Roberto Baggio", "sport": "足球", "country": "意大利"},
    {"name": "托蒂", "english_name": "Francesco Totti", "sport": "足球", "country": "意大利"},
    {"name": "德布劳内", "english_name": "Kevin De Bruyne", "sport": "足球", "country": "比利时"},
    {"name": "莫德里奇", "english_name": "Luka Modric", "sport": "足球", "country": "克罗地亚"},
    {"name": "拉莫斯", "english_name": "Sergio Ramos", "sport": "足球", "country": "西班牙"},
    {"name": "范迪克", "english_name": "Virgil van Dijk", "sport": "足球", "country": "荷兰"},
    {"name": "萨拉赫", "english_name": "Mohamed Salah", "sport": "足球", "country": "埃及"},
    {"name": "凯恩", "english_name": "Harry Kane", "sport": "足球", "country": "英格兰"},
    {"name": "本泽马", "english_name": "Karim Benzema", "sport": "足球", "country": "法国"},
    {"name": "格列兹曼", "english_name": "Antoine Griezmann", "sport": "足球", "country": "法国"},
    {"name": "苏亚雷斯", "english_name": "Luis Suarez", "sport": "足球", "country": "乌拉圭"},
    {"name": "莱万多夫斯基", "english_name": "Robert Lewandowski", "sport": "足球", "country": "波兰"},
    
    # 篮球明星 (20个)
    {"name": "迈克尔·乔丹", "english_name": "Michael Jordan", "sport": "篮球", "country": "美国"},
    {"name": "勒布朗·詹姆斯", "english_name": "LeBron James", "sport": "篮球", "country": "美国"},
    {"name": "科比·布莱恩特", "english_name": "Kobe Bryant", "sport": "篮球", "country": "美国"},
    {"name": "斯蒂芬·库里", "english_name": "Stephen Curry", "sport": "篮球", "country": "美国"},
    {"name": "凯文·杜兰特", "english_name": "Kevin Durant", "sport": "篮球", "country": "美国"},
    {"name": "沙奎尔·奥尼尔", "english_name": "Shaquille O'Neal", "sport": "篮球", "country": "美国"},
    {"name": "魔术师约翰逊", "english_name": "Magic Johnson", "sport": "篮球", "country": "美国"},
    {"name": "拉里·伯德", "english_name": "Larry Bird", "sport": "篮球", "country": "美国"},
    {"name": "蒂姆·邓肯", "english_name": "Tim Duncan", "sport": "篮球", "country": "美国"},
    {"name": "哈基姆·奥拉朱旺", "english_name": "Hakeem Olajuwon", "sport": "篮球", "country": "美国"},
    {"name": "卡里姆·阿卜杜尔-贾巴尔", "english_name": "Kareem Abdul-Jabbar", "sport": "篮球", "country": "美国"},
    {"name": "威尔特·张伯伦", "english_name": "Wilt Chamberlain", "sport": "篮球", "country": "美国"},
    {"name": "比尔·拉塞尔", "english_name": "Bill Russell", "sport": "篮球", "country": "美国"},
    {"name": "姚明", "english_name": "Yao Ming", "sport": "篮球", "country": "中国"},
    {"name": "字母哥", "english_name": "Giannis Antetokounmpo", "sport": "篮球", "country": "希腊"},
    {"name": "詹姆斯·哈登", "english_name": "James Harden", "sport": "篮球", "country": "美国"},
    {"name": "拉塞尔·威斯布鲁克", "english_name": "Russell Westbrook", "sport": "篮球", "country": "美国"},
    {"name": "保罗·乔治", "english_name": "Paul George", "sport": "篮球", "country": "美国"},
    {"name": "安东尼·戴维斯", "english_name": "Anthony Davis", "sport": "篮球", "country": "美国"},
    {"name": "卢卡·东契奇", "english_name": "Luka Doncic", "sport": "篮球", "country": "斯洛文尼亚"},
    
    # 网球明星 (15个)
    {"name": "费德勒", "english_name": "Roger Federer", "sport": "网球", "country": "瑞士"},
    {"name": "纳达尔", "english_name": "Rafael Nadal", "sport": "网球", "country": "西班牙"},
    {"name": "德约科维奇", "english_name": "Novak Djokovic", "sport": "网球", "country": "塞尔维亚"},
    {"name": "小威廉姆斯", "english_name": "Serena Williams", "sport": "网球", "country": "美国"},
    {"name": "大威廉姆斯", "english_name": "Venus Williams", "sport": "网球", "country": "美国"},
    {"name": "莎拉波娃", "english_name": "Maria Sharapova", "sport": "网球", "country": "俄罗斯"},
    {"name": "阿加西", "english_name": "Andre Agassi", "sport": "网球", "country": "美国"},
    {"name": "桑普拉斯", "english_name": "Pete Sampras", "sport": "网球", "country": "美国"},
    {"name": "格拉芙", "english_name": "Steffi Graf", "sport": "网球", "country": "德国"},
    {"name": "纳芙拉蒂洛娃", "english_name": "Martina Navratilova", "sport": "网球", "country": "美国"},
    {"name": "穆雷", "english_name": "Andy Murray", "sport": "网球", "country": "英国"},
    {"name": "瓦林卡", "english_name": "Stan Wawrinka", "sport": "网球", "country": "瑞士"},
    {"name": "西里奇", "english_name": "Marin Cilic", "sport": "网球", "country": "克罗地亚"},
    {"name": "哈勒普", "english_name": "Simona Halep", "sport": "网球", "country": "罗马尼亚"},
    {"name": "大坂直美", "english_name": "Naomi Osaka", "sport": "网球", "country": "日本"},
    
    # 田径明星 (15个)
    {"name": "博尔特", "english_name": "Usain Bolt", "sport": "田径", "country": "牙买加"},
    {"name": "刘翔", "english_name": "Liu Xiang", "sport": "田径", "country": "中国"},
    {"name": "苏炳添", "english_name": "Su Bingtian", "sport": "田径", "country": "中国"},
    {"name": "卡尔·刘易斯", "english_name": "Carl Lewis", "sport": "田径", "country": "美国"},
    {"name": "杰西·欧文斯", "english_name": "Jesse Owens", "sport": "田径", "country": "美国"},
    {"name": "埃德·摩西", "english_name": "Edwin Moses", "sport": "田径", "country": "美国"},
    {"name": "迈克尔·约翰逊", "english_name": "Michael Johnson", "sport": "田径", "country": "美国"},
    {"name": "阿什顿·伊顿", "english_name": "Ashton Eaton", "sport": "田径", "country": "美国"},
    {"name": "波拉·拉德克利夫", "english_name": "Paula Radcliffe", "sport": "田径", "country": "英国"},
    {"name": "海勒·格布雷塞拉西", "english_name": "Haile Gebrselassie", "sport": "田径", "country": "埃塞俄比亚"},
    {"name": "谢震业", "english_name": "Xie Zhenye", "sport": "田径", "country": "中国"},
    {"name": "巩立姣", "english_name": "Gong Lijiao", "sport": "田径", "country": "中国"},
    {"name": "王嘉男", "english_name": "Wang Jianan", "sport": "田径", "country": "中国"},
    {"name": "吕会会", "english_name": "Lv Huihui", "sport": "田径", "country": "中国"},
    {"name": "韦永丽", "english_name": "Wei Yongli", "sport": "田径", "country": "中国"},
    
    # 游泳明星 (10个)
    {"name": "菲尔普斯", "english_name": "Michael Phelps", "sport": "游泳", "country": "美国"},
    {"name": "凯蒂·莱德基", "english_name": "Katie Ledecky", "sport": "游泳", "country": "美国"},
    {"name": "亚当·佩蒂", "english_name": "Adam Peaty", "sport": "游泳", "country": "英国"},
    {"name": "马克·施皮茨", "english_name": "Mark Spitz", "sport": "游泳", "country": "美国"},
    {"name": "孙杨", "english_name": "Sun Yang", "sport": "游泳", "country": "中国"},
    {"name": "叶诗文", "english_name": "Ye Shiwen", "sport": "游泳", "country": "中国"},
    {"name": "宁泽涛", "english_name": "Ning Zetao", "sport": "游泳", "country": "中国"},
    {"name": "傅园慧", "english_name": "Fu Yuanhui", "sport": "游泳", "country": "中国"},
    {"name": "张雨霏", "english_name": "Zhang Yufei", "sport": "游泳", "country": "中国"},
    {"name": "汪顺", "english_name": "Wang Shun", "sport": "游泳", "country": "中国"},
    
    # 其他运动明星 (15个)
    {"name": "老虎伍兹", "english_name": "Tiger Woods", "sport": "高尔夫", "country": "美国"},
    {"name": "阿里", "english_name": "Muhammad Ali", "sport": "拳击", "country": "美国"},
    {"name": "泰森", "english_name": "Mike Tyson", "sport": "拳击", "country": "美国"},
    {"name": "汉密尔顿", "english_name": "Lewis Hamilton", "sport": "F1赛车", "country": "英国"},
    {"name": "舒马赫", "english_name": "Michael Schumacher", "sport": "F1赛车", "country": "德国"},
    {"name": "拜尔斯", "english_name": "Simone Biles", "sport": "体操", "country": "美国"},
    {"name": "李宁", "english_name": "Li Ning", "sport": "体操", "country": "中国"},
    {"name": "羽生结弦", "english_name": "Yuzuru Hanyu", "sport": "花样滑冰", "country": "日本"},
    {"name": "林丹", "english_name": "Lin Dan", "sport": "羽毛球", "country": "中国"},
    {"name": "李娜", "english_name": "Li Na", "sport": "网球", "country": "中国"},
    {"name": "丁俊晖", "english_name": "Ding Junhui", "sport": "台球", "country": "中国"},
    {"name": "奥沙利文", "english_name": "Ronnie O'Sullivan", "sport": "台球", "country": "英国"},
    {"name": "梅威瑟", "english_name": "Floyd Mayweather", "sport": "拳击", "country": "美国"},
    {"name": "帕奎奥", "english_name": "Manny Pacquiao", "sport": "拳击", "country": "菲律宾"},
    {"name": "维斯塔潘", "english_name": "Max Verstappen", "sport": "F1赛车", "country": "荷兰"}
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_sports_star(star_code, star_name, english_name, save_path):
    """
    使用百度搜图优先，失败后使用Google搜图获取体育明星图片
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{star_name} 体育明星",
        f"{star_name} 运动员",
        f"{english_name} sports star portrait",
        f"{english_name} athlete photo",
        f"{english_name} professional photo",
        f"{english_name}"
    ]

    # 首先尝试百度搜图
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 百度搜索: '{query}'...")
        try:
            baidu_crawler = BaiduImageCrawler(
                storage={'root_dir': save_path},
                log_level='CRITICAL'
            )
            baidu_crawler.crawl(keyword=query, max_num=1)

            files_after = set(os.listdir(save_path))
            new_files = files_after - files_before

            if new_files:
                temp_filename = new_files.pop()
                temp_filepath = os.path.join(save_path, temp_filename)

                # 检查文件大小
                file_size = os.path.getsize(temp_filepath)
                if file_size < 5000:  # 小于5KB的图片可能质量不好
                    print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
                    os.remove(temp_filepath)
                    continue

                final_filename = f"{star_code}.jpg"
                final_filepath = os.path.join(save_path, final_filename)

                # 如果目标文件已存在，删除它
                if os.path.exists(final_filepath):
                    os.remove(final_filepath)

                try:
                    os.rename(temp_filepath, final_filepath)
                    print(f"   ✅ 百度下载成功: {final_filename}")
                    return final_filename
                except OSError as e:
                    print(f"   ❌ 重命名失败: {e}")
                    if os.path.exists(temp_filepath):
                        os.remove(temp_filepath)
                    continue
        except Exception as e:
            print(f"   ❌ 百度搜图失败: {e}")
            continue

    print(f"   ⚠️  百度搜图全部失败，尝试Google搜图...")

    # 百度失败后尝试Google搜图
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 Google搜索: '{query}'...")
        try:
            google_crawler = GoogleImageCrawler(
                storage={'root_dir': save_path},
                log_level='CRITICAL'
            )
            google_crawler.crawl(keyword=query, max_num=1)

            files_after = set(os.listdir(save_path))
            new_files = files_after - files_before

            if new_files:
                temp_filename = new_files.pop()
                temp_filepath = os.path.join(save_path, temp_filename)

                # 检查文件大小
                file_size = os.path.getsize(temp_filepath)
                if file_size < 5000:  # 小于5KB的图片可能质量不好
                    print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
                    os.remove(temp_filepath)
                    continue

                final_filename = f"{star_code}.jpg"
                final_filepath = os.path.join(save_path, final_filename)

                # 如果目标文件已存在，删除它
                if os.path.exists(final_filepath):
                    os.remove(final_filepath)

                try:
                    os.rename(temp_filepath, final_filepath)
                    print(f"   ✅ Google下载成功: {final_filename}")
                    return final_filename
                except OSError as e:
                    print(f"   ❌ 重命名失败: {e}")
                    if os.path.exists(temp_filepath):
                        os.remove(temp_filepath)
                    continue
        except Exception as e:
            print(f"   ❌ Google搜图失败: {e}")
            continue

    print(f"   ⚠️  所有搜索引擎都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_sports_stars_js(stars_data):
    """创建sports_stars.js文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(JS_DATA_FILE_PATH), exist_ok=True)

        # 构建JS内容
        js_content = "const sportsStarsFiles = [\n"

        for star in stars_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{star['code']}',\n"
            js_content += f"    name: '{star['name']}',\n"
            js_content += f"    englishName: '{star['englishName']}',\n"
            js_content += f"    sport: '{star['sport']}',\n"
            js_content += f"    country: '{star['country']}',\n"
            js_content += f"    fileName: \"{star['fileName']}\"\n"
            js_content += f"  }},\n"

        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const sportsStars = sportsStarsFiles.map(star => ({\n"
        js_content += "  ...star,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('sports_stars', star.fileName)\n"
        js_content += "}));"

        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)

        print(f"✅ sports_stars.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建sports_stars.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取世界体育明星数据（单线程顺序执行）")
    print(f"总共需要处理 {len(SPORTS_STARS)} 个体育明星")
    print("========================================")

    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return

    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)

    print(f"\n⭐ 开始处理 {len(SPORTS_STARS)} 个体育明星...")

    # 顺序处理每个体育明星
    all_stars_data = []

    for index, star in enumerate(SPORTS_STARS, 1):
        star_code = f"star_{index}"
        star_name = star["name"]
        english_name = star["english_name"]
        sport = star["sport"]
        country = star["country"]
        print(f"\n[{index}/{len(SPORTS_STARS)}] 处理: {star_name} ({english_name}) - {sport}")

        # 下载图片
        downloaded_file = fetch_and_download_sports_star(star_code, star_name, english_name, IMAGE_SAVE_PATH)
        if downloaded_file:
            local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)

            # 压缩图片
            if compress_image(local_file_path):
                # 上传到OSS
                oss_key = f"images/sports_stars/{downloaded_file}"
                if upload_to_oss(bucket, local_file_path, oss_key):
                    all_stars_data.append({
                        'code': star_code,
                        'name': star_name,
                        'englishName': english_name,
                        'sport': sport,
                        'country': country,
                        'fileName': downloaded_file
                    })
                    print(f"   ✅ 处理成功")
                else:
                    print(f"   ❌ OSS上传失败")
            else:
                print(f"   ❌ 图片压缩失败")
        else:
            print(f"   ❌ 图片下载失败")

        # 添加延迟避免被限流
        time.sleep(3)

    print(f"\n📝 处理完成，成功添加了{len(all_stars_data)}个体育明星")

    # 创建sports_stars.js文件
    if all_stars_data:
        create_sports_stars_js(all_stars_data)

        print(f"\n✨ 体育明星数据预览：")
        for star in all_stars_data[:5]:  # 只显示前5个
            print(f"   {star['code']}: {star['name']} ({star['englishName']}) - {star['sport']}")
        if len(all_stars_data) > 5:
            print(f"   ... 还有{len(all_stars_data)-5}个")

    print("\n========================================")
    print("体育明星数据获取完成！")
    print("========================================")

if __name__ == "__main__":
    main()
