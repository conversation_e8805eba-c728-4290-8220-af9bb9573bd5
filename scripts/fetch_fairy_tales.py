import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "fairy_tales")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "fairy_tales.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 32个最著名的国内外童话故事和卡通片，按国家分类
FAIRY_TALES = [
    # 中国童话故事和动画片 (8个)
    "哪吒闹海", "大闹天宫", "葫芦兄弟", "黑猫警长",
    "舒克和贝塔", "喜羊羊与灰太狼", "熊出没", "大头儿子小头爸爸",
    
    # 日本动画片 (8个)
    "龙猫", "千与千寻", "天空之城", "魔女宅急便",
    "哆啦A梦", "名侦探柯南", "樱桃小丸子", "蜡笔小新",
    
    # 美国童话故事和动画片 (8个)
    "白雪公主", "灰姑娘", "小红帽", "睡美人",
    "狮子王", "冰雪奇缘", "玩具总动员", "超人总动员",
    
    # 其他国家童话故事 (8个)
    "小美人鱼", "美女与野兽", "长发公主", "阿拉丁",
    "海底总动员", "怪物公司", "飞屋环游记", "疯狂动物城"
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_fairy_tale(tale_code, tale_name, save_path):
    """
    使用icrawler获取童话故事图片并保存（使用百度搜图）
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{tale_name} 童话故事 高清",
        f"{tale_name} 卡通 动画",
        f"{tale_name} 迪士尼",
        f"{tale_name} 动画片",
        f"{tale_name} 儿童故事",
        f"{tale_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ⚠️  未找到新图片")
            continue

        # 找到新下载的图片文件
        new_file = list(new_files)[0]
        old_path = os.path.join(save_path, new_file)
        
        # 检查文件是否为有效图片
        if not os.path.isfile(old_path):
            print(f"   ⚠️  下载的不是有效文件")
            continue
            
        # 获取文件扩展名
        _, ext = os.path.splitext(new_file)
        if not ext.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            print(f"   ⚠️  文件格式不支持: {ext}")
            os.remove(old_path)
            continue
        
        # 重命名文件
        new_filename = f"{tale_code}.jpg"
        new_path = os.path.join(save_path, new_filename)
        
        try:
            # 如果是非JPEG格式，转换为JPEG
            if ext.lower() != '.jpg' and ext.lower() != '.jpeg':
                subprocess.run([
                    'sips', '-s', 'format', 'jpeg', old_path, '--out', new_path
                ], check=True, capture_output=True)
                os.remove(old_path)
            else:
                os.rename(old_path, new_path)
            
            # 压缩图片
            if compress_image(new_path):
                print(f"   ✅ 成功获取并处理: {new_filename}")
                return True
            else:
                print(f"   ❌ 图片压缩失败")
                os.remove(new_path)
                
        except Exception as e:
            print(f"   ❌ 处理图片失败: {e}")
            if os.path.exists(old_path):
                os.remove(old_path)
            if os.path.exists(new_path):
                os.remove(new_path)
            continue
    
    print(f"   ❌ 所有搜索尝试都失败了")
    return False

def upload_to_oss(bucket, local_path, oss_key):
    """上传文件到OSS"""
    try:
        bucket.put_object_from_file(oss_key, local_path)
        return True
    except Exception as e:
        print(f"   ❌ OSS上传失败: {e}")
        return False

def generate_js_data_file():
    """生成JavaScript数据文件"""
    print("\n📝 生成JavaScript数据文件...")
    
    js_content = '''const fairyTaleFiles = [
'''
    
    for i, tale_name in enumerate(FAIRY_TALES, 1):
        tale_code = f'fairy_tale_{i}'
        js_content += f'''  {{
    code: '{tale_code}',
    name: '{tale_name}',
    fileName: "{tale_code}.jpg"
  }},\n'''
    
    js_content += '''];

import imageConfig from '../config/imageConfig.js';

export const fairyTales = fairyTaleFiles.map(tale => ({
  ...tale,
  imageUrl: imageConfig.getImageUrl('fairy_tales', tale.fileName)
}));
'''
    
    try:
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        print(f"✅ JavaScript数据文件已生成: {JS_DATA_FILE_PATH}")
        return True
    except Exception as e:
        print(f"❌ 生成JavaScript数据文件失败: {e}")
        return False

def main():
    print("🎭 开始获取童话故事图片...")
    print(f"📁 图片保存路径: {IMAGE_SAVE_PATH}")
    print(f"📄 数据文件路径: {JS_DATA_FILE_PATH}")
    
    # 创建保存目录
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("⚠️  OSS客户端初始化失败，将跳过OSS上传")
    
    success_count = 0
    total_count = len(FAIRY_TALES)
    
    for i, tale_name in enumerate(FAIRY_TALES, 1):
        tale_code = f'fairy_tale_{i}'
        print(f"\n🎭 [{i}/{total_count}] 处理: {tale_name} ({tale_code})")
        
        # 检查文件是否已存在
        expected_filename = f"{tale_code}.jpg"
        expected_path = os.path.join(IMAGE_SAVE_PATH, expected_filename)
        
        if os.path.exists(expected_path):
            print(f"   ✅ 文件已存在，跳过: {expected_filename}")
            success_count += 1
            continue
        
        # 获取图片
        if fetch_and_download_fairy_tale(tale_code, tale_name, IMAGE_SAVE_PATH):
            success_count += 1
            
            # 上传到OSS
            if bucket:
                oss_key = f"images/fairy_tales/{expected_filename}"
                print(f"   📤 上传到OSS: {oss_key}")
                if upload_to_oss(bucket, expected_path, oss_key):
                    print(f"   ✅ OSS上传成功")
                else:
                    print(f"   ⚠️  OSS上传失败，但本地文件已保存")
        else:
            print(f"   ❌ 获取失败: {tale_name}")
        
        # 添加延迟避免被反爬
        time.sleep(2)
    
    print(f"\n📊 处理完成: {success_count}/{total_count} 成功")
    
    # 生成JavaScript数据文件
    if generate_js_data_file():
        print("\n🎉 童话故事数据获取完成！")
    else:
        print("\n⚠️  JavaScript数据文件生成失败")

if __name__ == "__main__":
    main()