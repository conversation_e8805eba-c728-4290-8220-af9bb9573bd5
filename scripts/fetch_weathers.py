import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "weathers")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "weathers.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 20种最常见的气象
WEATHER_SPECIES = [
    "晴天", "雨天", "阴天", "多云", "雪天",
    "雷阵雨", "雾", "霾", "沙尘暴", "龙卷风",
    "台风", "冰雹", "霜冻", "彩虹", "闪电",
    "暴风雪", "毛毛雨", "阵雨", "飓风", "季风"
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_weather(weather_code, weather_name, save_path):
    """
    使用icrawler获取气象图片并保存（使用百度搜图）
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{weather_name} 气象 高清",
        f"{weather_name} 天气 实拍",
        f"{weather_name} 自然现象",
        f"{weather_name} 气象图",
        f"{weather_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue
        
        # 找到新下载的文件
        new_file = list(new_files)[0]
        new_file_path = os.path.join(save_path, new_file)
        
        # 重命名文件
        new_filename = f"{weather_code}.jpg"
        renamed_path = os.path.join(save_path, new_filename)
        
        try:
            os.rename(new_file_path, renamed_path)
            print(f"   ✅ 下载成功: {new_filename}")
            return renamed_path
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            continue
    
    return None

def generate_js_data_file(weathers, js_file_path):
    """生成JS数据文件"""
    try:
        # 创建数据目录
        os.makedirs(os.path.dirname(js_file_path), exist_ok=True)
        
        # 生成JS内容
        js_content = "export const weathers = [\n"
        for weather in weathers:
            js_content += f"  {{ id: '{weather['id']}', name: '{weather['name']}', image: '/images/weathers/{weather['id']}.jpg' }},\n"
        js_content += "];\n"
        
        # 写入文件
        with open(js_file_path, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ 已生成JS数据文件: {js_file_path}")
        return True
    except Exception as e:
        print(f"❌ 生成JS数据文件失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🌤️ 开始获取气象图片")
    print("=" * 50)
    
    # 创建图片保存目录
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    
    weather_data = []
    
    for idx, weather_name in enumerate(WEATHER_SPECIES):
        weather_code = f"weather_{idx + 1}"
        print(f"\n[{idx + 1}/{len(WEATHER_SPECIES)}] 处理: {weather_name}")
        
        # 下载图片
        image_path = fetch_and_download_weather(weather_code, weather_name, IMAGE_SAVE_PATH)
        
        if not image_path:
            print(f"   ❌ 无法获取 {weather_name} 的图片，跳过")
            continue
        
        # 压缩图片
        if not compress_image(image_path):
            print(f"   ⚠️ 图片压缩失败，但继续处理")
        
        # 上传到OSS
        if bucket:
            oss_key = f"images/weathers/{weather_code}.jpg"
            try:
                bucket.put_object_from_file(oss_key, image_path)
                print(f"   ☁️ 已上传到OSS: {oss_key}")
            except Exception as e:
                print(f"   ❌ OSS上传失败: {e}")
        
        # 添加到数据列表
        weather_data.append({
            "id": weather_code,
            "name": weather_name,
            "image": f"/images/weathers/{weather_code}.jpg"
        })
    
    # 生成JS数据文件
    generate_js_data_file(weather_data, JS_DATA_FILE_PATH)
    
    print("\n✅ 所有气象图片处理完成！")

if __name__ == "__main__":
    main()