#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
from pathlib import Path
import oss2
from dotenv import load_dotenv

# 加载环境变量
env_path = Path(__file__).parent.parent / "backend" / "kpe-web" / ".env"
load_dotenv(env_path)

# OSS配置
OSS_ACCESS_KEY_ID = os.getenv('OSS_ACCESS_KEY_ID')
OSS_ACCESS_KEY_SECRET = os.getenv('OSS_ACCESS_KEY_SECRET')
OSS_ENDPOINT = os.getenv('OSS_ENDPOINT')
OSS_BUCKET_NAME = os.getenv('OSS_BUCKET_NAME')

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"🔧 压缩图片: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    print(f"   📏 原始尺寸: {width}x{height}, 文件大小: {file_size} bytes")
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        print(f"   📏 目标尺寸: {new_width}x{new_height}")
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            print(f"   🔄 缩放到: {new_width}x{new_height}")
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        print(f"   📊 缩放后大小: {current_size} bytes")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            print(f"   🔄 调整质量以减小文件大小...")
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    print(f"   📊 质量 {quality}%: {current_size} bytes")
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            if current_size and current_size > max_file_size:
                print(f"   🔄 进一步缩小尺寸...")
                scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
                for scale in scale_factors:
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None or current_size <= max_file_size:
                        break
                    
                    scaled_width = int(new_width * scale)
                    scaled_height = int(new_height * scale)
                    
                    print(f"   🔄 缩放到: {scaled_width}x{scaled_height}")
                    subprocess.run([
                        'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                    ], check=True, capture_output=True)
        
        # 获取最终信息
        final_width, final_height, final_size = get_image_info(image_path)
        print(f"   ✅ 压缩完成: {final_width}x{final_height}, {final_size} bytes")
        
        # 删除备份
        os.remove(backup_path)
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        print(f"📤 上传到OSS: {oss_key}")
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败，状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("使用方法: python upload_single_image.py <图片路径> <OSS文件名>")
        print("示例: python upload_single_image.py /path/to/actress_1.jpg actress_1.jpg")
        sys.exit(1)
    
    image_path = sys.argv[1]
    oss_filename = sys.argv[2]
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        sys.exit(1)
    
    print("=" * 50)
    print(f"上传图片到OSS: {os.path.basename(image_path)}")
    print("=" * 50)
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("❌ 无法初始化OSS客户端，请检查环境变量配置")
        sys.exit(1)
    
    # 创建临时工作副本
    temp_path = Path("temp_" + os.path.basename(image_path))
    try:
        # 复制文件到临时位置
        subprocess.run(['cp', image_path, str(temp_path)], check=True)
        
        # 压缩图片
        if not compress_image(temp_path):
            print(f"❌ 压缩图片失败")
            return
        
        # 上传到OSS
        oss_key = f"images/global-actresses/{oss_filename}"
        if upload_to_oss(bucket, temp_path, oss_key):
            print(f"✅ 图片上传完成！")
            print(f"🌐 OSS路径: {oss_key}")
        else:
            print(f"❌ 图片上传失败")
    
    finally:
        # 清理临时文件
        if temp_path.exists():
            temp_path.unlink()
    
    print("=" * 50)
    print("任务完成")
    print("=" * 50)

if __name__ == "__main__":
    main() 