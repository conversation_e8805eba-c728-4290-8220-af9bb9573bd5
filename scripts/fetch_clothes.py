import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import GoogleImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "clothes")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "clothes.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 衣服、裤子、袜子和帽子数据 - 衣服15个，裤子10个，袜子5个，帽子5个最常见的类型
CLOTHES_DATA = {
    "衣服": [
        "T恤", "衬衫", "西装", "毛衣", "卫衣",
        "夹克", "风衣", "羽绒服", "连衣裙", "背心",
        "马甲", "开衫", "外套", "大衣", "棉服"
    ],
    "裤子": [
        "牛仔裤", "休闲裤", "西装裤", "运动裤", "短裤",
        "沙滩裤", "紧身裤", "阔腿裤", "工装裤", "打底裤"
    ],
    "袜子": [
        "运动袜", "商务袜", "船袜", "长筒袜", "丝袜"
    ],
    "帽子": [
        "棒球帽", "毛线帽", "贝雷帽", "渔夫帽", "鸭舌帽"
    ]
}

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_clothes(clothes_code, clothes_name, category, save_path):
    """
    使用icrawler获取衣服裤子袜子图片并保存
    """
    # 根据类别和服装类型构建搜索关键词
    if category == '衣服':
        queries = [
            f"{clothes_name} clothing",
            f"{clothes_name} shirt",
            f"{clothes_name} 服装",
            f"{clothes_name} fashion"
        ]
    elif category == '裤子':
        queries = [
            f"{clothes_name} pants",
            f"{clothes_name} trousers",
            f"{clothes_name} 裤子",
            f"{clothes_name} fashion"
        ]
    elif category == '袜子':
        queries = [
            f"{clothes_name} socks",
            f"{clothes_name} hosiery",
            f"{clothes_name} 袜子",
            f"{clothes_name} fashion"
        ]
    elif category == '帽子':
        queries = [
            f"{clothes_name} hat",
            f"{clothes_name} cap",
            f"{clothes_name} 帽子",
            f"{clothes_name} headwear"
        ]
    else:
        queries = [f"{clothes_name}", f"{clothes_name} clothing"]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 搜索: '{query}'...")
        crawler = GoogleImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue

        temp_filename = new_files.pop()
        temp_filepath = os.path.join(save_path, temp_filename)

        # 检查文件大小
        file_size = os.path.getsize(temp_filepath)
        if file_size < 5000:  # 小于5KB的图片可能质量不好
            print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
            os.remove(temp_filepath)
            continue

        final_filename = f"{clothes_code}.jpg"
        final_filepath = os.path.join(save_path, final_filename)

        # 如果目标文件已存在，删除它
        if os.path.exists(final_filepath):
            os.remove(final_filepath)

        try:
            os.rename(temp_filepath, final_filepath)
            print(f"   ✅ 下载成功: {final_filename}")
            return final_filename
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            continue
    
    print(f"   ⚠️  所有查询都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_clothes_js(all_clothes_data):
    """创建clothes.js文件"""
    try:
        # 构建新的JS内容
        js_content = "const clothesFiles = [\n"
        
        for item in all_clothes_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{item['code']}',\n"
            js_content += f"    name: '{item['name']}',\n"
            js_content += f"    fileName: \"{item['fileName']}\",\n"
            js_content += f"    category: '{item['category']}'\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const clothes = clothesFiles.map(item => ({\n"
        js_content += "  ...item,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('clothes', item.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ clothes.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建clothes.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取衣服、裤子、袜子和帽子识别数据（衣服15个，裤子10个，袜子5个，帽子5个常见类型）")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    # 开始处理衣服裤子数据
    all_clothes_data = []
    current_code = 1
    
    for category, clothes_list in CLOTHES_DATA.items():
        print(f"\n👔 处理{category}类型...")
        
        for clothes_name in clothes_list:
            clothes_code = f"clothes_{current_code}"
            print(f"\n[{current_code}/35] 处理: {clothes_name} ({category})")
            
            # 下载图片
            downloaded_file = fetch_and_download_clothes(
                clothes_code, clothes_name, category, IMAGE_SAVE_PATH
            )
            
            if downloaded_file:
                local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)
                
                # 压缩图片
                if compress_image(local_file_path):
                    # 上传到OSS
                    oss_key = f"images/clothes/{downloaded_file}"
                    if upload_to_oss(bucket, local_file_path, oss_key):
                        all_clothes_data.append({
                            'code': clothes_code,
                            'name': clothes_name,
                            'fileName': downloaded_file,
                            'category': category
                        })
            
            current_code += 1
            time.sleep(0.5)  # 避免请求过于频繁
    
    print(f"\n📝 处理完成，成功添加了{len(all_clothes_data)}个服装类型")
    print(f"   总计: {current_code-1} 个类型")
    
    # 创建clothes.js文件
    if all_clothes_data:
        create_clothes_js(all_clothes_data)
        
        print(f"\n✨ 服装数据预览：")
        for item in all_clothes_data[:10]:  # 只显示前10个
            print(f"   {item['code']}: {item['name']} ({item['category']})")
        if len(all_clothes_data) > 10:
            print(f"   ... 还有{len(all_clothes_data)-10}个")
    
    print("\n========================================")
    print("衣服、裤子、袜子和帽子识别数据获取完成！")
    print("========================================")

if __name__ == "__main__":
    main()