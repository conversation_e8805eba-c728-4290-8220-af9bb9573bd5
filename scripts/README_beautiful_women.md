# 世界最美女人数据获取脚本使用说明

## 概述

`fetch_beautiful_women.py` 脚本用于获取世界最美女人数据，下载图片并上传到阿里云OSS，最后生成前端数据文件。

## 功能特性

1. **数据定义**: 包含30位世界著名女性明星的详细信息
2. **图片下载**: 从指定URL下载高质量图片
3. **图片压缩**: 自动压缩图片到150KB以下，最大尺寸800px
4. **OSS上传**: 上传到阿里云OSS存储
5. **前端集成**: 生成前端可用的JavaScript数据文件

## 依赖安装

```bash
# 安装Python依赖
pip install requests Pillow oss2
```

## 使用步骤

### 1. 准备图片URL

脚本中的 `sample_urls` 字典需要配置真实的图片URL：

```python
sample_urls = {
    'beauty_1': 'https://real-image-url.com/audrey_hepburn.jpg',
    'beauty_2': 'https://real-image-url.com/marilyn_monroe.jpg',
    # 添加更多真实URL
}
```

### 2. 手动下载图片（推荐）

由于版权和可用性问题，建议手动下载图片：

1. 在 `frontend/public/images/beautiful_women/` 目录下手动保存图片
2. 图片命名格式：`beauty_1.jpg`, `beauty_2.jpg`, ..., `beauty_30.jpg`
3. 建议图片尺寸：800x800px以内，文件大小150KB以下

### 3. 运行脚本

```bash
cd /Users/<USER>/work/kpe
python3 scripts/fetch_beautiful_women.py
```

## 脚本工作流程

1. **初始化**: 创建必要的目录和OSS客户端
2. **数据获取**: 加载30位女性明星的基础信息
3. **图片处理**: 
   - 检查本地是否已有图片
   - 如果配置了URL，尝试下载
   - 如果没有，提示手动下载
4. **OSS上传**: 
   - 检查图片文件大小
   - 如果超过150KB，自动压缩
   - 上传到OSS并获取公共URL
5. **生成文件**: 
   - 创建前端JavaScript数据文件
   - 保存完整JSON数据文件

## 输出文件

- `frontend/src/data/beautiful_women.js` - 前端数据文件
- `output/beautiful_women/beautiful_women_data.json` - 完整数据文件
- `frontend/public/images/beautiful_women/` - 本地图片目录

## OSS配置

脚本使用以下OSS配置：
- 端点：oss-cn-shanghai.aliyuncs.com
- 存储桶：kpe-system
- 图片路径：images/beautiful_women/

## 注意事项

1. **版权问题**: 确保使用的图片有合法使用权限
2. **图片质量**: 建议使用高质量的官方宣传照
3. **文件大小**: 脚本会自动压缩大文件，但建议预先优化
4. **网络连接**: 下载和上传需要稳定的网络连接
5. **OSS权限**: 确保OSS账号有上传权限

## 故障排除

### 常见错误

1. **ModuleNotFoundError**: 安装缺失的Python包
2. **OSS权限错误**: 检查OSS配置和权限
3. **下载失败**: 检查图片URL是否有效
4. **压缩失败**: 检查PIL库是否正确安装

### 调试建议

1. 先手动下载几张图片测试
2. 检查OSS连接是否正常
3. 查看脚本输出的详细日志
4. 确认目录权限正确

## 扩展功能

可以根据需要扩展脚本：

1. **批量URL配置**: 从CSV文件读取图片URL
2. **图片搜索API**: 集成图片搜索服务
3. **多格式支持**: 支持PNG、WebP等格式
4. **缓存机制**: 避免重复下载和上传

## 相关文件

- `upload_images_to_oss.py` - 通用OSS上传脚本
- `quizFactory.js` - 前端考试工厂
- `quizConfig.js` - 考试配置文件