#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
世界最美女人数据获取脚本

功能:
1. 定义30位世界最美女性明星数据
2. 下载并处理图片
3. 上传到OSS
4. 生成前端数据文件

作者: AI Assistant
日期: 2024
"""

import os
import sys
import json
import requests
import tempfile
import subprocess
import re
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
from PIL import Image
import oss2
import time
from icrawler.builtin import BaiduImageCrawler, GoogleImageCrawler

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 压缩配置
MAX_FILE_SIZE = 150 * 1024  # 150KB
QUALITY_STEPS = [85, 75, 65, 55, 45]  # 逐步降低质量
MAX_DIMENSION = 800  # 最大尺寸

class BeautifulWomenFetcher:
    """世界最美女人数据获取器"""
    
    def __init__(self):
        # 输出目录
        self.output_dir = os.path.join(os.path.dirname(__file__), '..', 'output', 'beautiful_women')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 前端数据文件路径
        self.frontend_data_path = os.path.join(
            os.path.dirname(__file__), '..', 'frontend', 'src', 'data', 'beautiful_women.js'
        )
        
        # 图片保存目录
        self.images_dir = Path(os.path.dirname(__file__)) / '..' / 'frontend' / 'public' / 'images' / 'beautiful_women'
        self.images_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化OSS客户端
        self.bucket = self.init_oss_client()
    
    def init_oss_client(self):
        """初始化OSS客户端"""
        try:
            auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
            bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
            return bucket
        except Exception as e:
            print(f"初始化OSS客户端失败: {e}")
            return None
    
    def compress_image(self, image_path: Path, max_file_size: int = 150 * 1024, max_dimension: int = 800) -> bool:
        """压缩单个图片"""
        print(f"   🔧 压缩: {image_path.name}")
        
        # 获取原始图片信息
        width, height, file_size = self.get_image_info(image_path)
        if width is None or height is None or file_size is None:
            return False
        
        # 如果文件已经符合要求，跳过
        if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
            print(f"   ✅ 已符合要求，跳过压缩")
            return True
        
        # 创建备份
        backup_path = str(image_path) + '.backup'
        try:
            subprocess.run(['cp', str(image_path), backup_path], check=True)
        except subprocess.CalledProcessError:
            print(f"   ❌ 创建备份失败")
            return False
        
        try:
            # 计算新尺寸
            new_width, new_height = self.calculate_resize_dimensions(width, height, max_dimension)
            
            # 第一步：如果需要，先缩放尺寸
            if new_width != width or new_height != height:
                subprocess.run([
                    'sips', '-z', str(new_height), str(new_width), str(image_path)
                ], check=True, capture_output=True)
            
            # 检查缩放后的文件大小
            _, _, current_size = self.get_image_info(image_path)
            if current_size is None:
                raise Exception("无法获取缩放后的文件大小")
            
            # 第二步：如果文件仍然太大，调整质量
            if current_size > max_file_size:
                # 对于JPEG文件，尝试调整质量
                if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                    quality_levels = [90, 80, 70, 60, 50, 40]
                    for quality in quality_levels:
                        subprocess.run([
                            'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                        ], check=True, capture_output=True)
                        
                        _, _, current_size = self.get_image_info(image_path)
                        if current_size is None:
                            continue
                        
                        if current_size <= max_file_size:
                            break
                
                # 如果还是太大，进一步缩小尺寸
                scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
                for scale in scale_factors:
                    _, _, current_size = self.get_image_info(image_path)
                    if current_size is None or current_size <= max_file_size:
                        break
                    
                    scaled_width = int(new_width * scale)
                    scaled_height = int(new_height * scale)
                    
                    subprocess.run([
                        'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                    ], check=True, capture_output=True)
            
            # 删除备份
            os.remove(backup_path)
            print(f"   ✅ 压缩完成")
            return True
            
        except Exception as e:
            print(f"   ❌ 压缩失败: {e}")
            # 恢复备份
            try:
                subprocess.run(['mv', backup_path, str(image_path)], check=True)
            except:
                pass
            return False
    
    def get_image_info(self, image_path: Path) -> tuple:
        """获取图片信息"""
        try:
            # 获取图片尺寸
            result = subprocess.run([
                'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
            ], capture_output=True, text=True, check=True)
            
            lines = result.stdout.strip().split('\n')
            width = None
            height = None
            
            for line in lines:
                if 'pixelWidth:' in line:
                    width = int(line.split(':')[1].strip())
                elif 'pixelHeight:' in line:
                    height = int(line.split(':')[1].strip())
            
            # 获取文件大小
            file_size = os.path.getsize(image_path)
            
            return width, height, file_size
        except (subprocess.CalledProcessError, ValueError, OSError) as e:
            print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
            return None, None, None
    
    def calculate_resize_dimensions(self, width: int, height: int, max_size: int = 800) -> tuple:
        """计算缩放后的尺寸"""
        if width <= max_size and height <= max_size:
            return width, height
        
        if width > height:
            new_width = max_size
            new_height = int(height * max_size / width)
        else:
            new_height = max_size
            new_width = int(width * max_size / height)
        
        return new_width, new_height
    
    def fetch_and_download_beautiful_woman(self, woman_code: str, woman_name: str) -> Optional[str]:
        """使用百度搜图优先，失败后使用Google搜图获取美女图片"""
        # 尝试多个搜索关键词来提高成功率
        queries = [
            f"{woman_name} 美女明星",
            f"{woman_name} 女明星",
            f"{woman_name} beautiful woman",
            f"{woman_name} actress photo",
            f"{woman_name} professional photo",
            f"{woman_name}"
        ]

        # 首先尝试百度搜图
        for query in queries:
            files_before = set(os.listdir(self.images_dir))

            print(f"   🔍 百度搜索: '{query}'...")
            try:
                baidu_crawler = BaiduImageCrawler(
                    storage={'root_dir': str(self.images_dir)},
                    log_level='CRITICAL'
                )
                baidu_crawler.crawl(keyword=query, max_num=1)

                files_after = set(os.listdir(self.images_dir))
                new_files = files_after - files_before

                if new_files:
                    temp_filename = new_files.pop()
                    temp_filepath = self.images_dir / temp_filename

                    # 检查文件大小
                    file_size = os.path.getsize(temp_filepath)
                    if file_size < 5000:  # 小于5KB的图片可能质量不好
                        print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
                        os.remove(temp_filepath)
                        continue

                    final_filename = f"{woman_code}.jpg"
                    final_filepath = self.images_dir / final_filename

                    # 如果目标文件已存在，删除它
                    if final_filepath.exists():
                        final_filepath.unlink()

                    try:
                        temp_filepath.rename(final_filepath)
                        print(f"   ✅ 百度下载成功: {final_filename}")
                        return final_filename
                    except OSError as e:
                        print(f"   ❌ 重命名失败: {e}")
                        if temp_filepath.exists():
                            temp_filepath.unlink()
                        continue
            except Exception as e:
                print(f"   ❌ 百度搜图失败: {e}")
                continue

        print(f"   ⚠️  百度搜图全部失败，尝试Google搜图...")

        # 百度失败后尝试Google搜图
        for query in queries:
            files_before = set(os.listdir(self.images_dir))

            print(f"   🔍 Google搜索: '{query}'...")
            try:
                google_crawler = GoogleImageCrawler(
                    storage={'root_dir': str(self.images_dir)},
                    log_level='CRITICAL'
                )
                google_crawler.crawl(keyword=query, max_num=1)

                files_after = set(os.listdir(self.images_dir))
                new_files = files_after - files_before

                if new_files:
                    temp_filename = new_files.pop()
                    temp_filepath = self.images_dir / temp_filename

                    # 检查文件大小
                    file_size = os.path.getsize(temp_filepath)
                    if file_size < 5000:  # 小于5KB的图片可能质量不好
                        print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
                        os.remove(temp_filepath)
                        continue

                    final_filename = f"{woman_code}.jpg"
                    final_filepath = self.images_dir / final_filename

                    # 如果目标文件已存在，删除它
                    if final_filepath.exists():
                        final_filepath.unlink()

                    try:
                        temp_filepath.rename(final_filepath)
                        print(f"   ✅ Google下载成功: {final_filename}")
                        return final_filename
                    except OSError as e:
                        print(f"   ❌ 重命名失败: {e}")
                        if temp_filepath.exists():
                            temp_filepath.unlink()
                        continue
            except Exception as e:
                print(f"   ❌ Google搜图失败: {e}")
                continue

        print(f"   ⚠️  所有搜索引擎都失败了")
        return None
    
    def upload_to_oss(self, local_path: Path, oss_key: str) -> Optional[str]:
        """上传图片到OSS"""
        if not self.bucket:
            print("OSS客户端未初始化")
            return None
        
        try:
            # 检查文件大小
            file_size = local_path.stat().st_size
            print(f"  上传: {oss_key} ({file_size} 字节)")
            
            upload_path = local_path
            temp_path = None
            
            # 如果文件过大，进行压缩
            if file_size > MAX_FILE_SIZE:
                print(f"    文件过大，开始压缩...")
                temp_path = self.compress_image(local_path, MAX_FILE_SIZE)
                upload_path = temp_path
            
            # 上传文件
            result = self.bucket.put_object_from_file(oss_key, str(upload_path))
            
            # 清理临时文件
            if temp_path and temp_path.exists():
                temp_path.unlink()
            
            # 检查是否上传成功
            if result.status == 200:
                oss_url = f"https://{OSS_BUCKET_NAME}.{OSS_ENDPOINT}/{oss_key}"
                print(f"  ✓ 上传成功: {oss_url}")
                return oss_url
            else:
                print(f"  ✗ 上传失败: {oss_key}, 状态码: {result.status}")
                return None
                
        except Exception as e:
            print(f"  ✗ 上传失败: {oss_key}, 错误: {e}")
            return None
    
    def get_beautiful_women_data(self) -> List[Dict[str, Any]]:
        """获取世界最美女人数据"""
        return [
            {
                'code': 'beauty_1',
                'name': '奥黛丽·赫本',
                'english_name': 'Audrey Hepburn',
                'country': '英国',
                'profession': '演员',
                'birth_year': 1929,
                'famous_works': ['罗马假日', '蒂凡尼的早餐', '窈窕淑女']
            },
            {
                'code': 'beauty_2',
                'name': '玛丽莲·梦露',
                'english_name': 'Marilyn Monroe',
                'country': '美国',
                'profession': '演员',
                'birth_year': 1926,
                'famous_works': ['七年之痒', '热情如火', '不合身的人']
            },
            {
                'code': 'beauty_3',
                'name': '安吉丽娜·朱莉',
                'english_name': 'Angelina Jolie',
                'country': '美国',
                'profession': '演员',
                'birth_year': 1975,
                'famous_works': ['古墓丽影', '史密斯夫妇', '换子疑云']
            },
            {
                'code': 'beauty_4',
                'name': '斯嘉丽·约翰逊',
                'english_name': 'Scarlett Johansson',
                'country': '美国',
                'profession': '演员',
                'birth_year': 1984,
                'famous_works': ['复仇者联盟', '迷失东京', '她']
            },
            {
                'code': 'beauty_5',
                'name': '娜塔莉·波特曼',
                'english_name': 'Natalie Portman',
                'country': '美国',
                'profession': '演员',
                'birth_year': 1981,
                'famous_works': ['黑天鹅', '星球大战', 'V字仇杀队']
            },
            {
                'code': 'beauty_6',
                'name': '查理兹·塞隆',
                'english_name': 'Charlize Theron',
                'country': '南非',
                'profession': '演员',
                'birth_year': 1975,
                'famous_works': ['女魔头', '疯狂的麦克斯', '原子金发女郎']
            },
            {
                'code': 'beauty_7',
                'name': '妮可·基德曼',
                'english_name': 'Nicole Kidman',
                'country': '澳大利亚',
                'profession': '演员',
                'birth_year': 1967,
                'famous_works': ['红磨坊', '时时刻刻', '大小谎言']
            },
            {
                'code': 'beauty_8',
                'name': '凯特·温斯莱特',
                'english_name': 'Kate Winslet',
                'country': '英国',
                'profession': '演员',
                'birth_year': 1975,
                'famous_works': ['泰坦尼克号', '朗读者', '革命之路']
            },
            {
                'code': 'beauty_9',
                'name': '艾玛·斯通',
                'english_name': 'Emma Stone',
                'country': '美国',
                'profession': '演员',
                'birth_year': 1988,
                'famous_works': ['爱乐之城', '超凡蜘蛛侠', '宠儿']
            },
            {
                'code': 'beauty_10',
                'name': '范冰冰',
                'english_name': 'Fan Bingbing',
                'country': '中国',
                'profession': '演员',
                'birth_year': 1981,
                'famous_works': ['武媚娘传奇', 'X战警', '苹果']
            },
            {
                'code': 'beauty_11',
                'name': '刘亦菲',
                'english_name': 'Liu Yifei',
                'country': '中国',
                'profession': '演员',
                'birth_year': 1987,
                'famous_works': ['花木兰', '仙剑奇侠传', '三生三世十里桃花']
            },
            {
                'code': 'beauty_12',
                'name': '章子怡',
                'english_name': 'Zhang Ziyi',
                'country': '中国',
                'profession': '演员',
                'birth_year': 1979,
                'famous_works': ['卧虎藏龙', '艺伎回忆录', '一代宗师']
            },
            {
                'code': 'beauty_13',
                'name': '巩俐',
                'english_name': 'Gong Li',
                'country': '中国',
                'profession': '演员',
                'birth_year': 1965,
                'famous_works': ['红高粱', '大红灯笼高高挂', '花木兰']
            },
            {
                'code': 'beauty_14',
                'name': '林志玲',
                'english_name': 'Lin Chi-ling',
                'country': '中国台湾',
                'profession': '模特/演员',
                'birth_year': 1974,
                'famous_works': ['赤壁', '决战刹马镇', '101次求婚']
            },
            {
                'code': 'beauty_15',
                'name': '全智贤',
                'english_name': 'Jun Ji-hyun',
                'country': '韩国',
                'profession': '演员',
                'birth_year': 1981,
                'famous_works': ['我的野蛮女友', '来自星星的你', '暗杀']
            },
            {
                'code': 'beauty_16',
                'name': '宋慧乔',
                'english_name': 'Song Hye-kyo',
                'country': '韩国',
                'profession': '演员',
                'birth_year': 1981,
                'famous_works': ['太阳的后裔', '蓝色生死恋', '男朋友']
            },
            {
                'code': 'beauty_17',
                'name': '金泰熙',
                'english_name': 'Kim Tae-hee',
                'country': '韩国',
                'profession': '演员',
                'birth_year': 1980,
                'famous_works': ['天国的阶梯', 'IRIS', '龙八夷']
            },
            {
                'code': 'beauty_18',
                'name': '石原里美',
                'english_name': 'Ishihara Satomi',
                'country': '日本',
                'profession': '演员',
                'birth_year': 1986,
                'famous_works': ['失恋巧克力职人', '朝5晚9', '非自然死亡']
            },
            {
                'code': 'beauty_19',
                'name': '新垣结衣',
                'english_name': 'Aragaki Yui',
                'country': '日本',
                'profession': '演员',
                'birth_year': 1988,
                'famous_works': ['恋爱世代', '逃避虽可耻但有用', '龙樱']
            },
            {
                'code': 'beauty_20',
                'name': '佐佐木希',
                'english_name': 'Sasaki Nozomi',
                'country': '日本',
                'profession': '模特/演员',
                'birth_year': 1988,
                'famous_works': ['天使之恋', '神探伽利略', '东京爱情故事']
            },
            {
                'code': 'beauty_21',
                'name': '莫妮卡·贝鲁奇',
                'english_name': 'Monica Bellucci',
                'country': '意大利',
                'profession': '演员/模特',
                'birth_year': 1964,
                'famous_works': ['西西里的美丽传说', '黑客帝国', '007：幽灵党']
            },
            {
                'code': 'beauty_22',
                'name': '苏菲·玛索',
                'english_name': 'Sophie Marceau',
                'country': '法国',
                'profession': '演员',
                'birth_year': 1966,
                'famous_works': ['初吻', '勇敢的心', '安娜·卡列尼娜']
            },
            {
                'code': 'beauty_23',
                'name': '佩内洛普·克鲁兹',
                'english_name': 'Penelope Cruz',
                'country': '西班牙',
                'profession': '演员',
                'birth_year': 1974,
                'famous_works': ['午夜巴塞罗那', '香草天空', '九']
            },
            {
                'code': 'beauty_24',
                'name': '艾玛·沃森',
                'english_name': 'Emma Watson',
                'country': '英国',
                'profession': '演员',
                'birth_year': 1990,
                'famous_works': ['哈利·波特', '美女与野兽', '小妇人']
            },
            {
                'code': 'beauty_25',
                'name': '凯拉·奈特莉',
                'english_name': 'Keira Knightley',
                'country': '英国',
                'profession': '演员',
                'birth_year': 1985,
                'famous_works': ['加勒比海盗', '傲慢与偏见', '赎罪']
            },
            {
                'code': 'beauty_26',
                'name': '海蒂·克鲁姆',
                'english_name': 'Heidi Klum',
                'country': '德国',
                'profession': '超模',
                'birth_year': 1973,
                'famous_works': ['维多利亚的秘密', '天桥骄子', '美国达人秀']
            },
            {
                'code': 'beauty_27',
                'name': '吉赛尔·邦辰',
                'english_name': 'Gisele Bundchen',
                'country': '巴西',
                'profession': '超模',
                'birth_year': 1980,
                'famous_works': ['维多利亚的秘密', '香奈儿', '路易威登']
            },
            {
                'code': 'beauty_28',
                'name': '米兰达·可儿',
                'english_name': 'Miranda Kerr',
                'country': '澳大利亚',
                'profession': '超模',
                'birth_year': 1983,
                'famous_works': ['维多利亚的秘密', 'KORA Organics', '施华洛世奇']
            },
            {
                'code': 'beauty_29',
                'name': '亚历山大·安布罗休',
                'english_name': 'Alessandra Ambrosio',
                'country': '巴西',
                'profession': '超模',
                'birth_year': 1981,
                'famous_works': ['维多利亚的秘密', '杜嘉班纳', '阿玛尼']
            },
            {
                'code': 'beauty_30',
                'name': '阿德里亚娜·利马',
                'english_name': 'Adriana Lima',
                'country': '巴西',
                'profession': '超模',
                'birth_year': 1981,
                'famous_works': ['维多利亚的秘密', '美宝莲', '超级碗广告']
            }
        ]
    
    def download_and_process_images(self, data: List[Dict[str, Any]]) -> None:
        """下载并处理图片"""
        print("开始下载和处理图片...")
        
        for i, beauty in enumerate(data, 1):
            print(f"处理 {i}/{len(data)}: {beauty['name']}")
            
            filename = f"{beauty['code']}.jpg"
            local_path = self.images_dir / filename
            
            # 如果本地已存在图片，跳过下载
            if local_path.exists():
                print(f"  图片已存在: {filename}")
                # 检查并压缩现有图片
                if self.compress_image(local_path):
                    beauty['local_image_path'] = str(local_path)
                continue
            
            # 使用搜图引擎下载图片
            downloaded_filename = self.fetch_and_download_beautiful_woman(beauty['code'], beauty['name'])
            if downloaded_filename:
                downloaded_path = self.images_dir / downloaded_filename
                # 压缩下载的图片
                if self.compress_image(downloaded_path):
                    beauty['local_image_path'] = str(downloaded_path)
                    print(f"  ✅ 下载并处理完成: {beauty['name']}")
                else:
                    print(f"  ❌ 图片压缩失败: {beauty['name']}")
            else:
                print(f"  ❌ 下载失败: {beauty['name']}")
            
            # 添加延时避免请求过快
            time.sleep(2)
    
    def upload_images_to_oss(self, data: List[Dict[str, Any]]) -> None:
        """上传图片到OSS"""
        print("开始上传图片到OSS...")
        
        if not self.bucket:
            print("OSS客户端未初始化，跳过上传")
            return
        
        for beauty in data:
            if 'local_image_path' not in beauty:
                print(f"  跳过 {beauty['name']}: 无本地图片")
                continue
            
            local_path = Path(beauty['local_image_path'])
            if not local_path.exists():
                print(f"  跳过 {beauty['name']}: 本地文件不存在")
                continue
            
            # 上传到OSS
            oss_key = f"images/beautiful_women/{beauty['code']}.jpg"
            oss_url = self.upload_to_oss(local_path, oss_key)
            
            if oss_url:
                beauty['image_url'] = oss_url
                print(f"  ✓ 上传成功: {beauty['name']}")
            else:
                print(f"  ✗ 上传失败: {beauty['name']}")
            
            # 添加延时
            time.sleep(0.5)
    
    def generate_frontend_data(self, data: List[Dict[str, Any]]) -> None:
        """生成前端数据文件"""
        print("生成前端数据文件...")
        
        # 简化数据结构，只保留前端需要的字段
        frontend_data = []
        for beauty in data:
            frontend_data.append({
                'code': beauty['code'],
                'name': beauty['name'],
                'fileName': f"{beauty['code']}.jpg"
            })
        
        # 生成JavaScript文件内容
        js_content = f'''/**
 * 世界最美女人数据
 * 自动生成，请勿手动修改
 */

export const beautifulWomen = {json.dumps(frontend_data, ensure_ascii=False, indent=2)};

// 数据完整性验证
export function validateBeautifulWomenData() {{
  const requiredFields = ['code', 'name', 'fileName'];
  
  for (const woman of beautifulWomen) {{
    for (const field of requiredFields) {{
      if (!woman[field]) {{
        console.error(`美女数据缺少必需字段: ${{field}}`, woman);
        return false;
      }}
    }}
  }}
  
  console.log(`美女数据验证通过，共 ${{beautifulWomen.length}} 条记录`);
  return true;
}}

// 导出数据长度
export const BEAUTIFUL_WOMEN_COUNT = beautifulWomen.length;
'''
        
        # 写入文件
        with open(self.frontend_data_path, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"前端数据文件已生成: {self.frontend_data_path}")
    
    def save_data_json(self, data: List[Dict[str, Any]]) -> None:
        """保存完整数据到JSON文件"""
        json_path = os.path.join(self.output_dir, 'beautiful_women_data.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"完整数据已保存: {json_path}")
    
    def run(self) -> None:
        """运行主流程"""
        print("=== 世界最美女人数据获取开始 ===")
        
        try:
            # 1. 获取数据
            data = self.get_beautiful_women_data()
            print(f"获取到 {len(data)} 位世界最美女人数据")
            
            # 2. 下载和处理图片
            self.download_and_process_images(data)
            
            # 3. 上传到OSS
            self.upload_images_to_oss(data)
            
            # 4. 生成前端数据文件
            self.generate_frontend_data(data)
            
            # 5. 保存完整数据
            self.save_data_json(data)
            
            print("=== 世界最美女人数据获取完成 ===")
            
        except Exception as e:
            print(f"错误: {e}")
            raise

def main():
    """主函数"""
    print("🚀 开始获取美女考试数据...")
    
    fetcher = BeautifulWomenFetcher()
    
    # 初始化OSS客户端
    if not fetcher.bucket:
        print("❌ OSS客户端初始化失败，退出程序")
        return
    
    # 获取美女数据
    print("\n📋 获取美女数据...")
    data = fetcher.get_beautiful_women_data()
    print(f"✅ 获取到 {len(data)} 位美女数据")
    
    # 下载并处理图片
    print("\n📥 下载并处理图片...")
    fetcher.download_and_process_images(data)
    
    # 上传图片到OSS
    print("\n☁️ 上传图片到OSS...")
    fetcher.upload_images_to_oss(data)
    
    # 生成前端数据文件
    print("\n📝 生成前端数据文件...")
    fetcher.generate_frontend_data(data)
    
    # 保存完整数据
    fetcher.save_data_json(data)
    
    # 统计结果
    successful_downloads = sum(1 for beauty in data if 'image_url' in beauty)
    print(f"\n✅ 美女考试数据获取完成！")
    print(f"📊 成功处理: {successful_downloads}/{len(data)} 张图片")
    print(f"📁 数据文件: {fetcher.frontend_data_path}")
    print(f"🖼️ 图片目录: {fetcher.images_dir}")
    
    if successful_downloads < len(data):
        print(f"⚠️ 有 {len(data) - successful_downloads} 张图片未能成功处理")

if __name__ == '__main__':
    main()