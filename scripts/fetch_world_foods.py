import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "world_foods")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "worldFoods.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 世界各国著名食物数据（50个国家，总共约80个食物）
WORLD_FOODS = [
    # 中国 - 3个
    {"country": "中国", "food": "北京烤鸭", "difficulty": 3},
    {"country": "中国", "food": "麻婆豆腐", "difficulty": 3},
    {"country": "中国", "food": "小笼包", "difficulty": 3},
    
    # 意大利 - 3个
    {"country": "意大利", "food": "意大利面", "difficulty": 2},
    {"country": "意大利", "food": "披萨", "difficulty": 1},
    {"country": "意大利", "food": "提拉米苏", "difficulty": 2},
    
    # 法国 - 3个
    {"country": "法国", "food": "法式鹅肝", "difficulty": 3},
    {"country": "法国", "food": "马卡龙", "difficulty": 2},
    {"country": "法国", "food": "可颂面包", "difficulty": 2},
    
    # 日本 - 3个
    {"country": "日本", "food": "寿司", "difficulty": 1},
    {"country": "日本", "food": "拉面", "difficulty": 2},
    {"country": "日本", "food": "天妇罗", "difficulty": 2},
    
    # 韩国 - 2个
    {"country": "韩国", "food": "泡菜", "difficulty": 2},
    {"country": "韩国", "food": "韩式烤肉", "difficulty": 2},
    
    # 美国 - 2个
    {"country": "美国", "food": "汉堡包", "difficulty": 1},
    {"country": "美国", "food": "热狗", "difficulty": 1},
    
    # 英国 - 2个
    {"country": "英国", "food": "炸鱼薯条", "difficulty": 2},
    {"country": "英国", "food": "英式下午茶", "difficulty": 2},
    
    # 德国 - 2个
    {"country": "德国", "food": "德式香肠", "difficulty": 2},
    {"country": "德国", "food": "椒盐卷饼", "difficulty": 3},
    
    # 西班牙 - 2个
    {"country": "西班牙", "food": "海鲜饭", "difficulty": 2},
    {"country": "西班牙", "food": "火腿", "difficulty": 2},
    
    # 墨西哥 - 2个
    {"country": "墨西哥", "food": "玉米饼", "difficulty": 2},
    {"country": "墨西哥", "food": "墨西哥卷饼", "difficulty": 2},
    
    # 印度 - 2个
    {"country": "印度", "food": "咖喱", "difficulty": 2},
    {"country": "印度", "food": "印度飞饼", "difficulty": 2},
    
    # 泰国 - 2个
    {"country": "泰国", "food": "冬阴功汤", "difficulty": 2},
    {"country": "泰国", "food": "泰式炒河粉", "difficulty": 2},
    
    # 越南 - 2个
    {"country": "越南", "food": "越南河粉", "difficulty": 2},
    {"country": "越南", "food": "春卷", "difficulty": 2},
    
    # 土耳其 - 2个
    {"country": "土耳其", "food": "土耳其烤肉", "difficulty": 2},
    {"country": "土耳其", "food": "土耳其软糖", "difficulty": 3},
    
    # 希腊 - 2个
    {"country": "希腊", "food": "希腊沙拉", "difficulty": 2},
    {"country": "希腊", "food": "慕萨卡", "difficulty": 3},
    
    # 俄罗斯 - 2个
    {"country": "俄罗斯", "food": "罗宋汤", "difficulty": 2},
    {"country": "俄罗斯", "food": "鱼子酱", "difficulty": 2},
    
    # 巴西 - 2个
    {"country": "巴西", "food": "巴西烤肉", "difficulty": 2},
    {"country": "巴西", "food": "阿萨伊果碗", "difficulty": 3},
    
    # 阿根廷 - 1个
    {"country": "阿根廷", "food": "阿根廷牛排", "difficulty": 2},
    
    # 秘鲁 - 1个
    {"country": "秘鲁", "food": "酸橘汁腌鱼", "difficulty": 3},
    
    # 智利 - 1个
    {"country": "智利", "food": "智利海鲈鱼", "difficulty": 3},
    
    # 加拿大 - 1个
    {"country": "加拿大", "food": "枫糖浆", "difficulty": 2},
    
    # 澳大利亚 - 1个
    {"country": "澳大利亚", "food": "袋鼠肉", "difficulty": 3},
    
    # 新西兰 - 1个
    {"country": "新西兰", "food": "奇异果", "difficulty": 2},
    
    # 埃及 - 1个
    {"country": "埃及", "food": "法拉费", "difficulty": 3},
    
    # 摩洛哥 - 1个
    {"country": "摩洛哥", "food": "塔吉锅", "difficulty": 3},
    
    # 南非 - 1个
    {"country": "南非", "food": "南非干肉条", "difficulty": 3},
    
    # 以色列 - 1个
    {"country": "以色列", "food": "鹰嘴豆泥", "difficulty": 3},
    
    # 黎巴嫩 - 1个
    {"country": "黎巴嫩", "food": "黎巴嫩烤肉", "difficulty": 3},
    
    # 伊朗 - 1个
    {"country": "伊朗", "food": "波斯米饭", "difficulty": 3},
    
    # 阿富汗 - 1个
    {"country": "阿富汗", "food": "阿富汗抓饭", "difficulty": 3},
    
    # 巴基斯坦 - 1个
    {"country": "巴基斯坦", "food": "巴基斯坦烤饼", "difficulty": 3},
    
    # 孟加拉国 - 1个
    {"country": "孟加拉国", "food": "孟加拉鱼咖喱", "difficulty": 3},
    
    # 斯里兰卡 - 1个
    {"country": "斯里兰卡", "food": "斯里兰卡咖喱", "difficulty": 3},
    
    # 缅甸 - 1个
    {"country": "缅甸", "food": "缅甸茶叶沙拉", "difficulty": 3},
    
    # 马来西亚 - 1个
    {"country": "马来西亚", "food": "椰浆饭", "difficulty": 2},
    
    # 新加坡 - 1个
    {"country": "新加坡", "food": "海南鸡饭", "difficulty": 2},
    
    # 印度尼西亚 - 1个
    {"country": "印度尼西亚", "food": "印尼炒饭", "difficulty": 2},
    
    # 菲律宾 - 1个
    {"country": "菲律宾", "food": "菲律宾烤乳猪", "difficulty": 3},
    
    # 柬埔寨 - 1个
    {"country": "柬埔寨", "food": "柬埔寨鱼汤面", "difficulty": 3},
    
    # 老挝 - 1个
    {"country": "老挝", "food": "老挝青木瓜沙拉", "difficulty": 3},
    
    # 蒙古 - 1个
    {"country": "蒙古", "food": "蒙古烤羊肉", "difficulty": 3},
    
    # 尼泊尔 - 1个
    {"country": "尼泊尔", "food": "尼泊尔饺子", "difficulty": 3},
    
    # 不丹 - 1个
    {"country": "不丹", "food": "不丹辣椒奶酪", "difficulty": 3},
    
    # 马尔代夫 - 1个
    {"country": "马尔代夫", "food": "马尔代夫鱼咖喱", "difficulty": 3},
    
    # 挪威 - 1个
    {"country": "挪威", "food": "挪威三文鱼", "difficulty": 2},
    
    # 瑞典 - 1个
    {"country": "瑞典", "food": "瑞典肉丸", "difficulty": 2},
    
    # 丹麦 - 1个
    {"country": "丹麦", "food": "丹麦酥", "difficulty": 2},
    
    # 芬兰 - 1个
    {"country": "芬兰", "food": "芬兰驯鹿肉", "difficulty": 3},
    
    # 冰岛 - 1个
    {"country": "冰岛", "food": "冰岛发酵鲨鱼肉", "difficulty": 3}
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_food(food_code, food_name, country_name, save_path):
    """
    使用icrawler获取食物图片并保存（使用百度搜图）
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{food_name} {country_name} 美食 高清",
        f"{food_name} 传统美食",
        f"{food_name} 料理 菜品",
        f"{country_name} {food_name}",
        f"{food_name} 食物图片",
        f"{food_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue

        temp_filename = new_files.pop()
        temp_filepath = os.path.join(save_path, temp_filename)

        # 检查文件大小
        file_size = os.path.getsize(temp_filepath)
        if file_size < 5000:  # 小于5KB的图片可能质量不好
            print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
            os.remove(temp_filepath)
            continue

        final_filename = f"{food_code}.jpg"
        final_filepath = os.path.join(save_path, final_filename)

        # 如果目标文件已存在，删除它
        if os.path.exists(final_filepath):
            os.remove(final_filepath)

        try:
            os.rename(temp_filepath, final_filepath)
            print(f"   ✅ 下载成功: {final_filename}")
            return final_filename
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            continue
    
    print(f"   ⚠️  所有查询都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_world_foods_js(foods_data):
    """创建worldFoods.js文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(JS_DATA_FILE_PATH), exist_ok=True)
        
        # 构建JS内容
        js_content = "const worldFoodsFiles = [\n"
        
        for food in foods_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{food['code']}',\n"
            js_content += f"    name: '{food['name']}',\n"
            js_content += f"    country: '{food['country']}',\n"
            js_content += f"    difficulty: {food['difficulty']},\n"
            js_content += f"    fileName: \"{food['fileName']}\"\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const worldFoods = worldFoodsFiles.map(food => ({\n"
        js_content += "  ...food,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('world_foods', food.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ worldFoods.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建worldFoods.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取世界各国著名食物数据（单线程顺序执行）")
    print(f"总共需要处理 {len(WORLD_FOODS)} 种食物")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    print(f"\n🍽️ 开始处理 {len(WORLD_FOODS)} 种世界美食...")
    
    # 顺序处理每种食物
    all_foods_data = []
    
    for index, food_item in enumerate(WORLD_FOODS, 1):
        food_code = f"food_{index}"
        food_name = food_item["food"]
        country_name = food_item["country"]
        difficulty = food_item["difficulty"]
        
        print(f"\n[{index}/{len(WORLD_FOODS)}] 处理: {country_name} - {food_name}")
        
        # 下载图片
        downloaded_file = fetch_and_download_food(food_code, food_name, country_name, IMAGE_SAVE_PATH)
        if downloaded_file:
            local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)
            
            # 压缩图片
            if compress_image(local_file_path):
                # 上传到OSS
                oss_key = f"images/world_foods/{downloaded_file}"
                if upload_to_oss(bucket, local_file_path, oss_key):
                    all_foods_data.append({
                        'code': food_code,
                        'name': food_name,
                        'country': country_name,
                        'difficulty': difficulty,
                        'fileName': downloaded_file
                    })
                    print(f"   ✅ 处理成功")
                else:
                    print(f"   ❌ OSS上传失败")
            else:
                print(f"   ❌ 图片压缩失败")
        else:
            print(f"   ❌ 图片下载失败")
        
        # 添加延迟避免被限流
        time.sleep(2)
    
    print(f"\n📝 处理完成，成功添加了{len(all_foods_data)}种世界美食")
    
    # 创建worldFoods.js文件
    if all_foods_data:
        create_world_foods_js(all_foods_data)
        
        print(f"\n✨ 世界美食数据预览：")
        for food in all_foods_data[:5]:  # 只显示前5个
            print(f"   {food['code']}: {food['country']} - {food['name']}")
        if len(all_foods_data) > 5:
            print(f"   ... 还有{len(all_foods_data)-5}种")
    
    print("\n========================================")
    print("世界各国著名食物数据获取完成！")
    print("========================================")

if __name__ == "__main__":
    main()