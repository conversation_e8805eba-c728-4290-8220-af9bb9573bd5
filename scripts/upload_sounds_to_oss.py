#!/usr/bin/env python3
"""
上传音频文件到阿里云OSS的脚本
"""

import os
import sys
import json
from pathlib import Path
import oss2
from typing import List, Dict, Tuple

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 路径配置
SCRIPT_DIR = Path(__file__).parent
PROJECT_ROOT = SCRIPT_DIR.parent
SOUNDS_DIR = PROJECT_ROOT / "frontend" / "public" / "sounds"

# 音频分类目录
SOUND_CATEGORIES = [
    'pinyin',
    '26word'
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"初始化OSS客户端失败: {e}")
        return None

def get_all_sounds() -> List[Tuple[str, Path]]:
    """获取所有需要上传的音频文件"""
    sounds = []
    
    for category in SOUND_CATEGORIES:
        category_dir = SOUNDS_DIR / category
        if not category_dir.exists():
            print(f"警告: 目录不存在 {category_dir}")
            continue
            
        for sound_file in category_dir.iterdir():
            if sound_file.is_file() and sound_file.suffix.lower() in ['.mp3', '.wav', '.ogg', '.m4a']:
                # OSS中的对象键格式: sounds/category/filename
                oss_key = f"sounds/{category}/{sound_file.name}"
                sounds.append((oss_key, sound_file))
    
    return sounds

def upload_sound_to_oss(bucket, oss_key: str, local_path: Path) -> bool:
    """上传单个音频文件到OSS"""
    try:
        # 上传文件
        result = bucket.put_object_from_file(oss_key, str(local_path))
        
        # 检查是否上传成功
        if result.status == 200:
            print(f"✓ 上传成功: {oss_key}")
            return True
        else:
            print(f"✗ 上传失败: {oss_key}, 状态码: {result.status}")
            return False
            
    except Exception as e:
        print(f"✗ 上传失败: {oss_key}, 错误: {e}")
        return False

def set_bucket_policy(bucket):
    """设置存储桶策略，使音频可以公开读取"""
    try:
        # 设置存储桶的公共读权限
        bucket.put_bucket_acl(oss2.BUCKET_ACL_PUBLIC_READ)
        print("✓ 设置存储桶公共读权限成功")
    except Exception as e:
        print(f"✗ 设置存储桶权限失败: {e}")

def generate_oss_url(oss_key: str) -> str:
    """生成OSS公共访问URL"""
    return f"https://{OSS_BUCKET_NAME}.{OSS_ENDPOINT}/{oss_key}"

def main():
    print("开始上传音频文件到阿里云OSS...")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        sys.exit(1)
    
    # 设置存储桶权限
    set_bucket_policy(bucket)
    
    # 获取所有音频文件
    sounds = get_all_sounds()
    print(f"找到 {len(sounds)} 个音频文件")
    
    if not sounds:
        print("没有找到音频文件，退出程序")
        return
    
    # 上传音频
    success_count = 0
    failed_count = 0
    
    for oss_key, local_path in sounds:
        if upload_sound_to_oss(bucket, oss_key, local_path):
            success_count += 1
        else:
            failed_count += 1
    
    print(f"\n上传完成:")
    print(f"成功: {success_count}")
    print(f"失败: {failed_count}")
    print(f"总计: {len(sounds)}")
    
    # 生成URL映射文件供后续使用
    url_mapping = {}
    for oss_key, local_path in sounds:
        category = oss_key.split('/')[1]  # 从 sounds/category/filename 中提取 category
        filename = oss_key.split('/')[-1]  # 从 sounds/category/filename 中提取 filename
        
        if category not in url_mapping:
            url_mapping[category] = {}
        
        url_mapping[category][filename] = generate_oss_url(oss_key)
    
    # 保存URL映射到文件
    mapping_file = SCRIPT_DIR / "oss_sounds_url_mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(url_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"\nURL映射已保存到: {mapping_file}")
    
    if success_count > 0:
        print(f"\n示例OSS URL:")
        first_key = list(sounds)[0][0]
        print(f"{generate_oss_url(first_key)}")

if __name__ == "__main__":
    main() 