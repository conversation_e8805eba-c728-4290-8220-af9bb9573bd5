import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "landmark_buildings")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "landmarkBuildings.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 50个国家的标志性建筑（城市和农村各一个）
LANDMARK_BUILDINGS = [
    # 亚洲国家
    {"country": "中国", "city": "天安门", "rural": "中国农村建筑"},
    {"country": "日本", "city": "东京塔", "rural": "日式茅草屋"},
    {"country": "韩国", "city": "首尔塔", "rural": "韩式传统民居"},
    {"country": "印度", "city": "泰姬陵", "rural": "印度村庄房屋"},
    {"country": "泰国", "city": "大皇宫", "rural": "泰式高脚屋"},
    {"country": "新加坡", "city": "鱼尾狮", "rural": "新加坡传统店屋"},
    {"country": "马来西亚", "city": "双子塔", "rural": "马来高脚屋"},
    {"country": "印尼", "city": "独立纪念塔", "rural": "印尼传统房屋"},
    {"country": "菲律宾", "city": "马尼拉大教堂", "rural": "菲律宾高脚屋"},
    {"country": "越南", "city": "胡志明陵墓", "rural": "越南传统民居"},
    {"country": "缅甸", "city": "仰光大金塔", "rural": "缅甸传统房屋"},
    {"country": "柬埔寨", "city": "吴哥窟", "rural": "柬埔寨高脚屋"},
    {"country": "老挝", "city": "塔銮", "rural": "老挝传统民居"},
    {"country": "蒙古", "city": "成吉思汗雕像", "rural": "蒙古包"},
    {"country": "哈萨克斯坦", "city": "巴伊杰列克塔", "rural": "哈萨克传统房屋"},
    
    # 欧洲国家
    {"country": "法国", "city": "埃菲尔铁塔", "rural": "法式乡村别墅"},
    {"country": "英国", "city": "大本钟", "rural": "英式乡村小屋"},
    {"country": "德国", "city": "新天鹅堡", "rural": "德式农舍"},
    {"country": "意大利", "city": "比萨斜塔", "rural": "托斯卡纳农庄"},
    {"country": "俄罗斯", "city": "红场", "rural": "俄式木屋"},
    {"country": "西班牙", "city": "圣家堂", "rural": "西班牙乡村房屋"},
    {"country": "荷兰", "city": "阿姆斯特丹王宫", "rural": "荷兰风车房"},
    {"country": "瑞士", "city": "马特洪峰", "rural": "瑞士木屋"},
    {"country": "奥地利", "city": "萨尔茨堡城堡", "rural": "奥地利农舍"},
    {"country": "比利时", "city": "原子球塔", "rural": "比利时乡村房屋"},
    {"country": "瑞典", "city": "斯德哥尔摩市政厅", "rural": "瑞典红房子"},
    {"country": "挪威", "city": "奥斯陆歌剧院", "rural": "挪威木屋"},
    {"country": "丹麦", "city": "小美人鱼雕像", "rural": "丹麦农舍"},
    {"country": "芬兰", "city": "赫尔辛基大教堂", "rural": "芬兰木屋"},
    {"country": "波兰", "city": "华沙文化宫", "rural": "波兰乡村房屋"},
    {"country": "捷克", "city": "布拉格城堡", "rural": "捷克乡村房屋"},
    {"country": "匈牙利", "city": "布达佩斯国会大厦", "rural": "匈牙利农舍"},
    {"country": "希腊", "city": "帕特农神庙", "rural": "希腊岛屿房屋"},
    
    # 美洲国家
    {"country": "美国", "city": "自由女神像", "rural": "美式农场房屋"},
    {"country": "加拿大", "city": "CN塔", "rural": "加拿大木屋"},
    {"country": "巴西", "city": "基督像", "rural": "巴西乡村房屋"},
    {"country": "阿根廷", "city": "方尖碑", "rural": "潘帕斯草原房屋"},
    {"country": "墨西哥", "city": "奇琴伊察", "rural": "墨西哥乡村房屋"},
    {"country": "智利", "city": "圣地亚哥大教堂", "rural": "智利乡村房屋"},
    {"country": "秘鲁", "city": "马丘比丘", "rural": "秘鲁传统房屋"},
    {"country": "哥伦比亚", "city": "波哥大大教堂", "rural": "哥伦比亚乡村房屋"},
    
    # 非洲国家
    {"country": "埃及", "city": "金字塔", "rural": "尼罗河村庄"},
    {"country": "南非", "city": "桌山", "rural": "南非传统圆屋"},
    {"country": "肯尼亚", "city": "内罗毕国家公园", "rural": "肯尼亚传统房屋"},
    {"country": "摩洛哥", "city": "哈桑二世清真寺", "rural": "摩洛哥传统房屋"},
    {"country": "尼日利亚", "city": "拉各斯国家剧院", "rural": "尼日利亚传统房屋"},
    
    # 大洋洲国家
    {"country": "澳大利亚", "city": "悉尼歌剧院", "rural": "澳洲农场房屋"},
    {"country": "新西兰", "city": "天空塔", "rural": "新西兰牧场小屋"},
    
    # 中东国家
    {"country": "阿联酋", "city": "哈利法塔", "rural": "阿拉伯传统房屋"},
    {"country": "土耳其", "city": "圣索菲亚大教堂", "rural": "土耳其乡村房屋"},
    {"country": "沙特阿拉伯", "city": "麦加大清真寺", "rural": "沙特传统房屋"},
    {"country": "伊朗", "city": "伊斯法罕清真寺", "rural": "伊朗传统房屋"},
    {"country": "以色列", "city": "哭墙", "rural": "以色列传统房屋"}
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_building(building_code, building_name, country, save_path):
    """
    使用icrawler获取建筑图片并保存
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{country} {building_name} 高清",
        f"{building_name} {country} 建筑",
        f"{country} {building_name} 标志性建筑",
        f"{building_name} 著名建筑",
        f"{country} {building_name}",
        f"{building_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue

        temp_filename = new_files.pop()
        temp_filepath = os.path.join(save_path, temp_filename)

        # 检查文件大小
        file_size = os.path.getsize(temp_filepath)
        if file_size < 5000:  # 小于5KB的图片可能质量不好
            print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
            os.remove(temp_filepath)
            continue

        final_filename = f"{building_code}.jpg"
        final_filepath = os.path.join(save_path, final_filename)

        # 如果目标文件已存在，删除它
        if os.path.exists(final_filepath):
            os.remove(final_filepath)

        try:
            os.rename(temp_filepath, final_filepath)
            print(f"   ✅ 下载成功: {final_filename}")
            return final_filename
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            continue
    
    print(f"   ⚠️  所有查询都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_landmark_buildings_js(buildings_data):
    """创建landmarkBuildings.js文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(JS_DATA_FILE_PATH), exist_ok=True)
        
        # 构建JS内容
        js_content = "const landmarkBuildingFiles = [\n"
        
        for building in buildings_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{building['code']}',\n"
            js_content += f"    country: '{building['country']}',\n"
            js_content += f"    buildingName: '{building['buildingName']}',\n"
            js_content += f"    buildingType: '{building['buildingType']}',\n"
            js_content += f"    fileName: \"{building['fileName']}\"\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const landmarkBuildings = landmarkBuildingFiles.map(building => ({\n"
        js_content += "  ...building,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('landmark_buildings', building.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ landmarkBuildings.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建landmarkBuildings.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取各国标志性建筑数据（单线程顺序执行）")
    print(f"总共需要处理 {len(LANDMARK_BUILDINGS)} 个国家，每个国家2张图片")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    print(f"\n🏛️ 开始处理 {len(LANDMARK_BUILDINGS)} 个国家的标志性建筑...")
    
    # 顺序处理每个国家的建筑
    all_buildings_data = []
    
    for index, country_data in enumerate(LANDMARK_BUILDINGS, 1):
        country = country_data["country"]
        city_building = country_data["city"]
        rural_building = country_data["rural"]
        
        print(f"\n[{index}/{len(LANDMARK_BUILDINGS)}] 处理国家: {country}")
        
        # 处理城市建筑
        city_code = f"city_{index}"
        print(f"  处理城市建筑: {city_building}")
        downloaded_city_file = fetch_and_download_building(city_code, city_building, country, IMAGE_SAVE_PATH)
        if downloaded_city_file:
            local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_city_file)
            
            # 压缩图片
            if compress_image(local_file_path):
                # 上传到OSS
                oss_key = f"images/landmark_buildings/{downloaded_city_file}"
                if upload_to_oss(bucket, local_file_path, oss_key):
                    all_buildings_data.append({
                        'code': city_code,
                        'country': country,
                        'buildingName': city_building,
                        'buildingType': 'city',
                        'fileName': downloaded_city_file
                    })
                    print(f"   ✅ 城市建筑处理成功")
                else:
                    print(f"   ❌ 城市建筑OSS上传失败")
            else:
                print(f"   ❌ 城市建筑图片压缩失败")
        else:
            print(f"   ❌ 城市建筑图片下载失败")
        
        # 添加延迟避免被限流
        time.sleep(2)
        
        # 处理农村建筑
        rural_code = f"rural_{index}"
        print(f"  处理农村建筑: {rural_building}")
        downloaded_rural_file = fetch_and_download_building(rural_code, rural_building, country, IMAGE_SAVE_PATH)
        if downloaded_rural_file:
            local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_rural_file)
            
            # 压缩图片
            if compress_image(local_file_path):
                # 上传到OSS
                oss_key = f"images/landmark_buildings/{downloaded_rural_file}"
                if upload_to_oss(bucket, local_file_path, oss_key):
                    all_buildings_data.append({
                        'code': rural_code,
                        'country': country,
                        'buildingName': rural_building,
                        'buildingType': 'rural',
                        'fileName': downloaded_rural_file
                    })
                    print(f"   ✅ 农村建筑处理成功")
                else:
                    print(f"   ❌ 农村建筑OSS上传失败")
            else:
                print(f"   ❌ 农村建筑图片压缩失败")
        else:
            print(f"   ❌ 农村建筑图片下载失败")
        
        # 添加延迟避免被限流
        time.sleep(2)
    
    print(f"\n📝 处理完成，成功添加了{len(all_buildings_data)}个建筑")
    
    # 创建landmarkBuildings.js文件
    if all_buildings_data:
        create_landmark_buildings_js(all_buildings_data)
        
        print(f"\n✨ 标志性建筑数据预览：")
        for building in all_buildings_data[:10]:  # 只显示前10个
            print(f"   {building['code']}: {building['country']} - {building['buildingName']} ({building['buildingType']})")
        if len(all_buildings_data) > 10:
            print(f"   ... 还有{len(all_buildings_data)-10}个")
    
    print("\n========================================")
    print("各国标志性建筑数据获取完成！")
    print("========================================")

if __name__ == "__main__":
    main()