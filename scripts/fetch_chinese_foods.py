import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "chinese_foods")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "chinese_foods.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 中国各城市著名食物（按知名度选择1-3个）
CHINESE_FOODS = [
    # 北京
    {"name": "北京烤鸭", "city": "北京", "fame": 5},
    {"name": "炸酱面", "city": "北京", "fame": 4},
    {"name": "豆汁", "city": "北京", "fame": 3},
    
    # 上海
    {"name": "小笼包", "city": "上海", "fame": 5},
    {"name": "生煎包", "city": "上海", "fame": 4},
    {"name": "白切鸡", "city": "上海", "fame": 3},
    
    # 广州
    {"name": "白切鸡", "city": "广州", "fame": 5},
    {"name": "肠粉", "city": "广州", "fame": 4},
    {"name": "双皮奶", "city": "广州", "fame": 3},
    
    # 成都
    {"name": "麻婆豆腐", "city": "成都", "fame": 5},
    {"name": "火锅", "city": "成都", "fame": 5},
    {"name": "担担面", "city": "成都", "fame": 4},
    
    # 西安
    {"name": "肉夹馍", "city": "西安", "fame": 5},
    {"name": "凉皮", "city": "西安", "fame": 4},
    {"name": "羊肉泡馍", "city": "西安", "fame": 4},
    
    # 重庆
    {"name": "重庆火锅", "city": "重庆", "fame": 5},
    {"name": "重庆小面", "city": "重庆", "fame": 4},
    {"name": "酸辣粉", "city": "重庆", "fame": 4},
    
    # 天津
    {"name": "狗不理包子", "city": "天津", "fame": 5},
    {"name": "煎饼果子", "city": "天津", "fame": 4},
    {"name": "麻花", "city": "天津", "fame": 3},
    
    # 南京
    {"name": "盐水鸭", "city": "南京", "fame": 5},
    {"name": "鸭血粉丝汤", "city": "南京", "fame": 4},
    
    # 杭州
    {"name": "西湖醋鱼", "city": "杭州", "fame": 5},
    {"name": "东坡肉", "city": "杭州", "fame": 4},
    {"name": "龙井虾仁", "city": "杭州", "fame": 3},
    
    # 苏州
    {"name": "松鼠桂鱼", "city": "苏州", "fame": 5},
    {"name": "阳澄湖大闸蟹", "city": "苏州", "fame": 4},
    
    # 武汉
    {"name": "热干面", "city": "武汉", "fame": 5},
    {"name": "武昌鱼", "city": "武汉", "fame": 4},
    {"name": "豆皮", "city": "武汉", "fame": 3},
    
    # 长沙
    {"name": "臭豆腐", "city": "长沙", "fame": 5},
    {"name": "口味虾", "city": "长沙", "fame": 4},
    {"name": "糖油粑粑", "city": "长沙", "fame": 3},
    
    # 青岛
    {"name": "青岛啤酒", "city": "青岛", "fame": 5},
    {"name": "蛤蜊", "city": "青岛", "fame": 4},
    
    # 大连
    {"name": "海参", "city": "大连", "fame": 5},
    {"name": "鲍鱼", "city": "大连", "fame": 4},
    
    # 哈尔滨
    {"name": "红肠", "city": "哈尔滨", "fame": 5},
    {"name": "锅包肉", "city": "哈尔滨", "fame": 4},
    
    # 沈阳
    {"name": "白肉血肠", "city": "沈阳", "fame": 4},
    {"name": "鸡架", "city": "沈阳", "fame": 3},
    
    # 兰州
    {"name": "兰州拉面", "city": "兰州", "fame": 5},
    {"name": "牛肉面", "city": "兰州", "fame": 5},
    
    # 昆明
    {"name": "过桥米线", "city": "昆明", "fame": 5},
    {"name": "汽锅鸡", "city": "昆明", "fame": 4},
    
    # 贵阳
    {"name": "酸汤鱼", "city": "贵阳", "fame": 5},
    {"name": "丝娃娃", "city": "贵阳", "fame": 3},
    
    # 南宁
    {"name": "螺蛳粉", "city": "南宁", "fame": 5},
    {"name": "老友粉", "city": "南宁", "fame": 4},
    
    # 海口
    {"name": "文昌鸡", "city": "海口", "fame": 5},
    {"name": "椰子鸡", "city": "海口", "fame": 4},
    
    # 拉萨
    {"name": "酥油茶", "city": "拉萨", "fame": 5},
    {"name": "青稞酒", "city": "拉萨", "fame": 4},
    
    # 乌鲁木齐
    {"name": "大盘鸡", "city": "乌鲁木齐", "fame": 5},
    {"name": "羊肉串", "city": "乌鲁木齐", "fame": 4},
    {"name": "抓饭", "city": "乌鲁木齐", "fame": 3},
    
    # 银川
    {"name": "手抓羊肉", "city": "银川", "fame": 5},
    {"name": "羊杂碎", "city": "银川", "fame": 3},
    
    # 呼和浩特
    {"name": "烤全羊", "city": "呼和浩特", "fame": 5},
    {"name": "奶茶", "city": "呼和浩特", "fame": 4},
    
    # 石家庄
    {"name": "驴肉火烧", "city": "石家庄", "fame": 5},
    {"name": "缸炉烧饼", "city": "石家庄", "fame": 3},
    
    # 太原
    {"name": "刀削面", "city": "太原", "fame": 5},
    {"name": "过油肉", "city": "太原", "fame": 4},
    
    # 济南
    {"name": "把子肉", "city": "济南", "fame": 4},
    {"name": "九转大肠", "city": "济南", "fame": 3},
    
    # 郑州
    {"name": "烩面", "city": "郑州", "fame": 5},
    {"name": "胡辣汤", "city": "郑州", "fame": 4},
    
    # 合肥
    {"name": "臭鳜鱼", "city": "合肥", "fame": 5},
    {"name": "庐州烤鸭", "city": "合肥", "fame": 3},
    
    # 福州
    {"name": "佛跳墙", "city": "福州", "fame": 5},
    {"name": "鱼丸", "city": "福州", "fame": 4},
    
    # 南昌
    {"name": "瓦罐汤", "city": "南昌", "fame": 4},
    {"name": "白糖糕", "city": "南昌", "fame": 3},
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_food(food_code, food_name, save_path):
    """
    使用icrawler获取中国食物图片并保存（使用百度搜图）
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{food_name} 美食 高清",
        f"{food_name} 中国菜 传统",
        f"{food_name} 特色菜",
        f"{food_name} 菜品",
        f"{food_name} 食物图片",
        f"{food_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue

        temp_filename = new_files.pop()
        temp_filepath = os.path.join(save_path, temp_filename)

        # 检查文件大小
        file_size = os.path.getsize(temp_filepath)
        if file_size < 5000:  # 小于5KB的图片可能质量不好
            print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
            os.remove(temp_filepath)
            continue

        final_filename = f"{food_code}.jpg"
        final_filepath = os.path.join(save_path, final_filename)

        # 如果目标文件已存在，删除它
        if os.path.exists(final_filepath):
            os.remove(final_filepath)

        try:
            os.rename(temp_filepath, final_filepath)
            print(f"   ✅ 下载成功: {final_filename}")
            return final_filename
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            continue
    
    print(f"   ⚠️  所有查询都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_chinese_foods_js(foods_data):
    """创建chinese_foods.js文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(JS_DATA_FILE_PATH), exist_ok=True)
        
        # 构建JS内容
        js_content = "const chineseFoodsFiles = [\n"
        
        for food in foods_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{food['code']}',\n"
            js_content += f"    name: '{food['name']}',\n"
            js_content += f"    city: '{food['city']}',\n"
            js_content += f"    fame: {food['fame']},\n"
            js_content += f"    fileName: \"{food['fileName']}\"\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const chineseFoods = chineseFoodsFiles.map(food => ({\n"
        js_content += "  ...food,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('chinese_foods', food.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ chinese_foods.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建chinese_foods.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取中国著名食物数据（单线程顺序执行）")
    print(f"总共需要处理 {len(CHINESE_FOODS)} 种食物")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    print(f"\n🍜 开始处理 {len(CHINESE_FOODS)} 种中国著名食物...")
    
    # 顺序处理每种食物
    all_foods_data = []
    
    for index, food_info in enumerate(CHINESE_FOODS, 1):
        food_code = f"chinese_food_{index}"
        food_name = food_info['name']
        print(f"\n[{index}/{len(CHINESE_FOODS)}] 处理: {food_name} ({food_info['city']})")
        
        # 下载图片
        downloaded_file = fetch_and_download_food(food_code, food_name, IMAGE_SAVE_PATH)
        if downloaded_file:
            local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)
            
            # 压缩图片
            if compress_image(local_file_path):
                # 上传到OSS
                oss_key = f"images/chinese_foods/{downloaded_file}"
                if upload_to_oss(bucket, local_file_path, oss_key):
                    all_foods_data.append({
                        'code': food_code,
                        'name': food_name,
                        'city': food_info['city'],
                        'fame': food_info['fame'],
                        'fileName': downloaded_file
                    })
                    print(f"   ✅ 处理成功")
                else:
                    print(f"   ❌ OSS上传失败")
            else:
                print(f"   ❌ 图片压缩失败")
        else:
            print(f"   ❌ 图片下载失败")
        
        # 添加延迟避免被限流
        time.sleep(2)
    
    print(f"\n📝 处理完成，成功添加了{len(all_foods_data)}种中国著名食物")
    
    # 创建chinese_foods.js文件
    if all_foods_data:
        create_chinese_foods_js(all_foods_data)
        
        print(f"\n✨ 中国著名食物数据预览：")
        for food in all_foods_data[:5]:  # 只显示前5个
            print(f"   {food['code']}: {food['name']} ({food['city']})")
        if len(all_foods_data) > 5:
            print(f"   ... 还有{len(all_foods_data)-5}种")
    
    print("\n========================================")
    print("中国著名食物数据获取完成！")
    print("========================================")

if __name__ == "__main__":
    main()