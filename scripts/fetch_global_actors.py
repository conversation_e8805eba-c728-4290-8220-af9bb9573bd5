import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "global-actors")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "globalActors.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 世界各国最具代表的男明星数据 - 按人种分类
GLOBAL_ACTORS_DATA = {
    "黄种人": {
        "中国": ["成龙", "李连杰", "周润发", "刘德华", "梁朝伟", "古天乐", "吴彦祖", "彭于晏"],
        "香港": ["周星驰", "郭富城", "张学友", "黎明", "陈伟霆"],
        "台湾": ["金城武", "阮经天", "赵又廷", "柯震东"],
        "日本": ["木村拓哉", "福山雅治", "山下智久", "三浦春马", "竹内涼真"],
        "韩国": ["李敏镐", "玄彬", "孔刘", "朴宝剑", "宋仲基", "丁海寅"],
        "泰国": ["马里奥·毛瑞尔", "纳得克·库吉米亚"],
        "越南": ["阮德海"],
        "菲律宾": ["约翰·劳埃德·克鲁兹", "皮奥洛·帕斯奎尔"],
        "印度尼西亚": ["切尔西·伊斯兰", "拉菲·阿赫马德"],
        "马来西亚": ["黄明志", "张智霖"],
        "新加坡": ["李南星", "陈天文"],
        "印度": ["阿米尔·汗", "沙鲁克·汗", "萨尔曼·汗", "兰维尔·辛格", "阿克谢·库玛尔"]
    },
    "白种人": {
        "美国": ["汤姆·汉克斯", "莱昂纳多·迪卡普里奥", "布拉德·皮特", "威尔·史密斯", "小罗伯特·唐尼", "马特·达蒙", "瑞安·雷诺兹", "克里斯·埃文斯"],
        "英国": ["休·格兰特", "科林·费尔斯", "丹尼尔·克雷格", "汤姆·希德勒斯顿", "本尼迪克特·康伯巴奇", "伊德里斯·艾尔巴"],
        "法国": ["让·雷诺", "吉约姆·卡内", "文森特·卡塞尔", "奥马尔·希"],
        "德国": ["蒂尔·施威格", "马蒂亚斯·施维赫夫", "丹尼尔·布吕尔"],
        "意大利": ["罗伯托·贝尼尼", "莫妮卡·贝鲁奇", "皮埃尔弗兰切斯科·法维诺"],
        "西班牙": ["哈维尔·巴登", "安东尼奥·班德拉斯", "佩内洛普·克鲁兹"],
        "俄罗斯": ["米拉·乔沃维奇", "马克西姆·马特维耶夫", "丹尼拉·科兹洛夫斯基"],
        "澳大利亚": ["休·杰克曼", "拉塞尔·克劳", "克里斯·海姆斯沃斯", "利亚姆·海姆斯沃斯"],
        "加拿大": ["瑞安·雷诺兹", "基努·里维斯", "塞思·罗根", "迈克尔·塞拉"],
        "瑞典": ["亚历山大·斯卡斯加德", "斯特兰·斯卡斯加德", "乔尔·金纳曼"],
        "丹麦": ["麦斯·米科尔森", "尼古拉·科斯特-瓦尔道"],
        "荷兰": ["鲁特格尔·哈尔", "米希尔·赫伊斯曼"],
        "挪威": ["阿克塞尔·亨尼", "克里斯托弗·希夫耶"],
        "波兰": ["马尔钦·多洛钦斯基", "托马斯·科特"],
        "捷克": ["伊万·特洛伊", "卡雷尔·罗登"],
        "匈牙利": ["伊万·费尼奥", "桑多尔·查尼"],
        "罗马尼亚": ["弗洛林·皮尔斯奇", "拉兹万·瓦西列斯库"],
        "希腊": ["扬尼斯·斯坦科格鲁", "阿列克斯·迪米特里亚德斯"],
        "芬兰": ["萨穆利·埃德尔曼", "彼得·弗兰岑"],
        "奥地利": ["克里斯托弗·瓦尔兹", "马克西米利安·谢尔"]
    },
    "黑种人": {
        "美国": ["威尔·史密斯", "丹泽尔·华盛顿", "摩根·弗里曼", "塞缪尔·L·杰克逊", "杰米·福克斯"],
        "英国": ["伊德里斯·艾尔巴", "约翰·博耶加", "奇维特尔·埃吉奥福"],
        "法国": ["奥马尔·希", "让·雷诺"],
        "南非": ["特里·费托", "沙尔托·科普雷"],
        "尼日利亚": ["奇维特尔·埃吉奥福", "大卫·奥伊洛沃"],
        "加纳": ["科菲·西里博", "约翰·杜马洛"],
        "牙买加": ["鲍勃·马利", "肖恩·保罗"],
        "巴西": ["拉萨罗·拉莫斯", "瓦格纳·莫拉"],
        "古巴": ["安迪·加西亚", "奥斯卡·伊萨克"]
    }
}

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_actor(actor_code, actor_name, country, race, save_path):
    """
    使用icrawler获取男明星图片并保存（百度图片搜索）
    """
    # 根据明星姓名构建搜索关键词 - 优先搜索年轻时的照片
    queries = [
        f"{actor_name} 男演员 年轻",
        f"{actor_name} 明星 帅气",
        f"{actor_name} 男星 写真",
        f"{actor_name} actor young",
        f"{actor_name} 演员 肖像",
        f"{actor_name} 明星照片",
        f"{actor_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 百度搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue

        temp_filename = new_files.pop()
        temp_filepath = os.path.join(save_path, temp_filename)

        # 检查文件大小
        file_size = os.path.getsize(temp_filepath)
        if file_size < 5000:  # 小于5KB的图片可能质量不好
            print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
            os.remove(temp_filepath)
            continue

        final_filename = f"{actor_code}.jpg"
        final_filepath = os.path.join(save_path, final_filename)

        # 如果目标文件已存在，删除它
        if os.path.exists(final_filepath):
            os.remove(final_filepath)

        try:
            os.rename(temp_filepath, final_filepath)
            print(f"   ✅ 下载成功: {final_filename}")
            return final_filename
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            continue
    
    print(f"   ⚠️  所有查询都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_global_actors_js(all_actors_data):
    """创建globalActors.js文件"""
    try:
        # 构建新的JS内容
        js_content = "const actorFiles = [\n"
        
        for item in all_actors_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{item['code']}',\n"
            js_content += f"    name: '{item['name']}',\n"
            js_content += f"    country: '{item['country']}',\n"
            js_content += f"    race: '{item['race']}',\n"
            js_content += f"    fileName: \"{item['fileName']}\"\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const globalActors = actorFiles.map(item => ({\n"
        js_content += "  ...item,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('global-actors', item.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ globalActors.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建globalActors.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取世界各国最具代表的男明星数据（百度图片搜索）")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    # 开始处理男明星数据
    all_actors_data = []
    current_code = 1
    
    for race, countries in GLOBAL_ACTORS_DATA.items():
        print(f"\n🌍 处理{race}男明星...")
        
        for country, actors in countries.items():
            print(f"\n  📍 处理{country}男明星...")
            
            for actor_name in actors:
                actor_code = f"actor_{current_code}"
                print(f"\n[{current_code}] 处理: {actor_name} ({country} - {race})")
                
                # 下载图片
                downloaded_file = fetch_and_download_actor(
                    actor_code, actor_name, country, race, IMAGE_SAVE_PATH
                )
                
                if downloaded_file:
                    local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)
                    
                    # 压缩图片
                    if compress_image(local_file_path):
                        # 上传到OSS
                        oss_key = f"images/global-actors/{downloaded_file}"
                        if upload_to_oss(bucket, local_file_path, oss_key):
                            all_actors_data.append({
                                'code': actor_code,
                                'name': actor_name,
                                'country': country,
                                'race': race,
                                'fileName': downloaded_file
                            })
                
                current_code += 1
                time.sleep(1)  # 百度搜索间隔稍长一些避免被限制
    
    print(f"\n📝 处理完成，成功添加了{len(all_actors_data)}位男明星")
    
    # 创建globalActors.js文件
    if all_actors_data:
        create_global_actors_js(all_actors_data)
        
        print(f"\n✨ 男明星数据预览：")
        for item in all_actors_data[:10]:  # 只显示前10个
            print(f"   {item['code']}: {item['name']} ({item['country']} - {item['race']})")
        if len(all_actors_data) > 10:
            print(f"   ... 还有{len(all_actors_data)-10}位")
    
    print("\n========================================")
    print("世界各国最具代表的男明星数据获取完成！（百度图片搜索）")
    print("========================================")

if __name__ == "__main__":
    main() 