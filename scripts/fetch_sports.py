import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "sports")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "sports.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 40个最常见的体育运动
SPORT_SPECIES = [
    "足球", "篮球", "乒乓球", "羽毛球", "网球",
    "游泳", "跑步", "跳高", "跳远", "举重",
    "体操", "武术", "太极拳", "瑜伽", "健身",
    "排球", "棒球", "高尔夫", "台球", "保龄球",
    "滑冰", "滑雪", "自行车", "马拉松", "拳击",
    "跆拳道", "柔道", "摔跤", "击剑", "射箭",
    "射击", "马术", "帆船", "皮划艇", "攀岩",
    "蹦床", "花样滑冰", "冰球", "橄榄球", "手球"
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_sport(sport_code, sport_name, save_path):
    """
    使用icrawler获取体育运动图片并保存（使用百度搜图）
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{sport_name} 体育运动 高清",
        f"{sport_name} 运动项目",
        f"{sport_name} 体育",
        f"{sport_name} 运动",
        f"{sport_name} 比赛",
        f"{sport_name}"
    ]
    
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 搜索: '{query}'...")
        crawler = BaiduImageCrawler(
            storage={'root_dir': save_path},
            log_level='CRITICAL'
        )
        try:
            crawler.crawl(keyword=query, max_num=1)
        except Exception as e:
            print(f"   ❌ 爬取失败: {e}")
            continue

        files_after = set(os.listdir(save_path))
        new_files = files_after - files_before

        if not new_files:
            print(f"   ❌ 未下载到文件")
            continue

        temp_filename = new_files.pop()
        temp_filepath = os.path.join(save_path, temp_filename)

        # 检查文件大小
        file_size = os.path.getsize(temp_filepath)
        if file_size < 5000:  # 小于5KB的图片可能质量不好
            print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
            os.remove(temp_filepath)
            continue

        final_filename = f"{sport_code}.jpg"
        final_filepath = os.path.join(save_path, final_filename)

        # 如果目标文件已存在，删除它
        if os.path.exists(final_filepath):
            os.remove(final_filepath)

        try:
            os.rename(temp_filepath, final_filepath)
            print(f"   ✅ 下载成功: {final_filename}")
            return final_filename
        except OSError as e:
            print(f"   ❌ 重命名失败: {e}")
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            continue
    
    print(f"   ⚠️  所有查询都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_sports_js(sports_data):
    """创建sports.js文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(JS_DATA_FILE_PATH), exist_ok=True)
        
        # 构建JS内容
        js_content = "const sportFiles = [\n"
        
        for sport in sports_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{sport['code']}',\n"
            js_content += f"    name: '{sport['name']}',\n"
            js_content += f"    fileName: \"{sport['fileName']}\"\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const sports = sportFiles.map(sport => ({\n"
        js_content += "  ...sport,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('sports', sport.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ sports.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建sports.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取常见体育运动数据（单线程顺序执行）")
    print(f"总共需要处理 {len(SPORT_SPECIES)} 个体育运动项目")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    print(f"\n🏃 开始处理 {len(SPORT_SPECIES)} 个体育运动项目...")
    
    # 顺序处理每个体育运动项目
    all_sports_data = []
    
    for index, sport_name in enumerate(SPORT_SPECIES, 1):
        sport_code = f"sport_{index}"
        print(f"\n[{index}/{len(SPORT_SPECIES)}] 处理: {sport_name}")
        
        # 下载图片
        downloaded_file = fetch_and_download_sport(sport_code, sport_name, IMAGE_SAVE_PATH)
        if downloaded_file:
            local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)
            
            # 压缩图片
            if compress_image(local_file_path):
                # 上传到OSS
                oss_key = f"images/sports/{downloaded_file}"
                if upload_to_oss(bucket, local_file_path, oss_key):
                    all_sports_data.append({
                        'code': sport_code,
                        'name': sport_name,
                        'fileName': downloaded_file
                    })
                    print(f"   ✅ 处理成功")
                else:
                    print(f"   ❌ OSS上传失败")
            else:
                print(f"   ❌ 图片压缩失败")
        else:
            print(f"   ❌ 图片下载失败")
        
        # 添加延迟避免被限流
        time.sleep(2)
    
    print(f"\n📝 处理完成，成功添加了{len(all_sports_data)}个体育运动项目")
    
    # 创建sports.js文件
    if all_sports_data:
        create_sports_js(all_sports_data)
        
        print(f"\n✨ 体育运动数据预览：")
        for sport in all_sports_data[:5]:  # 只显示前5个
            print(f"   {sport['code']}: {sport['name']}")
        if len(all_sports_data) > 5:
            print(f"   ... 还有{len(all_sports_data)-5}个")
    
    print("\n========================================")
    print("体育运动数据获取完成！")
    print("========================================")

if __name__ == "__main__":
    main()