import os
import re
import shutil
import json
import subprocess
import time
from pathlib import Path
from icrawler.builtin import BaiduImageCrawler, GoogleImageCrawler
import oss2

# --- 路径配置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
FRONTEND_DIR = os.path.join(PROJECT_ROOT, "frontend")
IMAGE_SAVE_PATH = os.path.join(FRONTEND_DIR, "public", "images", "beautiful_sceneries")
JS_DATA_FILE_PATH = os.path.join(FRONTEND_DIR, "src", "data", "beautiful_sceneries.js")

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 世界上人气最高、一致认为最美的风景
BEAUTIFUL_SCENERIES = [
    {"name": "马尔代夫海滩", "country": "马尔代夫"},
    {"name": "瑞士阿尔卑斯山", "country": "瑞士"},
    {"name": "挪威峡湾", "country": "挪威"},
    {"name": "新西兰米尔福德峡湾", "country": "新西兰"},
    {"name": "冰岛蓝湖温泉", "country": "冰岛"},
    {"name": "日本富士山", "country": "日本"},
    {"name": "法国普罗旺斯薰衣草田", "country": "法国"},
    {"name": "意大利托斯卡纳", "country": "意大利"},
    {"name": "希腊圣托里尼", "country": "希腊"},
    {"name": "土耳其卡帕多奇亚", "country": "土耳其"},
    {"name": "加拿大班夫国家公园", "country": "加拿大"},
    {"name": "美国大峡谷", "country": "美国"},
    {"name": "澳大利亚大堡礁", "country": "澳大利亚"},
    {"name": "印度泰姬陵", "country": "印度"},
    {"name": "秘鲁马丘比丘", "country": "秘鲁"},
    {"name": "智利复活节岛", "country": "智利"},
    {"name": "约旦佩特拉古城", "country": "约旦"},
    {"name": "埃及金字塔", "country": "埃及"},
    {"name": "肯尼亚马赛马拉", "country": "肯尼亚"},
    {"name": "南非好望角", "country": "南非"},
    {"name": "中国桂林山水", "country": "中国"},
    {"name": "中国张家界", "country": "中国"},
    {"name": "泰国普吉岛", "country": "泰国"},
    {"name": "印尼巴厘岛", "country": "印度尼西亚"},
    {"name": "菲律宾长滩岛", "country": "菲律宾"},
    {"name": "越南下龙湾", "country": "越南"},
    {"name": "柬埔寨吴哥窟", "country": "柬埔寨"},
    {"name": "缅甸蒲甘", "country": "缅甸"},
    {"name": "尼泊尔珠穆朗玛峰", "country": "尼泊尔"},
    {"name": "不丹虎穴寺", "country": "不丹"},
    {"name": "俄罗斯贝加尔湖", "country": "俄罗斯"},
    {"name": "芬兰北极光", "country": "芬兰"},
    {"name": "丹麦法罗群岛", "country": "丹麦"},
    {"name": "英国苏格兰高地", "country": "英国"},
    {"name": "爱尔兰莫赫悬崖", "country": "爱尔兰"},
    {"name": "西班牙伊比萨岛", "country": "西班牙"},
    {"name": "葡萄牙辛特拉", "country": "葡萄牙"},
    {"name": "摩洛哥撒哈拉沙漠", "country": "摩洛哥"},
    {"name": "巴西伊瓜苏瀑布", "country": "巴西"},
    {"name": "阿根廷巴塔哥尼亚", "country": "阿根廷"}
]

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"❌ 初始化OSS客户端失败: {e}")
        return None

def get_image_info(image_path):
    """获取图片信息"""
    try:
        # 获取图片尺寸
        result = subprocess.run([
            'sips', '-g', 'pixelWidth', '-g', 'pixelHeight', str(image_path)
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        width = None
        height = None
        
        for line in lines:
            if 'pixelWidth:' in line:
                width = int(line.split(':')[1].strip())
            elif 'pixelHeight:' in line:
                height = int(line.split(':')[1].strip())
        
        # 获取文件大小
        file_size = os.path.getsize(image_path)
        
        return width, height, file_size
    except (subprocess.CalledProcessError, ValueError, OSError) as e:
        print(f"   ⚠️  获取图片信息失败 {image_path}: {e}")
        return None, None, None

def calculate_resize_dimensions(width, height, max_size=1080):
    """计算缩放后的尺寸"""
    if width <= max_size and height <= max_size:
        return width, height
    
    if width > height:
        new_width = max_size
        new_height = int(height * max_size / width)
    else:
        new_height = max_size
        new_width = int(width * max_size / height)
    
    return new_width, new_height

def compress_image(image_path, max_file_size=300 * 1024, max_dimension=1080):
    """压缩单个图片"""
    print(f"   🔧 压缩: {os.path.basename(image_path)}")
    
    # 获取原始图片信息
    width, height, file_size = get_image_info(image_path)
    if width is None or height is None or file_size is None:
        return False
    
    # 如果文件已经符合要求，跳过
    if file_size <= max_file_size and width <= max_dimension and height <= max_dimension:
        print(f"   ✅ 已符合要求，跳过压缩")
        return True
    
    # 创建备份
    backup_path = str(image_path) + '.backup'
    try:
        subprocess.run(['cp', str(image_path), backup_path], check=True)
    except subprocess.CalledProcessError:
        print(f"   ❌ 创建备份失败")
        return False
    
    try:
        # 计算新尺寸
        new_width, new_height = calculate_resize_dimensions(width, height, max_dimension)
        
        # 第一步：如果需要，先缩放尺寸
        if new_width != width or new_height != height:
            subprocess.run([
                'sips', '-z', str(new_height), str(new_width), str(image_path)
            ], check=True, capture_output=True)
        
        # 检查缩放后的文件大小
        _, _, current_size = get_image_info(image_path)
        if current_size is None:
            raise Exception("无法获取缩放后的文件大小")
        
        # 第二步：如果文件仍然太大，调整质量
        if current_size > max_file_size:
            # 对于JPEG文件，尝试调整质量
            if str(image_path).lower().endswith(('.jpg', '.jpeg')):
                quality_levels = [90, 80, 70, 60, 50, 40]
                for quality in quality_levels:
                    subprocess.run([
                        'sips', '--setProperty', 'formatOptions', str(quality), str(image_path)
                    ], check=True, capture_output=True)
                    
                    _, _, current_size = get_image_info(image_path)
                    if current_size is None:
                        continue
                    
                    if current_size <= max_file_size:
                        break
            
            # 如果还是太大，进一步缩小尺寸
            scale_factors = [0.9, 0.8, 0.7, 0.6, 0.5]
            for scale in scale_factors:
                _, _, current_size = get_image_info(image_path)
                if current_size is None or current_size <= max_file_size:
                    break
                
                scaled_width = int(new_width * scale)
                scaled_height = int(new_height * scale)
                
                subprocess.run([
                    'sips', '-z', str(scaled_height), str(scaled_width), str(image_path)
                ], check=True, capture_output=True)
        
        # 删除备份
        os.remove(backup_path)
        print(f"   ✅ 压缩完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 压缩失败: {e}")
        # 恢复备份
        try:
            subprocess.run(['mv', backup_path, str(image_path)], check=True)
        except:
            pass
        return False

def fetch_and_download_scenery(scenery_code, scenery_name, save_path):
    """
    使用Google搜图优先，失败后使用百度搜图获取最美风景图片
    """
    # 尝试多个搜索关键词来提高成功率
    queries = [
        f"{scenery_name} beautiful landscape 4k",
        f"{scenery_name} scenic view high quality",
        f"{scenery_name} tourism photography",
        f"{scenery_name} 风景 高清",
        f"{scenery_name} 美景",
        f"{scenery_name}"
    ]
    
    # 首先尝试Google搜图
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 Google搜索: '{query}'...")
        try:
            google_crawler = GoogleImageCrawler(
                storage={'root_dir': save_path},
                log_level='CRITICAL'
            )
            google_crawler.crawl(keyword=query, max_num=1)
            
            files_after = set(os.listdir(save_path))
            new_files = files_after - files_before

            if new_files:
                temp_filename = new_files.pop()
                temp_filepath = os.path.join(save_path, temp_filename)

                # 检查文件大小
                file_size = os.path.getsize(temp_filepath)
                if file_size < 5000:  # 小于5KB的图片可能质量不好
                    print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
                    os.remove(temp_filepath)
                    continue

                final_filename = f"{scenery_code}.jpg"
                final_filepath = os.path.join(save_path, final_filename)

                # 如果目标文件已存在，删除它
                if os.path.exists(final_filepath):
                    os.remove(final_filepath)

                try:
                    os.rename(temp_filepath, final_filepath)
                    print(f"   ✅ Google下载成功: {final_filename}")
                    return final_filename
                except OSError as e:
                    print(f"   ❌ 重命名失败: {e}")
                    if os.path.exists(temp_filepath):
                        os.remove(temp_filepath)
                    continue
        except Exception as e:
            print(f"   ❌ Google搜图失败: {e}")
            continue
    
    print(f"   ⚠️  Google搜图全部失败，尝试百度搜图...")
    
    # Google失败后尝试百度搜图
    for query in queries:
        files_before = set(os.listdir(save_path))

        print(f"   🔍 百度搜索: '{query}'...")
        try:
            baidu_crawler = BaiduImageCrawler(
                storage={'root_dir': save_path},
                log_level='CRITICAL'
            )
            baidu_crawler.crawl(keyword=query, max_num=1)
            
            files_after = set(os.listdir(save_path))
            new_files = files_after - files_before

            if new_files:
                temp_filename = new_files.pop()
                temp_filepath = os.path.join(save_path, temp_filename)

                # 检查文件大小
                file_size = os.path.getsize(temp_filepath)
                if file_size < 5000:  # 小于5KB的图片可能质量不好
                    print(f"   ⚠️  文件过小 ({file_size} bytes)，尝试下一个查询...")
                    os.remove(temp_filepath)
                    continue

                final_filename = f"{scenery_code}.jpg"
                final_filepath = os.path.join(save_path, final_filename)

                # 如果目标文件已存在，删除它
                if os.path.exists(final_filepath):
                    os.remove(final_filepath)

                try:
                    os.rename(temp_filepath, final_filepath)
                    print(f"   ✅ 百度下载成功: {final_filename}")
                    return final_filename
                except OSError as e:
                    print(f"   ❌ 重命名失败: {e}")
                    if os.path.exists(temp_filepath):
                        os.remove(temp_filepath)
                    continue
        except Exception as e:
            print(f"   ❌ 百度搜图失败: {e}")
            continue
    
    print(f"   ⚠️  所有搜索引擎都失败了")
    return None

def upload_to_oss(bucket, local_file_path, oss_key):
    """上传文件到OSS"""
    try:
        result = bucket.put_object_from_file(oss_key, str(local_file_path))
        if result.status == 200:
            print(f"   ✅ OSS上传成功: {oss_key}")
            return True
        else:
            print(f"   ❌ OSS上传失败: {oss_key}, 状态码: {result.status}")
            return False
    except Exception as e:
        print(f"   ❌ OSS上传异常: {e}")
        return False

def create_beautiful_sceneries_js(sceneries_data):
    """创建beautiful_sceneries.js文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(JS_DATA_FILE_PATH), exist_ok=True)
        
        # 构建JS内容
        js_content = "const beautifulSceneryFiles = [\n"
        
        for scenery in sceneries_data:
            js_content += f"  {{\n"
            js_content += f"    code: '{scenery['code']}',\n"
            js_content += f"    name: '{scenery['name']}',\n"
            js_content += f"    country: '{scenery['country']}',\n"
            js_content += f"    fileName: \"{scenery['fileName']}\"\n"
            js_content += f"  }},\n"
        
        js_content += "];\n\n"
        js_content += "import imageConfig from '../config/imageConfig.js';\n\n"
        js_content += "export const beautifulSceneries = beautifulSceneryFiles.map(scenery => ({\n"
        js_content += "  ...scenery,\n"
        js_content += "  imageUrl: imageConfig.getImageUrl('beautiful_sceneries', scenery.fileName)\n"
        js_content += "}));"
        
        # 写入文件
        with open(JS_DATA_FILE_PATH, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"✅ beautiful_sceneries.js文件创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建beautiful_sceneries.js失败: {e}")
        return False

def main():
    """主函数"""
    print("========================================")
    print("开始获取世界最美风景数据（单线程顺序执行）")
    print(f"总共需要处理 {len(BEAUTIFUL_SCENERIES)} 个最美风景")
    print("========================================")
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        return
    
    # 确保图片保存目录存在
    os.makedirs(IMAGE_SAVE_PATH, exist_ok=True)
    
    print(f"\n🏞️ 开始处理 {len(BEAUTIFUL_SCENERIES)} 个最美风景...")
    
    # 顺序处理每个风景
    all_sceneries_data = []
    
    for index, scenery in enumerate(BEAUTIFUL_SCENERIES, 1):
        scenery_code = f"scenery_{index}"
        scenery_name = scenery["name"]
        scenery_country = scenery["country"]
        print(f"\n[{index}/{len(BEAUTIFUL_SCENERIES)}] 处理: {scenery_name} ({scenery_country})")
        
        # 下载图片
        downloaded_file = fetch_and_download_scenery(scenery_code, scenery_name, IMAGE_SAVE_PATH)
        if downloaded_file:
            local_file_path = os.path.join(IMAGE_SAVE_PATH, downloaded_file)
            
            # 压缩图片
            if compress_image(local_file_path):
                # 上传到OSS
                oss_key = f"images/beautiful_sceneries/{downloaded_file}"
                if upload_to_oss(bucket, local_file_path, oss_key):
                    all_sceneries_data.append({
                        'code': scenery_code,
                        'name': scenery_name,
                        'country': scenery_country,
                        'fileName': downloaded_file
                    })
                    print(f"   ✅ 处理成功")
                else:
                    print(f"   ❌ OSS上传失败")
            else:
                print(f"   ❌ 图片压缩失败")
        else:
            print(f"   ❌ 图片下载失败")
        
        # 添加延迟避免被限流
        time.sleep(3)
    
    print(f"\n📝 处理完成，成功添加了{len(all_sceneries_data)}个最美风景")
    
    # 创建beautiful_sceneries.js文件
    if all_sceneries_data:
        create_beautiful_sceneries_js(all_sceneries_data)
        
        print(f"\n✨ 最美风景数据预览：")
        for scenery in all_sceneries_data[:5]:  # 只显示前5个
            print(f"   {scenery['code']}: {scenery['name']} ({scenery['country']})")
        if len(all_sceneries_data) > 5:
            print(f"   ... 还有{len(all_sceneries_data)-5}个")
    
    print("\n========================================")
    print("最美风景数据获取完成！")
    print("========================================")

if __name__ == "__main__":
    main()