#!/usr/bin/env python3
"""
上传图片到阿里云OSS的脚本
"""

import os
import sys
import json
from pathlib import Path
import oss2
from typing import List, Dict, Tuple
from PIL import Image
import tempfile

# OSS配置
OSS_ENDPOINT = 'oss-cn-shanghai.aliyuncs.com'
OSS_ACCESS_KEY_ID = 'LTAI5t6kHqkgzhiNRfCPX3Ls'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_BUCKET_NAME = 'kpe-system'

# 路径配置
SCRIPT_DIR = Path(__file__).parent
PROJECT_ROOT = SCRIPT_DIR.parent
IMAGES_DIR = PROJECT_ROOT / "frontend" / "public" / "images"

# 图片分类目录
IMAGE_CATEGORIES = [
    'animals',
    'car-logos', 
    'china-landmarks',
    'country-flags',
    'plants',
    'world-landmarks',
    'ethnicities',
    'world-flags-plus',
    'japanese-actresses'  # 添加日本女优目录
]

# 压缩配置
MAX_FILE_SIZE = 150 * 1024  # 150KB
QUALITY_STEPS = [85, 75, 65, 55, 45]  # 逐步降低质量
MAX_DIMENSION = 800  # 最大尺寸

def init_oss_client():
    """初始化OSS客户端"""
    try:
        auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET_NAME)
        return bucket
    except Exception as e:
        print(f"初始化OSS客户端失败: {e}")
        return None

def compress_image(image_path: Path, max_size: int = MAX_FILE_SIZE) -> Path:
    """
    压缩图片到指定大小以下
    
    Args:
        image_path: 原始图片路径
        max_size: 最大文件大小（字节）
    
    Returns:
        压缩后的临时文件路径
    """
    print(f"  压缩图片: {image_path.name}")
    
    with Image.open(image_path) as img:
        # 转换为RGB模式（如果是RGBA）
        if img.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            if img.mode == 'P':
                img = img.convert('RGBA')
            background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
            img = background
        
        # 获取原始尺寸
        original_width, original_height = img.size
        print(f"    原始尺寸: {original_width}x{original_height}")
        
        # 如果尺寸过大，先调整尺寸
        if max(original_width, original_height) > MAX_DIMENSION:
            ratio = MAX_DIMENSION / max(original_width, original_height)
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            print(f"    调整尺寸: {new_width}x{new_height}")
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
        temp_path = Path(temp_file.name)
        temp_file.close()
        
        # 尝试不同的质量设置
        for quality in QUALITY_STEPS:
            img.save(temp_path, 'JPEG', quality=quality, optimize=True)
            file_size = temp_path.stat().st_size
            print(f"    质量 {quality}: {file_size} 字节")
            
            if file_size <= max_size:
                print(f"    ✓ 压缩成功: {file_size} 字节 (质量: {quality})")
                return temp_path
        
        # 如果还是太大，进一步缩小尺寸
        current_width, current_height = img.size
        for scale in [0.8, 0.6, 0.4]:
            new_width = int(current_width * scale)
            new_height = int(current_height * scale)
            scaled_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            for quality in QUALITY_STEPS:
                scaled_img.save(temp_path, 'JPEG', quality=quality, optimize=True)
                file_size = temp_path.stat().st_size
                print(f"    尺寸 {new_width}x{new_height}, 质量 {quality}: {file_size} 字节")
                
                if file_size <= max_size:
                    print(f"    ✓ 压缩成功: {file_size} 字节 (尺寸: {new_width}x{new_height}, 质量: {quality})")
                    return temp_path
        
        # 如果仍然无法满足要求，使用最小设置
        final_quality = min(QUALITY_STEPS)
        img.save(temp_path, 'JPEG', quality=final_quality, optimize=True)
        final_size = temp_path.stat().st_size
        print(f"    ⚠ 使用最小设置: {final_size} 字节 (质量: {final_quality})")
        return temp_path

def get_all_images() -> List[Tuple[str, Path]]:
    """获取所有需要上传的图片文件"""
    images = []
    
    for category in IMAGE_CATEGORIES:
        category_dir = IMAGES_DIR / category
        if not category_dir.exists():
            print(f"警告: 目录不存在 {category_dir}")
            continue
            
        for img_file in category_dir.iterdir():
            if img_file.is_file() and img_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                # OSS中的对象键格式: images/category/filename
                oss_key = f"images/{category}/{img_file.name}"
                images.append((oss_key, img_file))
    
    return images

def upload_image_to_oss(bucket, oss_key: str, local_path: Path) -> bool:
    """上传单个图片到OSS，如果文件过大则先压缩"""
    try:
        # 检查文件大小
        file_size = local_path.stat().st_size
        print(f"上传: {oss_key} ({file_size} 字节)")
        
        upload_path = local_path
        temp_path = None
        
        # 如果文件过大，进行压缩
        if file_size > MAX_FILE_SIZE:
            print(f"  文件过大，开始压缩...")
            temp_path = compress_image(local_path, MAX_FILE_SIZE)
            upload_path = temp_path
        
        # 上传文件
        result = bucket.put_object_from_file(oss_key, str(upload_path))
        
        # 清理临时文件
        if temp_path and temp_path.exists():
            temp_path.unlink()
        
        # 检查是否上传成功
        if result.status == 200:
            print(f"✓ 上传成功: {oss_key}")
            return True
        else:
            print(f"✗ 上传失败: {oss_key}, 状态码: {result.status}")
            return False
            
    except Exception as e:
        print(f"✗ 上传失败: {oss_key}, 错误: {e}")
        return False

def set_bucket_policy(bucket):
    """设置存储桶策略，使图片可以公开读取"""
    try:
        # 设置存储桶的公共读权限
        bucket.put_bucket_acl(oss2.BUCKET_ACL_PUBLIC_READ)
        print("✓ 设置存储桶公共读权限成功")
    except Exception as e:
        print(f"✗ 设置存储桶权限失败: {e}")

def generate_oss_url(oss_key: str) -> str:
    """生成OSS公共访问URL"""
    return f"https://{OSS_BUCKET_NAME}.{OSS_ENDPOINT}/{oss_key}"

def main():
    print("开始上传图片到阿里云OSS...")
    
    # 检查PIL是否可用
    try:
        from PIL import Image
        print("✓ PIL库检查通过")
    except ImportError:
        print("✗ 错误: 需要安装PIL库 (pip install Pillow)")
        sys.exit(1)
    
    # 初始化OSS客户端
    bucket = init_oss_client()
    if not bucket:
        print("OSS客户端初始化失败，退出程序")
        sys.exit(1)
    
    # 设置存储桶权限
    set_bucket_policy(bucket)
    
    # 获取所有图片
    images = get_all_images()
    print(f"找到 {len(images)} 个图片文件")
    
    if not images:
        print("没有找到图片文件，退出程序")
        return
    
    # 上传图片
    success_count = 0
    failed_count = 0
    
    for oss_key, local_path in images:
        if upload_image_to_oss(bucket, oss_key, local_path):
            success_count += 1
        else:
            failed_count += 1
    
    print(f"\n上传完成:")
    print(f"成功: {success_count}")
    print(f"失败: {failed_count}")
    print(f"总计: {len(images)}")
    
    # 生成URL映射文件供后续使用
    url_mapping = {}
    for oss_key, local_path in images:
        category = oss_key.split('/')[1]  # 从 images/category/filename 中提取 category
        filename = oss_key.split('/')[-1]  # 从 images/category/filename 中提取 filename
        
        if category not in url_mapping:
            url_mapping[category] = {}
        
        url_mapping[category][filename] = generate_oss_url(oss_key)
    
    # 保存URL映射到文件
    mapping_file = SCRIPT_DIR / "oss_url_mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(url_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"\nURL映射已保存到: {mapping_file}")
    
    if success_count > 0:
        print(f"\n示例OSS URL:")
        first_key = list(images)[0][0]
        print(f"{generate_oss_url(first_key)}")

if __name__ == "__main__":
    main() 