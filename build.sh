#!/bin/bash

echo "🚀 构建KPE全栈项目..."

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 检查必要的工具
echo "🔍 检查必要工具..."

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误：Maven未安装或不在PATH中"
    exit 1
fi
echo "✅ Maven已安装"

# 检查pnpm
if ! command -v pnpm &> /dev/null; then
    echo "❌ 错误：pnpm未安装或不在PATH中"
    echo "💡 请先安装pnpm: npm install -g pnpm"
    exit 1
fi
echo "✅ pnpm已安装"

# 构建后端
echo ""
echo "📦 构建后端项目..."
cd backend

if [ ! -f "pom.xml" ]; then
    echo "❌ 错误：backend目录中未找到pom.xml文件"
    exit 1
fi

echo "正在执行: mvn clean install -DskipTests"
mvn clean install -DskipTests

if [ $? -eq 0 ]; then
    echo "✅ 后端构建成功！"
else
    echo "❌ 后端构建失败，请检查错误信息"
    exit 1
fi

# 回到项目根目录
cd "$SCRIPT_DIR"

# 构建前端
echo ""
echo "🎨 构建前端项目..."
cd frontend

if [ ! -f "package.json" ]; then
    echo "❌ 错误：frontend目录中未找到package.json文件"
    exit 1
fi

echo "正在执行: pnpm install"
pnpm install

if [ $? -eq 0 ]; then
    echo "✅ 前端依赖安装成功！"
else
    echo "❌ 前端依赖安装失败，请检查错误信息"
    exit 1
fi

# 回到项目根目录
cd "$SCRIPT_DIR"

echo ""
echo "🎉 全栈项目构建完成！"
echo "📝 构建摘要："
echo "   ✅ 后端Maven模块已安装到本地仓库"
echo "   ✅ 前端依赖已安装完成"
echo ""
echo "💡 接下来可以："
echo "   • 使用 ./start.sh 启动项目"
echo "   • 或分别启动后端和前端："
echo "     - 后端: cd backend && ./start-backend.sh"
echo "     - 前端: cd frontend && pnpm dev" 