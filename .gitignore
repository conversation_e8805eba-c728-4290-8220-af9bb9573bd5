# KPE 项目根目录 .gitignore

# ===== 操作系统相关 =====
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===== IDE 相关 =====
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

pnpm-lock.yaml
pnpm-workspace.yaml

# Visual Studio Code
.vscode/
*.code-workspace

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# ===== 日志文件 =====
*.log
logs/
*.log.*

# ===== 临时文件 =====
*.tmp
*.temp
*.cache
*.pid
*.seed
*.pid.lock

# ===== 环境配置文件 =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===== 备份文件 =====
*.bak
*.backup
*.orig
*.rej

# ===== 压缩文件 =====
*.zip
*.tar.gz
*.rar
*.7z

# ===== 项目特定 =====
# 构建输出目录
build/
dist/
out/

# 依赖目录
node_modules/
# target/ (由后端子项目管理)

# 测试覆盖率报告
coverage/
*.lcov

# 文档生成
docs/generated/

# 部署相关
deploy/
*.deploy

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 证书文件
*.pem
*.key
*.crt
*.p12
*.pfx

# 配置文件中的敏感信息
config/local.yml
config/production.yml
application-local.yml
application-prod.yml

# 运行时文件
*.pid
nohup.out

# 编辑器临时文件
*~
.#*
\#*#
.*.swp
.*.swo

# 系统文件
.fseventsd
.DocumentRevisions-V100
.TemporaryItems
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
